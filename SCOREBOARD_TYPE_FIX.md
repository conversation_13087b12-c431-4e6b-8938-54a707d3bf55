# GameScoreboard 类型冲突修复

## 🔧 问题描述

**错误信息**: 
```
Type mismatch: cannot convert from org.bukkit.scoreboard.ScoreboardManager to com.projectSource.ultimateManhurt.scoreboard.ScoreboardManager
```

**原因**: 
我们的自定义类 `com.projectSource.ultimateManhurt.scoreboard.ScoreboardManager` 与 Bukkit 的 `org.bukkit.scoreboard.ScoreboardManager` 类名冲突，导致编译器无法正确识别类型。

## ✅ 修复方案

### 1. **明确指定类型**
```java
// 修复前 (有歧义)
ScoreboardManager manager = Bukkit.getScoreboardManager();

// 修复后 (明确指定)
org.bukkit.scoreboard.ScoreboardManager manager = Bukkit.getScoreboardManager();
```

### 2. **添加空值检查**
```java
// 增加安全检查
org.bukkit.scoreboard.ScoreboardManager manager = Bukkit.getScoreboardManager();
if (manager == null) {
    throw new IllegalStateException("ScoreboardManager is not available");
}
```

### 3. **修复过期API**
```java
// 修复前 (过期API)
this.objective = scoreboard.registerNewObjective("manhunt", "dummy", Component.text(""));

// 修复后 (现代API)
this.objective = scoreboard.registerNewObjective("manhunt", Criteria.DUMMY, Component.text(""));
```

## 📋 修复的具体位置

### 构造函数中的计分板创建
```java
// 创建计分板
org.bukkit.scoreboard.ScoreboardManager manager = Bukkit.getScoreboardManager();
if (manager == null) {
    throw new IllegalStateException("ScoreboardManager is not available");
}
this.scoreboard = manager.getNewScoreboard();
this.objective = scoreboard.registerNewObjective("manhunt", Criteria.DUMMY, Component.text(""));
this.objective.setDisplaySlot(DisplaySlot.SIDEBAR);
```

### hide()方法中的计分板重置
```java
public void hide() {
    org.bukkit.scoreboard.ScoreboardManager manager = Bukkit.getScoreboardManager();
    if (manager != null) {
        player.setScoreboard(manager.getMainScoreboard());
    }
}
```

## 🎯 为什么会出现这个问题？

1. **包名冲突**: 我们创建了与Bukkit同名的类
2. **导入歧义**: Java编译器无法确定使用哪个ScoreboardManager
3. **API版本**: 使用了过期的字符串参数而不是Criteria枚举

## 🚀 最佳实践建议

### 1. **避免与核心API同名**
```java
// 好的命名
com.projectSource.ultimateManhurt.scoreboard.ManhuntScoreboardManager

// 避免的命名
com.projectSource.ultimateManhurt.scoreboard.ScoreboardManager (与Bukkit冲突)
```

### 2. **明确导入**
```java
// 在需要时明确指定完整类名
org.bukkit.scoreboard.ScoreboardManager bukkitManager = ...;
com.projectSource.ultimateManhurt.scoreboard.ScoreboardManager customManager = ...;
```

### 3. **使用现代API**
```java
// 使用枚举而不是字符串
Criteria.DUMMY  // ✅ 推荐
"dummy"         // ❌ 过期
```

## ✅ 修复结果

- ✅ 类型冲突已解决
- ✅ 编译错误已修复
- ✅ 使用现代API
- ✅ 添加了安全检查
- ✅ 代码更加健壮

现在GameScoreboard可以正常编译和运行了！
