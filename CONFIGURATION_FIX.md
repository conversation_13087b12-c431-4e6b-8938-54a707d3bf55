# 配置文件加载问题修复

## 问题描述

在重载插件后出现了以下错误：
```
[WARN]: [UltimateManhurt] 加载物品失败 (槽位 X): class org.bukkit.configuration.MemorySection cannot be cast to class java.util.Map
```

## 问题原因

这个问题发生在`CustomTemplateManager.java`中加载StartKit模板时，代码尝试将Bukkit配置文件中的`MemorySection`对象强制转换为`Map<String, Object>`。

### 原始问题代码
```java
// 错误的代码
Map<String, Object> itemsSection = config.getConfigurationSection("items").getValues(false);
for (Map.Entry<String, Object> entry : itemsSection.entrySet()) {
    @SuppressWarnings("unchecked")
    Map<String, Object> itemData = (Map<String, Object>) entry.getValue(); // 这里会失败
    ItemStack item = ItemStack.deserialize(itemData);
}
```

## 修复方案

修改了`CustomTemplateManager.java`中的`loadTemplateFromYaml`方法，正确处理Bukkit配置文件的层级结构：

### 修复后的代码
```java
// 正确的代码
if (config.contains("items")) {
    ConfigurationSection itemsSection = config.getConfigurationSection("items");
    if (itemsSection != null) {
        for (String slotKey : itemsSection.getKeys(false)) {
            try {
                int slot = Integer.parseInt(slotKey);
                ConfigurationSection itemSection = itemsSection.getConfigurationSection(slotKey);
                if (itemSection != null) {
                    Map<String, Object> itemData = itemSection.getValues(true);
                    ItemStack item = ItemStack.deserialize(itemData);
                    template.setItem(slot, item);
                }
            } catch (Exception e) {
                plugin.getLogger().warning("加载物品失败 (槽位 " + slotKey + "): " + e.getMessage());
            }
        }
    }
}
```

## 修改的文件

### CustomTemplateManager.java
- **位置**: `src\main\java\com\projectSource\ultimateManhurt\kit\CustomTemplateManager.java`
- **修改内容**:
  1. 添加了`ConfigurationSection`的import
  2. 修改了`loadTemplateFromYaml`方法中的物品加载逻辑
  3. 使用正确的配置文件API来处理嵌套的配置结构

## 技术细节

### 问题根源
- Bukkit的YAML配置文件在加载时会创建`MemorySection`对象来表示配置节
- 直接将`MemorySection`转换为`Map`会导致`ClassCastException`
- 需要使用`ConfigurationSection`的API来正确访问嵌套数据

### 解决方法
1. 使用`getConfigurationSection()`获取配置节
2. 使用`getKeys(false)`遍历子键
3. 对每个子键再次使用`getConfigurationSection()`获取具体的物品数据
4. 使用`getValues(true)`获取可序列化的Map数据

## 影响范围

这个修复解决了以下问题：
- StartKit模板加载失败
- 自定义装备包无法正确加载
- 插件重载时的警告消息

## 测试建议

1. **创建自定义模板测试**:
   - 创建一个房间
   - 设置自定义装备包
   - 保存为模板
   - 重载插件
   - 验证模板是否正确加载

2. **装备包功能测试**:
   - 测试速通者和捕猎者装备包设置
   - 验证物品是否正确显示和应用
   - 测试装备包的启用/禁用功能

3. **配置文件完整性测试**:
   - 检查是否还有其他配置加载警告
   - 验证所有功能是否正常工作

## 预防措施

为了避免类似问题，在处理Bukkit配置文件时应该：

1. **使用正确的API**:
   ```java
   // 好的做法
   ConfigurationSection section = config.getConfigurationSection("path");
   if (section != null) {
       Map<String, Object> data = section.getValues(true);
   }
   
   // 避免的做法
   Map<String, Object> data = (Map<String, Object>) config.get("path");
   ```

2. **添加空值检查**:
   ```java
   ConfigurationSection section = config.getConfigurationSection("path");
   if (section != null) {
       // 安全地处理配置数据
   }
   ```

3. **使用适当的异常处理**:
   ```java
   try {
       // 配置加载代码
   } catch (Exception e) {
       plugin.getLogger().warning("配置加载失败: " + e.getMessage());
   }
   ```

## 状态

✅ **已修复** - 配置文件加载问题已解决，插件可以正常重载而不会出现类型转换错误。
