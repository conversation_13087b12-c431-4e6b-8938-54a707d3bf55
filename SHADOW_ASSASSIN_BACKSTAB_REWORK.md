# 暗影刺客背刺技能重做

## 🔄 技能重做概览

### 修改前
- **伤害机制：** 造成额外50%伤害（基于原攻击伤害）
- **冷却时间：** 无冷却，每次背刺都能触发
- **伤害计算：** 原伤害 + 原伤害 × 50%

### 修改后
- **伤害机制：** 造成目标最大生命值40%的伤害
- **冷却时间：** 8秒冷却时间
- **伤害计算：** 目标最大生命值 × 40%

## 🗡️ 新背刺机制

### 技术实现
```java
/**
 * 处理暗影刺客背刺被动技能
 * 从背后攻击敌人造成目标最大生命值40%的伤害，8秒冷却时间
 */
public void handleShadowAssassinBackstab(Player attacker, Player victim, EntityDamageByEntityEvent event) {
    UUID attackerId = attacker.getUniqueId();
    String skillName = "背刺";
    
    // 检查冷却时间
    if (plugin.getProfessionManager().getSkillCooldown().isOnCooldown(attackerId, skillName)) {
        return; // 冷却中，不触发背刺
    }
    
    // 角度检测（120度背刺判定）
    if (dotProduct < -0.5) {
        // 造成目标最大生命值40%的伤害
        double victimMaxHealth = victim.getAttribute(Attribute.MAX_HEALTH).getValue();
        double backstabDamage = victimMaxHealth * 0.40;
        
        // 设置为背刺伤害（替换原伤害）
        event.setDamage(backstabDamage);
        
        // 设置冷却时间
        plugin.getProfessionManager().getSkillCooldown().setCooldown(attackerId, skillName, 8);
    }
}
```

### 核心特点
1. **固定伤害：** 不再依赖武器攻击力
2. **百分比伤害：** 基于目标最大生命值
3. **冷却限制：** 8秒内只能触发一次
4. **替换伤害：** 完全替换原攻击伤害

## 📊 伤害对比分析

### 不同目标最大生命值的背刺伤害
| 目标最大生命值 | 40%背刺伤害 | 说明 |
|---------------|-------------|------|
| 20.0 | 8.0 | 标准玩家 |
| 30.0 | 12.0 | 生命增强玩家 |
| 40.0 | 16.0 | 高生命值玩家 |
| 50.0 | 20.0 | 极高生命值玩家 |

### 与修改前的对比
**修改前（50%额外伤害）：**
- 木剑攻击：4 + 2 = 6伤害
- 铁剑攻击：6 + 3 = 9伤害
- 钻石剑攻击：7 + 3.5 = 10.5伤害

**修改后（40%最大生命值）：**
- 对20血玩家：固定8伤害
- 对30血玩家：固定12伤害
- 对40血玩家：固定16伤害

## ⚖️ 平衡性分析

### 技能强化方面
**优势提升：**
- **伤害稳定：** 不再依赖武器品质
- **高血量克制：** 对高生命值目标更有效
- **伤害保证：** 最低也有8伤害（对20血目标）

**新的战术价值：**
- 专门克制高血量职业
- 不受武器限制的稳定输出
- 对坦克型职业有威胁

### 技能限制方面
**新增限制：**
- **冷却时间：** 8秒内只能使用一次
- **机会成本：** 错过背刺需要等待冷却
- **预判要求：** 需要更精确的时机把握

**战术考量：**
- 不能连续背刺同一目标
- 需要选择最佳背刺时机
- 冷却期间只能进行普通攻击

## 🎮 游戏体验变化

### 暗影刺客玩家体验
**技能使用策略：**
- **时机选择：** 需要选择最佳背刺时机
- **目标优先级：** 优先攻击高血量目标
- **冷却管理：** 需要管理8秒冷却时间

**战术适应：**
- 不能依赖连续背刺
- 需要在冷却期间使用其他战术
- 背刺失败的代价更高

### 敌方玩家体验
**应对策略：**
- **冷却窗口：** 背刺后8秒内相对安全
- **位置意识：** 仍需注意背后防护
- **血量管理：** 高血量不再是绝对优势

**反制机会：**
- 背刺后有8秒反击窗口
- 可以通过走位避免背刺角度
- 冷却期间暗影刺客威胁降低

## 🎯 技能定位变化

### 修改前的定位
- **连续输出：** 可以持续进行背刺攻击
- **武器依赖：** 伤害受武器品质影响
- **低血量优势：** 对低血量目标效果一般

### 修改后的定位
- **爆发输出：** 单次高伤害，然后进入冷却
- **武器无关：** 伤害不受武器影响
- **坦克克制：** 专门对付高血量目标

## 🧪 平衡性测试建议

### 伤害测试
1. **不同血量目标：**
   ```
   1. 测试对20、30、40血目标的背刺伤害
   2. 验证40%计算是否准确
   3. 确认伤害替换机制正常工作
   ```

2. **武器无关性：**
   ```
   1. 使用不同品质武器进行背刺
   2. 验证伤害是否都是40%最大生命值
   3. 确认不受武器攻击力影响
   ```

### 冷却机制测试
1. **冷却时间：**
   ```
   1. 进行背刺后立即尝试再次背刺
   2. 验证8秒冷却是否生效
   3. 测试冷却结束后能否正常背刺
   ```

2. **冷却显示：**
   ```
   1. 验证玩家能否看到背刺冷却状态
   2. 测试冷却期间的提示信息
   3. 确认冷却时间计算准确
   ```

### 平衡性测试
1. **与其他职业对比：**
   ```
   1. 测试暗影刺客与高血量职业的对战
   2. 评估背刺的威胁程度
   3. 观察8秒冷却的影响
   ```

2. **战术适应：**
   ```
   1. 观察玩家如何适应新的背刺机制
   2. 测试冷却期间的战术选择
   3. 评估技能的整体平衡性
   ```

## 📈 预期影响

### 职业平衡
- **暗影刺客：** 更专业化的刺客定位
- **高血量职业：** 面临更大威胁
- **整体平衡：** 减少了背刺的滥用

### 战术深度
- **时机把握：** 背刺时机变得更重要
- **冷却管理：** 增加了技能管理要素
- **目标选择：** 需要选择最有价值的背刺目标

## 🎉 重做总结

成功将暗影刺客的背刺技能从"连续额外伤害"重做为"爆发百分比伤害"：

- ✅ **伤害机制：** 从50%额外伤害改为40%最大生命值伤害
- ✅ **冷却限制：** 添加8秒冷却时间，防止滥用
- ✅ **武器无关：** 不再依赖武器品质，伤害更稳定
- ✅ **定位明确：** 专门克制高血量目标的爆发刺客
- ✅ **平衡改善：** 增加了使用限制，提高了战术深度

现在暗影刺客真正成为了一个需要精确时机把握的专业刺客，既有强大的爆发能力，又有合理的使用限制！🗡️✨

**重要特点：**
1. 高伤害但有冷却限制
2. 专门克制高血量目标
3. 不受武器品质影响
4. 需要更高的操作技巧
