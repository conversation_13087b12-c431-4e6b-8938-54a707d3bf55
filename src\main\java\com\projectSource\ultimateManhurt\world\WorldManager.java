package com.projectSource.ultimateManhurt.world;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.world.dimension.DimensionBinder;

import org.bukkit.*;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.File;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 世界管理器
 * 负责游戏世界的创建、管理和清理
 */
public class WorldManager {

    private final UltimateManhurt plugin;
    private final Map<String, GameWorld> gameWorlds = new ConcurrentHashMap<>();
    private final Map<String, String> worldToRoomMap = new ConcurrentHashMap<>();
    private final DimensionBinder dimensionBinder;
    // private final WorldGenerator worldGenerator; // 暂时禁用自定义生成器

    public WorldManager(UltimateManhurt plugin) {
        this.plugin = plugin;
        this.dimensionBinder = new DimensionBinder(this);
        // this.worldGenerator = new WorldGenerator(plugin); // 暂时禁用
        initialize();
    }

    /**
     * 初始化世界管理器
     */
    private void initialize() {
        // 注册维度绑定器事件监听器
        plugin.getServer().getPluginManager().registerEvents(dimensionBinder, plugin);

        // 清理遗留的游戏世界
        cleanupOrphanedWorlds();
        plugin.getLogger().info("世界管理器已初始化");
    }

    /**
     * 为房间创建游戏世界组
     */
    public CompletableFuture<GameWorld> createGameWorld(Room room) {
        CompletableFuture<GameWorld> future = new CompletableFuture<>();

        String roomId = room.getId();
        String worldName = generateWorldName(roomId);

        plugin.getLogger().info("开始为房间 " + roomId + " 创建游戏世界组...");

        // 必须在主线程中创建世界
        org.bukkit.Bukkit.getScheduler().runTask(plugin, () -> {
            try {
                plugin.getLogger().info("创建主世界: " + worldName);

                // 创建主世界
                World overworld = createWorld(worldName, World.Environment.NORMAL, room.getSettings().getWorldSeed());
                if (overworld == null) {
                    future.completeExceptionally(new RuntimeException("无法创建主世界"));
                    return;
                }

                plugin.getLogger().info("主世界创建完成，开始创建下界...");

                // 创建下界
                World nether = createWorld(worldName + "_nether", World.Environment.NETHER, room.getSettings().getWorldSeed());
                if (nether == null) {
                    future.completeExceptionally(new RuntimeException("无法创建下界"));
                    return;
                }

                plugin.getLogger().info("下界创建完成，开始创建末地...");

                // 创建末地
                World end = createWorld(worldName + "_the_end", World.Environment.THE_END, room.getSettings().getWorldSeed());
                if (end == null) {
                    future.completeExceptionally(new RuntimeException("无法创建末地"));
                    return;
                }

                plugin.getLogger().info("所有世界创建完成，开始配置游戏世界...");

                // 创建游戏世界对象
                GameWorld gameWorld = new GameWorld(roomId, overworld, nether, end);

                // 配置世界设置
                configureWorld(gameWorld, room);

                // 绑定维度传送
                dimensionBinder.bindDimensions(gameWorld);

                // 注册世界
                gameWorlds.put(roomId, gameWorld);
                worldToRoomMap.put(overworld.getName(), roomId);
                worldToRoomMap.put(nether.getName(), roomId);
                worldToRoomMap.put(end.getName(), roomId);

                plugin.getLogger().info("为房间 " + roomId + " 创建游戏世界组完成！");
                future.complete(gameWorld);

            } catch (Exception e) {
                plugin.getLogger().severe("创建游戏世界失败: " + e.getMessage());
                e.printStackTrace();
                future.completeExceptionally(new RuntimeException("创建游戏世界失败", e));
            }
        });

        return future;
    }

    /**
     * 创建单个世界
     */
    private World createWorld(String name, World.Environment environment, long seed) {
        try {
            plugin.getLogger().info("开始创建世界: " + name + " (环境: " + environment + ", 种子: " + (seed == 0 ? "随机" : seed) + ")");

            // 检查世界是否已存在
            World existingWorld = Bukkit.getWorld(name);
            if (existingWorld != null) {
                plugin.getLogger().info("世界 " + name + " 已存在，直接返回");
                return existingWorld;
            }

            WorldCreator creator = new WorldCreator(name);
            creator.environment(environment);

            if (seed != 0) {
                creator.seed(seed);
            }

            // 使用默认世界生成器以确保正常的地形生成
            // creator.generator(worldGenerator); // 暂时禁用自定义生成器

            long startTime = System.currentTimeMillis();
            World world = Bukkit.createWorld(creator);
            long endTime = System.currentTimeMillis();

            if (world != null) {
                plugin.getLogger().info("成功创建世界: " + name + " (耗时: " + (endTime - startTime) + "ms)");
            } else {
                plugin.getLogger().severe("创建世界失败: " + name + " - 返回null");
            }

            return world;

        } catch (Exception e) {
            plugin.getLogger().severe("创建世界异常: " + name + " - " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 配置世界设置
     */
    private void configureWorld(GameWorld gameWorld, Room room) {
        var settings = room.getSettings();

        // 配置主世界
        configureWorldRules(gameWorld.getOverworld(), settings);

        // 配置下界
        configureWorldRules(gameWorld.getNether(), settings);

        // 配置末地
        configureWorldRules(gameWorld.getEnd(), settings);

        // 设置世界出生点
        Location spawnLocation;
        if (settings.isCustomSpawnLogic()) {
            // 使用自定义出生点逻辑
            spawnLocation = findSafeSpawnLocation(gameWorld.getOverworld());
            plugin.getLogger().info("使用自定义出生点逻辑，出生点: " + spawnLocation);
        } else {
            // 使用原版Minecraft出生点逻辑
            spawnLocation = gameWorld.getOverworld().getSpawnLocation();
            plugin.getLogger().info("使用原版出生点逻辑，出生点: " + spawnLocation);
        }
        gameWorld.getOverworld().setSpawnLocation(spawnLocation);

        // 根据胜利模式配置玩家出生点
        gameWorld.configureSpawnPoints(settings.getVictoryMode(), settings);
    }

    /**
     * 配置世界规则
     */
    private void configureWorldRules(World world, com.projectSource.ultimateManhurt.room.RoomSettings settings) {
        world.setDifficulty(settings.getDifficulty());
        world.setGameRule(GameRule.DO_DAYLIGHT_CYCLE, settings.isDoDaylightCycle());
        world.setGameRule(GameRule.LOCATOR_BAR, settings.isLocatorBar());
        world.setGameRule(GameRule.DO_WEATHER_CYCLE, settings.isDoWeatherCycle());
        world.setGameRule(GameRule.KEEP_INVENTORY, settings.isKeepInventory());
        world.setGameRule(GameRule.NATURAL_REGENERATION, settings.isNaturalRegeneration());
        world.setGameRule(GameRule.SHOW_DEATH_MESSAGES, settings.isShowDeathMessages());
        world.setGameRule(GameRule.DO_MOB_SPAWNING, true);
        world.setGameRule(GameRule.DO_FIRE_TICK, true);
        world.setGameRule(GameRule.MOB_GRIEFING, true);

        // PVP设置
        world.setPVP(settings.isPvpEnabled());

        // 时间设置
        world.setTime(0); // 从白天开始
    }

    /**
     * 寻找安全的出生点
     */
    private Location findSafeSpawnLocation(World world) {
        // 在主世界寻找安全的出生点
        Location spawn = world.getSpawnLocation();

        // 首先尝试在原出生点附近寻找真正的地表位置
        Location surfaceLocation = findTrueSurfaceSpawn(world, spawn.getBlockX(), spawn.getBlockZ());
        if (surfaceLocation != null) {
            return surfaceLocation;
        }

        // 如果原出生点不合适，在附近区域搜索
        for (int radius = 1; radius <= 5; radius++) {
            for (int angle = 0; angle < 360; angle += 45) {
                double radians = Math.toRadians(angle);
                int x = spawn.getBlockX() + (int)(radius * 16 * Math.cos(radians));
                int z = spawn.getBlockZ() + (int)(radius * 16 * Math.sin(radians));

                Location testLocation = findTrueSurfaceSpawn(world, x, z);
                if (testLocation != null) {
                    plugin.getLogger().info("在距离原出生点 " + (radius * 16) + " 格处找到安全出生点: " + testLocation);
                    return testLocation;
                }
            }
        }

        // 如果还是找不到，使用原来的逻辑作为后备
        for (int y = world.getMaxHeight() - 1; y > world.getMinHeight(); y--) {
            Location testLocation = new Location(world, spawn.getX(), y, spawn.getZ());
            if (isSafeLocation(testLocation)) {
                return testLocation;
            }
        }

        // 最后的后备方案：返回默认出生点
        plugin.getLogger().warning("无法找到安全的出生点，使用默认出生点: " + spawn);
        return spawn;
    }

    /**
     * 寻找真正的地表出生点
     */
    private Location findTrueSurfaceSpawn(World world, int x, int z) {
        // 获取该位置的最高点
        int highestY = world.getHighestBlockYAt(x, z);

        // 检查是否在海洋中
        if (highestY < world.getSeaLevel()) {
            // 在海洋中，在海面上创建平台
            return createOceanSpawnPlatform(world, x, z);
        }

        // 从最高点开始向下搜索真正的地表
        for (int y = highestY; y >= Math.max(world.getSeaLevel(), world.getMinHeight() + 5); y--) {
            if (isTrueSurfaceSpawn(world, x, y, z)) {
                return new Location(world, x + 0.5, y + 1, z + 0.5);
            }
        }

        return null;
    }

    /**
     * 检查是否是真正的地表出生点
     */
    private boolean isTrueSurfaceSpawn(World world, int x, int y, int z) {
        // 检查基本安全性
        if (!isSafeLocation(new Location(world, x, y + 1, z))) {
            return false;
        }

        // 检查天空开放度（确保不在洞穴中）
        int skyAccess = 0;
        for (int checkY = y + 3; checkY <= Math.min(y + 20, world.getMaxHeight() - 1); checkY++) {
            if (world.getBlockAt(x, checkY, z).getType().isAir()) {
                skyAccess++;
            }
        }

        // 需要至少10格的天空开放度
        return skyAccess >= 10;
    }

    /**
     * 在海洋中创建出生平台
     */
    private Location createOceanSpawnPlatform(World world, int x, int z) {
        int seaLevel = world.getSeaLevel();

        // 创建一个3x3的石头平台
        for (int dx = -1; dx <= 1; dx++) {
            for (int dz = -1; dz <= 1; dz++) {
                world.getBlockAt(x + dx, seaLevel, z + dz).setType(Material.STONE);
                world.getBlockAt(x + dx, seaLevel + 1, z + dz).setType(Material.AIR);
                world.getBlockAt(x + dx, seaLevel + 2, z + dz).setType(Material.AIR);
            }
        }

        plugin.getLogger().info("在海洋位置 (" + x + ", " + z + ") 创建了出生平台");
        return new Location(world, x + 0.5, seaLevel + 1, z + 0.5);
    }

    /**
     * 检查位置是否安全
     */
    private boolean isSafeLocation(Location location) {
        World world = location.getWorld();
        if (world == null) return false;

        int x = location.getBlockX();
        int y = location.getBlockY();
        int z = location.getBlockZ();

        // 检查脚下有实体方块
        Material groundMaterial = world.getBlockAt(x, y - 1, z).getType();
        if (!groundMaterial.isSolid()) {
            return false;
        }

        // 检查头部和身体位置没有实体方块
        Material headMaterial = world.getBlockAt(x, y + 1, z).getType();
        Material bodyMaterial = world.getBlockAt(x, y, z).getType();

        return headMaterial.isAir() && bodyMaterial.isAir();
    }

    /**
     * 生成世界名称
     */
    private String generateWorldName(String roomId) {
        String prefix = plugin.getConfigManager().getString("world.name-prefix", "manhunt_");
        return prefix + roomId + "_" + System.currentTimeMillis();
    }

    /**
     * 获取游戏世界
     */
    public GameWorld getGameWorld(String roomId) {
        return gameWorlds.get(roomId);
    }

    /**
     * 根据世界名称获取房间ID
     */
    public String getRoomIdByWorld(String worldName) {
        return worldToRoomMap.get(worldName);
    }

    /**
     * 传送玩家到游戏世界
     */
    public void teleportToGameWorld(Player player, String roomId) {
        GameWorld gameWorld = gameWorlds.get(roomId);
        if (gameWorld == null) {
            plugin.getLogger().warning("尝试传送玩家到不存在的游戏世界: " + roomId);
            return;
        }

        Location spawnLocation = gameWorld.getOverworld().getSpawnLocation();
        player.teleport(spawnLocation);
    }

    /**
     * 传送玩家到主服务器世界
     */
    public void teleportToMainWorld(Player player) {
        World mainWorld = Bukkit.getWorlds().get(0); // 获取主世界
        Location spawnLocation = mainWorld.getSpawnLocation();
        player.teleport(spawnLocation);
    }

    /**
     * 删除游戏世界
     */
    public CompletableFuture<Void> deleteGameWorld(String roomId) {
        CompletableFuture<Void> future = new CompletableFuture<>();

        // 必须在主线程中执行世界操作
        Bukkit.getScheduler().runTask(plugin, () -> {
            try {
                GameWorld gameWorld = gameWorlds.remove(roomId);
                if (gameWorld == null) {
                    future.complete(null);
                    return;
                }

                plugin.getLogger().info("开始删除房间 " + roomId + " 的游戏世界组...");

                // 传送所有玩家离开这些世界
                evacuateWorld(gameWorld.getOverworld());
                evacuateWorld(gameWorld.getNether());
                evacuateWorld(gameWorld.getEnd());

                // 解除维度绑定
                dimensionBinder.unbindDimensions(gameWorld);

                // 延迟卸载和删除世界，确保玩家已经离开
                Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    try {
                        // 卸载世界
                        unloadWorld(gameWorld.getOverworld());
                        unloadWorld(gameWorld.getNether());
                        unloadWorld(gameWorld.getEnd());

                        // 在异步线程中删除世界文件
                        Bukkit.getScheduler().runTaskAsynchronously(plugin, () -> {
                            try {
                                deleteWorldFiles(gameWorld.getOverworld());
                                deleteWorldFiles(gameWorld.getNether());
                                deleteWorldFiles(gameWorld.getEnd());

                                plugin.getLogger().info("已删除房间 " + roomId + " 的游戏世界组");
                                future.complete(null);
                            } catch (Exception e) {
                                plugin.getLogger().severe("删除世界文件失败: " + e.getMessage());
                                future.completeExceptionally(e);
                            }
                        });

                        // 清理映射
                        worldToRoomMap.remove(gameWorld.getOverworld().getName());
                        worldToRoomMap.remove(gameWorld.getNether().getName());
                        worldToRoomMap.remove(gameWorld.getEnd().getName());

                    } catch (Exception e) {
                        plugin.getLogger().severe("卸载世界失败: " + e.getMessage());
                        future.completeExceptionally(e);
                    }
                }, 60L); // 3秒后卸载世界

            } catch (Exception e) {
                plugin.getLogger().severe("删除游戏世界失败: " + e.getMessage());
                future.completeExceptionally(e);
            }
        });

        return future;
    }

    /**
     * 疏散世界中的所有玩家
     */
    private void evacuateWorld(World world) {
        List<Player> players = new ArrayList<>(world.getPlayers());
        for (Player player : players) {
            teleportToMainWorld(player);
        }
    }

    /**
     * 卸载世界
     */
    private void unloadWorld(World world) {
        if (world != null) {
            Bukkit.unloadWorld(world, false);
        }
    }

    /**
     * 删除世界文件
     */
    private void deleteWorldFiles(World world) {
        if (world == null) return;

        File worldFolder = world.getWorldFolder();
        if (worldFolder.exists()) {
            deleteDirectory(worldFolder);
        }
    }

    /**
     * 递归删除目录
     */
    private void deleteDirectory(File directory) {
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    deleteDirectory(file);
                }
            }
        }
        directory.delete();
    }

    /**
     * 清理孤立的游戏世界
     */
    private void cleanupOrphanedWorlds() {
        String prefix = plugin.getConfigManager().getString("world.name-prefix", "manhunt_");

        for (World world : Bukkit.getWorlds()) {
            if (world.getName().startsWith(prefix)) {
                // 检查是否有对应的房间
                String roomId = worldToRoomMap.get(world.getName());
                if (roomId == null || !gameWorlds.containsKey(roomId)) {
                    plugin.getLogger().info("发现孤立的游戏世界，正在清理: " + world.getName());

                    // 疏散玩家
                    evacuateWorld(world);

                    // 延迟删除世界
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            unloadWorld(world);
                            deleteWorldFiles(world);
                        }
                    }.runTaskLater(plugin, 100L); // 5秒后删除
                }
            }
        }
    }

    /**
     * 获取维度绑定器
     */
    public DimensionBinder getDimensionBinder() {
        return dimensionBinder;
    }

    /**
     * 关闭世界管理器
     */
    public void shutdown() {
        // 删除所有游戏世界
        List<CompletableFuture<Void>> deleteTasks = new ArrayList<>();
        for (String roomId : new ArrayList<>(gameWorlds.keySet())) {
            deleteTasks.add(deleteGameWorld(roomId));
        }

        // 等待所有删除任务完成
        CompletableFuture.allOf(deleteTasks.toArray(new CompletableFuture[0])).join();

        plugin.getLogger().info("世界管理器已关闭");
    }
}
