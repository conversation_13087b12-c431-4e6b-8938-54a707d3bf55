package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import net.kyori.adventure.text.Component;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.Arrays;
import java.util.List;
import java.util.function.Consumer;

/**
 * GUI基类
 * 提供基础的GUI功能
 */
public abstract class BaseGui {
    
    protected final UltimateManhurt plugin;
    protected final Player player;
    protected final Inventory inventory;
    protected final String title;
    protected final int size;
    
    public BaseGui(UltimateManhurt plugin, Player player, String title, int size) {
        this.plugin = plugin;
        this.player = player;
        this.title = title;
        this.size = size;
        this.inventory = Bukkit.createInventory(null, size, ComponentUtil.parse(title));

        // 不在构造函数中调用setupGui()，让子类在构造完成后手动调用
    }
    
    /**
     * 设置GUI内容
     */
    protected abstract void setupGui();
    
    /**
     * 处理点击事件
     */
    public abstract void handleClick(InventoryClickEvent event);
    
    /**
     * 打开GUI
     */
    public void open() {
        openWithRetry(0);
    }

    /**
     * 带重试机制的GUI打开
     */
    private void openWithRetry(int attemptCount) {
        // 检查玩家是否在线
        if (!player.isOnline()) {
            return;
        }

        // 检查玩家是否在载具中
        if (player.isInsideVehicle()) {
            // 第一次尝试时提示玩家并自动离开载具
            if (attemptCount == 0) {
                ComponentUtil.sendMessage(player, ComponentUtil.warning("检测到你在载具中，正在尝试打开GUI..."));
                player.leaveVehicle();
            }

            // 如果尝试次数超过限制，提示玩家手动离开载具
            if (attemptCount >= 10) { // 最多尝试10次，约2秒
                ComponentUtil.sendMessage(player, ComponentUtil.error("无法自动离开载具，请手动离开载具后重新尝试打开GUI"));
                ComponentUtil.sendMessage(player, ComponentUtil.info("提示：按 Shift 键可以离开大多数载具"));
                ComponentUtil.sendMessage(player, ComponentUtil.info("或者使用 /manhunt gui force 强制打开GUI"));
                return;
            }

            // 延迟重试
            org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
                openWithRetry(attemptCount + 1);
            }, 4L); // 每次延迟4 tick (0.2秒)
        } else {
            // 成功离开载具或本来就不在载具中，打开GUI
            if (attemptCount > 0) {
                ComponentUtil.sendMessage(player, ComponentUtil.success("已成功离开载具，正在打开GUI..."));
            }
            player.openInventory(inventory);
        }
    }

    /**
     * 强制打开GUI（忽略载具状态）
     */
    public void forceOpen() {
        if (!player.isOnline()) {
            return;
        }

        if (player.isInsideVehicle()) {
            ComponentUtil.sendMessage(player, ComponentUtil.warning("强制在载具中打开GUI，可能会有显示问题"));
        }

        player.openInventory(inventory);
    }
    
    /**
     * 关闭GUI
     */
    public void close() {
        player.closeInventory();
    }
    
    /**
     * 刷新GUI
     */
    public void refresh() {
        inventory.clear();
        setupGui();
    }
    
    /**
     * 创建物品
     */
    protected ItemStack createItem(Material material, String name, String... lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            meta.displayName(ComponentUtil.parse(name));
            
            if (lore.length > 0) {
                List<Component> loreComponents = Arrays.stream(lore)
                        .map(ComponentUtil::parse)
                        .toList();
                meta.lore(loreComponents);
            }
            
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * 创建可点击的物品
     */
    protected ItemStack createClickableItem(Material material, String name, Consumer<InventoryClickEvent> onClick, String... lore) {
        return createItem(material, name, lore);
    }
    
    /**
     * 设置物品到指定位置
     */
    protected void setItem(int slot, ItemStack item) {
        if (slot >= 0 && slot < size) {
            inventory.setItem(slot, item);
        }
    }

    /**
     * 获取指定位置的物品
     */
    protected ItemStack getItem(int slot) {
        if (slot >= 0 && slot < size) {
            return inventory.getItem(slot);
        }
        return null;
    }
    
    /**
     * 填充边框
     */
    protected void fillBorder(Material material) {
        ItemStack borderItem = createItem(material, " ");
        
        // 顶部和底部行
        for (int i = 0; i < 9; i++) {
            setItem(i, borderItem);
            setItem(size - 9 + i, borderItem);
        }
        
        // 左右边
        for (int i = 9; i < size - 9; i += 9) {
            setItem(i, borderItem);
            setItem(i + 8, borderItem);
        }
    }
    
    /**
     * 创建返回按钮
     */
    protected ItemStack createBackButton() {
        return createItem(Material.ARROW, "<yellow>返回", "<gray>点击返回上一页");
    }
    
    /**
     * 创建关闭按钮
     */
    protected ItemStack createCloseButton() {
        return createItem(Material.BARRIER, "<red>关闭", "<gray>点击关闭界面");
    }
    
    /**
     * 创建确认按钮
     */
    protected ItemStack createConfirmButton() {
        return createItem(Material.LIME_CONCRETE, "<green>确认", "<gray>点击确认操作");
    }
    
    /**
     * 创建取消按钮
     */
    protected ItemStack createCancelButton() {
        return createItem(Material.RED_CONCRETE, "<red>取消", "<gray>点击取消操作");
    }
    
    /**
     * 创建信息物品
     */
    protected ItemStack createInfoItem(Material material, String name, String... info) {
        return createItem(material, name, info);
    }
    
    /**
     * 播放点击音效
     */
    protected void playClickSound() {
        player.playSound(player.getLocation(), org.bukkit.Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
    }
    
    /**
     * 播放成功音效
     */
    protected void playSuccessSound() {
        player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.0f);
    }
    
    /**
     * 播放错误音效
     */
    protected void playErrorSound() {
        player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
    }
    
    /**
     * 发送消息给玩家
     */
    protected void sendMessage(String message) {
        ComponentUtil.sendMessage(player, ComponentUtil.parse(message));
    }
    
    /**
     * 发送成功消息
     */
    protected void sendSuccess(String message) {
        ComponentUtil.sendMessage(player, ComponentUtil.success(message));
    }
    
    /**
     * 发送错误消息
     */
    protected void sendError(String message) {
        ComponentUtil.sendMessage(player, ComponentUtil.error(message));
    }

    /**
     * 发送信息消息
     */
    protected void sendInfo(String message) {
        ComponentUtil.sendMessage(player, ComponentUtil.info(message));
    }
    
    /**
     * 检查是否是GUI的库存
     */
    public boolean isThisInventory(Inventory inventory) {
        return this.inventory.equals(inventory);
    }
    
    // Getter方法
    public Player getPlayer() {
        return player;
    }
    
    public Inventory getInventory() {
        return inventory;
    }
    
    public String getTitle() {
        return title;
    }
    
    public int getSize() {
        return size;
    }
}
