# 新职业实现文档 - 村民和赏金猎人

## 概述
成功添加了两个新职业：**村民**（速通者阵营）和**赏金猎人**（捕猎者阵营），现在双方各有10个职业。

## 🟢 村民 (VILLAGER) - 速通者阵营

### 基本信息
- **职业名称**: 村民
- **描述**: 善良的村民，用朴实的力量守护同伴
- **图标**: 绿宝石 (EMERALD)
- **颜色**: 绿色 (GREEN)
- **表情符号**: 👨‍🌾

### 被动技能 - 村民互助
- **效果**: 20格内每有一个队友，村民和其队友获得5%伤害减免（最多25%）
- **治疗机制**: 队友受伤时，村民有20%概率为其回复2点生命值（伪随机）
- **实现**: 通过定时任务每6秒检测一次，给予抗性效果模拟伤害减免

### 主动技能 - 上帝之手
- **冷却时间**: 120秒
- **效果**: 为20格内所有队友提供15秒生命恢复II+饱和I效果
- **自身增益**: 村民自身获得抗性I效果20秒
- **范围**: 20格球形范围

## 🔴 赏金猎人 (BOUNTY_HUNTER) - 捕猎者阵营

### 基本信息
- **职业名称**: 赏金猎人
- **描述**: 狡猾的窃贼，专门偷取敌人的资源和装备
- **图标**: 皮革靴子 (LEATHER_BOOTS)
- **颜色**: 深灰色 (DARK_GRAY)
- **表情符号**: 🥷

### 被动技能 - 忍术
- **物品偷取**: 攻击敌人时20%概率从其背包中随机偷取1-3个物品（伪随机）
- **装备偷取**: 7%概率窃取其身上的装备（有耐久损耗）
- **特殊机制**: 物品是以个为单位偷取，不是整组

### 主动技能 - 暗影步
- **冷却时间**: 75秒
- **效果**: 获得8秒隐身效果+速度II
- **特殊机制**: 
  - 期间攻击敌人不会解除隐身
  - 攻击时必定触发忍术效果（偷取物品到背包）
  - 受到伤害会立即现形

## 技术实现细节

### 文件修改列表
1. **Profession.java** - 添加两个新职业枚举
2. **ActiveSkillHandler.java** - 添加主动技能处理方法
3. **PassiveSkillHandler.java** - 添加被动技能处理方法
4. **ProfessionManager.java** - 添加职业初始化和清理逻辑
5. **ProfessionListener.java** - 添加事件监听处理

### 核心功能实现

#### 村民互助系统
- 使用定时任务检测队友数量和距离
- 通过抗性效果模拟伤害减免
- 伪随机系统控制治疗触发概率
- 事件监听器处理队友受伤时的治疗

#### 赏金猎人偷取系统
- 攻击事件监听器触发偷取检测
- 随机选择背包槽位进行物品偷取
- 装备偷取包含耐久度损耗机制
- 暗影步状态管理和现形机制

### 平衡性考虑
1. **村民**:
   - 伤害减免需要队友在身边才生效，鼓励团队作战
   - 治疗概率适中，不会过于强势
   - 主动技能冷却时间较长，需要合理使用

2. **赏金猎人**:
   - 偷取概率设置合理，不会过于频繁
   - 暗影步有反制机制（受伤现形）
   - 偷取的是单个物品而非整组，避免过于强势

## 游戏影响

### 战术层面
- **村民**为速通者提供了专业的团队支援能力
- **赏金猎人**为捕猎者提供了资源干扰和潜行能力
- 两个职业形成了有趣的对抗关系

### 策略深度
- 村民鼓励速通者抱团作战
- 赏金猎人增加了隐身和偷取的战术选择
- 丰富了双方的职业组合策略

## 后续优化建议
1. 根据实际游戏数据调整偷取概率和治疗概率
2. 可以考虑添加更多视觉和音效反馈
3. 监控游戏平衡性，必要时进行数值调整

## 职业统计更新
- **捕猎者阵营**: 10个职业（新增赏金猎人）
- **速通者阵营**: 10个职业（新增村民）
- **总计**: 20个职业，双方完全平衡
