package com.projectSource.ultimateManhurt.tablist;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.room.Room;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.scheduler.BukkitRunnable;

/**
 * Tablist事件监听器
 * 处理玩家加入/离开事件，自动更新Tablist显示
 */
public class TablistListener implements Listener {

    private final UltimateManhurt plugin;

    public TablistListener(UltimateManhurt plugin) {
        this.plugin = plugin;
    }

    /**
     * 玩家加入事件
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();

        // 延迟2tick创建Tablist，确保数据加载和其他系统初始化完成
        new BukkitRunnable() {
            @Override
            public void run() {
                if (player.isOnline()) {
                    createAppropriateTablist(player);

                    // 更新所有玩家的显示名称
                    plugin.getTablistManager().updateAllPlayerDisplayNames();
                }
            }
        }.runTaskLater(plugin, 2L);
    }

    /**
     * 玩家离开事件
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();

        // 移除玩家的Tablist
        plugin.getTablistManager().removeTablist(player);

        // 延迟更新其他玩家的显示名称
        new BukkitRunnable() {
            @Override
            public void run() {
                plugin.getTablistManager().updateAllPlayerDisplayNames();
            }
        }.runTaskLater(plugin, 1L);
    }

    /**
     * 为玩家创建合适的Tablist
     */
    private void createAppropriateTablist(Player player) {
        // 检查玩家是否在游戏中
        GameSession gameSession = findPlayerGameSession(player);
        if (gameSession != null) {
            plugin.getLogger().info("为玩家 " + player.getName() + " 创建游戏Tablist (状态: " + gameSession.getState() + ")");
            plugin.getTablistManager().createGameTablist(player, gameSession);
            return;
        }

        // 检查玩家是否在房间中
        Room room = findPlayerRoom(player);
        if (room != null) {
            plugin.getLogger().info("为玩家 " + player.getName() + " 创建房间Tablist (房间: " + room.getName() + ")");
            plugin.getTablistManager().createRoomTablist(player, room);
            return;
        }

        // 默认创建大厅Tablist
        plugin.getLogger().info("为玩家 " + player.getName() + " 创建大厅Tablist");
        plugin.getTablistManager().createDefaultTablist(player);
    }

    /**
     * 查找玩家所在的游戏会话
     */
    private GameSession findPlayerGameSession(Player player) {
        return plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
    }

    /**
     * 查找玩家所在的房间
     */
    private Room findPlayerRoom(Player player) {
        return plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
    }
}
