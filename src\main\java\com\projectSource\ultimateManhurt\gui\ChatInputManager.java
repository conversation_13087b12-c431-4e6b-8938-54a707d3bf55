package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

/**
 * 聊天输入管理器
 * 处理GUI中需要聊天输入的功能
 */
public class ChatInputManager implements Listener {
    
    private final UltimateManhurt plugin;
    private final Map<UUID, ChatInputSession> activeSessions = new ConcurrentHashMap<>();
    
    public ChatInputManager(UltimateManhurt plugin) {
        this.plugin = plugin;
    }
    
    /**
     * 开始聊天输入会话
     */
    public void startChatInput(Player player, String prompt, Consumer<String> onInput, Runnable onCancel) {
        UUID playerId = player.getUniqueId();
        
        // 取消现有会话
        cancelChatInput(player);
        
        // 创建新会话
        ChatInputSession session = new ChatInputSession(player, prompt, onInput, onCancel);
        activeSessions.put(playerId, session);
        
        // 发送提示消息
        ComponentUtil.sendMessage(player, ComponentUtil.parse("<yellow>" + prompt));
        ComponentUtil.sendMessage(player, ComponentUtil.parse("<gray>输入 'cancel' 取消操作"));
        
        // 设置超时
        new BukkitRunnable() {
            @Override
            public void run() {
                ChatInputSession currentSession = activeSessions.get(playerId);
                if (currentSession == session) {
                    // 会话超时
                    activeSessions.remove(playerId);
                    ComponentUtil.sendMessage(player, ComponentUtil.warning("输入超时，操作已取消"));
                    if (onCancel != null) {
                        onCancel.run();
                    }
                }
            }
        }.runTaskLater(plugin, 600L); // 30秒超时
    }
    
    /**
     * 取消聊天输入
     */
    public void cancelChatInput(Player player) {
        UUID playerId = player.getUniqueId();
        ChatInputSession session = activeSessions.remove(playerId);
        
        if (session != null && session.onCancel != null) {
            session.onCancel.run();
        }
    }
    
    /**
     * 检查玩家是否在输入状态
     */
    public boolean isInChatInput(Player player) {
        return activeSessions.containsKey(player.getUniqueId());
    }
    
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();
        
        ChatInputSession session = activeSessions.get(playerId);
        if (session == null) {
            return; // 不在输入状态
        }
        
        // 取消聊天事件，防止消息广播
        event.setCancelled(true);
        
        String message = event.getMessage().trim();
        
        // 检查是否取消
        if ("cancel".equalsIgnoreCase(message)) {
            activeSessions.remove(playerId);
            ComponentUtil.sendMessage(player, ComponentUtil.info("操作已取消"));
            
            if (session.onCancel != null) {
                // 在主线程执行回调
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        session.onCancel.run();
                    }
                }.runTask(plugin);
            }
            return;
        }
        
        // 处理输入
        activeSessions.remove(playerId);
        
        // 在主线程执行回调
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    session.onInput.accept(message);
                } catch (Exception e) {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("处理输入时发生错误: " + e.getMessage()));
                    plugin.getLogger().severe("处理聊天输入时发生错误: " + e.getMessage());
                }
            }
        }.runTask(plugin);
    }
    
    /**
     * 聊天输入会话
     */
    private static class ChatInputSession {
        final Player player;
        final String prompt;
        final Consumer<String> onInput;
        final Runnable onCancel;
        final long startTime;
        
        ChatInputSession(Player player, String prompt, Consumer<String> onInput, Runnable onCancel) {
            this.player = player;
            this.prompt = prompt;
            this.onInput = onInput;
            this.onCancel = onCancel;
            this.startTime = System.currentTimeMillis();
        }
    }
    
    /**
     * 清理所有会话
     */
    public void cleanup() {
        activeSessions.clear();
    }
    
    /**
     * 获取活跃会话数量
     */
    public int getActiveSessionCount() {
        return activeSessions.size();
    }
}
