package com.projectSource.ultimateManhurt.profession;

import com.projectSource.ultimateManhurt.game.PlayerRole;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextColor;
import org.bukkit.Material;

/**
 * 职业枚举
 * 定义所有可选择的职业及其技能
 */
public enum Profession {
    
    // 捕猎者职业
    ENDERMAN(
        "末影人",
        "闪烁大师，擅长瞬间移动和突袭",
        PlayerRole.HUNTER,
        Material.ENDER_PEARL,
        NamedTextColor.DARK_PURPLE,
        "👤",
        // 被动技能
        "闪烁",
        "受到一次攻击会闪烁到玩家身后，对该玩家造成一次攻击伤害并使该玩家获得5s的失明效果。45秒冷却",
        45, // 45秒冷却
        // 主动技能
        "末影传送",
        "当任意的剑在副手时右键，可以闪烁到前方一段距离(指针指向位置，无论距离远近并且不会传送到方块里面)。如果在前方位置有玩家则会闪烁到该名玩家背后并获得速度三效果。但是受到攻击的3s内无法使用",
        12
    ),
    
    BUTCHER(
        "屠夫",
        "近战专家，拥有钩子控制和防御能力",
        PlayerRole.HUNTER,
        Material.IRON_AXE,
        NamedTextColor.RED,
        "🔪",
        // 被动技能
        "腐肉堆积",
        "受到近战攻击30%概率获得抗性III效果3秒",
        0, // 被动技能无冷却
        // 主动技能
        "钩子",
        "副手持剑右键，钩住视线内玩家拉到身边。对敌人造成6点伤害+3秒缓慢X+跳跃禁用，对队友无负面效果",
        18
    ),
    
    SKELETON(
        "骷髅",
        "远程射手，精通弓箭战术和诅咒魔法",
        PlayerRole.HUNTER,
        Material.BOW,
        NamedTextColor.GRAY,
        "💀",
        // 被动技能
        "积少成多",
        "射箭50%概率额外获得一根箭矢",
        0, // 被动技能无冷却
        // 主动技能
        "巫妖诅咒",
        "副手持剑右键开关。开启时射箭扣除自身13%当前生命值，但箭矢附带3秒减速II+5秒凋零II效果",
        0 // 开关技能无冷却
    ),
    
    // 速通者职业
    EXPLORER(
        "探险家",
        "资源收集专家，擅长快速获取材料",
        PlayerRole.SPEEDRUNNER,
        Material.DIAMOND_PICKAXE,
        NamedTextColor.BLUE,
        "⛏",
        // 被动技能
        "寻宝直觉",
        "挖掘矿物时40%概率2倍掉落，20%概率3倍掉落，10%概率4倍掉落。触发时回复3点饱食度",
        0, // 被动技能无冷却
        // 主动技能
        "急速挖掘",
        "副手持剑右键，获得急迫III效果15秒",
        25
    ),
    
    IRON_GOLEM(
        "铁傀儡",
        "坚固守护者，拥有强大的防御和支援能力",
        PlayerRole.SPEEDRUNNER,
        Material.IRON_BLOCK,
        NamedTextColor.WHITE,
        "🤖",
        // 被动技能
        "强击",
        "受到敌人攻击15%概率免疫伤害并100%反伤给攻击者（不对队友生效）",
        0, // 被动技能无冷却
        // 主动技能
        "治疗光环",
        "副手持剑右键，为自己和周围5格友军提供伤害吸收IV效果15秒",
        45
    ),
    
    HUNTER(
        "猎人",
        "自然之子，拥有多样化的生存技能",
        PlayerRole.SPEEDRUNNER,
        Material.LEATHER_CHESTPLATE,
        NamedTextColor.GREEN,
        "🏹",
        // 被动技能
        "自然祝福",
        "每45秒自动获得随机增益效果",
        45,
        // 主动技能
        "战斗专注",
        "副手持剑右键，开启18秒战斗专注。期间近战攻击回复1.5血，射箭回复4.5血",
        150
    ),

    SHADOW_ASSASSIN(
        "暗影刺客",
        "隐秘杀手，擅长背刺和隐身",
        PlayerRole.SPEEDRUNNER,
        Material.NETHERITE_SWORD,
        NamedTextColor.DARK_PURPLE,
        "🗡",
        // 被动技能
        "背刺",
        "从背后攻击敌人造成原伤害+目标最大生命值25%额外伤害，8秒冷却（不对队友生效）",
        8, // 8秒冷却
        // 主动技能
        "魅影无形",
        "副手持剑右键，对猎人阵营隐身10秒。攻击敌人后现形，指南针仍可追踪",
        30
    ),

    SPIDER(
        "蜘蛛",
        "毒性猎手，拥有跳跃和毒液攻击",
        PlayerRole.HUNTER,
        Material.SPIDER_EYE,
        NamedTextColor.DARK_RED,
        "🕷",
        // 被动技能
        "毒液狂飙",
        "攻击蜘蛛的敌人获得3秒中毒效果，每次受伤重置冷却",
        0, // 被动技能无冷却
        // 主动技能
        "跳跃",
        "副手持剑右键，高跳落地。自身受40%最大生命值伤害，8格内敌人受2-12伤害+中毒I-V效果5秒（距离越近伤害越高）",
        18
    ),

    PIGLIN(
        "猪灵",
        "团队支援者，拥有光环和集结能力",
        PlayerRole.HUNTER,
        Material.GOLD_INGOT,
        NamedTextColor.GOLD,
        "🐷",
        // 被动技能
        "狂意",
        "光环：30格内友军获得速度I，敌方速通者发光+受到10%额外伤害",
        0, // 被动技能无冷却
        // 主动技能
        "猪灵集结",
        "副手持剑右键，传送所有队友到身边，全队获得抗性I效果30秒",
        120
    ),

    SHAMAN(
        "萨满",
        "自然召唤师，拥有狼伙伴和风暴控制",
        PlayerRole.SPEEDRUNNER,
        Material.BONE,
        NamedTextColor.GREEN,
        "🐕",
        // 被动技能
        "动物伙伴",
        "3分钟后获得2只狼（30血5攻击）。狼死亡扣除萨满20%最大生命值，击杀猎人额外获得1只狼，狼90秒后复活",
        0, // 被动技能无冷却
        // 主动技能
        "龙卷风暴",
        "副手持剑右键，16格风场8秒。场内所有人减速IV，萨满无敌，猎人受魔法伤害（0.5×人数递增至2.5×人数）",
        150
    ),

    // 新增捕猎者职业
    FEAR_LORD(
        "恐惧魔王",
        "暗影主宰，通过窃取敌人生命力增强自己",
        PlayerRole.HUNTER,
        Material.WITHER_SKELETON_SKULL,
        NamedTextColor.DARK_PURPLE,
        "👹",
        // 被动技能
        "灵魂虹吸",
        "击杀玩家回复20%最大生命值+获得力量I效果18秒",
        0, // 被动技能无冷却
        // 主动技能
        "影压",
        "副手持剑右键，发射凋灵头颅造成小爆炸（1-2格）。造成5+连击次数×1.5的真实伤害，可以致死（不对队友生效）",
        15
    ),

    // 新增速通者职业
    LENA(
        "莱娜",
        "森林守护者，为队友提供自然祝福和敏捷移动",
        PlayerRole.SPEEDRUNNER,
        Material.OAK_LEAVES,
        NamedTextColor.GREEN,
        "🌿",
        // 被动技能
        "森之祝福",
        "光环：10格内友军获得35%闪避效果，白天额外提供生命恢复I",
        0, // 被动技能无冷却
        // 主动技能
        "钩爪",
        "副手持剑右键，钩住视线内方块获得冲力。免疫此次摔落伤害，落地为周围友军提供抗性提升I效果8秒",
        21
    ),

    CAPTAIN(
        "船长",
        "海上领袖，为队友承担伤害并提供强大保护",
        PlayerRole.SPEEDRUNNER,
        Material.COMPASS,
        NamedTextColor.BLUE,
        "☠",
        // 被动技能
        "朗姆酒",
        "血量低于75%时触发：10秒内受伤减免95%，15秒后分5次返还伤害（不致死），60秒冷却",
        60, // 60秒冷却
        // 主动技能
        "海灵诅咒",
        "副手持剑右键，10秒内60格友军受伤减免75%，其中80%伤害由船长承担",
        75
    ),

    ZOMBIE(
        "僵尸",
        "不死猎手，通过攻击获得力量并拥有强大生存能力",
        PlayerRole.HUNTER,
        Material.ROTTEN_FLESH,
        NamedTextColor.DARK_GREEN,
        "🧟",
        // 被动技能
        "尸鬼狂怒",
        "攻击速通者窃取其饱食度（窃取量=造成伤害×0.5）",
        0, // 被动技能冷却时间为0
        // 主动技能
        "回光返照",
        "副手持剑右键，8秒内敌人对僵尸的伤害转为治疗效果",
        90
    ),

    // 新增职业
    ROBOT(
        "机器人",
        "建筑工程师，擅长建造和陷阱设置",
        PlayerRole.SPEEDRUNNER,
        Material.IRON_PICKAXE,
        NamedTextColor.GRAY,
        "🔧",
        // 被动技能
        "分则能成",
        "每放置一组方块获得一个金苹果",
        0, // 被动技能无冷却
        // 主动技能
        "陷阱",
        "副手持剑右键，在原地生成隐形地雷。感应范围1.5格，猎人触碰0.5秒后爆炸，潜行可避免。最多9个地雷，45秒内重复触发伤害叠加，不伤害友军",
        15
    ),

    WITCH(
        "女巫",
        "药水专家，掌握治疗和诅咒的双重力量",
        PlayerRole.HUNTER,
        Material.BREWING_STAND,
        NamedTextColor.DARK_PURPLE,
        "🧙",
        // 被动技能
        "药水专精",
        "右键背包中的药水转为魔药（友军增益）或毒药（敌军负面）。魔药提供随机增益，毒药造成随机负面效果，30秒冷却",
        30, // 30秒冷却
        // 主动技能
        "巫毒诅咒",
        "副手持剑右键，12秒内6格敌人每秒失去5%当前生命值（真实伤害），禁止回血，血量低于25%最大生命值时立即死亡",
        60
    ),

    // 新增速通者职业 - 方源
    FANGYUAN(
        "方源",
        "时光掌控者，能够操控时间回溯和创造时光分身",
        PlayerRole.SPEEDRUNNER,
        Material.CLOCK,
        NamedTextColor.GOLD,
        "⏰",
        // 被动技能
        "春秋蝉",
        "当方源血量降低到13%时，有概率让所有玩家回到上一个创建的光阴分身状态。360秒冷却（冷却期间仍可使用，概率=基础概率×冷却进度）",
        360, // 360秒冷却
        // 主动技能
        "春秋必成",
        "副手持剑右键，记录所有玩家的背包状态、血量、饥饿条、位置。再次使用回到记录状态。300秒冷却",
        300
    ),

    // 新增猎人职业 - 暗夜领主
    NIGHT_LORD(
        "暗夜领主",
        "黑夜的主宰，在夜晚获得强大力量并能操控黑暗",
        PlayerRole.HUNTER,
        Material.NETHERITE_HELMET,
        NamedTextColor.DARK_BLUE,
        "🌙",
        // 被动技能
        "暗夜猎影",
        "在夜晚时被动获得速度2、生命恢复1、夜视的加成",
        0, // 被动技能无冷却
        // 主动技能
        "暗月升起",
        "副手持剑右键，接下来30秒将变成黑夜，同时暗夜领主可以飞行。120秒冷却",
        120
    ),

    // 新增速通者职业 - 村民
    VILLAGER(
        "村民",
        "善良的村民，用朴实的力量守护同伴",
        PlayerRole.SPEEDRUNNER,
        Material.EMERALD,
        NamedTextColor.GREEN,
        "👨‍🌾",
        // 被动技能
        "村民互助",
        "20格内每有一个队友，村民和其队友获得5%伤害减免（最多25%）。队友受伤时，村民有20%概率为其回复2点生命值",
        0, // 被动技能无冷却
        // 主动技能
        "上帝之手",
        "副手持剑右键，为20格内所有队友提供15秒生命恢复II+饱和I效果。村民自身获得抗性I效果20秒",
        120
    ),

    // 新增捕猎者职业 - 赏金猎人
    BOUNTY_HUNTER(
        "赏金猎人",
        "狡猾的窃贼，专门偷取敌人的资源和装备",
        PlayerRole.HUNTER,
        Material.LEATHER_BOOTS,
        NamedTextColor.DARK_GRAY,
        "🥷",
        // 被动技能
        "忍术",
        "攻击敌人时20%概率从其背包中随机偷取1-3个物品到自己背包。7%概率窃取其身上的装备",
        0, // 被动技能无冷却
        // 主动技能
        "暗影步",
        "副手持剑右键，获得8秒隐身效果+速度II。期间攻击敌人不会解除隐身，且必定触发忍术效果。但受到伤害会立即现形",
        75
    );
    
    private final String displayName;
    private final String description;
    private final PlayerRole requiredRole;
    private final Material icon;
    private final TextColor color;
    private final String emoji;
    
    // 被动技能信息
    private final String passiveSkillName;
    private final String passiveSkillDescription;
    private final int passiveSkillCooldown; // 秒，0表示无冷却
    
    // 主动技能信息
    private final String activeSkillName;
    private final String activeSkillDescription;
    private final int activeSkillCooldown; // 秒
    
    Profession(String displayName, String description, PlayerRole requiredRole, Material icon, 
               TextColor color, String emoji, String passiveSkillName, String passiveSkillDescription,
               int passiveSkillCooldown, String activeSkillName, String activeSkillDescription,
               int activeSkillCooldown) {
        this.displayName = displayName;
        this.description = description;
        this.requiredRole = requiredRole;
        this.icon = icon;
        this.color = color;
        this.emoji = emoji;
        this.passiveSkillName = passiveSkillName;
        this.passiveSkillDescription = passiveSkillDescription;
        this.passiveSkillCooldown = passiveSkillCooldown;
        this.activeSkillName = activeSkillName;
        this.activeSkillDescription = activeSkillDescription;
        this.activeSkillCooldown = activeSkillCooldown;
    }
    
    // Getter方法
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public PlayerRole getRequiredRole() {
        return requiredRole;
    }
    
    public Material getIcon() {
        return icon;
    }
    
    public TextColor getColor() {
        return color;
    }
    
    public String getEmoji() {
        return emoji;
    }
    
    public String getPassiveSkillName() {
        return passiveSkillName;
    }
    
    public String getPassiveSkillDescription() {
        return passiveSkillDescription;
    }
    
    public int getPassiveSkillCooldown() {
        return passiveSkillCooldown;
    }
    
    public String getActiveSkillName() {
        return activeSkillName;
    }
    
    public String getActiveSkillDescription() {
        return activeSkillDescription;
    }
    
    public int getActiveSkillCooldown() {
        return activeSkillCooldown;
    }
    
    /**
     * 获取带颜色的显示名称组件
     */
    public Component getDisplayComponent() {
        return Component.text(emoji + " " + displayName, color);
    }
    
    /**
     * 获取详细信息组件
     */
    public Component getDetailComponent() {
        return Component.text()
                .append(getDisplayComponent())
                .append(Component.text(" - ", NamedTextColor.GRAY))
                .append(Component.text(description, NamedTextColor.WHITE))
                .build();
    }
    
    /**
     * 检查职业是否适用于指定角色
     */
    public boolean isAvailableForRole(PlayerRole role) {
        return this.requiredRole == role;
    }
    
    /**
     * 获取指定角色可用的所有职业
     */
    public static Profession[] getAvailableProfessions(PlayerRole role) {
        return java.util.Arrays.stream(values())
                .filter(profession -> profession.isAvailableForRole(role))
                .toArray(Profession[]::new);
    }
    
    /**
     * 根据名称获取职业
     */
    public static Profession getByName(String name) {
        for (Profession profession : values()) {
            if (profession.name().equalsIgnoreCase(name) || 
                profession.getDisplayName().equals(name)) {
                return profession;
            }
        }
        return null;
    }
}
