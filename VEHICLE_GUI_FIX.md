# 载具中玩家GUI打开问题修复

## 🐛 问题描述

**问题现象：**
- 玩家在船上时无法打开GUI界面
- 使用 `/manhunt gui` 命令没有反应
- 其他GUI相关功能也可能受到影响

**问题原因：**
- Bukkit/Spigot 在玩家处于载具中时会阻止某些GUI操作
- 原代码直接调用 `player.openInventory()` 没有检查玩家状态
- 载具状态包括：船、矿车、马匹等

## 🔧 解决方案

### 1. BaseGui.java 修复

**修复位置：** `src/main/java/com/projectSource/ultimateManhurt/gui/BaseGui.java`

**修复前：**
```java
public void open() {
    player.openInventory(inventory);
}
```

**修复后（循环检测机制）：**
```java
public void open() {
    openWithRetry(0);
}

private void openWithRetry(int attemptCount) {
    // 检查玩家是否在线
    if (!player.isOnline()) {
        return;
    }

    // 检查玩家是否在载具中
    if (player.isInsideVehicle()) {
        // 第一次尝试时提示玩家并自动离开载具
        if (attemptCount == 0) {
            ComponentUtil.sendMessage(player, ComponentUtil.warning("检测到你在载具中，正在尝试打开GUI..."));
            player.leaveVehicle();
        }

        // 如果尝试次数超过限制，提示玩家手动离开载具
        if (attemptCount >= 10) { // 最多尝试10次，约2秒
            ComponentUtil.sendMessage(player, ComponentUtil.error("无法自动离开载具，请手动离开载具后重新尝试打开GUI"));
            ComponentUtil.sendMessage(player, ComponentUtil.info("提示：按 Shift 键可以离开大多数载具"));
            ComponentUtil.sendMessage(player, ComponentUtil.info("或者使用 /manhunt gui force 强制打开GUI"));
            return;
        }

        // 延迟重试
        org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
            openWithRetry(attemptCount + 1);
        }, 4L); // 每次延迟4 tick (0.2秒)
    } else {
        // 成功离开载具或本来就不在载具中，打开GUI
        if (attemptCount > 0) {
            ComponentUtil.sendMessage(player, ComponentUtil.success("已成功离开载具，正在打开GUI..."));
        }
        player.openInventory(inventory);
    }
}
```

### 2. GuiManager.java 增强

**修复位置：** `src/main/java/com/projectSource/ultimateManhurt/gui/GuiManager.java`

**添加强制打开功能：**
```java
/**
 * 强制打开房间列表GUI（忽略载具状态）
 */
public void forceOpenRoomListGui(Player player) {
    RoomListGui gui = new RoomListGui(plugin, player);
    UUID playerId = player.getUniqueId();

    // 关闭现有GUI
    closeGui(player);

    // 强制打开新GUI
    openGuis.put(playerId, gui);
    gui.forceOpen();
}
```

### 3. 命令支持增强

**修复位置：** `src/main/java/com/projectSource/ultimateManhurt/command/ManhuntCommand.java`

**添加 force 参数支持：**
```java
private void handleOpenGui(Player player, String[] args) {
    boolean forceOpen = args.length > 1 && "force".equalsIgnoreCase(args[1]);

    if (forceOpen) {
        plugin.getGuiManager().forceOpenRoomListGui(player);
        ComponentUtil.sendMessage(player, ComponentUtil.warning("已强制打开房间列表GUI（忽略载具状态）"));
    } else {
        plugin.getGuiManager().openRoomListGui(player);
        ComponentUtil.sendMessage(player, ComponentUtil.info("已打开房间列表GUI"));
    }
}
```

## 🎯 修复特点

### 循环检测机制
- **智能重试：** 最多尝试10次（约2秒），每次间隔0.2秒
- **自动离开：** 第一次检测时自动让玩家离开载具
- **状态监控：** 持续监控玩家载具状态直到成功或超时

### 多重解决方案
- **自动处理：** 优先尝试自动离开载具
- **手动提示：** 超时后提示玩家手动离开载具
- **强制打开：** 提供 `/manhunt gui force` 强制打开选项

### 用户体验
- **进度反馈：** 实时告知玩家处理状态
- **成功提示：** 成功离开载具时显示确认消息
- **多种选择：** 提供多种解决方案供玩家选择

### 技术细节
- **异步处理：** 使用 Bukkit 调度器循环检测
- **状态验证：** 每次检查玩家在线状态和载具状态
- **资源管理：** 超时后自动停止检测，避免资源浪费

## 🚀 影响范围

### 受益的GUI
- **房间列表GUI：** `/manhunt gui` 命令
- **房间设置GUI：** 房间配置界面
- **玩家管理GUI：** 玩家角色设置
- **装备包GUI：** StartKit 管理
- **统计GUI：** 玩家统计和排行榜
- **所有其他GUI：** 继承自 BaseGui 的所有界面

### 支持的载具类型
- **船只：** 各种类型的船
- **矿车：** 普通矿车、动力矿车等
- **马匹：** 马、驴、骡子等
- **其他载具：** 所有 Bukkit 识别的载具

## 🧪 测试建议

### 测试场景
1. **自动处理测试：**
   ```
   1. 玩家坐在船上
   2. 执行 /manhunt gui
   3. 验证：显示处理提示，自动离开船只，成功打开GUI
   ```

2. **循环检测测试：**
   ```
   1. 玩家坐在无法离开的载具中（如被困的船）
   2. 执行 /manhunt gui
   3. 验证：循环尝试10次后显示手动提示
   ```

3. **强制打开测试：**
   ```
   1. 玩家坐在载具中
   2. 执行 /manhunt gui force
   3. 验证：忽略载具状态，直接打开GUI
   ```

4. **正常情况测试：**
   ```
   1. 玩家在地面上
   2. 打开GUI
   3. 验证：正常打开，无额外延迟
   ```

### 预期结果
- ✅ **载具中（可离开）：** 自动离开载具，显示成功消息，打开GUI
- ✅ **载具中（无法离开）：** 循环尝试后提示手动操作和强制选项
- ✅ **强制打开：** 忽略载具状态，直接打开GUI并显示警告
- ✅ **正常状态：** 立即打开GUI，无额外处理

## 📝 技术说明

### 延迟时间选择
- **3 tick 延迟：** 约 0.15 秒，足够让玩家完全离开载具
- **平衡考虑：** 既保证功能正常，又不会让用户感觉明显延迟

### 错误处理
- **在线检查：** 防止玩家离线时的空指针异常
- **状态验证：** 确保玩家真正离开载具
- **友好提示：** 告知用户操作状态和可能的问题

### 向后兼容
- **无破坏性：** 不影响现有的GUI功能
- **透明处理：** 对正常用户完全透明
- **性能友好：** 只在需要时进行额外处理

## 🎉 总结

成功修复了玩家在载具中无法打开GUI的问题：

- ✅ **问题识别：** 准确定位载具状态导致的GUI打开失败
- ✅ **循环检测：** 智能重试机制，最多尝试10次确保成功
- ✅ **多重方案：** 自动处理 + 手动提示 + 强制打开三重保障
- ✅ **用户友好：** 提供清晰的进度反馈和多种解决选项
- ✅ **全面覆盖：** 修复影响所有继承自 BaseGui 的界面
- ✅ **资源优化：** 超时机制避免无限循环，保护服务器性能
- ✅ **命令增强：** 添加 `/manhunt gui force` 强制打开选项

现在玩家无论在什么载具中，都有多种方式正常使用所有GUI功能！🚢🔄📋✨
