package com.projectSource.ultimateManhurt.game;

import org.bukkit.Material;

/**
 * 胜利模式枚举
 * 定义不同的游戏胜利条件模式
 */
public enum VictoryMode {
    
    /**
     * 生命模式
     * 捕猎者需要在时间内击杀掉速通者的所有生命
     * 速通者需要存活到时间结束
     */
    LIFE_MODE(
        "生命模式",
        "捕猎者击杀速通者所有生命获胜，速通者存活到时间结束获胜",
        Material.TOTEM_OF_UNDYING,
        "⚔️ 生死对决！捕猎者必须在时间内击杀速通者",
        true,  // 需要生命系统
        false, // 不需要末影龙
        false  // 不需要积分系统
    ),
    
    /**
     * 末影龙模式
     * 速通者需要击败末影龙
     * 捕猎者需要阻止速通者击败末影龙
     */
    DRAGON_MODE(
        "末影龙模式",
        "速通者击败末影龙获胜，捕猎者阻止速通者获胜",
        Material.DRAGON_HEAD,
        "🐉 终极挑战！速通者必须击败末影龙",
        true,  // 需要生命系统
        true,  // 需要末影龙
        false  // 不需要积分系统
    ),
    
    /**
     * 积分模式
     * 速通者通过完成里程碑获得积分，达到目标积分获胜
     * 捕猎者需要阻止速通者达到目标积分
     */
    SCORE_MODE(
        "积分模式",
        "速通者通过完成里程碑获得积分，达到目标积分获胜",
        Material.EXPERIENCE_BOTTLE,
        "🏆 里程碑挑战！速通者通过完成任务获得积分",
        true,  // 需要生命系统
        false, // 不需要末影龙
        true   // 需要积分系统
    ),
    
    /**
     * 混合模式
     * 速通者可以通过击败末影龙或达到目标积分获胜
     * 捕猎者需要阻止速通者完成任何胜利条件
     */
    HYBRID_MODE(
        "混合模式",
        "速通者可以通过击败末影龙或达到目标积分获胜",
        Material.NETHER_STAR,
        "⭐ 多重选择！速通者可以选择击败末影龙或完成积分挑战",
        true,  // 需要生命系统
        true,  // 需要末影龙
        true   // 需要积分系统
    ),

    /**
     * 守卫模式
     * 速通者需要守卫中心塔的凋零，捕猎者需要击杀凋零
     * 速通者在(0,0)出生，捕猎者在外围出生
     */
    GUARD_MODE(
        "守卫模式",
        "速通者守卫中心塔的凋零，捕猎者击杀凋零获胜",
        Material.WITHER_SKELETON_SKULL,
        "🏰 塔防对决！速通者守卫凋零，捕猎者攻城略地",
        true,  // 需要生命系统
        false, // 不需要末影龙
        false, // 不需要积分系统
        true   // 需要守卫系统
    );
    
    private final String displayName;
    private final String description;
    private final Material icon;
    private final String gameDescription;
    private final boolean requiresLifeSystem;
    private final boolean requiresDragon;
    private final boolean requiresScoreSystem;
    private final boolean requiresGuardSystem;

    VictoryMode(String displayName, String description, Material icon, String gameDescription,
                boolean requiresLifeSystem, boolean requiresDragon, boolean requiresScoreSystem) {
        this(displayName, description, icon, gameDescription, requiresLifeSystem, requiresDragon, requiresScoreSystem, false);
    }

    VictoryMode(String displayName, String description, Material icon, String gameDescription,
                boolean requiresLifeSystem, boolean requiresDragon, boolean requiresScoreSystem, boolean requiresGuardSystem) {
        this.displayName = displayName;
        this.description = description;
        this.icon = icon;
        this.gameDescription = gameDescription;
        this.requiresLifeSystem = requiresLifeSystem;
        this.requiresDragon = requiresDragon;
        this.requiresScoreSystem = requiresScoreSystem;
        this.requiresGuardSystem = requiresGuardSystem;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public Material getIcon() {
        return icon;
    }
    
    public String getGameDescription() {
        return gameDescription;
    }
    
    public boolean requiresLifeSystem() {
        return requiresLifeSystem;
    }
    
    public boolean requiresDragon() {
        return requiresDragon;
    }
    
    public boolean requiresScoreSystem() {
        return requiresScoreSystem;
    }

    public boolean requiresGuardSystem() {
        return requiresGuardSystem;
    }

    /**
     * 获取详细说明
     */
    public String[] getDetailedDescription() {
        return switch (this) {
            case LIFE_MODE -> new String[] {
                "<gray>胜利条件：",
                "<green>• 速通者：存活到时间结束",
                "<red>• 捕猎者：击杀速通者所有生命",
                "",
                "<gray>特点：",
                "<white>• 纯粹的生存挑战",
                "<white>• 强调战斗和逃生技巧",
                "<white>• 适合短时间激烈对战"
            };
            case DRAGON_MODE -> new String[] {
                "<gray>胜利条件：",
                "<green>• 速通者：击败末影龙",
                "<red>• 捕猎者：阻止速通者击败末影龙",
                "",
                "<gray>特点：",
                "<white>• 经典Minecraft挑战",
                "<white>• 需要完整的游戏进程",
                "<white>• 适合长时间策略对战"
            };
            case SCORE_MODE -> new String[] {
                "<gray>胜利条件：",
                "<green>• 速通者：达到目标积分",
                "<red>• 捕猎者：阻止速通者达到目标积分",
                "",
                "<gray>特点：",
                "<white>• 多样化的胜利路径",
                "<white>• 奖励探索和建造",
                "<white>• 适合中等时长游戏"
            };
            case HYBRID_MODE -> new String[] {
                "<gray>胜利条件：",
                "<green>• 速通者：击败末影龙 或 达到目标积分",
                "<red>• 捕猎者：阻止速通者完成任何胜利条件",
                "",
                "<gray>特点：",
                "<white>• 最大的策略自由度",
                "<white>• 多种胜利路径选择",
                "<white>• 适合高级玩家对战"
            };
            case GUARD_MODE -> new String[] {
                "<gray>胜利条件：",
                "<green>• 速通者：守卫凋零直到时间结束",
                "<red>• 捕猎者：击杀中心塔的凋零",
                "",
                "<gray>特点：",
                "<white>• 塔防式对战体验",
                "<white>• 速通者在中心防守",
                "<white>• 捕猎者从外围攻城",
                "<white>• 凋零具有强大的防御能力"
            };
        };
    }
    
    /**
     * 获取推荐游戏时长（分钟）
     */
    public int getRecommendedDuration() {
        return switch (this) {
            case LIFE_MODE -> 15;      // 15分钟 - 快节奏战斗
            case DRAGON_MODE -> 45;    // 45分钟 - 完整速通流程
            case SCORE_MODE -> 30;     // 30分钟 - 中等时长挑战
            case HYBRID_MODE -> 60;    // 60分钟 - 最大自由度
            case GUARD_MODE -> 25;     // 25分钟 - 塔防对战
        };
    }
    
    /**
     * 获取推荐目标积分
     */
    public int getRecommendedTargetScore() {
        return switch (this) {
            case LIFE_MODE -> 0;       // 不使用积分
            case DRAGON_MODE -> 0;     // 不使用积分
            case SCORE_MODE -> 400;    // 中等难度积分
            case HYBRID_MODE -> 600;   // 较高难度积分
            case GUARD_MODE -> 0;      // 不使用积分
        };
    }
    
    /**
     * 是否为默认模式
     */
    public boolean isDefault() {
        return this == DRAGON_MODE;
    }
}
