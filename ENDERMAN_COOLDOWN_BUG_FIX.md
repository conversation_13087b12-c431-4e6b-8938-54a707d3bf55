# 末影人被动技能冷却时间Bug修复

## 🐛 问题发现

从日志分析发现了关键问题：

```
[11:35:33] 末影人 YamabukiAlice 闪烁技能冷却中，剩余 1 秒
[11:35:37] 末影人 YamabukiAlice 闪烁技能冷却中，剩余 43 秒
```

**问题分析：**
- 冷却时间从1秒跳到43秒，说明技能被触发了
- 但是没有看到我们添加的调试日志
- 这意味着技能不是通过事件监听器触发的

## 🔍 根本原因

**发现了真正的Bug：**

在 `Profession.java` 中，末影人的被动技能冷却时间被设置为 **45秒**：

```java
ENDERMAN(
    // ...
    // 被动技能
    "闪烁",
    "受到一次攻击会闪烁到安全位置",
    45, // ← 这里是问题！
    // ...
),
```

**问题所在：**
`ProfessionManager` 中有一个定时任务每秒检查被动技能：

```java
// 启动被动技能任务（每秒检查一次）
passiveSkillTask = Bukkit.getScheduler().runTaskTimer(plugin, 
    this::processPassiveSkills, 20L, 20L);
```

**错误逻辑：**
```java
private void processPassiveSkillWithCooldown(Player player, Profession profession) {
    if (profession.getPassiveSkillCooldown() <= 0) {
        return; // 无冷却时间的被动技能不在这里处理
    }
    
    // 检查是否到了触发时间
    if (!skillCooldown.isOnCooldown(playerId, skillName)) {
        // 执行被动技能效果 ← 这里错误地触发了末影人技能！
        executePassiveSkill(player, profession);
        
        // 设置下次触发时间
        skillCooldown.setCooldown(playerId, skillName, profession.getPassiveSkillCooldown());
    }
}
```

**结果：**
- 系统认为末影人的闪烁是一个需要每45秒自动触发的技能
- 每45秒自动调用一次，但是 `executePassiveSkill` 方法中没有处理末影人
- 所以只设置了冷却时间，但没有实际传送

## ✅ 修复方案

### 🔧 核心修复

**将末影人被动技能冷却时间改为0：**

```java
ENDERMAN(
    // ...
    // 被动技能
    "闪烁",
    "受到一次攻击会闪烁到安全位置",
    0, // 被动技能不是定时触发的，设置为0
    // ...
),
```

### 📋 技能分类说明

**定时触发的被动技能（冷却时间 > 0）：**
- 猎人的自然祝福（每45秒获得随机Buff）

**事件触发的被动技能（冷却时间 = 0）：**
- 末影人的闪烁（受到攻击时触发）
- 屠夫的腐肉堆积（受到近战攻击时触发）
- 铁傀儡的强击（受到攻击时触发）

## 🎯 修复效果

**修复前的错误流程：**
```
1. 玩家受到攻击
2. ProfessionListener 检查冷却时间（已被定时任务设置）
3. 显示"技能冷却中"，不执行传送
4. 45秒后，定时任务触发，设置新的冷却时间
5. 循环重复，玩家永远无法通过攻击触发闪烁
```

**修复后的正确流程：**
```
1. 玩家受到攻击
2. ProfessionListener 检查冷却时间（无冷却或已过期）
3. 执行传送逻辑
4. 设置45秒冷却时间
5. 45秒内再次受攻击时显示冷却中
6. 45秒后可以再次触发
```

## 🧪 测试验证

**测试步骤：**
1. 重新加载插件或重启服务器
2. 设置末影人职业：`/manhunt professiontest set ENDERMAN`
3. 让其他玩家攻击
4. 应该能看到传送效果和详细日志

**预期日志输出：**
```
[INFO]: 检测到末影人 YamabukiAlice 受到攻击，尝试触发闪烁
[INFO]: 末影人 YamabukiAlice 闪烁技能可用，开始传送
[INFO]: 开始寻找安全位置，玩家当前位置: X, Y, Z
[INFO]: 找到安全位置: X, Y, Z
[INFO]: 传送成功！
```

## 📊 其他职业检查

**确认其他职业的配置正确：**

| 职业 | 被动技能类型 | 冷却时间设置 | 是否正确 |
|------|-------------|-------------|----------|
| 末影人 | 事件触发 | 0 ✅ | 正确 |
| 屠夫 | 事件触发 | 0 | 需要检查 |
| 骷髅 | 无被动技能 | 0 | 正确 |
| 探索者 | 无被动技能 | 0 | 正确 |
| 铁傀儡 | 事件触发 | 0 | 需要检查 |
| 猎人 | 定时触发 | 45 | 正确 |

## 🔄 后续优化

**清理调试代码：**
修复确认后，可以移除添加的详细调试日志，保留必要的错误日志即可。

**代码优化：**
可以考虑在 `processPassiveSkillWithCooldown` 方法中添加更明确的职业检查，避免类似问题。

## 📝 经验总结

**这个Bug的教训：**
1. **配置驱动的系统需要仔细设计：** 冷却时间参数被两个不同的系统使用
2. **事件触发 vs 定时触发：** 需要明确区分这两种被动技能类型
3. **调试的重要性：** 通过日志分析发现了真正的问题根源
4. **系统理解：** 需要全面理解代码的执行流程

**修复验证：**
这个修复应该能完全解决末影人闪烁不生效的问题，因为它解决了根本原因：错误的技能分类和触发机制。

现在末影人的闪烁被动技能应该能正常工作了！🎯✨
