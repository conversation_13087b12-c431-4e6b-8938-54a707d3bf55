# 铁傀儡强击技能调整

## 修改内容

### ⚡ 强击被动技能调整

**修改前：**
- 受到攻击有概率会免疫该次伤害并且将伤害*0.4返回给攻击者
- 使用伪随机分布，大约20%触发概率，40%反伤

**修改后：**
- 受到攻击有**15%概率**会免疫该次伤害并且将伤害***1.0返回给攻击者**
- 触发概率降低，但反伤大幅提升

## 技术实现

### 🔧 代码变更

**原代码：**
```java
// 20%概率触发
if (pseudoRandom.checkTrigger(playerId, eventName, 0.2)) {
    double counterDamage = damage * 0.4; // 40%反伤
    attacker.damage(counterDamage);
    return true; // 免疫伤害
}
return false; // 不免疫伤害
```

**新代码：**
```java
// 15%概率触发
if (pseudoRandom.checkTrigger(playerId, eventName, 0.15)) {
    double counterDamage = damage * 1.0; // 100%反伤
    attacker.damage(counterDamage);
    return true; // 免疫伤害
}
return false; // 不免疫伤害
```

### 📋 主要变化

1. **降低触发概率：** 从20%降低到15%
2. **大幅提升反伤：** 从40%提升到100%
3. **平衡性调整：** 更低频但更高伤的反击

## 平衡性分析

### ⚖️ 平衡性影响

**增强方面：**
- ✅ **反击威力大幅提升：** 反伤从40%提升到100%
- ✅ **高风险高回报：** 触发时造成等额反伤
- ✅ **威慑效果增强：** 攻击者面临更大风险

**平衡考虑：**
- ✅ **触发概率降低：** 从20%降到15%，更难触发
- ✅ **风险与收益平衡：** 更低频但更高伤的反击
- ✅ **战术多样性：** 攻击者需要权衡风险

### 📊 效果对比

| 方面 | 修改前 | 修改后 |
|------|--------|--------|
| 触发概率 | ~20% | ~15% |
| 反伤比例 | 40% | 100% |
| 伤害免疫 | 偶尔 | 更少但仍有 |
| 反击威力 | 中等 | 极强 |
| 威慑效果 | 有限 | 显著增强 |

## 游戏体验影响

### 🛡️ 对铁傀儡玩家

**优势：**
- ✅ **极强生存能力：** 免疫所有物理伤害
- ✅ **反击威慑：** 攻击者会受到反伤
- ✅ **稳定表现：** 不再依赖运气

**注意事项：**
- ⚠️ **可能过于强势：** 需要观察游戏平衡
- ⚠️ **其他伤害源：** 仍会受到环境伤害（岩浆、跌落等）

### ⚔️ 对攻击者

**挑战：**
- ❌ **攻击无效：** 物理攻击无法造成伤害
- ❌ **必受反伤：** 每次攻击都会受到反击
- ❌ **战术调整：** 需要寻找其他对付方式

**应对策略：**
- 💡 **环境伤害：** 利用岩浆、跌落等环境因素
- 💡 **团队配合：** 多人协作分担反伤
- 💡 **避免近战：** 寻找远程或间接攻击方式

## 测试建议

### 🧪 功能测试

1. **基础测试：**
   ```bash
   /manhunt professiontest set IRON_GOLEM
   # 让其他玩家攻击，确认100%免疫和反伤
   ```

2. **反伤计算测试：**
   - 记录攻击伤害值
   - 确认反伤 = 攻击伤害 * 0.4
   - 验证反伤正确应用到攻击者

3. **环境伤害测试：**
   - 测试岩浆伤害是否仍然有效
   - 测试跌落伤害是否仍然有效
   - 确认只有玩家攻击被免疫

### ⚖️ 平衡性测试

1. **1v1对战测试：**
   - 铁傀儡 vs 其他职业
   - 观察战斗结果和持续时间
   - 评估是否过于强势

2. **团队战测试：**
   - 多人攻击铁傀儡的效果
   - 反伤分散的影响
   - 团队配合的重要性

3. **长期游戏测试：**
   - 观察游戏整体平衡
   - 收集玩家反馈
   - 必要时进行调整

## 可能的后续调整

### 🔧 如果过于强势

**选项1：降低反伤比例**
```java
double counterDamage = damage * 0.3; // 从0.4降到0.3
```

**选项2：添加冷却时间**
```java
// 每次触发后有短暂冷却（如2-3秒）
if (!isOnCooldown(playerId, "强击")) {
    // 执行强击效果
    setCooldown(playerId, "强击", 3);
}
```

**选项3：部分免疫**
```java
// 减少伤害而不是完全免疫
double reducedDamage = damage * 0.2; // 只受到20%伤害
return false; // 不完全免疫，但大幅减少伤害
```

### 🎯 如果仍需增强

**选项1：增加反伤比例**
```java
double counterDamage = damage * 0.5; // 从0.4提升到0.5
```

**选项2：添加额外效果**
```java
// 给攻击者添加负面效果
attacker.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 40, 0));
```

## 文件修改列表

1. **PassiveSkillHandler.java**
   - 修改 `handleIronGolemCounterAttack` 方法
   - 移除概率检查逻辑
   - 简化为100%触发

2. **Profession.java**
   - 更新铁傀儡被动技能描述
   - 移除"有概率"字样

3. **PROFESSION_SYSTEM_README.md**
   - 更新铁傀儡技能说明
   - 标注100%触发特性

## 总结

这次修改将铁傀儡的强击被动技能从概率触发改为100%触发，大幅增强了铁傀儡的防御能力。这是一个**重大的平衡性调整**，需要通过实际游戏测试来验证是否合适。

**预期效果：**
- ✅ 铁傀儡成为极强的防御型职业
- ✅ 攻击铁傀儡需要更多策略考虑
- ✅ 游戏战术多样性可能增加

**需要关注：**
- ⚠️ 是否会让铁傀儡过于强势
- ⚠️ 其他职业是否需要相应调整
- ⚠️ 整体游戏平衡是否受到影响

建议在实际游戏中测试一段时间，根据玩家反馈和游戏数据来评估是否需要进一步调整。🛡️⚡
