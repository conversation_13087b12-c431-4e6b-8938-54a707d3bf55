package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.kit.StartKit;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

/**
 * StartKit编辑器GUI
 * 允许房主直接编辑装备包内容
 */
public class StartKitEditorGui extends BaseGui {
    
    private final Room room;
    private final PlayerRole role;
    private StartKit editingKit;
    
    public StartKitEditorGui(UltimateManhurt plugin, Player player, Room room, PlayerRole role) {
        super(plugin, player, "编辑装备包 - " + getRoleDisplayName(role), 54); // 保持6行
        this.room = room;
        this.role = role;
        this.editingKit = new StartKit(getCurrentKit()); // 创建副本进行编辑
        setupGui();
    }
    
    private static String getRoleDisplayName(PlayerRole role) {
        return role == PlayerRole.SPEEDRUNNER ? "速通者" : "捕猎者";
    }
    
    private StartKit getCurrentKit() {
        return role == PlayerRole.SPEEDRUNNER ? 
            room.getSettings().getSpeedrunnerKit() : 
            room.getSettings().getHunterKit();
    }
    
    @Override
    protected void setupGui() {
        // 检查权限
        if (!room.isOwner(this.player.getUniqueId())) {
            setItem(22, createItem(Material.BARRIER, "<red>权限不足", 
                "<gray>只有房主可以编辑装备包"));
            return;
        }
        
        // 标题信息
        setupHeader();
        
        // 装备包编辑区域 (9x4 = 36个槽位，对应玩家背包)
        setupEditingArea();
        
        // 控制按钮
        setupControlButtons();
        
        // 装饰性边框
        setupBorder();
    }
    
    /**
     * 设置标题区域
     */
    private void setupHeader() {
        Material roleMaterial = role == PlayerRole.SPEEDRUNNER ? Material.IRON_SWORD : Material.BOW;
        String roleColor = role == PlayerRole.SPEEDRUNNER ? "<green>" : "<red>";
        
        setItem(4, createItem(roleMaterial, roleColor + "<bold>" + getRoleDisplayName(role) + "装备包编辑器",
            "<gray>装备包名称: <white>" + editingKit.getName(),
            "<gray>装备包描述: <white>" + editingKit.getDescription(),
            "",
            "<yellow>在下方区域放置物品来编辑装备包",
            "<yellow>物品将按照背包槽位顺序分发给玩家"));
    }
    
    /**
     * 设置编辑区域
     */
    private void setupEditingArea() {
        // 显示当前装备包内容
        // 槽位9-44对应玩家背包的0-35槽位
        for (int i = 0; i < 36; i++) {
            int guiSlot = i + 9; // GUI中的槽位
            ItemStack item = editingKit.getItem(i);

            if (item != null) {
                setItem(guiSlot, item.clone());
            } else {
                // 空槽位显示为空气，允许玩家放置物品
                setItem(guiSlot, null);
            }
        }

        // 显示额外物品 (槽位45-52，共8个槽位)
        setupExtraItemsArea();
    }

    /**
     * 设置额外物品区域
     */
    private void setupExtraItemsArea() {
        // 显示当前额外物品 (槽位45-52，共8个槽位)
        for (int i = 0; i < 8; i++) {
            int guiSlot = 45 + i; // GUI槽位45-52

            if (i < editingKit.getExtraItems().size()) {
                ItemStack extraItem = editingKit.getExtraItems().get(i);
                setItem(guiSlot, extraItem.clone());
            } else {
                // 空槽位显示提示
                setItem(guiSlot, createItem(Material.LIGHT_GRAY_STAINED_GLASS_PANE,
                    "<gray>额外物品槽位 " + (i + 1),
                    "<yellow>放置物品到此处添加额外物品",
                    "<gray>额外物品会在主要装备之后给予玩家"));
            }
        }
    }
    
    /**
     * 获取槽位类型名称
     */
    private String getSlotTypeName(int slot) {
        if (slot >= 0 && slot <= 8) {
            return "快捷栏";
        } else if (slot >= 9 && slot <= 35) {
            return "背包";
        } else {
            return "未知";
        }
    }
    
    /**
     * 设置控制按钮
     */
    private void setupControlButtons() {
        // 第一行控制按钮
        Material roleMaterial = role == PlayerRole.SPEEDRUNNER ? Material.IRON_SWORD : Material.BOW;
        String roleColor = role == PlayerRole.SPEEDRUNNER ? "<green>" : "<red>";

        // 标题
        setItem(0, createItem(roleMaterial, roleColor + "<bold>" + getRoleDisplayName(role) + "装备包编辑器",
            "<gray>装备包名称: <white>" + editingKit.getName(),
            "<gray>装备包描述: <white>" + editingKit.getDescription(),
            "<gray>额外物品数量: <white>" + editingKit.getExtraItems().size(),
            "",
            "<yellow>主要装备: 第2-5行 (对应玩家背包)",
            "<yellow>额外物品: 第6行 (额外给予玩家)"));

        // 保存装备包
        setItem(1, createItem(Material.EMERALD, "<green><bold>保存",
            "<gray>保存当前编辑的装备包",
            "<yellow>点击确认保存"));

        // 清空装备包
        setItem(2, createItem(Material.LAVA_BUCKET, "<red><bold>清空",
            "<gray>清空所有装备包内容",
            "<yellow>点击清空所有物品"));

        // 预览装备包
        setItem(3, createItem(Material.SPYGLASS, "<blue><bold>预览",
            "<gray>预览装备包内容和摘要",
            "<yellow>点击查看详细信息"));

        // 从模板加载
        setItem(4, createItem(Material.BOOKSHELF, "<color:#8d5caa><bold>加载模板",
            "<gray>从预设模板加载装备包",
            "<yellow>点击选择模板"));

        // 保存为模板
        setItem(5, createItem(Material.WRITABLE_BOOK, "<color:#8d5caa><bold>保存为模板",
            "<gray>将当前装备包保存为自定义模板",
            "<yellow>点击保存为模板"));

        // 取消编辑
        setItem(7, createItem(Material.BARRIER, "<red>取消",
            "<gray>取消编辑，不保存更改"));

        // 返回装备包管理
        setItem(53, createItem(Material.ARROW, "<yellow>返回",
            "<gray>返回装备包管理界面"));
    }
    
    /**
     * 设置边框
     */
    private void setupBorder() {
        // 顶部边框
        for (int i = 0; i < 9; i++) {
            if (i != 4 && inventory.getItem(i) == null) {
                setItem(i, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));
            }
        }
        
        // 底部边框
        for (int i = 45; i < 54; i++) {
            if (inventory.getItem(i) == null) {
                setItem(i, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));
            }
        }
    }
    
    @Override
    public void handleClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        int slot = event.getSlot();
        
        // 主要装备编辑区域 (槽位9-44) 允许玩家操作
        if (slot >= 9 && slot <= 44) {
            handleEditingAreaClick(event);
            return;
        }

        // 额外物品编辑区域 (槽位45-52) 允许玩家操作
        if (slot >= 45 && slot <= 52) {
            handleExtraItemsAreaClick(event);
            return;
        }

        // 其他区域取消事件
        event.setCancelled(true);

        if (event.getClickedInventory() != this.inventory) {
            return;
        }

        ItemStack item = event.getCurrentItem();
        if (item == null || item.getType() == Material.AIR) {
            return;
        }

        this.playClickSound();

        switch (slot) {
            case 1: // 保存装备包
                handleSave();
                break;
            case 2: // 清空装备包
                handleClear();
                break;
            case 3: // 预览装备包
                handlePreview();
                break;
            case 4: // 加载模板
                handleLoadTemplate();
                break;
            case 5: // 保存为模板
                handleSaveAsTemplate();
                break;
            case 7: // 取消
                handleCancel();
                break;
            case 53: // 返回
                handleBack();
                break;
        }
    }
    
    /**
     * 处理编辑区域点击
     */
    private void handleEditingAreaClick(InventoryClickEvent event) {
        // 允许玩家在编辑区域自由操作物品
        // 不取消事件，让玩家可以放置、移动、删除物品

        // 延迟更新装备包内容，确保物品操作完成
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            updateKitFromInventory();
        }, 1L);
    }

    /**
     * 处理额外物品区域点击
     */
    private void handleExtraItemsAreaClick(InventoryClickEvent event) {
        // 允许玩家在额外物品区域自由操作物品
        // 不取消事件，让玩家可以放置、移动、删除物品

        // 延迟更新装备包内容，确保物品操作完成
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            updateKitFromInventory();
            // 刷新额外物品区域显示
            setupExtraItemsArea();
        }, 1L);
    }
    
    /**
     * 从GUI库存更新装备包内容
     */
    private void updateKitFromInventory() {
        editingKit.clear();

        // 更新主要物品 (槽位9-44对应背包0-35)
        for (int i = 0; i < 36; i++) {
            int guiSlot = i + 9;
            ItemStack item = inventory.getItem(guiSlot);

            if (item != null && item.getType() != Material.AIR &&
                item.getType() != Material.LIGHT_GRAY_STAINED_GLASS_PANE) {
                editingKit.setItem(i, item);
            }
        }

        // 更新额外物品 (槽位45-52)
        editingKit.getExtraItems().clear();
        for (int i = 0; i < 8; i++) {
            int guiSlot = 45 + i;
            ItemStack item = inventory.getItem(guiSlot);

            if (item != null && item.getType() != Material.AIR &&
                item.getType() != Material.LIGHT_GRAY_STAINED_GLASS_PANE) {
                editingKit.addExtraItem(item);
            }
        }

        // 刷新标题显示
        Material roleMaterial = role == PlayerRole.SPEEDRUNNER ? Material.IRON_SWORD : Material.BOW;
        String roleColor = role == PlayerRole.SPEEDRUNNER ? "<green>" : "<red>";
        setItem(0, createItem(roleMaterial, roleColor + "<bold>" + getRoleDisplayName(role) + "装备包编辑器",
            "<gray>装备包名称: <white>" + editingKit.getName(),
            "<gray>装备包描述: <white>" + editingKit.getDescription(),
            "<gray>额外物品数量: <white>" + editingKit.getExtraItems().size(),
            "",
            "<yellow>主要装备: 第2-5行 (对应玩家背包)",
            "<yellow>额外物品: 第6行 (额外给予玩家)"));
    }
    
    /**
     * 保存装备包
     */
    private void handleSave() {
        updateKitFromInventory();
        
        // 保存到房间设置
        if (role == PlayerRole.SPEEDRUNNER) {
            room.getSettings().setSpeedrunnerKit(editingKit);
        } else {
            room.getSettings().setHunterKit(editingKit);
        }
        
        sendSuccess("装备包已保存！");
        playSuccessSound();
        
        // 返回装备包管理界面
        close();
        plugin.getGuiManager().openStartKitGui(player, room);
    }
    
    /**
     * 清空装备包
     */
    private void handleClear() {
        // 清空编辑区域
        for (int i = 9; i <= 44; i++) {
            setItem(i, null);
        }
        
        editingKit.clear();
        setupEditingArea(); // 重新设置空槽位提示
        
        sendInfo("装备包已清空");
    }
    
    /**
     * 预览装备包
     */
    private void handlePreview() {
        updateKitFromInventory();
        plugin.getStartKitManager().previewKit(player, editingKit);
    }
    
    /**
     * 加载模板
     */
    private void handleLoadTemplate() {
        close();
        plugin.getGuiManager().openStartKitTemplateGui(player, room, role);
    }

    /**
     * 保存为模板
     */
    private void handleSaveAsTemplate() {
        updateKitFromInventory();

        // 这里可以打开一个输入GUI让用户输入模板名称
        // 暂时使用默认名称
        String templateName = getRoleDisplayName(role) + "自定义模板_" + System.currentTimeMillis();

        boolean success = plugin.getCustomTemplateManager().saveCustomTemplate(player, editingKit, templateName);
        if (success) {
            sendSuccess("模板保存成功！");
            playSuccessSound();
        } else {
            sendError("模板保存失败！");
            playErrorSound();
        }
    }

    /**
     * 取消编辑
     */
    private void handleCancel() {
        sendInfo("已取消编辑");
        close();
        plugin.getGuiManager().openStartKitGui(player, room);
    }
    
    /**
     * 返回装备包管理
     */
    private void handleBack() {
        close();
        plugin.getGuiManager().openStartKitGui(player, room);
    }
}
