# 职业系统修复说明

## 修复的问题

### 1. 末影人被动技能不生效 ✅

**问题原因：**
- 事件监听器的优先级不够高，可能被其他插件拦截
- 伤害事件处理顺序问题

**修复方案：**
- 将 `ProfessionListener` 中的 `onPlayerDamage` 事件优先级从 `EventPriority.HIGH` 改为 `EventPriority.HIGHEST`
- 确保末影人闪烁技能在其他事件处理之前执行

**修复文件：**
- `src/main/java/com/projectSource/ultimateManhurt/profession/listener/ProfessionListener.java`

### 2. 职业显示到Tablist和计分板 ✅

**新增功能：**

#### Tablist显示
- 在玩家名称前显示职业emoji图标
- 使用职业对应的颜色
- 可通过配置文件控制显示开关：`tablist.player-display.show-profession`

**显示格式：**
```
[延迟] [角色] [职业] 玩家名称 [血量]
例如：🟢 ⚔ 👤 Alice ❤❤❤❤❤
```

#### 计分板显示
- 在角色信息下方显示职业信息
- 显示职业名称和emoji
- 如果未选择职业则显示"未选择"

**显示格式：**
```
角色: ⚔ 速通者
职业: 👤 末影人
```

**修复文件：**
- `src/main/java/com/projectSource/ultimateManhurt/tablist/TablistUtil.java`
- `src/main/java/com/projectSource/ultimateManhurt/scoreboard/GameScoreboard.java`

## 技术细节

### 事件优先级调整
```java
@EventHandler(priority = EventPriority.HIGHEST)
public void onPlayerDamage(EntityDamageByEntityEvent event) {
    // 末影人闪烁等被动技能处理
}
```

### Tablist职业指示器
```java
private static Component buildProfessionIndicator(Player player, UltimateManhurt plugin) {
    Profession profession = plugin.getProfessionManager().getPlayerProfession(player.getUniqueId());
    if (profession == null) {
        return Component.empty();
    }
    return Component.text(profession.getEmoji(), profession.getColor());
}
```

### 计分板职业信息
```java
if (gameSession.getRoom().getSettings().isProfessionSystemEnabled()) {
    Profession profession = plugin.getProfessionManager().getPlayerProfession(player.getUniqueId());
    if (profession != null) {
        Component professionComponent = Component.text()
                .append(ComponentUtil.parse("<gray>职业: "))
                .append(profession.getDisplayComponent())
                .build();
        lines.add(toLegacy(professionComponent));
    } else {
        lines.add(toLegacy(ComponentUtil.parse("<gray>职业: <red>未选择")));
    }
}
```

## 配置选项

### Tablist配置
在配置文件中添加以下选项来控制职业显示：

```yaml
tablist:
  player-display:
    show-profession: true  # 是否显示职业图标
```

## 测试方法

### 测试末影人被动技能
1. 使用 `/manhunt professiontest set ENDERMAN` 设置末影人职业
2. 让其他玩家攻击你
3. 观察是否会自动传送到安全位置
4. 检查45秒冷却时间是否正常

### 测试Tablist显示
1. 启用职业系统并选择职业
2. 按Tab键查看玩家列表
3. 确认玩家名称前显示职业emoji

### 测试计分板显示
1. 在游戏中查看右侧计分板
2. 确认在角色信息下方显示职业信息
3. 测试未选择职业时的显示

## 职业Emoji对照表

| 职业 | Emoji | 颜色 |
|------|-------|------|
| 末影人 | 👤 | 深紫色 |
| 屠夫 | 🔪 | 红色 |
| 骷髅 | 💀 | 灰色 |
| 探险家 | ⛏️ | 蓝色 |
| 铁傀儡 | 🤖 | 白色 |
| 猎人 | 🏹 | 绿色 |

## 注意事项

1. **职业显示仅在启用职业系统时生效**
2. **观察者不会显示职业信息**
3. **职业选择完成后立即更新显示**
4. **支持实时更新，无需重启服务器**

## 兼容性

- ✅ 兼容现有的Tablist和计分板系统
- ✅ 不影响其他插件的显示
- ✅ 支持动态开关职业系统
- ✅ 向后兼容未启用职业系统的房间
