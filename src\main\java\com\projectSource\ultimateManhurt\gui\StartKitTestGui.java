package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.kit.StartKit;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 装备包测试GUI
 * 提供装备包的详细预览和统计信息
 */
public class StartKitTestGui extends BaseGui {
    
    private final Room room;
    private PlayerRole currentRole = PlayerRole.SPEEDRUNNER; // 当前查看的角色
    
    public StartKitTestGui(UltimateManhurt plugin, Player player, Room room) {
        super(plugin, player, "<gold><bold>装备包测试", 54);
        this.room = room;
        setupGui();
    }
    
    @Override
    protected void setupGui() {
        // 检查权限
        if (!room.isOwner(player.getUniqueId())) {
            setItem(22, createItem(Material.BARRIER, "<red>权限不足", 
                "<gray>只有房主可以管理装备包"));
            return;
        }
        
        // 角色切换按钮
        setupRoleSelector();
        
        // 装备包信息
        setupKitInfo();
        
        // 装备包统计
        setupKitStats();
        
        // 控制按钮
        setupControlButtons();
        
        // 填充边框
        fillBorder(Material.GRAY_STAINED_GLASS_PANE);
    }
    
    /**
     * 设置角色选择器
     */
    private void setupRoleSelector() {
        // 速通者选择
        Material speedrunnerMaterial = currentRole == PlayerRole.SPEEDRUNNER ? Material.DIAMOND_SWORD : Material.STONE_SWORD;
        setItem(10, createItem(speedrunnerMaterial, 
            currentRole == PlayerRole.SPEEDRUNNER ? "<green><bold>速通者 (当前)" : "<gray>速通者",
            "<gray>查看速通者装备包",
            currentRole == PlayerRole.SPEEDRUNNER ? "<green>当前正在查看" : "<yellow>点击切换"));
        
        // 捕猎者选择
        Material hunterMaterial = currentRole == PlayerRole.HUNTER ? Material.BOW : Material.STICK;
        setItem(16, createItem(hunterMaterial, 
            currentRole == PlayerRole.HUNTER ? "<red><bold>捕猎者 (当前)" : "<gray>捕猎者",
            "<gray>查看捕猎者装备包",
            currentRole == PlayerRole.HUNTER ? "<red>当前正在查看" : "<yellow>点击切换"));
    }
    
    /**
     * 设置装备包信息
     */
    private void setupKitInfo() {
        StartKit kit = getCurrentKit();
        
        if (kit == null) {
            setItem(13, createItem(Material.BARRIER, "<red>装备包不存在",
                "<gray>当前角色没有配置装备包"));
            return;
        }
        
        // 装备包基本信息
        setItem(4, createItem(Material.CHEST, "<gold><bold>" + kit.getName(),
            "<gray>描述: <white>" + kit.getDescription(),
            "<gray>物品数量: <white>" + kit.getItems().size(),
            "<gray>角色: <white>" + (currentRole == PlayerRole.SPEEDRUNNER ? "速通者" : "捕猎者")));
        
        // 装备包预览按钮
        setItem(13, createItem(Material.ENDER_EYE, "<blue><bold>完整预览",
            "<gray>在背包中预览装备包内容",
            "<gray>这不会影响你的当前物品",
            "",
            "<yellow>点击打开完整预览"));
    }
    
    /**
     * 设置装备包统计
     */
    private void setupKitStats() {
        StartKit kit = getCurrentKit();
        
        if (kit == null) {
            return;
        }
        
        // 统计物品类型
        Map<Material, Integer> itemCounts = new HashMap<>();
        Map<String, Integer> categoryStats = new HashMap<>();

        for (ItemStack item : kit.getItems().values()) {
            if (item != null && item.getType() != Material.AIR) {
                itemCounts.merge(item.getType(), item.getAmount(), Integer::sum);

                // 分类统计
                String category = getItemCategory(item.getType());
                categoryStats.merge(category, item.getAmount(), Integer::sum);
            }
        }
        
        // 显示统计信息
        List<String> statsLore = new ArrayList<>();
        statsLore.add("<gray>装备包统计信息:");
        statsLore.add("");
        
        for (Map.Entry<String, Integer> entry : categoryStats.entrySet()) {
            statsLore.add("<gray>• " + entry.getKey() + ": <white>" + entry.getValue() + " 个");
        }
        
        statsLore.add("");
        statsLore.add("<gray>总物品数: <white>" + kit.getItems().size());
        statsLore.add("<gray>不同类型: <white>" + itemCounts.size());
        
        setItem(22, createItem(Material.BOOK, "<yellow><bold>装备包统计",
            statsLore.toArray(new String[0])));
        
        // 显示主要物品
        setupMainItems(itemCounts);
    }
    
    /**
     * 显示主要物品
     */
    private void setupMainItems(Map<Material, Integer> itemCounts) {
        List<Map.Entry<Material, Integer>> sortedItems = new ArrayList<>(itemCounts.entrySet());
        sortedItems.sort((a, b) -> b.getValue().compareTo(a.getValue()));
        
        int[] slots = {28, 29, 30, 31, 32, 33, 34}; // 显示前7个主要物品
        
        for (int i = 0; i < Math.min(slots.length, sortedItems.size()); i++) {
            Map.Entry<Material, Integer> entry = sortedItems.get(i);
            Material material = entry.getKey();
            int count = entry.getValue();
            
            setItem(slots[i], createItem(material, "<white>" + getItemDisplayName(material),
                "<gray>数量: <white>" + count,
                "<gray>类型: <white>" + getItemCategory(material)));
        }
    }
    
    /**
     * 设置控制按钮
     */
    private void setupControlButtons() {
        // 给予装备包
        setItem(45, createItem(Material.EMERALD, "<green><bold>给予装备包",
            "<gray>将装备包物品给予到你的背包",
            "<gray>用于测试装备包内容",
            "",
            "<yellow>点击获得装备包物品",
            "<red>注意: 请确保背包有足够空间"));
        
        // 清空背包
        setItem(46, createItem(Material.LAVA_BUCKET, "<red><bold>清空背包",
            "<gray>清空你的背包和装备栏",
            "<gray>用于测试前的准备",
            "",
            "<yellow>点击清空背包",
            "<red>警告: 这将删除你的所有物品"));
        
        // 比较装备包
        setItem(47, createItem(Material.COMPARATOR, "<blue><bold>比较装备包",
            "<gray>比较速通者和捕猎者装备包",
            "<gray>查看两者的差异",
            "",
            "<yellow>点击查看比较结果"));
        
        // 导出装备包
        setItem(48, createItem(Material.PAPER, "<purple><bold>导出信息",
            "<gray>在聊天中显示装备包详细信息",
            "<gray>便于分享和记录",
            "",
            "<yellow>点击导出到聊天"));
        
        // 返回按钮
        setItem(53, createItem(Material.ARROW, "<red><bold>返回",
            "<gray>返回装备包管理",
            "<yellow>点击返回"));
    }
    
    @Override
    public void handleClick(InventoryClickEvent event) {
        event.setCancelled(true);
        
        if (room == null) {
            return;
        }
        
        // 检查权限
        if (!room.isOwner(player.getUniqueId())) {
            sendError("只有房主可以管理装备包！");
            playErrorSound();
            return;
        }
        
        int slot = event.getSlot();
        playClickSound();
        
        switch (slot) {
            case 10: // 切换到速通者
                if (currentRole != PlayerRole.SPEEDRUNNER) {
                    currentRole = PlayerRole.SPEEDRUNNER;
                    refresh();
                }
                break;
            case 16: // 切换到捕猎者
                if (currentRole != PlayerRole.HUNTER) {
                    currentRole = PlayerRole.HUNTER;
                    refresh();
                }
                break;
            case 13: // 完整预览
                handleFullPreview();
                break;
            case 45: // 给予装备包
                handleGiveKit();
                break;
            case 46: // 清空背包
                handleClearInventory();
                break;
            case 47: // 比较装备包
                handleCompareKits();
                break;
            case 48: // 导出信息
                handleExportInfo();
                break;
            case 53: // 返回
                handleBack();
                break;
        }
    }
    
    /**
     * 获取当前装备包
     */
    private StartKit getCurrentKit() {
        return currentRole == PlayerRole.SPEEDRUNNER ? 
            room.getSettings().getSpeedrunnerKit() : 
            room.getSettings().getHunterKit();
    }
    
    /**
     * 完整预览
     */
    private void handleFullPreview() {
        StartKit kit = getCurrentKit();
        if (kit != null) {
            plugin.getStartKitManager().previewKit(player, kit);
            sendSuccess("已打开装备包完整预览");
        } else {
            sendError("装备包不存在");
        }
    }
    
    /**
     * 给予装备包
     */
    private void handleGiveKit() {
        StartKit kit = getCurrentKit();
        if (kit == null) {
            sendError("装备包不存在");
            return;
        }

        try {
            // 直接将装备包物品添加到玩家背包
            for (ItemStack item : kit.getItems().values()) {
                if (item != null && item.getType() != Material.AIR) {
                    player.getInventory().addItem(item.clone());
                }
            }
            sendSuccess("已将装备包物品给予到你的背包");
            playSuccessSound();
        } catch (Exception e) {
            sendError("给予装备包时发生错误");
            playErrorSound();
        }
    }
    
    /**
     * 清空背包
     */
    private void handleClearInventory() {
        player.getInventory().clear();
        player.getInventory().setArmorContents(new ItemStack[4]);
        sendSuccess("已清空你的背包和装备栏");
        playSuccessSound();
    }
    
    /**
     * 比较装备包
     */
    private void handleCompareKits() {
        StartKit speedrunnerKit = room.getSettings().getSpeedrunnerKit();
        StartKit hunterKit = room.getSettings().getHunterKit();
        
        if (speedrunnerKit == null || hunterKit == null) {
            sendError("需要两个装备包都存在才能比较");
            return;
        }
        
        sendSuccess("装备包比较:");
        sendSuccess("速通者: " + speedrunnerKit.getItems().size() + " 物品");
        sendSuccess("捕猎者: " + hunterKit.getItems().size() + " 物品");
        sendSuccess("差异: " + Math.abs(speedrunnerKit.getItems().size() - hunterKit.getItems().size()) + " 物品");
    }
    
    /**
     * 导出信息
     */
    private void handleExportInfo() {
        StartKit kit = getCurrentKit();
        if (kit == null) {
            sendError("装备包不存在");
            return;
        }
        
        String roleText = currentRole == PlayerRole.SPEEDRUNNER ? "速通者" : "捕猎者";
        sendSuccess("=== " + roleText + "装备包信息 ===");
        sendSuccess("名称: " + kit.getName());
        sendSuccess("描述: " + kit.getDescription());
        sendSuccess("物品数量: " + kit.getItems().size());
        sendSuccess("========================");
    }
    
    /**
     * 返回装备包管理
     */
    private void handleBack() {
        close();
        plugin.getGuiManager().openStartKitGui(player, room);
    }
    
    // 工具方法
    private String getItemCategory(Material material) {
        String name = material.name();
        if (name.contains("SWORD") || name.contains("AXE") || name.contains("BOW") || name.contains("CROSSBOW")) {
            return "武器";
        } else if (name.contains("HELMET") || name.contains("CHESTPLATE") || name.contains("LEGGINGS") || name.contains("BOOTS")) {
            return "护甲";
        } else if (name.contains("PICKAXE") || name.contains("SHOVEL") || name.contains("HOE")) {
            return "工具";
        } else if (name.contains("FOOD") || name.contains("BREAD") || name.contains("MEAT") || name.contains("APPLE")) {
            return "食物";
        } else if (name.contains("POTION") || name.contains("SPLASH")) {
            return "药水";
        } else {
            return "其他";
        }
    }
    
    private String getItemDisplayName(Material material) {
        return material.name().toLowerCase().replace("_", " ");
    }
    
    protected void sendSuccess(String message) {
        ComponentUtil.sendMessage(player, ComponentUtil.success(message));
    }

    protected void sendError(String message) {
        ComponentUtil.sendMessage(player, ComponentUtil.error(message));
    }

    protected void playClickSound() {
        player.playSound(player.getLocation(), org.bukkit.Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
    }

    protected void playErrorSound() {
        player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
    }

    protected void playSuccessSound() {
        player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.0f);
    }
}
