package com.projectSource.ultimateManhurt.scoreboard;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.game.GameState;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.util.ColorUtil;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import com.projectSource.ultimateManhurt.util.TimeUtil;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextColor;
import net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scoreboard.*;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 游戏计分板
 * 类Hypixel风格的动态计分板
 */
public class GameScoreboard {
    
    private final UltimateManhurt plugin;
    private final Player player;
    private final GameSession gameSession;
    private final Scoreboard scoreboard;
    private final Objective objective;
    
    // 动画相关
    private int animationTick = 0;
    private final String[] titleFrames;
    
    public GameScoreboard(UltimateManhurt plugin, Player player, GameSession gameSession) {
        this.plugin = plugin;
        this.player = player;
        this.gameSession = gameSession;
        
        // 创建计分板
        org.bukkit.scoreboard.ScoreboardManager manager = Bukkit.getScoreboardManager();
        if (manager == null) {
            throw new IllegalStateException("ScoreboardManager is not available");
        }
        this.scoreboard = manager.getNewScoreboard();
        this.objective = scoreboard.registerNewObjective("manhunt", Criteria.DUMMY, Component.text(""));
        this.objective.setDisplaySlot(DisplaySlot.SIDEBAR);
        
        // 初始化标题动画帧
        this.titleFrames = createTitleFrames();
        
        updateContent();
    }
    
    /**
     * 创建标题动画帧
     */
    private String[] createTitleFrames() {
        String baseTitle = "ULTIMATE MANHUNT";
        List<String> frames = new ArrayList<>();

        // 简化的渐变色动画
        TextColor[] colors = {
            NamedTextColor.RED,
            NamedTextColor.GOLD,
            NamedTextColor.YELLOW,
            NamedTextColor.GREEN,
            NamedTextColor.AQUA,
            NamedTextColor.BLUE,
            NamedTextColor.LIGHT_PURPLE
        };

        for (int i = 0; i < colors.length; i++) {
            TextColor primaryColor = colors[i];
            TextColor secondaryColor = colors[(i + 1) % colors.length];

            // 创建简单的双色渐变标题（左右添加空格避免拥挤）
            String frame = "<gradient:" + ColorUtil.toHex(primaryColor) + ":" +
                          ColorUtil.toHex(secondaryColor) + "><bold> " + baseTitle + " </bold></gradient>";
            frames.add(frame);
        }

        return frames.toArray(new String[0]);
    }
    
    /**
     * 显示计分板
     */
    public void show() {
        player.setScoreboard(scoreboard);
    }
    
    /**
     * 隐藏计分板
     */
    public void hide() {
        org.bukkit.scoreboard.ScoreboardManager manager = Bukkit.getScoreboardManager();
        if (manager != null) {
            player.setScoreboard(manager.getMainScoreboard());
        }
    }
    
    /**
     * 更新计分板
     */
    public void update() {
        updateTitle();
        updateContent();
    }
    
    /**
     * 更新标题
     */
    private void updateTitle() {
        animationTick = (animationTick + 1) % titleFrames.length;
        String titleFrame = titleFrames[animationTick];
        Component titleComponent = ComponentUtil.parse(titleFrame);
        objective.displayName(titleComponent);
    }
    
    /**
     * 更新内容
     */
    public void updateContent() {
        // 清除现有分数
        for (String entry : new ArrayList<>(scoreboard.getEntries())) {
            scoreboard.resetScores(entry);
        }

        List<String> lines = createScoreboardLines();

        // 限制行数以避免计分板过长
        if (lines.size() > 15) {
            lines = lines.subList(0, 15);
        }

        // 设置分数（从下往上）
        for (int i = 0; i < lines.size(); i++) {
            String line = lines.get(i);
            if (line != null && !line.isEmpty()) {
                // 确保行长度不超过限制
                line = ensureLength(line, 40);
                Score score = objective.getScore(line);
                score.setScore(lines.size() - i);
            }
        }
    }
    
    /**
     * 创建计分板行
     */
    private List<String> createScoreboardLines() {
        List<String> lines = new ArrayList<>();
        int lineIndex = 0;

        // 时间信息（在标题下方）
        GameState state = gameSession.getState();
        if (state == GameState.RUNNING || state == GameState.PAUSED) {
            long remaining = gameSession.getTimer().getRemainingSeconds();
            Component timeComponent = TimeUtil.createCountdownComponent(remaining);
            lines.add(toLegacy(timeComponent));
        } else {
            lines.add(toLegacy(ComponentUtil.parse("<gray>等待开始...")));
        }

        // 空行
        lines.add(createUniqueEntry("", lineIndex++));

        // 房间ID（缩写格式）
        String roomId = gameSession.getRoom().getId();
        String shortRoomId = roomId.length() > 8 ? roomId.substring(0, 8) : roomId;
        lines.add(toLegacy(ComponentUtil.parse("<gray>房间: <white>" + shortRoomId)));

        // 游戏状态
        lines.add(toLegacy(state.getDisplayComponent()));

        // 空行
        lines.add(createUniqueEntry("", lineIndex++));

        // 玩家角色信息
        PlayerRole playerRole = gameSession.getPlayerRole(player.getUniqueId());
        if (playerRole != null) {
            Component roleComponent = Component.text()
                    .append(ComponentUtil.parse("<gray>角色: "))
                    .append(playerRole.getDisplayComponent())
                    .build();
            lines.add(toLegacy(roleComponent));
        }

        // 玩家职业信息（如果启用职业系统）
        if (gameSession.getRoom().getSettings().isProfessionSystemEnabled()) {
            com.projectSource.ultimateManhurt.profession.Profession profession =
                plugin.getProfessionManager().getPlayerProfession(player.getUniqueId());

            if (profession != null) {
                Component professionComponent = Component.text()
                        .append(ComponentUtil.parse("<gray>职业: "))
                        .append(profession.getDisplayComponent())
                        .build();
                lines.add(toLegacy(professionComponent));
            } else {
                lines.add(toLegacy(ComponentUtil.parse("<gray>职业: <red>未选择")));
            }
        }

        // 空行
        lines.add(createUniqueEntry("", lineIndex++));

        // 玩家统计
        addPlayerStats(lines);

        // 空行
        lines.add(createUniqueEntry("", lineIndex++));

        // 服务器IP（在最底部）
        String serverIp = plugin.getConfigManager().getString("scoreboard.server-ip", "mc.example.com");
        lines.add(toLegacy(ComponentUtil.parse("<yellow>" + serverIp)));

        return lines;
    }
    
    /**
     * 添加玩家统计信息
     */
    private void addPlayerStats(List<String> lines) {
        // 速通者数量
        int speedrunners = gameSession.getPlayersByRole(PlayerRole.SPEEDRUNNER).size();
        int aliveSpeedrunners = (int) gameSession.getPlayersByRole(PlayerRole.SPEEDRUNNER).stream()
                .filter(gameSession::isPlayerAlive)
                .count();

        if (speedrunners > 0) {
            // 计算剩余生命数
            int speedrunnerLives = gameSession.getRoom().getSettings().getSpeedrunnerLives();
            String livesText;

            if (speedrunnerLives == 0) {
                livesText = "∞";
            } else {
                // 计算所有速通者的剩余生命数
                int minRemainingLives = speedrunnerLives;
                int maxRemainingLives = 0;

                for (UUID speedrunnerId : gameSession.getPlayersByRole(PlayerRole.SPEEDRUNNER)) {
                    int deaths = gameSession.getPlayerDeaths(speedrunnerId);
                    int remainingLives = Math.max(0, speedrunnerLives - deaths);
                    minRemainingLives = Math.min(minRemainingLives, remainingLives);
                    maxRemainingLives = Math.max(maxRemainingLives, remainingLives);
                }

                // 如果所有人剩余生命数相同，显示单个数字；否则显示范围
                if (minRemainingLives == maxRemainingLives) {
                    livesText = String.valueOf(minRemainingLives);
                } else {
                    livesText = minRemainingLives + "-" + maxRemainingLives;
                }
            }

            lines.add(toLegacy(ComponentUtil.parse("<green>速通者: <white>" + aliveSpeedrunners + "/" + speedrunners + " <gray>(" + livesText + "命)")));
        }

        // 捕猎者数量
        int hunters = gameSession.getPlayersByRole(PlayerRole.HUNTER).size();
        int aliveHunters = (int) gameSession.getPlayersByRole(PlayerRole.HUNTER).stream()
                .filter(gameSession::isPlayerAlive)
                .count();

        if (hunters > 0) {
            // 计算捕猎者剩余生命数
            int hunterLives = gameSession.getRoom().getSettings().getHunterLives();
            String hunterLivesText;

            if (hunterLives == 0) {
                hunterLivesText = "∞";
            } else {
                // 计算所有捕猎者的剩余生命数
                int minRemainingLives = hunterLives;
                int maxRemainingLives = 0;

                for (UUID hunterId : gameSession.getPlayersByRole(PlayerRole.HUNTER)) {
                    int deaths = gameSession.getPlayerDeaths(hunterId);
                    int remainingLives = Math.max(0, hunterLives - deaths);
                    minRemainingLives = Math.min(minRemainingLives, remainingLives);
                    maxRemainingLives = Math.max(maxRemainingLives, remainingLives);
                }

                // 如果所有人剩余生命数相同，显示单个数字；否则显示范围
                if (minRemainingLives == maxRemainingLives) {
                    hunterLivesText = String.valueOf(minRemainingLives);
                } else {
                    hunterLivesText = minRemainingLives + "-" + maxRemainingLives;
                }
            }

            lines.add(toLegacy(ComponentUtil.parse("<red>捕猎者: <white>" + aliveHunters + "/" + hunters + " <gray>(" + hunterLivesText + "命)")));
        }

        // 观察者数量
        int spectators = gameSession.getPlayersByRole(PlayerRole.SPECTATOR).size();
        if (spectators > 0) {
            lines.add(toLegacy(ComponentUtil.parse("<gray>观察者: <white>" + spectators)));
        }

        // 如果没有任何玩家，显示等待玩家
        if (speedrunners == 0 && hunters == 0) {
            lines.add(toLegacy(ComponentUtil.parse("<gray>等待玩家加入...")));
        }

        // 守卫模式特殊显示
        if (gameSession.getRoom().getSettings().getVictoryMode() == com.projectSource.ultimateManhurt.game.VictoryMode.GUARD_MODE) {
            addGuardModeStats(lines);
        }

        // 个人统计（只在游戏进行中显示）
        if (gameSession.getState() == GameState.RUNNING) {
            addPersonalStats(lines);
        }
    }

    /**
     * 添加守卫模式特殊统计
     */
    private void addGuardModeStats(List<String> lines) {
        if (gameSession.getState() != GameState.RUNNING) {
            return;
        }

        // 获取守卫模式管理器
        com.projectSource.ultimateManhurt.game.guard.GuardModeManager guardManager = gameSession.getGuardModeManager();
        if (guardManager == null) {
            return;
        }

        // 空行
        lines.add("");

        // 守卫凋零状态
        org.bukkit.entity.Wither guardWither = guardManager.getGuardWither();
        if (guardWither != null && !guardWither.isDead()) {
            double currentHealth = guardWither.getHealth();
            double maxHealth = guardWither.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
            int healthPercent = (int) ((currentHealth / maxHealth) * 100);

            // 创建血量条
            String healthBar = createHealthBar(currentHealth, maxHealth, 10);

            lines.add(toLegacy(ComponentUtil.parse("<dark_red><bold>守卫凋零</bold>")));
            lines.add(toLegacy(ComponentUtil.parse("<red>血量: " + healthBar + " <white>" + (int)currentHealth + "/" + (int)maxHealth)));
            lines.add(toLegacy(ComponentUtil.parse("<red>状态: <white>" + (healthPercent > 75 ? "健康" : healthPercent > 50 ? "受伤" : healthPercent > 25 ? "危险" : "濒死"))));
        } else {
            lines.add(toLegacy(ComponentUtil.parse("<dark_red><bold>守卫凋零</bold>")));
            lines.add(toLegacy(ComponentUtil.parse("<gray>已死亡")));
        }

        // 护盾状态
        if (guardManager.isShieldActive()) {
            long shieldRemaining = guardManager.getShieldRemainingTime();
            String shieldTime = formatTime(shieldRemaining);
            lines.add(toLegacy(ComponentUtil.parse("<blue>护盾: <white>激活 (" + shieldTime + ")")));
        } else {
            lines.add(toLegacy(ComponentUtil.parse("<gray>护盾: 未激活")));
        }
    }

    /**
     * 创建血量条
     */
    private String createHealthBar(double current, double max, int length) {
        double ratio = current / max;
        int filled = (int) (ratio * length);

        StringBuilder bar = new StringBuilder();
        for (int i = 0; i < length; i++) {
            if (i < filled) {
                if (ratio > 0.6) {
                    bar.append("<green>█");
                } else if (ratio > 0.3) {
                    bar.append("<yellow>█");
                } else {
                    bar.append("<red>█");
                }
            } else {
                bar.append("<dark_gray>█");
            }
        }

        return bar.toString();
    }

    /**
     * 格式化时间
     */
    private String formatTime(long seconds) {
        if (seconds >= 60) {
            long minutes = seconds / 60;
            long remainingSeconds = seconds % 60;
            return minutes + ":" + String.format("%02d", remainingSeconds);
        } else {
            return seconds + "s";
        }
    }

    /**
     * 添加个人统计信息
     */
    private void addPersonalStats(List<String> lines) {
        UUID playerId = player.getUniqueId();
        PlayerRole playerRole = gameSession.getPlayerRole(playerId);

        if (playerRole == null || playerRole == PlayerRole.SPECTATOR) {
            return;
        }

        // 添加分隔线
        lines.add(createUniqueEntry("", lines.size()));

        // 击杀数
        int kills = gameSession.getPlayerKills().getOrDefault(playerId, 0);
        lines.add(toLegacy(ComponentUtil.parse("<gold>击杀: <white>" + kills)));

        // 死亡数
        int deaths = gameSession.getPlayerDeaths().getOrDefault(playerId, 0);
        lines.add(toLegacy(ComponentUtil.parse("<red>死亡: <white>" + deaths)));

        // 存活状态
        boolean isAlive = gameSession.isPlayerAlive(playerId);
        String status = isAlive ? "<green>存活" : "<red>死亡";
        lines.add(toLegacy(ComponentUtil.parse("<gray>状态: " + status)));

        // 技能冷却信息（如果启用了职业系统）
        if (gameSession.getRoom().getSettings().isProfessionSystemEnabled()) {
            addSkillCooldownInfo(lines, playerId);
        }
    }

    /**
     * 添加技能冷却信息
     */
    private void addSkillCooldownInfo(List<String> lines, UUID playerId) {
        com.projectSource.ultimateManhurt.profession.ProfessionManager professionManager = plugin.getProfessionManager();
        com.projectSource.ultimateManhurt.profession.Profession profession = professionManager.getPlayerProfession(playerId);

        if (profession == null) {
            return;
        }

        com.projectSource.ultimateManhurt.profession.skill.SkillCooldown skillCooldown = professionManager.getSkillCooldown();

        // 添加分隔线
        lines.add(createUniqueEntry("", lines.size()));

        // 主动技能冷却
        String activeSkillName = profession.getActiveSkillName();
        if (activeSkillName != null && !activeSkillName.isEmpty()) {
            int activeCooldown = skillCooldown.getRemainingCooldown(playerId, activeSkillName);
            if (activeCooldown > 0) {
                lines.add(toLegacy(ComponentUtil.parse("<aqua>主动: <red>" + activeSkillName + " (" + activeCooldown + "s)")));
            } else {
                lines.add(toLegacy(ComponentUtil.parse("<aqua>主动: <green>" + activeSkillName + " (就绪)")));
            }
        }

        // 被动技能冷却（如果有冷却时间）
        String passiveSkillName = profession.getPassiveSkillName();
        int passiveSkillCooldown = profession.getPassiveSkillCooldown();
        if (passiveSkillName != null && !passiveSkillName.isEmpty() && passiveSkillCooldown > 0) {
            int passiveCooldown = skillCooldown.getRemainingCooldown(playerId, passiveSkillName);
            if (passiveCooldown > 0) {
                lines.add(toLegacy(ComponentUtil.parse("<yellow>被动: <red>" + passiveSkillName + " (" + passiveCooldown + "s)")));
            } else {
                lines.add(toLegacy(ComponentUtil.parse("<yellow>被动: <green>" + passiveSkillName + " (就绪)")));
            }
        }
    }

    /**
     * 将Component转换为传统格式字符串
     */
    private String toLegacy(Component component) {
        return LegacyComponentSerializer.legacySection().serialize(component);
    }
    
    /**
     * 确保字符串长度不超过限制
     */
    private String ensureLength(String text, int maxLength) {
        if (text.length() > maxLength) {
            return text.substring(0, maxLength);
        }
        return text;
    }
    
    /**
     * 创建唯一的计分板条目
     */
    private String createUniqueEntry(String base, int index) {
        if (base.isEmpty()) {
            // 为空行创建唯一标识
            StringBuilder entry = new StringBuilder();
            for (int i = 0; i <= index; i++) {
                entry.append("§r"); // 这里保留§r，因为这是Minecraft内部的重置代码，不是显示文本
            }
            return entry.toString();
        }

        // 为非空行添加唯一后缀
        StringBuilder entry = new StringBuilder(base);
        for (int i = 0; i < index; i++) {
            entry.append("§r"); // 这里保留§r，因为这是Minecraft内部的重置代码，不是显示文本
        }
        return ensureLength(entry.toString(), 40);
    }
}
