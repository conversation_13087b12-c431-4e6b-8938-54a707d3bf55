package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.room.RoomInvite;
import com.projectSource.ultimateManhurt.room.RoomType;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.List;

/**
 * 邀请管理GUI
 * 显示待处理的邀请和邀请管理功能
 */
public class InviteGui extends BaseGui {
    
    private final Room room;
    
    public InviteGui(UltimateManhurt plugin, Player player, Room room) {
        super(plugin, player, "邀请管理 - " + room.getName(), 54);
        this.room = room;
        setupGui();
    }
    
    @Override
    protected void setupGui() {
        // 检查是否为私人房间
        if (room.getType() != RoomType.PRIVATE) {
            setItem(22, createItem(Material.BARRIER, "<red>错误", 
                "<gray>只有私人房间可以管理邀请"));
            return;
        }
        
        // 检查是否为房主
        if (!room.isOwner(this.player.getUniqueId())) {
            setItem(22, createItem(Material.BARRIER, "<red>权限不足", 
                "<gray>只有房主可以管理邀请"));
            return;
        }
        
        // 显示房间信息
        setItem(4, createItem(Material.EMERALD_BLOCK, "<green><bold>" + room.getName(),
            "<gray>房间类型: " + room.getType().getColor() + room.getType().getDisplayName(),
            "<gray>当前人数: <white>" + room.getPlayerCount() + "/" + room.getSettings().getMaxPlayers(),
            "<gray>房间状态: " + (room.getGameState().isActive() ? "<red>游戏中" : "<green>等待中")));
        
        // 邀请玩家按钮
        setItem(20, createItem(Material.PAPER, "<yellow><bold>邀请玩家",
            "<gray>点击邀请在线玩家加入房间",
            "<yellow>使用命令: /manhunt invite <玩家名>"));
        
        // 显示在线玩家列表（可邀请的）
        List<Player> onlinePlayers = getInvitablePlayers();
        int slot = 28;
        for (int i = 0; i < Math.min(onlinePlayers.size(), 7); i++) {
            Player onlinePlayer = onlinePlayers.get(i);
            setItem(slot + i, createPlayerItem(onlinePlayer));
        }
        
        // 显示待处理邀请
        displayPendingInvites();
        
        // 返回按钮
        setItem(49, createItem(Material.BARRIER, "<red>返回", "<gray>返回房间设置"));
        
        // 装饰性物品
        for (int i = 0; i < inventory.getSize(); i++) {
            if (inventory.getItem(i) == null) {
                setItem(i, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));
            }
        }
    }
    
    /**
     * 显示待处理邀请
     */
    private void displayPendingInvites() {
        // 这里可以显示当前房间的待处理邀请数量
        // 由于邀请是按被邀请者存储的，需要遍历所有待处理邀请
        int pendingCount = 0;
        // TODO: 实现获取当前房间待处理邀请数量的逻辑
        
        setItem(24, createItem(Material.CLOCK, "<gold><bold>待处理邀请",
            "<gray>当前待处理: <white>" + pendingCount + " 个",
            "<gray>邀请将在60秒后自动过期"));
    }
    
    /**
     * 获取可邀请的玩家列表
     */
    private List<Player> getInvitablePlayers() {
        List<Player> invitablePlayers = new ArrayList<>();
        
        for (Player onlinePlayer : this.plugin.getServer().getOnlinePlayers()) {
            // 排除自己
            if (onlinePlayer.getUniqueId().equals(this.player.getUniqueId())) {
                continue;
            }
            
            // 排除已在房间中的玩家
            if (room.containsPlayer(onlinePlayer.getUniqueId())) {
                continue;
            }
            
            // 排除已在其他房间中的玩家
            if (this.plugin.getRoomManager().isPlayerInRoom(onlinePlayer.getUniqueId())) {
                continue;
            }
            
            // 排除已有待处理邀请的玩家
            RoomInvite pendingInvite = this.plugin.getRoomManager().getInviteManager()
                .getPendingInvite(onlinePlayer.getUniqueId());
            if (pendingInvite != null) {
                continue;
            }
            
            invitablePlayers.add(onlinePlayer);
        }
        
        return invitablePlayers;
    }
    
    /**
     * 创建玩家物品
     */
    private ItemStack createPlayerItem(Player targetPlayer) {
        List<String> lore = new ArrayList<>();
        lore.add("<gray>玩家: <white>" + targetPlayer.getName());
        lore.add("<gray>状态: <green>可邀请");
        lore.add("");
        lore.add("<yellow>点击邀请此玩家");
        
        return createItem(Material.PLAYER_HEAD, "<green>" + targetPlayer.getName(), 
            lore.toArray(new String[0]));
    }
    
    @Override
    public void handleClick(InventoryClickEvent event) {
        // 取消所有点击事件，防止物品被拿出
        event.setCancelled(true);
        
        // 确保是玩家点击
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        // 确保点击的是这个GUI的库存
        if (event.getClickedInventory() != this.inventory) {
            return;
        }
        
        ItemStack item = event.getCurrentItem();
        if (item == null || item.getType() == Material.AIR) {
            return;
        }
        
        this.playClickSound();
        
        int slot = event.getSlot();
        
        switch (slot) {
            case 20: // 邀请玩家按钮
                handleInviteButton();
                break;
            case 49: // 返回
                close();
                this.plugin.getGuiManager().openRoomSettingsGui(this.player, room);
                break;
            default:
                // 检查是否点击了玩家头像（slot 28-34）
                if (slot >= 28 && slot <= 34) {
                    handlePlayerClick(slot - 28);
                }
                break;
        }
    }
    
    /**
     * 处理邀请按钮点击
     */
    private void handleInviteButton() {
        ComponentUtil.sendMessage(this.player, ComponentUtil.info("请使用命令邀请玩家:"));
        ComponentUtil.sendMessage(this.player, ComponentUtil.parse("<yellow>/manhunt invite <玩家名>"));
        close();
    }
    
    /**
     * 处理玩家头像点击
     */
    private void handlePlayerClick(int playerIndex) {
        List<Player> invitablePlayers = getInvitablePlayers();
        
        if (playerIndex >= 0 && playerIndex < invitablePlayers.size()) {
            Player targetPlayer = invitablePlayers.get(playerIndex);
            
            // 发送邀请
            boolean success = this.plugin.getRoomManager().getInviteManager()
                .sendInvite(this.player, targetPlayer, room);
            
            if (success) {
                // 刷新界面
                setupGui();
            }
        }
    }
}
