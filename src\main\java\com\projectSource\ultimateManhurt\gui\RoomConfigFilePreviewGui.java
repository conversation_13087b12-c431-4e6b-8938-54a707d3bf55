package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.room.RoomSettings;
import com.projectSource.ultimateManhurt.room.RoomSettingsManager;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 房间配置文件预览GUI
 * 用于预览配置文件的详细内容
 */
public class RoomConfigFilePreviewGui extends BaseGui {
    
    private final Room room;
    private final String fileName;
    private final RoomSettingsManager settingsManager;
    private final RoomSettings previewSettings;
    
    public RoomConfigFilePreviewGui(UltimateManhurt plugin, Player player, Room room, String fileName) {
        super(plugin, player, "<blue><bold>配置预览 - " + fileName, 54);
        this.room = room;
        this.fileName = fileName;
        this.settingsManager = plugin.getRoomSettingsManager();
        this.previewSettings = settingsManager.loadSettings(fileName);
        setupGui();
    }
    
    @Override
    protected void setupGui() {
        // 检查配置文件是否加载成功
        if (previewSettings == null) {
            setItem(22, createItem(Material.BARRIER, "<red>配置文件加载失败",
                "<gray>无法读取配置文件: " + fileName));
            setItem(49, createBackButton());
            return;
        }
        
        // 填充边框
        fillBorder(Material.GRAY_STAINED_GLASS_PANE);
        
        // 标题
        setItem(4, createItem(Material.SPYGLASS, "<blue><bold>配置文件预览",
            "<gray>文件名: <white>" + fileName,
            "<yellow>查看配置文件的详细设置"));
        
        // 显示配置详情
        displayConfigDetails();
        
        // 控制按钮
        setupControlButtons();
    }
    
    /**
     * 显示配置详情
     */
    private void displayConfigDetails() {
        // 基础设置
        setItem(10, createItem(Material.CLOCK, "<gold><bold>基础设置",
            "<gray>游戏时长: <white>" + previewSettings.getGameDurationMinutes() + "分钟",
            "<gray>世界种子: <white>" + (previewSettings.getWorldSeed() == 0 ? "随机" : previewSettings.getWorldSeed()),
            "<gray>游戏难度: <white>" + previewSettings.getDifficulty().name(),
            "<gray>观察者模式: <white>" + previewSettings.getSpectatorGameMode().name()));
        
        // 玩家设置
        setItem(11, createItem(Material.PLAYER_HEAD, "<green><bold>玩家设置",
            "<gray>最大玩家数: <white>" + previewSettings.getMaxPlayers(),
            "<gray>速通者上限: <white>" + previewSettings.getMaxSpeedrunners(),
            "<gray>捕猎者上限: <white>" + previewSettings.getMaxHunters(),
            "<gray>允许观察者: <white>" + (previewSettings.isAllowSpectators() ? "是" : "否")));
        
        // 游戏规则
        setItem(12, createItem(Material.IRON_SWORD, "<red><bold>游戏规则",
            "<gray>PVP: <white>" + (previewSettings.isPvpEnabled() ? "启用" : "禁用"),
            "<gray>友军伤害: <white>" + (previewSettings.isFriendlyFire() ? "启用" : "禁用"),
            "<gray>死亡保留物品: <white>" + (previewSettings.isKeepInventory() ? "是" : "否"),
            "<gray>自然回血: <white>" + (previewSettings.isNaturalRegeneration() ? "启用" : "禁用"),
            "<gray>显示死亡消息: <white>" + (previewSettings.isShowDeathMessages() ? "是" : "否")));
        
        // 世界设置
        setItem(13, createItem(Material.GRASS_BLOCK, "<blue><bold>世界设置",
            "<gray>生成结构: <white>" + (previewSettings.isGenerateStructures() ? "是" : "否"),
            "<gray>生成奖励箱: <white>" + (previewSettings.isGenerateBonusChest() ? "是" : "否"),
            "<gray>启用命令方块: <white>" + (previewSettings.isEnableCommandBlocks() ? "是" : "否"),
            "<gray>昼夜循环: <white>" + (previewSettings.isDoDaylightCycle() ? "启用" : "禁用"),
            "<gray>天气循环: <white>" + (previewSettings.isDoWeatherCycle() ? "启用" : "禁用")));
        
        // 特殊功能
        setItem(14, createItem(Material.COMPASS, "<purple><bold>特殊功能",
            "<gray>指南针追踪: <white>" + (previewSettings.isCompassTracking() ? "启用" : "禁用"),
            "<gray>更新间隔: <white>" + previewSettings.getCompassUpdateInterval() + "秒",
            "<gray>定位条: <white>" + (previewSettings.isLocatorBar() ? "启用" : "禁用"),
            "<gray>末影珍珠冷却: <white>" + (previewSettings.isEnderPearlCooldown() ? "启用" : "禁用"),
            "<gray>冷却时间: <white>" + previewSettings.getEnderPearlCooldownSeconds() + "秒"));
        
        // 系统设置
        setItem(15, createItem(Material.COMMAND_BLOCK, "<yellow><bold>系统设置",
            "<gray>Ban Pick系统: <white>" + (previewSettings.isBanPickEnabled() ? "启用" : "禁用"),
            "<gray>职业系统: <white>" + (previewSettings.isProfessionSystemEnabled() ? "启用" : "禁用"),
            "<gray>豁免系统: <white>" + (previewSettings.isImmunityEnabled() ? "启用" : "禁用"),
            "<gray>豁免时长: <white>" + previewSettings.getImmunityDurationSeconds() + "秒"));
        
        // 胜利条件
        setItem(16, createItem(Material.DRAGON_HEAD, "<light_purple><bold>胜利条件",
            "<gray>胜利模式: <white>" + previewSettings.getVictoryMode().name(),
            "<gray>末影龙重生: <white>" + (previewSettings.isDragonRespawn() ? "是" : "否"),
            "<gray>超时捕猎者获胜: <white>" + (previewSettings.isTimeoutHuntersWin() ? "是" : "否"),
            "<gray>速通者生命: <white>" + (previewSettings.getSpeedrunnerLives() == 0 ? "无限" : previewSettings.getSpeedrunnerLives()),
            "<gray>捕猎者生命: <white>" + (previewSettings.getHunterLives() == 0 ? "无限" : previewSettings.getHunterLives())));
        
        // 血量设置
        setItem(19, createItem(Material.GOLDEN_APPLE, "<red><bold>血量设置",
            "<gray>速通者血量: <white>" + previewSettings.getSpeedrunnerMaxHealth() + "❤",
            "<gray>捕猎者血量: <white>" + previewSettings.getHunterMaxHealth() + "❤"));
        
        // 装备包设置
        setItem(20, createItem(Material.CHEST, "<aqua><bold>装备包设置",
            "<gray>装备包系统: <white>" + (previewSettings.isStartKitEnabled() ? "启用" : "禁用"),
            "<gray>速通者装备: <white>" + (previewSettings.getSpeedrunnerKit() != null ? previewSettings.getSpeedrunnerKit().getName() : "无"),
            "<gray>捕猎者装备: <white>" + (previewSettings.getHunterKit() != null ? previewSettings.getHunterKit().getName() : "无")));
        
        // 守卫模式设置（如果启用）
        if (previewSettings.getVictoryMode().requiresGuardSystem()) {
            setItem(21, createItem(Material.WITHER_SKELETON_SKULL, "<dark_gray><bold>守卫模式设置",
                "<gray>凋零血量: <white>" + previewSettings.getWitherMaxHealth() + "❤",
                "<gray>减疗效果: <white>" + (int)(previewSettings.getWitherHealingReduction() * 100) + "%",
                "<gray>攻击间隔: <white>" + previewSettings.getWitherAttackInterval() + "秒",
                "<gray>攻击伤害: <white>" + previewSettings.getWitherAttackDamage() + "❤",
                "<gray>护盾时长: <white>" + previewSettings.getWitherShieldDuration() + "秒"));
        }
        
        // 积分模式设置（如果启用）
        if (previewSettings.getVictoryMode().requiresScoreSystem()) {
            setItem(22, createItem(Material.EXPERIENCE_BOTTLE, "<green><bold>积分模式设置",
                "<gray>目标分数: <white>" + previewSettings.getTargetScore(),
                "<gray>里程碑系统: <white>" + (previewSettings.getMilestoneSettings() != null ? "启用" : "禁用")));
        }
        
        // 自定义规则
        if (!previewSettings.getCustomRules().isEmpty()) {
            setItem(23, createItem(Material.WRITABLE_BOOK, "<orange><bold>自定义规则",
                "<gray>规则数量: <white>" + previewSettings.getCustomRules().size(),
                "<gray>点击查看详细规则"));
        }
    }
    
    /**
     * 设置控制按钮
     */
    private void setupControlButtons() {
        // 加载此配置按钮
        if (room.isOwner(player.getUniqueId())) {
            setItem(48, createItem(Material.EMERALD, "<green><bold>加载此配置",
                "<gray>将此配置应用到当前房间",
                "<red>注意: 这将覆盖当前所有设置"));
        }
        
        // 返回按钮
        setItem(49, createBackButton());
        
        // 删除此配置按钮
        if (room.isOwner(player.getUniqueId())) {
            setItem(50, createItem(Material.LAVA_BUCKET, "<red><bold>删除此配置",
                "<gray>永久删除此配置文件",
                "<red>此操作不可撤销！"));
        }
    }
    
    @Override
    public void handleClick(InventoryClickEvent event) {
        event.setCancelled(true);
        
        if (event.getClickedInventory() != inventory) {
            return;
        }
        
        int slot = event.getSlot();
        ItemStack item = event.getCurrentItem();
        
        if (item == null || item.getType() == Material.AIR) {
            return;
        }
        
        playClickSound();
        
        switch (slot) {
            case 48: // 加载此配置
                if (room.isOwner(player.getUniqueId())) {
                    handleLoadConfig();
                } else {
                    sendError("只有房主可以加载配置！");
                }
                break;
            case 49: // 返回
                close();
                plugin.getGuiManager().openRoomConfigFileManagementGui(player, room);
                break;
            case 50: // 删除此配置
                if (room.isOwner(player.getUniqueId())) {
                    handleDeleteConfig();
                } else {
                    sendError("只有房主可以删除配置！");
                }
                break;
            case 23: // 查看自定义规则
                if (!previewSettings.getCustomRules().isEmpty()) {
                    showCustomRules();
                }
                break;
        }
    }
    
    /**
     * 处理加载配置
     */
    private void handleLoadConfig() {
        if (previewSettings != null) {
            // 将预览的设置复制到当前房间设置
            copySettingsTo(previewSettings, room.getSettings());
            sendSuccess("成功加载配置文件: " + fileName);
            playSuccessSound();
            
            // 返回房间设置界面
            close();
            plugin.getGuiManager().openRoomSettingsGui(player, room);
        } else {
            sendError("配置文件加载失败！");
            playErrorSound();
        }
    }
    
    /**
     * 处理删除配置
     */
    private void handleDeleteConfig() {
        boolean success = settingsManager.deleteSettings(fileName);
        if (success) {
            sendSuccess("成功删除配置文件: " + fileName);
            playSuccessSound();
            
            // 返回配置文件管理界面
            close();
            plugin.getGuiManager().openRoomConfigFileManagementGui(player, room);
        } else {
            sendError("删除配置文件失败！");
            playErrorSound();
        }
    }
    
    /**
     * 显示自定义规则
     */
    private void showCustomRules() {
        StringBuilder rules = new StringBuilder("自定义规则:\n");
        for (String key : previewSettings.getCustomRules().keySet()) {
            Object value = previewSettings.getCustomRules().get(key);
            rules.append("• ").append(key).append(": ").append(value).append("\n");
        }
        sendInfo(rules.toString());
    }

    /**
     * 将源设置复制到目标设置
     */
    private void copySettingsTo(RoomSettings source, RoomSettings target) {
        // 基础设置
        target.setGameDurationMinutes(source.getGameDurationMinutes());
        target.setWorldSeed(source.getWorldSeed());
        target.setDifficulty(source.getDifficulty());
        target.setSpectatorGameMode(source.getSpectatorGameMode());

        // 玩家设置
        target.setMaxPlayers(source.getMaxPlayers());
        target.setMaxSpeedrunners(source.getMaxSpeedrunners());
        target.setMaxHunters(source.getMaxHunters());
        target.setAllowSpectators(source.isAllowSpectators());

        // 游戏规则
        target.setPvpEnabled(source.isPvpEnabled());
        target.setFriendlyFire(source.isFriendlyFire());
        target.setKeepInventory(source.isKeepInventory());
        target.setNaturalRegeneration(source.isNaturalRegeneration());
        target.setShowDeathMessages(source.isShowDeathMessages());

        // 世界设置
        target.setGenerateStructures(source.isGenerateStructures());
        target.setGenerateBonusChest(source.isGenerateBonusChest());
        target.setEnableCommandBlocks(source.isEnableCommandBlocks());
        target.setDoDaylightCycle(source.isDoDaylightCycle());
        target.setDoWeatherCycle(source.isDoWeatherCycle());
        target.setCustomSpawnLogic(source.isCustomSpawnLogic());

        // 特殊功能
        target.setCompassTracking(source.isCompassTracking());
        target.setCompassUpdateInterval(source.getCompassUpdateInterval());
        target.setLocatorBar(source.isLocatorBar());
        target.setEnderPearlCooldown(source.isEnderPearlCooldown());
        target.setEnderPearlCooldownSeconds(source.getEnderPearlCooldownSeconds());
        target.setNetherPortalDelay(source.isNetherPortalDelay());
        target.setNetherPortalDelaySeconds(source.getNetherPortalDelaySeconds());

        // Ban Pick系统
        target.setBanPickEnabled(source.isBanPickEnabled());
        target.setBanPickPhaseTimeSeconds(source.getBanPickPhaseTimeSeconds());

        // 职业系统
        target.setProfessionSystemEnabled(source.isProfessionSystemEnabled());

        // 豁免设置
        target.setImmunityEnabled(source.isImmunityEnabled());
        target.setImmunityDurationSeconds(source.getImmunityDurationSeconds());

        // 胜利条件
        target.setDragonRespawn(source.isDragonRespawn());
        target.setTimeoutHuntersWin(source.isTimeoutHuntersWin());
        target.setSpeedrunnerLives(source.getSpeedrunnerLives());
        target.setHunterLives(source.getHunterLives());
        target.setAllowSpeedrunnerRespawn(source.isAllowSpeedrunnerRespawn());
        target.setAllowHunterRespawn(source.isAllowHunterRespawn());

        // 血量设置
        target.setSpeedrunnerMaxHealth(source.getSpeedrunnerMaxHealth());
        target.setHunterMaxHealth(source.getHunterMaxHealth());

        // 胜利模式设置
        target.setVictoryMode(source.getVictoryMode());
        target.setTargetScore(source.getTargetScore());

        // 守卫模式设置
        target.setWitherMaxHealth(source.getWitherMaxHealth());
        target.setWitherHealingReduction(source.getWitherHealingReduction());
        target.setWitherAttackInterval(source.getWitherAttackInterval());
        target.setWitherAttackDamage(source.getWitherAttackDamage());
        target.setWitherShieldDuration(source.getWitherShieldDuration());
        target.setWitherShieldReduction(source.getWitherShieldReduction());
        target.setHunterSpawnDistance(source.getHunterSpawnDistance());
        target.setWitherCanMove(source.isWitherCanMove());
        target.setWitherTargetHuntersOnly(source.isWitherTargetHuntersOnly());
        target.setWitherDestroyBlocks(source.isWitherDestroyBlocks());
        target.setWitherEffectDuration(source.getWitherEffectDuration());
        target.setWitherEffectLevel(source.getWitherEffectLevel());

        // StartKit设置
        target.setStartKitEnabled(source.isStartKitEnabled());
        if (source.getSpeedrunnerKit() != null) {
            target.setSpeedrunnerKit(new com.projectSource.ultimateManhurt.kit.StartKit(source.getSpeedrunnerKit()));
        }
        if (source.getHunterKit() != null) {
            target.setHunterKit(new com.projectSource.ultimateManhurt.kit.StartKit(source.getHunterKit()));
        }

        // 里程碑设置
        if (source.getMilestoneSettings() != null) {
            target.setMilestoneSettings(source.getMilestoneSettings());
        }

        // 自定义规则
        target.getCustomRules().clear();
        target.getCustomRules().putAll(source.getCustomRules());
    }
}
