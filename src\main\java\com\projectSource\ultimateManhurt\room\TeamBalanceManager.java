package com.projectSource.ultimateManhurt.room;

import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.ranking.EloSystem;
import com.projectSource.ultimateManhurt.data.PlayerData;
import com.projectSource.ultimateManhurt.UltimateManhurt;

import java.util.*;

/**
 * 阵营平衡管理器
 * 负责分析和建议阵营平衡
 */
public class TeamBalanceManager {
    
    private final UltimateManhurt plugin;
    
    public TeamBalanceManager(UltimateManhurt plugin) {
        this.plugin = plugin;
    }
    
    /**
     * 分析房间的阵营平衡状况
     */
    public BalanceAnalysis analyzeBalance(Room room) {
        List<UUID> speedrunners = new ArrayList<>(room.getPlayersByRole(PlayerRole.SPEEDRUNNER));
        List<UUID> hunters = new ArrayList<>(room.getPlayersByRole(PlayerRole.HUNTER));
        
        BalanceAnalysis analysis = new BalanceAnalysis();
        
        // 人数分析
        analysis.speedrunnerCount = speedrunners.size();
        analysis.hunterCount = hunters.size();
        analysis.maxSpeedrunners = room.getSettings().getMaxSpeedrunners();
        analysis.maxHunters = room.getSettings().getMaxHunters();
        
        // ELO分析（所有房间类型都计算ELO）
        analysis.speedrunnerAvgElo = calculateAverageElo(speedrunners);
        analysis.hunterAvgElo = calculateAverageElo(hunters);
        analysis.eloDifference = Math.abs(analysis.speedrunnerAvgElo - analysis.hunterAvgElo);
        
        // 平衡度评估
        analysis.balanceLevel = evaluateBalance(analysis);
        analysis.recommendations = generateRecommendations(analysis);
        
        return analysis;
    }
    
    /**
     * 计算平均ELO
     */
    private int calculateAverageElo(List<UUID> playerIds) {
        if (playerIds.isEmpty()) return 1200; // 默认ELO
        
        int totalElo = 0;
        int validPlayers = 0;
        
        for (UUID playerId : playerIds) {
            PlayerData playerData = plugin.getDataManager().getPlayerData(playerId);
            if (playerData != null) {
                totalElo += EloSystem.getPlayerElo(playerData);
                validPlayers++;
            }
        }
        
        return validPlayers > 0 ? totalElo / validPlayers : 1200;
    }
    
    /**
     * 评估平衡等级
     */
    private BalanceLevel evaluateBalance(BalanceAnalysis analysis) {
        // 检查人数平衡
        if (analysis.speedrunnerCount == 0 || analysis.hunterCount == 0) {
            return BalanceLevel.UNPLAYABLE;
        }
        
        // 检查人数比例
        double ratio = (double) analysis.hunterCount / analysis.speedrunnerCount;
        if (ratio < 1.0 || ratio > 5.0) {
            return BalanceLevel.POOR;
        }
        
        // 天梯模式下检查ELO差距
        if (analysis.eloDifference > 0) {
            if (analysis.eloDifference > 300) {
                return BalanceLevel.POOR;
            } else if (analysis.eloDifference > 150) {
                return BalanceLevel.FAIR;
            } else if (analysis.eloDifference > 50) {
                return BalanceLevel.GOOD;
            } else {
                return BalanceLevel.EXCELLENT;
            }
        }
        
        // 普通模式下只看人数比例
        if (ratio >= 1.5 && ratio <= 3.0) {
            return BalanceLevel.GOOD;
        } else {
            return BalanceLevel.FAIR;
        }
    }
    
    /**
     * 生成平衡建议
     */
    private List<String> generateRecommendations(BalanceAnalysis analysis) {
        List<String> recommendations = new ArrayList<>();
        
        // 人数建议
        if (analysis.speedrunnerCount == 0) {
            recommendations.add("需要至少1个速通者才能开始游戏");
        }
        if (analysis.hunterCount == 0) {
            recommendations.add("需要至少1个捕猎者才能开始游戏");
        }
        
        // 人数比例建议
        double ratio = analysis.hunterCount > 0 ? (double) analysis.hunterCount / analysis.speedrunnerCount : 0;
        if (ratio > 5.0) {
            recommendations.add("捕猎者人数过多，建议减少捕猎者或增加速通者");
        } else if (ratio < 1.0 && analysis.hunterCount > 0) {
            recommendations.add("速通者人数过多，建议增加捕猎者");
        }
        
        // ELO建议（天梯模式）
        if (analysis.eloDifference > 200) {
            if (analysis.speedrunnerAvgElo > analysis.hunterAvgElo) {
                recommendations.add("速通者平均ELO过高，建议调整阵营分配");
            } else {
                recommendations.add("捕猎者平均ELO过高，建议调整阵营分配");
            }
        }
        
        // 理想配置建议
        if (analysis.speedrunnerCount == 1 && analysis.hunterCount >= 2 && analysis.hunterCount <= 4) {
            recommendations.add("当前配置接近理想状态");
        } else if (analysis.speedrunnerCount == 2 && analysis.hunterCount >= 3 && analysis.hunterCount <= 6) {
            recommendations.add("当前配置接近理想状态");
        } else {
            recommendations.add("建议配置: 1-2个速通者，2-4个捕猎者");
        }
        
        return recommendations;
    }
    
    /**
     * 获取建议的阵营分配
     */
    public Map<PlayerRole, Integer> getSuggestedTeamSizes(int totalPlayers) {
        Map<PlayerRole, Integer> suggestion = new HashMap<>();
        
        if (totalPlayers <= 3) {
            suggestion.put(PlayerRole.SPEEDRUNNER, 1);
            suggestion.put(PlayerRole.HUNTER, totalPlayers - 1);
        } else if (totalPlayers <= 6) {
            suggestion.put(PlayerRole.SPEEDRUNNER, 1);
            suggestion.put(PlayerRole.HUNTER, totalPlayers - 1);
        } else if (totalPlayers <= 10) {
            suggestion.put(PlayerRole.SPEEDRUNNER, 2);
            suggestion.put(PlayerRole.HUNTER, totalPlayers - 2);
        } else {
            suggestion.put(PlayerRole.SPEEDRUNNER, Math.max(2, totalPlayers / 5));
            suggestion.put(PlayerRole.HUNTER, totalPlayers - suggestion.get(PlayerRole.SPEEDRUNNER));
        }
        
        return suggestion;
    }
    
    /**
     * 平衡分析结果
     */
    public static class BalanceAnalysis {
        public int speedrunnerCount;
        public int hunterCount;
        public int maxSpeedrunners;
        public int maxHunters;
        public int speedrunnerAvgElo;
        public int hunterAvgElo;
        public int eloDifference;
        public BalanceLevel balanceLevel;
        public List<String> recommendations;
        
        public boolean canStartGame() {
            return speedrunnerCount > 0 && hunterCount > 0;
        }
        
        public String getBalanceDescription() {
            switch (balanceLevel) {
                case EXCELLENT: return "完美平衡";
                case GOOD: return "良好平衡";
                case FAIR: return "基本平衡";
                case POOR: return "平衡较差";
                case UNPLAYABLE: return "无法游戏";
                default: return "未知";
            }
        }
        
        public String getBalanceColor() {
            switch (balanceLevel) {
                case EXCELLENT: return "<green>";
                case GOOD: return "<yellow>";
                case FAIR: return "<gold>";
                case POOR: return "<red>";
                case UNPLAYABLE: return "<dark_red>";
                default: return "<gray>";
            }
        }
    }
    
    /**
     * 平衡等级
     */
    public enum BalanceLevel {
        EXCELLENT,  // 完美平衡
        GOOD,       // 良好平衡
        FAIR,       // 基本平衡
        POOR,       // 平衡较差
        UNPLAYABLE  // 无法游戏
    }
}
