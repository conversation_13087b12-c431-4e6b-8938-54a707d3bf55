# 屠夫增强 & 职业冷却时间同步

## 📊 职业冷却时间修改同步

### 你的修改内容
根据你的手动修改，以下职业的冷却时间已更新：

| 职业 | 技能 | 原冷却时间 | 新冷却时间 | 变化 |
|------|------|-----------|-----------|------|
| **刺客 (ASSASSIN)** | 末影传送 | 15秒 | **12秒** | ⬇️ 减少3秒 |
| **牧师 (PRIEST)** | 治疗光环 | 45秒 | **75秒** | ⬆️ 增加30秒 |
| **猎人 (HUNTER)** | 战斗专注 | 90秒 | **150秒** | ⬆️ 增加60秒 |

### 平衡性分析
- **刺客增强：** 传送冷却减少，提高机动性和生存能力
- **牧师削弱：** 治疗光环冷却增加，减少团队支援频率
- **猎人削弱：** 战斗专注冷却大幅增加，降低持续作战能力

## 🔪 屠夫职业增强

### 1. 钩子技能增强

**搜索距离提升：**
```java
// 原来：12格搜索距离
Player targetPlayer = findPlayerInDirection(player, direction, 12);

// 现在：20格搜索距离
double hookRange = 20.0;
Player targetPlayer = findPlayerInDirection(player, direction, hookRange);
```

**新增粒子效果：**
```java
/**
 * 显示钩子轨迹粒子效果
 */
private void showHookTrajectory(Player player, Vector direction, double maxDistance) {
    Location start = player.getEyeLocation();
    
    // 异步显示粒子效果，避免阻塞主线程
    Bukkit.getScheduler().runTaskAsynchronously(plugin, () -> {
        for (double distance = 0.5; distance <= maxDistance; distance += 0.5) {
            Location particleLoc = start.clone().add(direction.clone().multiply(distance));
            
            // 在主线程中显示粒子
            Bukkit.getScheduler().runTask(plugin, () -> {
                // 显示红色粒子效果（血腥钩子）
                particleLoc.getWorld().spawnParticle(
                    Particle.DUST, 
                    particleLoc, 
                    3, // 粒子数量
                    0.1, 0.1, 0.1, // 扩散范围
                    0, // 额外数据
                    new Particle.DustOptions(Color.RED, 1.0f) // 红色粒子
                );
                
                // 添加一些血滴效果
                particleLoc.getWorld().spawnParticle(
                    Particle.BLOCK,
                    particleLoc,
                    2, // 粒子数量
                    0.05, 0.05, 0.05, // 扩散范围
                    0.1, // 速度
                    Material.REDSTONE_BLOCK.createBlockData()
                );
            });
            
            // 延迟显示，创造轨迹效果
            try {
                Thread.sleep(20); // 20ms延迟
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    });
}
```

### 2. 腐肉堆积被动技能增强

**概率提升：**
```java
// 原来：15%概率触发
if (pseudoRandom.checkTrigger(playerId, eventName, 0.15)) {

// 现在：30%概率触发
if (pseudoRandom.checkTrigger(playerId, eventName, 0.30)) {
```

**描述更新：**
```java
// Profession.java 中的描述
"腐肉堆积",
"受到近战攻击有30%概率获得抗性三效果3s", // 从15%更新为30%
```

## 🎯 增强效果总结

### 钩子技能改进
- **🎯 搜索距离：** 12格 → **20格** (+67%范围)
- **✨ 视觉效果：** 新增红色粒子轨迹效果
- **🎨 粒子类型：** 
  - 红色尘埃粒子（主轨迹）
  - 红石方块碎片（血滴效果）
- **⚡ 性能优化：** 异步粒子渲染，不阻塞主线程

### 腐肉堆积改进
- **📈 触发概率：** 15% → **30% (+100%提升)**
- **🛡️ 效果不变：** 抗性三效果3秒
- **⚖️ 平衡考虑：** 大幅提升生存能力

## 🎮 技术实现细节

### 粒子效果系统
- **异步处理：** 避免主线程阻塞
- **轨迹动画：** 20ms间隔创造流畅轨迹
- **双重粒子：** 主轨迹+血滴效果
- **颜色定制：** 红色主题符合屠夫职业

### 伪随机系统
- **概率控制：** 使用伪随机确保概率准确性
- **事件标识：** 独立的"腐肉堆积"事件追踪
- **玩家隔离：** 每个玩家独立的概率状态

## 🧪 测试建议

### 钩子技能测试
1. **距离测试：**
   ```
   1. 设置屠夫职业：/manhunt professiontest set BUTCHER
   2. 在20格距离内放置目标玩家
   3. 使用钩子技能，确认能够命中
   4. 测试超过20格的距离，确认无法命中
   ```

2. **粒子效果测试：**
   ```
   1. 在开阔区域使用钩子技能
   2. 观察红色粒子轨迹是否正常显示
   3. 检查粒子是否沿着正确方向延伸
   4. 确认粒子效果不会造成卡顿
   ```

### 腐肉堆积测试
1. **概率测试：**
   ```
   1. 设置屠夫职业
   2. 让其他玩家进行多次近战攻击
   3. 统计触发次数，应该接近30%
   4. 确认效果为抗性三3秒
   ```

## 📈 平衡性影响

### 屠夫职业强化
- **攻击性提升：** 钩子距离增加67%，更容易命中目标
- **生存性提升：** 腐肉堆积概率翻倍，大幅提升防御能力
- **视觉体验：** 粒子效果增强技能反馈和沉浸感

### 整体职业平衡
- **刺客：** 机动性小幅提升（冷却-3秒）
- **屠夫：** 全面大幅强化（距离+67%，概率+100%）
- **牧师：** 支援能力削弱（冷却+30秒）
- **猎人：** 持续作战能力大幅削弱（冷却+60秒）

## 🎉 总结

成功实现了屠夫职业的全面增强：

- ✅ **钩子距离：** 从12格增加到20格
- ✅ **粒子效果：** 新增红色轨迹粒子系统
- ✅ **腐肉堆积：** 概率从15%提升到30%
- ✅ **冷却同步：** 确认了你的冷却时间修改
- ✅ **性能优化：** 异步粒子渲染避免卡顿
- ✅ **平衡调整：** 屠夫成为更强力的近战职业

现在屠夫职业拥有更远的钩子距离、炫酷的粒子效果和更高的生存能力，成为了真正的血腥战士！🔪✨
