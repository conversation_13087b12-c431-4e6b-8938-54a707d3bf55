# 死亡保护系统实现总结

## 🎯 功能概述

实现了速通者的里程碑指南书和猎人的特殊指南针在死亡时不会掉落的保护机制。

## 🔧 技术实现

### 1. **特殊物品标识系统**

#### CompassUtil工具类
- **创建特殊指南针**: `createHunterCompass()`
- **识别特殊指南针**: `isHunterCompass(ItemStack)`
- **通用保护检测**: `isProtectedItem(ItemStack)`
- **获取物品类型**: `getProtectedItemType(ItemStack)`

#### 标识机制
使用Bukkit的PersistentDataContainer为特殊物品添加标识：
```java
// 猎人指南针标识
meta.getPersistentDataContainer().set(
    new NamespacedKey("ultimatemanhunt", "protected_item"),
    PersistentDataType.STRING,
    "hunter_compass"
);

// 速通者指南书标识
meta.getPersistentDataContainer().set(
    new NamespacedKey("ultimatemanhurt", "protected_item"),
    PersistentDataType.STRING,
    "speedrunner_guide_book"
);
```

### 2. **物品分发系统**

#### StartKitManager增强
在游戏开始时自动分发特殊物品：

**速通者**：
- 在积分模式和混合模式下获得里程碑指南书
- 书籍带有特殊保护标识

**猎人**：
- 所有模式下都获得特殊指南针
- 指南针带有特殊保护标识和说明

#### 分发时机
- **时机**: 游戏开始时，在`actuallyStartGame()`中调用
- **位置**: `StartKitManager.giveStartKit()`方法
- **顺序**: 先分发普通装备，再分发特殊物品

### 3. **死亡保护机制**

#### ManhuntRules增强
在`onPlayerDeath`事件中添加特殊物品保护：

```java
// 即使不保留背包，也要保护特殊物品不掉落
if (settings.isKeepInventory()) {
    event.setKeepInventory(true);
    event.getDrops().clear();
} else {
    protectSpecialItems(event, role);
}
```

#### 保护流程
1. **检测阶段**: 遍历玩家死亡掉落物品
2. **识别阶段**: 使用`shouldProtectItem()`判断是否需要保护
3. **移除阶段**: 从掉落列表中移除受保护的物品
4. **恢复阶段**: 延迟1秒后重新给予玩家

#### 兼容性处理
支持新旧两种识别方式：
- **新方式**: 通过PersistentDataContainer标识
- **旧方式**: 通过物品类型和元数据（向后兼容）

## 📋 物品详细信息

### 猎人特殊指南针
```yaml
名称: "🧭 猎人指南针"
类型: COMPASS
描述:
  - "指向最近的速通者"
  - "右键切换追踪目标"
  - ""
  - "死亡时不会掉落"
特殊标识: "hunter_compass"
```

### 速通者里程碑指南书
```yaml
名称: "里程碑指南"
类型: WRITTEN_BOOK
作者: "UltimateManhurt"
描述:
  - "📖 里程碑指南书籍"
  - "包含所有里程碑信息"
  - ""
  - "死亡时不会掉落"
特殊标识: "speedrunner_guide_book"
```

## 🔄 工作流程

### 游戏开始时
1. **普通装备分发**: 通过StartKit系统分发基础装备
2. **特殊物品分发**: 
   - 速通者获得里程碑指南书（积分/混合模式）
   - 猎人获得特殊指南针（所有模式）
3. **通知玩家**: 显示获得特殊物品的消息

### 玩家死亡时
1. **死亡事件触发**: PlayerDeathEvent
2. **角色识别**: 获取死亡玩家的角色
3. **物品检查**: 遍历所有掉落物品
4. **保护判断**: 检查是否为受保护的特殊物品
5. **物品移除**: 从掉落列表中移除受保护物品
6. **延迟恢复**: 1秒后重新给予玩家
7. **通知玩家**: 显示保护成功的消息

### 物品识别逻辑
```java
// 新版本识别（推荐）
if (CompassUtil.isProtectedItem(item)) {
    String itemType = CompassUtil.getProtectedItemType(item);
    if ("hunter_compass".equals(itemType) && role == PlayerRole.HUNTER) {
        return true;
    }
}

// 兼容性识别（向后兼容）
if (role == PlayerRole.HUNTER && item.getType() == Material.COMPASS) {
    return true; // 保护所有指南针
}
```

## ✅ 功能特点

### 1. **精确识别**
- 使用PersistentDataContainer确保准确识别
- 避免误保护普通物品
- 支持多种特殊物品类型

### 2. **角色匹配**
- 速通者只保护里程碑指南书
- 猎人只保护特殊指南针
- 避免跨角色物品保护

### 3. **向后兼容**
- 支持旧版本没有特殊标识的物品
- 渐进式升级，不影响现有游戏

### 4. **用户友好**
- 清晰的物品描述说明保护功能
- 死亡后及时通知保护结果
- 自动恢复到背包，无需手动操作

### 5. **性能优化**
- 只在死亡时进行检查，平时无性能影响
- 延迟恢复避免与重生机制冲突
- 高效的物品识别算法

## 🎮 游戏体验提升

### 对速通者
- **不再担心**: 里程碑指南书丢失
- **专注游戏**: 可以放心进行高风险操作
- **信息保障**: 重要的进度信息始终可用

### 对猎人
- **追踪保障**: 特殊指南针不会丢失
- **战术自由**: 可以更积极地参与战斗
- **功能完整**: 追踪功能始终可用

### 对游戏平衡
- **公平性**: 所有玩家都有基本工具保障
- **竞技性**: 减少因意外丢失工具导致的不公平
- **流畅性**: 避免因工具丢失导致的游戏中断

## 🔧 维护和扩展

### 添加新的受保护物品
1. 在CompassUtil中添加新的创建方法
2. 定义新的物品类型标识
3. 在ManhuntRules中添加识别逻辑
4. 在适当的地方添加分发逻辑

### 自定义保护规则
可以通过修改`shouldProtectItem`方法来自定义保护规则：
- 基于玩家权限的保护
- 基于游戏模式的保护
- 基于物品稀有度的保护

## 📝 总结

这个死亡保护系统成功实现了：

🎯 **核心功能**: 速通者的书和猎人的指南针死亡时不掉落
🔧 **技术可靠**: 使用PersistentDataContainer确保准确识别
🎮 **用户友好**: 自动分发、自动保护、及时通知
⚖️ **游戏平衡**: 保障基本工具，提升游戏体验
🔄 **向后兼容**: 支持旧版本物品，平滑升级

系统设计灵活，易于维护和扩展，为游戏提供了更好的用户体验！
