# UltimateManhurt Tablist 系统

## 概述

UltimateManhurt Tablist系统是一个专为Manhunt游戏设计的高级Tab列表显示系统，提供实时的游戏信息、玩家状态和服务器性能监控。

## 功能特性

### 🎮 动态内容显示
- **游戏状态感知**: 根据玩家当前状态（大厅、房间等待、游戏中）显示不同的Tablist内容
- **实时更新**: 自动更新游戏时间、存活玩家、服务器性能等信息
- **动画效果**: 头部标题支持动画切换效果

### 👥 玩家信息显示
- **延迟指示器**: 显示玩家延迟，支持颜色分级（绿色/黄色/红色）
- **血量显示**: 实时显示玩家血量百分比，支持颜色分级
- **角色标识**: 游戏中显示玩家角色（速通者⚔/猎人🏹/观察者👁）
- **权限颜色**: 根据玩家权限显示不同的名称颜色

### 📊 性能监控
- **TPS监控**: 实时显示服务器TPS，支持可视化进度条
- **内存使用**: 显示服务器内存使用情况
- **实体统计**: 显示世界中的实体数量
- **在线玩家**: 显示当前在线玩家数量

### 🏠 房间信息
- **房间状态**: 显示房间名称和玩家数量
- **角色分配**: 显示速通者和猎人的数量分配
- **可视化进度条**: 使用Unicode字符显示各种数据的可视化进度条

## 系统架构

### 核心类

1. **TablistManager**: 主管理器，负责创建和管理所有玩家的Tablist
2. **PlayerTablist**: 单个玩家的Tablist实例，处理内容更新和显示
3. **TablistUtil**: 工具类，包含所有构建Tablist内容的静态方法
4. **TablistListener**: 事件监听器，处理玩家加入/离开等事件

### Tablist类型

- **LOBBY**: 大厅状态，显示服务器整体信息
- **ROOM**: 房间等待状态，显示房间信息和玩家分配
- **GAME**: 游戏进行状态，显示游戏时间、存活玩家等

## 配置选项

```yaml
tablist:
  # 更新间隔（tick）
  update-interval: 20
  
  # 玩家显示设置
  player-display:
    enabled: true
    show-ping: true
    show-health: true
    show-role: true
    
    # 延迟阈值设置
    ping-thresholds:
      good: 50      # 绿色延迟阈值（毫秒）
      medium: 150   # 黄色延迟阈值（毫秒）
    
    # 血量阈值设置
    health-thresholds:
      excellent: 75   # 优秀血量阈值（百分比）
      good: 50        # 良好血量阈值（百分比）
      medium: 25      # 中等血量阈值（百分比）
```

## 命令使用

### 基本命令
```
/manhunt tablist reload    # 重新加载Tablist配置（需要管理员权限）
/manhunt tablist toggle    # 切换Tablist显示
/manhunt tablist refresh   # 刷新Tablist
```

## 集成说明

### 游戏状态集成
- 游戏开始时自动切换到游戏Tablist
- 玩家死亡/复活时自动更新存活玩家数量
- 游戏结束时自动切换回大厅Tablist

### 房间系统集成
- 玩家加入/离开房间时自动更新Tablist
- 角色分配变化时实时更新显示
- 房间设置变更时自动刷新

### 事件驱动更新
- 玩家加入/离开服务器时自动创建/移除Tablist
- 游戏状态变化时自动更新内容
- 定时任务确保信息实时性

## 性能优化

- **异步更新**: 使用定时任务避免频繁更新
- **缓存机制**: 缓存动画帧和计算结果
- **条件更新**: 只在必要时更新Tablist内容
- **内存管理**: 及时清理不需要的Tablist实例

## 可视化元素

### 进度条
使用Unicode字符创建可视化进度条：
- `█` `▇` `▆` `▅` `▄` `▃` `▂` `▁` ` ` (9级精度)
- 支持渐变颜色（绿色→黄色→红色）

### 图标系统
- ⚔ 速通者
- 🏹 猎人  
- 👁 观察者
- ❤ 血量
- 📡 延迟
- 🔘 TPS
- 💾 内存
- 🎯 实体
- 👥 玩家

## 扩展性

系统设计支持轻松扩展：
- 添加新的Tablist类型
- 自定义显示内容
- 集成其他插件数据
- 支持多语言显示

## 注意事项

1. 需要Paper服务器或支持Adventure API的服务器
2. 建议Minecraft版本1.21+
3. 更新间隔不建议设置过低（最低10tick）
4. 大量玩家时可能需要调整更新频率

## 故障排除

### 常见问题
1. **Tablist不显示**: 检查配置文件中的`enabled`设置
2. **更新不及时**: 调整`update-interval`设置
3. **性能问题**: 增加更新间隔或禁用部分功能

### 调试命令
```
/manhunt tablist refresh   # 强制刷新Tablist
/manhunt tablist toggle    # 切换显示状态进行测试
```
