import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.Difficulty;

/**
 * 随机出生点功能测试
 */
public class RandomSpawnTest {
    
    /**
     * 测试随机出生点配置
     */
    public static void testRandomSpawnConfiguration() {
        System.out.println("=== 随机出生点配置测试 ===");
        
        // 创建房间设置
        com.projectSource.ultimateManhurt.room.RoomSettings settings = 
            new com.projectSource.ultimateManhurt.room.RoomSettings();
        
        // 测试默认值
        assert !settings.isRandomSpawnEnabled() : "默认应该禁用随机出生点";
        assert settings.getRandomSpawnDistance() == 100 : "默认距离应该是100格";
        
        // 测试启用随机出生点
        settings.setRandomSpawnEnabled(true);
        assert settings.isRandomSpawnEnabled() : "应该能够启用随机出生点";
        
        // 测试距离设置
        settings.setRandomSpawnDistance(200);
        assert settings.getRandomSpawnDistance() == 200 : "应该能够设置距离为200格";
        
        // 测试边界值
        settings.setRandomSpawnDistance(30); // 小于最小值
        assert settings.getRandomSpawnDistance() == 50 : "距离应该被限制为最小值50格";
        
        settings.setRandomSpawnDistance(600); // 大于最大值
        assert settings.getRandomSpawnDistance() == 500 : "距离应该被限制为最大值500格";
        
        System.out.println("✓ 随机出生点配置测试通过");
    }
    
    /**
     * 测试距离计算
     */
    public static void testDistanceCalculation() {
        System.out.println("=== 距离计算测试 ===");
        
        // 模拟两个位置
        MockLocation location1 = new MockLocation(0, 64, 0);
        MockLocation location2 = new MockLocation(100, 64, 0);
        
        double distance = location1.distance(location2);
        assert Math.abs(distance - 100.0) < 0.1 : "距离计算应该正确";
        
        // 测试对角线距离
        MockLocation location3 = new MockLocation(100, 64, 100);
        double diagonalDistance = location1.distance(location3);
        double expectedDistance = Math.sqrt(100*100 + 100*100);
        assert Math.abs(diagonalDistance - expectedDistance) < 0.1 : "对角线距离计算应该正确";
        
        System.out.println("✓ 距离计算测试通过");
    }
    
    /**
     * 测试随机角度生成
     */
    public static void testRandomAngleGeneration() {
        System.out.println("=== 随机角度生成测试 ===");
        
        // 生成多个随机角度，确保在有效范围内
        for (int i = 0; i < 100; i++) {
            double angle = Math.random() * 2 * Math.PI;
            assert angle >= 0 && angle <= 2 * Math.PI : "角度应该在0到2π之间";
            
            // 测试坐标计算
            int distance = 100;
            int x = (int) (distance * Math.cos(angle));
            int z = (int) (distance * Math.sin(angle));
            
            // 验证生成的坐标在合理范围内
            assert Math.abs(x) <= distance : "X坐标应该在距离范围内";
            assert Math.abs(z) <= distance : "Z坐标应该在距离范围内";
        }
        
        System.out.println("✓ 随机角度生成测试通过");
    }
    
    /**
     * 测试配置文件保存和加载
     */
    public static void testConfigurationSaveLoad() {
        System.out.println("=== 配置文件保存加载测试 ===");
        
        // 创建设置
        com.projectSource.ultimateManhurt.room.RoomSettings originalSettings = 
            new com.projectSource.ultimateManhurt.room.RoomSettings();
        originalSettings.setRandomSpawnEnabled(true);
        originalSettings.setRandomSpawnDistance(150);
        
        // 克隆设置（模拟保存和加载过程）
        com.projectSource.ultimateManhurt.room.RoomSettings clonedSettings = originalSettings.clone();
        
        // 验证克隆的设置
        assert clonedSettings.isRandomSpawnEnabled() == originalSettings.isRandomSpawnEnabled() : 
            "克隆的随机出生点启用状态应该一致";
        assert clonedSettings.getRandomSpawnDistance() == originalSettings.getRandomSpawnDistance() : 
            "克隆的随机出生点距离应该一致";
        
        System.out.println("✓ 配置文件保存加载测试通过");
    }
    
    /**
     * 模拟Location类用于测试
     */
    static class MockLocation {
        private double x, y, z;
        
        public MockLocation(double x, double y, double z) {
            this.x = x;
            this.y = y;
            this.z = z;
        }
        
        public double distance(MockLocation other) {
            double dx = this.x - other.x;
            double dy = this.y - other.y;
            double dz = this.z - other.z;
            return Math.sqrt(dx*dx + dy*dy + dz*dz);
        }
        
        public int getBlockX() { return (int) x; }
        public int getBlockY() { return (int) y; }
        public int getBlockZ() { return (int) z; }
    }
    
    /**
     * 运行所有测试
     */
    public static void main(String[] args) {
        System.out.println("开始随机出生点功能测试...\n");
        
        try {
            testRandomSpawnConfiguration();
            testDistanceCalculation();
            testRandomAngleGeneration();
            testConfigurationSaveLoad();
            
            System.out.println("\n🎉 所有测试通过！随机出生点功能实现正确。");
            
        } catch (AssertionError e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("❌ 测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
