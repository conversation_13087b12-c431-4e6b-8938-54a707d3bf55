# 种子设置GUI测试指南

## 测试环境准备

1. 启动Minecraft服务器
2. 加载UltimateManhurt插件
3. 创建一个房间
4. 打开房间设置GUI
5. 确认操作模式切换功能正常

## 功能测试清单

### 1. GUI打开测试

#### 1.1 鼠标操作模式下打开
- [ ] 切换到鼠标操作模式
- [ ] 左键点击世界种子设置项
- [ ] 成功打开种子设置GUI
- [ ] GUI标题显示为"世界种子设置"
- [ ] GUI大小为54格（6行9列）

#### 1.2 聊天输入模式下的行为
- [ ] 切换到聊天输入模式
- [ ] 点击世界种子设置项
- [ ] 直接进入聊天输入状态（不打开GUI）
- [ ] 可以在聊天栏中输入种子值
- [ ] 输入完成后正确应用种子设置

#### 1.3 快速随机种子测试
- [ ] 在鼠标操作模式下
- [ ] Shift+左键点击世界种子
- [ ] 种子直接设置为0（随机）
- [ ] 显示成功消息
- [ ] GUI自动刷新

### 2. 数字输入功能测试

#### 2.1 基本数字输入
- [ ] 点击数字按钮1-9，正确输入对应数字
- [ ] 点击数字按钮0，正确输入0
- [ ] 当前输入显示区域实时更新
- [ ] 数字按钮描述显示当前输入状态

#### 2.2 连续数字输入
- [ ] 连续点击多个数字按钮
- [ ] 数字按正确顺序组合
- [ ] 实时显示完整的输入数字
- [ ] 长度计数器正确显示位数

#### 2.3 输入长度限制
- [ ] 输入18位数字后
- [ ] 继续点击数字按钮无效
- [ ] 显示"种子长度已达到最大限制"错误提示
- [ ] 播放错误音效

### 3. 功能按钮测试

#### 3.1 正负号切换测试
- [ ] 点击±按钮，切换到负数模式
- [ ] 按钮描述更新为"负数"
- [ ] 当前输入显示区域显示负号
- [ ] 再次点击切换回正数模式
- [ ] 按钮描述更新为"正数"

#### 3.2 退格功能测试
- [ ] 输入几位数字后点击退格按钮
- [ ] 最后一位数字被删除
- [ ] 当前输入显示正确更新
- [ ] 显示"已删除最后一位"提示
- [ ] 在空输入状态下点击退格
- [ ] 显示"没有可删除的内容"错误提示

#### 3.3 清空功能测试
- [ ] 输入一些数字和设置正负号
- [ ] 点击清空按钮
- [ ] 所有输入被清空
- [ ] 正负号重置为正数
- [ ] 显示"已清空输入"提示
- [ ] 界面状态完全重置

### 4. 预设种子测试

#### 4.1 预设种子显示测试
- [ ] 确认显示6个预设种子按钮
- [ ] 每个按钮显示正确的名称和种子值：
  - [ ] 经典404 (404)
  - [ ] 地狱666 (666)
  - [ ] 幸运888 (888)
  - [ ] 简单数字 (1234567890)
  - [ ] 负数种子 (-1234567890)
  - [ ] 时间种子 (基于时间的值)

#### 4.2 预设种子应用测试
- [ ] 点击任意预设种子按钮
- [ ] 种子值立即应用到房间设置
- [ ] 显示成功设置消息
- [ ] 自动返回房间设置GUI
- [ ] 房间设置GUI中显示正确的种子值

### 5. 智能种子生成测试

#### 5.1 随机种子生成测试
- [ ] 点击"随机种子"按钮
- [ ] 生成一个随机的long类型种子值
- [ ] 种子值应用到房间设置
- [ ] 显示包含具体种子值的成功消息
- [ ] 自动返回房间设置GUI

#### 5.2 玩家种子生成测试
- [ ] 点击"玩家种子"按钮
- [ ] 基于当前玩家用户名生成种子
- [ ] 种子值应用到房间设置
- [ ] 显示包含用户名和种子值的成功消息
- [ ] 同一玩家多次点击应生成相同种子

### 6. 确认和返回功能测试

#### 6.1 确认设置测试
- [ ] 通过按钮输入一个种子值
- [ ] 点击"确认设置"按钮
- [ ] 种子值正确应用到房间设置
- [ ] 显示成功设置消息
- [ ] 自动返回房间设置GUI
- [ ] 房间设置GUI显示正确的种子值

#### 6.2 返回功能测试
- [ ] 在种子设置GUI中点击"返回"按钮
- [ ] 成功返回房间设置GUI
- [ ] 未保存的输入被丢弃
- [ ] 房间设置保持原有状态

### 7. 界面显示测试

#### 7.1 当前输入显示测试
- [ ] 当前输入区域正确显示种子值
- [ ] 空输入时显示"0"
- [ ] 负数正确显示负号
- [ ] 长度计数器准确显示位数
- [ ] 操作提示信息清晰易懂

#### 7.2 按钮状态更新测试
- [ ] 数字按钮描述包含当前输入状态
- [ ] 正负号按钮正确显示当前符号状态
- [ ] 确认按钮显示当前将要设置的种子值
- [ ] 所有按钮图标和颜色合适

### 8. 错误处理测试

#### 8.1 数值溢出测试
- [ ] 输入接近Long.MAX_VALUE的数字
- [ ] 系统正确处理不会崩溃
- [ ] 超出范围时返回0或合理默认值

#### 8.2 边界情况测试
- [ ] 输入0值的处理
- [ ] 输入最大负数的处理
- [ ] 空输入状态的各种操作
- [ ] 连续快速点击的处理

### 9. 用户体验测试

#### 9.1 操作流畅性测试
- [ ] 按钮点击响应迅速
- [ ] 界面更新无延迟
- [ ] 音效反馈及时
- [ ] 连续操作无卡顿

#### 9.2 信息清晰度测试
- [ ] 所有按钮功能一目了然
- [ ] 当前状态显示清楚
- [ ] 错误提示信息明确
- [ ] 成功消息内容详细

### 10. 集成测试

#### 10.1 与房间设置GUI集成测试
- [ ] 从房间设置GUI正确跳转
- [ ] 设置完成后正确返回
- [ ] 种子值在房间设置GUI中正确显示
- [ ] 操作模式状态保持一致

#### 10.2 与聊天输入的兼容性测试
- [ ] 鼠标操作模式下右键可打开聊天输入
- [ ] 聊天输入模式下点击直接进入聊天输入
- [ ] 聊天输入和GUI设置结果一致
- [ ] 两种模式可以正常切换使用
- [ ] 聊天输入支持0值表示随机种子

## 测试结果记录

### 通过的测试项
- [ ] GUI打开功能
- [ ] 数字输入功能
- [ ] 功能按钮操作
- [ ] 预设种子选择
- [ ] 智能种子生成
- [ ] 确认和返回功能
- [ ] 界面显示效果
- [ ] 错误处理机制
- [ ] 用户体验
- [ ] 系统集成

### 发现的问题
记录测试过程中发现的任何问题：

1. 
2. 
3. 

### 改进建议
记录可能的改进建议：

1. 
2. 
3. 

## 测试完成确认

- [ ] 所有核心功能正常工作
- [ ] 用户界面友好易用
- [ ] 错误处理完善
- [ ] 性能表现良好
- [ ] 与现有系统集成良好
- [ ] 功能符合设计要求

测试人员：___________
测试日期：___________
测试版本：___________
