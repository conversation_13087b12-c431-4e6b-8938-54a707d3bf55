# 猪灵光环和萨满狗战斗AI修复

## 🐷 猪灵光环伤害机制修复

### 问题描述
**修复前：** 猪灵光环给速通者添加虚弱效果作为"伤害加深标记"
**问题：** 虚弱效果会降低攻击力，这不是我们想要的效果

**修复后：** 猪灵光环直接增加对速通者的伤害，不添加任何负面效果

### 技术实现

**光环效果更新：**
```java
// 修复前（错误）
// 伤害加深标记（通过虚弱效果表示，实际伤害加深在伤害事件中处理）
onlinePlayer.addPotionEffect(new PotionEffect(PotionEffectType.WEAKNESS, 60, 0));

// 修复后（正确）
// 给范围内的速通者高亮标记（伤害加深在伤害事件中处理）
onlinePlayer.addPotionEffect(new PotionEffect(PotionEffectType.GLOWING, 60, 0)); // 只有高亮效果
```

**伤害加深处理：**
```java
/**
 * 处理猪灵光环伤害加深
 */
private void handlePiglinAuraDamageBoost(EntityDamageByEntityEvent event, Player attacker, Player victim) {
    // 检查受害者是否是速通者
    if (gameSession.getPlayerRole(victim.getUniqueId()) != PlayerRole.SPEEDRUNNER) {
        return;
    }
    
    // 寻找范围内的猪灵
    for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
        Profession profession = plugin.getProfessionManager().getPlayerProfession(onlinePlayer.getUniqueId());
        if (profession != Profession.PIGLIN) {
            continue;
        }
        
        // 检查是否是同一游戏会话的猎人
        if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) != PlayerRole.HUNTER) {
            continue;
        }
        
        // 检查距离
        double distance = victim.getLocation().distance(onlinePlayer.getLocation());
        if (distance <= 30.0) { // 30格范围内
            // 增加10%伤害
            double originalDamage = event.getDamage();
            double boostedDamage = originalDamage * 1.10;
            event.setDamage(boostedDamage);
            
            return; // 找到一个猪灵就足够了
        }
    }
}
```

### 效果对比

**修复前：**
- ❌ 速通者获得虚弱效果（降低攻击力）
- ❌ 伤害加深效果不明确
- ❌ 负面效果影响游戏体验

**修复后：**
- ✅ 速通者只获得高亮效果（便于识别）
- ✅ 直接增加10%伤害
- ✅ 清晰的伤害机制

## 🐕 萨满狗战斗AI修复

### 问题描述
**修复前：** 萨满攻击敌人或被攻击时，狗无动于衷，不参与战斗
**问题：** 狗的AI不够智能，无法保护萨满或协助攻击

**修复后：** 萨满攻击敌人或被攻击时，狗会主动攻击相应目标

### 技术实现

**战斗事件监听：**
```java
/**
 * 处理萨满狗战斗AI
 */
private void handleShamanDogCombat(Player attacker, Player victim) {
    // 检查攻击者是否是萨满
    Profession attackerProfession = plugin.getProfessionManager().getPlayerProfession(attacker.getUniqueId());
    if (attackerProfession == Profession.SHAMAN) {
        // 萨满攻击敌人，让狗攻击目标
        plugin.getProfessionManager().getPassiveSkillHandler().commandShamanDogsToAttack(attacker, victim);
    }
    
    // 检查受害者是否是萨满
    Profession victimProfession = plugin.getProfessionManager().getPlayerProfession(victim.getUniqueId());
    if (victimProfession == Profession.SHAMAN) {
        // 萨满被攻击，让狗攻击攻击者
        plugin.getProfessionManager().getPassiveSkillHandler().commandShamanDogsToAttack(victim, attacker);
    }
}
```

**狗攻击指令系统：**
```java
/**
 * 命令萨满的狗攻击指定目标
 */
public void commandShamanDogsToAttack(Player shaman, Player target) {
    UUID shamanId = shaman.getUniqueId();
    List<Wolf> dogs = shamanDogs.get(shamanId);
    
    if (dogs == null || dogs.isEmpty()) {
        return; // 没有狗
    }
    
    // 检查目标是否有效
    if (target == null || !target.isOnline() || target.isDead()) {
        return;
    }
    
    // 检查萨满和目标是否在同一世界
    if (!shaman.getWorld().equals(target.getWorld())) {
        return;
    }
    
    // 命令所有有效的狗攻击目标
    int dogsCommanded = 0;
    for (Wolf dog : dogs) {
        if (dog != null && !dog.isDead() && dog.isValid()) {
            // 检查狗是否在合理范围内（50格内）
            double distance = dog.getLocation().distance(target.getLocation());
            if (distance <= 50.0) {
                dog.setTarget(target);
                dogsCommanded++;
            }
        }
    }
    
    if (dogsCommanded > 0) {
        plugin.getLogger().info("萨满 " + shaman.getName() + " 的 " + dogsCommanded + " 只狗开始攻击 " + target.getName());
    }
}
```

### 智能特性

**1. 双向触发：**
- **萨满攻击敌人：** 狗协助攻击萨满的目标
- **萨满被攻击：** 狗保护萨满，攻击攻击者

**2. 安全检查：**
- **目标有效性：** 检查目标是否在线且未死亡
- **世界一致性：** 确保萨满和目标在同一世界
- **狗状态检查：** 只命令有效且存活的狗

**3. 范围限制：**
- **50格范围：** 只有50格内的狗会响应攻击指令
- **避免过远追击：** 防止狗跑得太远无法回到萨满身边

**4. 日志记录：**
- **详细记录：** 记录有多少只狗响应了攻击指令
- **便于调试：** 方便排查狗AI问题

## 🎮 游戏体验改进

### 猪灵光环体验

**对速通者：**
- ✅ **清晰威胁：** 高亮效果明确显示受到光环影响
- ✅ **真实伤害：** 直接增加10%伤害，效果明确
- ✅ **无负面干扰：** 不会影响速通者的攻击能力

**对猎人：**
- ✅ **明确增益：** 对速通者造成更多伤害
- ✅ **战术价值：** 鼓励靠近猪灵获得伤害加成

### 萨满狗战斗体验

**对萨满：**
- ✅ **主动支援：** 狗会主动协助攻击
- ✅ **被动保护：** 被攻击时狗会反击
- ✅ **智能AI：** 狗的行为更符合预期

**对敌人：**
- ✅ **多重威胁：** 需要同时应对萨满和狗群
- ✅ **战术考虑：** 攻击萨满会引来狗群反击

## 📊 技术细节

### 猪灵光环伤害计算
```java
// 原始伤害
double originalDamage = event.getDamage();

// 增加10%伤害
double boostedDamage = originalDamage * 1.10;

// 应用新伤害
event.setDamage(boostedDamage);
```

### 狗攻击目标设置
```java
// 设置狗的攻击目标
dog.setTarget(target);

// 这会让狗：
// 1. 停止当前行为
// 2. 追击指定目标
// 3. 攻击目标直到目标死亡或逃离
```

### 事件处理流程
```
玩家攻击事件触发
    ↓
检查萨满龙卷风暴无敌状态
    ↓
处理猪灵光环伤害加深
    ↓
处理萨满狗战斗AI
    ↓
处理其他被动技能
```

## 🧪 测试建议

### 猪灵光环测试
1. **伤害加深测试：**
   ```
   1. 猪灵站在固定位置
   2. 猎人攻击30格内的速通者
   3. 验证伤害是否增加10%
   4. 确认速通者只有高亮效果，无虚弱效果
   ```

2. **范围测试：**
   ```
   1. 测试30格边界的伤害加成
   2. 验证超出30格时无伤害加成
   ```

### 萨满狗AI测试
1. **攻击协助测试：**
   ```
   1. 萨满攻击敌人
   2. 观察狗是否攻击相同目标
   3. 验证50格范围限制
   ```

2. **保护反击测试：**
   ```
   1. 敌人攻击萨满
   2. 观察狗是否攻击攻击者
   3. 验证狗的反应速度
   ```

3. **边界情况测试：**
   ```
   1. 目标死亡时狗的行为
   2. 萨满和目标在不同世界时的处理
   3. 狗死亡或无效时的处理
   ```

## 🎯 修复总结

成功修复了两个重要问题：

### 猪灵光环修复
- ✅ **移除虚弱效果：** 不再给速通者添加负面效果
- ✅ **直接伤害加深：** 在伤害事件中直接增加10%伤害
- ✅ **保留高亮效果：** 便于识别受光环影响的速通者
- ✅ **清晰机制：** 伤害加深效果明确可见

### 萨满狗AI修复
- ✅ **智能战斗：** 狗会响应萨满的攻击和被攻击
- ✅ **双向触发：** 攻击协助和保护反击都已实现
- ✅ **安全检查：** 完善的目标和状态验证
- ✅ **范围控制：** 50格范围避免狗跑得太远

### 关键改进点
1. **猪灵光环：** 从"虚弱标记"改为"直接伤害加深"
2. **萨满狗AI：** 从"被动跟随"改为"主动战斗"
3. **用户体验：** 更直观的效果和更智能的AI行为
4. **技术实现：** 更清晰的代码逻辑和更完善的边界处理

现在猪灵的光环提供真正的伤害加成，萨满的狗也会智能地参与战斗！🐷⚡🐕✨

**重要特点：**
- 真实的10%伤害加深效果
- 智能的狗战斗AI系统
- 完善的安全检查和边界处理
- 优化的用户体验和反馈
