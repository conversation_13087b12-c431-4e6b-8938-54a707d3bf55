package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.kit.StartKit;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.List;

/**
 * 自定义模板管理GUI
 * 显示和管理房主的自定义模板
 */
public class CustomTemplateGui extends BaseGui {
    
    private final Room room;
    private final PlayerRole targetRole;
    private final List<StartKit> playerTemplates;
    
    public CustomTemplateGui(UltimateManhurt plugin, Player player, Room room, PlayerRole targetRole) {
        super(plugin, player, "我的自定义模板", 54);
        this.room = room;
        this.targetRole = targetRole;
        this.playerTemplates = plugin.getCustomTemplateManager().getPlayerTemplates(player.getUniqueId());
        setupGui();
    }
    
    @Override
    protected void setupGui() {
        // 标题
        setItem(4, createItem(Material.WRITABLE_BOOK, "<gold><bold>我的自定义模板",
            "<gray>管理你创建的装备包模板",
            "<gray>模板数量: <white>" + playerTemplates.size()));
        
        // 显示自定义模板
        displayCustomTemplates();
        
        // 控制按钮
        setupControlButtons();
        
        // 装饰性物品
        fillEmptySlots();
    }
    
    /**
     * 显示自定义模板
     */
    private void displayCustomTemplates() {
        if (playerTemplates.isEmpty()) {
            setItem(22, createItem(Material.BARRIER, "<red>暂无自定义模板",
                "<gray>你还没有创建任何自定义模板",
                "<yellow>在装备包编辑器中保存模板"));
            return;
        }
        
        // 显示模板（最多显示28个）
        int startSlot = 10;
        for (int i = 0; i < Math.min(playerTemplates.size(), 28); i++) {
            StartKit template = playerTemplates.get(i);
            int slot = getSlotForIndex(startSlot, i);
            
            if (slot != -1) {
                setItem(slot, createTemplateItem(template, i));
            }
        }
    }
    
    /**
     * 获取索引对应的槽位
     */
    private int getSlotForIndex(int startSlot, int index) {
        int row = index / 7;
        int col = index % 7;
        
        if (row >= 4) return -1; // 最多4行
        
        return startSlot + row * 9 + col;
    }
    
    /**
     * 创建模板物品
     */
    private ItemStack createTemplateItem(StartKit template, int index) {
        List<String> lore = new ArrayList<>();
        lore.add("<gray>模板名称: <white>" + template.getName());
        lore.add("<gray>描述: <white>" + template.getDescription());
        lore.add("<gray>装备摘要: <white>" + template.getSummary());
        lore.add("<gray>物品数量: <white>" + template.getItemCount());
        lore.add("");
        
        if (targetRole != null) {
            String roleText = targetRole == PlayerRole.SPEEDRUNNER ? "速通者" : "捕猎者";
            lore.add("<yellow>左键: 应用到" + roleText + "装备包");
        } else {
            lore.add("<yellow>左键: 选择应用角色");
        }
        lore.add("<yellow>右键: 预览模板内容");
        lore.add("<yellow>Shift+右键: 删除模板");
        
        // 根据模板内容选择图标
        Material icon = getTemplateIcon(template);
        
        return createItem(icon, "<aqua><bold>" + template.getName(),
            lore.toArray(new String[0]));
    }
    
    /**
     * 根据模板内容获取图标
     */
    private Material getTemplateIcon(StartKit template) {
        // 检查模板中的主要武器来决定图标
        for (ItemStack item : template.getItems().values()) {
            if (item != null) {
                Material type = item.getType();
                if (type.name().contains("SWORD")) {
                    return type;
                } else if (type == Material.BOW) {
                    return Material.BOW;
                } else if (type.name().contains("PICKAXE")) {
                    return type;
                }
            }
        }
        
        // 默认图标
        return Material.CHEST;
    }
    
    /**
     * 设置控制按钮
     */
    private void setupControlButtons() {
        // 创建新模板
        setItem(45, createItem(Material.CRAFTING_TABLE, "<green><bold>创建新模板",
            "<gray>在装备包编辑器中创建新模板",
            "<yellow>点击进入编辑器"));
        
        // 模板统计
        String stats = plugin.getCustomTemplateManager().getTemplateStats();
        setItem(46, createItem(Material.BOOK, "<blue><bold>模板统计",
            "<gray>" + stats,
            "<gray>你的模板: <white>" + playerTemplates.size() + " 个"));
        
        // 帮助信息
        setItem(47, createItem(Material.KNOWLEDGE_BOOK, "<yellow><bold>帮助",
            "<gray>自定义模板功能说明：",
            "<gray>• 在编辑器中点击\"保存为模板\"",
            "<gray>• 模板会自动保存到文件",
            "<gray>• 可以在任何房间中使用",
            "<gray>• 只有创建者可以删除模板"));
        
        // 返回
        setItem(53, createItem(Material.ARROW, "<red>返回",
            "<gray>返回模板选择"));
    }
    
    /**
     * 填充空槽位
     */
    private void fillEmptySlots() {
        for (int i = 0; i < inventory.getSize(); i++) {
            if (inventory.getItem(i) == null) {
                setItem(i, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));
            }
        }
    }
    
    @Override
    public void handleClick(InventoryClickEvent event) {
        event.setCancelled(true);
        
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        if (event.getClickedInventory() != this.inventory) {
            return;
        }
        
        ItemStack item = event.getCurrentItem();
        if (item == null || item.getType() == Material.AIR) {
            return;
        }
        
        this.playClickSound();
        
        int slot = event.getSlot();
        boolean isRightClick = event.isRightClick();
        boolean isShiftClick = event.isShiftClick();
        
        // 检查是否点击了模板
        int templateIndex = getTemplateIndexBySlot(slot);
        if (templateIndex != -1 && templateIndex < playerTemplates.size()) {
            handleTemplateClick(templateIndex, isRightClick, isShiftClick);
            return;
        }
        
        // 控制按钮处理
        switch (slot) {
            case 45: // 创建新模板
                handleCreateTemplate();
                break;
            case 46: // 模板统计
                handleStats();
                break;
            case 47: // 帮助
                handleHelp();
                break;
            case 53: // 返回
                handleBack();
                break;
        }
    }
    
    /**
     * 根据槽位获取模板索引
     */
    private int getTemplateIndexBySlot(int slot) {
        // 模板显示区域：10-16, 19-25, 28-34, 37-43
        int[] validSlots = {10, 11, 12, 13, 14, 15, 16,
                           19, 20, 21, 22, 23, 24, 25,
                           28, 29, 30, 31, 32, 33, 34,
                           37, 38, 39, 40, 41, 42, 43};
        
        for (int i = 0; i < validSlots.length; i++) {
            if (validSlots[i] == slot) {
                return i;
            }
        }
        
        return -1;
    }
    
    /**
     * 处理模板点击
     */
    private void handleTemplateClick(int templateIndex, boolean isRightClick, boolean isShiftClick) {
        StartKit template = playerTemplates.get(templateIndex);
        
        if (isShiftClick && isRightClick) {
            // Shift+右键：删除模板
            handleDeleteTemplate(template);
        } else if (isRightClick) {
            // 右键：预览模板
            plugin.getStartKitManager().previewKit(player, template);
        } else {
            // 左键：应用模板
            handleApplyTemplate(template);
        }
    }
    
    /**
     * 应用模板
     */
    private void handleApplyTemplate(StartKit template) {
        if (targetRole != null) {
            StartKit newKit = new StartKit(template);
            
            if (targetRole == PlayerRole.SPEEDRUNNER) {
                room.getSettings().setSpeedrunnerKit(newKit);
            } else {
                room.getSettings().setHunterKit(newKit);
            }
            
            String roleText = targetRole == PlayerRole.SPEEDRUNNER ? "速通者" : "捕猎者";
            sendSuccess("已将模板 \"" + template.getName() + "\" 应用到" + roleText + "装备包");
            playSuccessSound();
            
            // 返回装备包管理
            close();
            plugin.getGuiManager().openStartKitGui(player, room);
        } else {
            sendError("请先选择要应用的角色");
        }
    }
    
    /**
     * 删除模板
     */
    private void handleDeleteTemplate(StartKit template) {
        if (plugin.getCustomTemplateManager().deleteCustomTemplate(player, template.getTemplateId())) {
            // 刷新界面
            playerTemplates.remove(template);
            refresh();
        }
    }
    
    /**
     * 创建新模板
     */
    private void handleCreateTemplate() {
        if (targetRole != null) {
            close();
            plugin.getGuiManager().openStartKitEditorGui(player, room, targetRole);
        } else {
            sendError("请先选择要编辑的角色装备包");
        }
    }
    
    /**
     * 显示统计信息
     */
    private void handleStats() {
        sendInfo("模板统计信息已显示在物品描述中");
    }
    
    /**
     * 显示帮助
     */
    private void handleHelp() {
        sendInfo("自定义模板帮助信息已显示在物品描述中");
    }
    
    /**
     * 返回
     */
    private void handleBack() {
        close();
        plugin.getGuiManager().openStartKitTemplateGui(player, room, targetRole);
    }
}
