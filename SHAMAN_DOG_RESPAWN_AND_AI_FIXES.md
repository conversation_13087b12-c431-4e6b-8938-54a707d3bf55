# 萨满狗复活和AI修复

## 🐕 修复概览

### 主要修复
1. **复活位置修复：** 防止狗复活时卡在地里
2. **复活数量限制：** 最多只复活2只初始狗
3. **默认攻击目标：** 狗默认攻击最近的猎人

### 修复原因
- **位置问题：** 原有生成算法可能导致狗卡在地下
- **数量失控：** 击杀奖励的狗也会复活，导致狗数量无限增长
- **AI优化：** 狗需要有明确的攻击目标

## 🔧 复活位置修复

### 问题分析
**原始问题：** 狗复活时可能生成在地下或不安全的位置

### 修复方案

**1. 改进的位置搜索算法：**
```java
/**
 * 寻找安全的狗生成位置
 */
private Location findSafeDogSpawnLocation(Location center) {
    for (int attempts = 0; attempts < 20; attempts++) { // 增加尝试次数
        double angle = random.nextDouble() * 2 * Math.PI;
        double distance = 2 + random.nextDouble() * 4; // 2-6格距离
        
        double x = center.getX() + Math.cos(angle) * distance;
        double z = center.getZ() + Math.sin(angle) * distance;
        
        Location testLoc = new Location(center.getWorld(), x, center.getY(), z);
        
        // 优先向上寻找安全位置
        for (int yOffset = 0; yOffset <= 10; yOffset++) {
            testLoc.setY(center.getY() + yOffset);
            if (isSafeDogLocation(testLoc)) {
                testLoc.setY(testLoc.getY() + 0.5); // 稍微抬高一点
                return testLoc;
            }
        }
        
        // 如果向上找不到，尝试向下找
        for (int yOffset = -1; yOffset >= -5; yOffset--) {
            testLoc.setY(center.getY() + yOffset);
            if (isSafeDogLocation(testLoc)) {
                testLoc.setY(testLoc.getY() + 0.5); // 稍微抬高一点
                return testLoc;
            }
        }
    }
    
    // 如果找不到安全位置，在萨满正上方生成
    return center.clone().add(0, 1, 0);
}
```

**2. 增强的安全检查：**
```java
/**
 * 检查位置是否适合狗生成
 */
private boolean isSafeDogLocation(Location loc) {
    Material ground = loc.getBlock().getType();
    Material above = loc.clone().add(0, 1, 0).getBlock().getType();
    Material above2 = loc.clone().add(0, 2, 0).getBlock().getType();
    Material below = loc.clone().add(0, -1, 0).getBlock().getType();

    // 检查地面是否安全（不是空气，不是危险方块）
    boolean groundSafe = ground.isSolid() && 
                       ground != Material.LAVA && 
                       ground != Material.WATER &&
                       ground != Material.CACTUS &&
                       ground != Material.MAGMA_BLOCK;
    
    // 检查上方空间是否足够
    boolean spaceAbove = (above.isAir() || above == Material.SHORT_GRASS || above == Material.TALL_GRASS) &&
                       (above2.isAir() || above2 == Material.SHORT_GRASS || above2 == Material.TALL_GRASS);
    
    // 检查不会掉落（下方有支撑）
    boolean hasSupport = below.isSolid() || ground.isSolid();

    return groundSafe && spaceAbove && hasSupport;
}
```

### 关键改进
- ✅ **向上优先：** 优先在萨满上方寻找安全位置
- ✅ **高度调整：** 生成位置稍微抬高0.5格，避免卡在地面
- ✅ **危险检测：** 避免在岩浆、水、仙人掌等危险位置生成
- ✅ **支撑检查：** 确保狗不会掉落到虚空

## 📊 复活数量限制

### 问题分析
**原始问题：** 所有狗死亡后都会复活，包括击杀奖励获得的狗，导致狗数量无限增长

### 修复方案

**1. 初始狗数量记录：**
```java
// 萨满初始狗数量记录 <萨满UUID, 初始狗数量>
private final Map<UUID, Integer> shamanInitialDogCount = new HashMap<>();
```

**2. 记录初始狗数量：**
```java
// 生成两只初始狗
for (int i = 0; i < 2; i++) {
    Wolf dog = spawnDogForShaman(shaman);
    if (dog != null) {
        dogs.add(dog);
    }
}

// 记录初始狗数量（用于限制复活）
shamanInitialDogCount.put(shamanId, dogs.size());
```

**3. 限制复活逻辑：**
```java
// 检查是否可以复活（只有初始狗可以复活，且最多2只）
Integer initialCount = shamanInitialDogCount.get(shamanId);
if (initialCount != null && initialCount > 0) {
    // 安排90秒后复活
    scheduleDogRespawn(dog, shaman);
    // 减少可复活的初始狗数量
    shamanInitialDogCount.put(shamanId, initialCount - 1);
    plugin.getLogger().info("萨满 " + shaman.getName() + " 的初始狗死亡，将在90秒后复活，剩余可复活狗数量: " + (initialCount - 1));
} else {
    plugin.getLogger().info("萨满 " + shaman.getName() + " 的额外狗死亡，不会复活");
}
```

### 复活规则
- ✅ **初始狗：** 最初的2只狗可以复活
- ❌ **奖励狗：** 击杀奖励获得的狗不会复活
- 🔢 **数量限制：** 最多只能复活2次（对应2只初始狗）
- ⏰ **复活间隔：** 每只狗死亡后90秒复活

## 🎯 默认攻击目标

### 问题分析
**原始问题：** 狗生成后没有明确的攻击目标，可能会无所事事

### 修复方案

**设置默认攻击目标：**
```java
/**
 * 设置狗默认攻击猎人
 */
private void setDogToAttackHunters(Wolf dog, Player shaman) {
    GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(shaman.getUniqueId());
    if (gameSession == null) {
        return;
    }
    
    // 寻找最近的猎人作为目标
    Player nearestHunter = null;
    double nearestDistance = Double.MAX_VALUE;
    
    for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
        // 检查是否是猎人
        if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) != PlayerRole.HUNTER) {
            continue;
        }
        
        // 检查是否在同一世界
        if (!onlinePlayer.getWorld().equals(dog.getWorld())) {
            continue;
        }
        
        // 计算距离
        double distance = dog.getLocation().distance(onlinePlayer.getLocation());
        if (distance < nearestDistance && distance <= 50.0) { // 50格范围内
            nearestDistance = distance;
            nearestHunter = onlinePlayer;
        }
    }
    
    // 如果找到猎人，设置为攻击目标
    if (nearestHunter != null) {
        dog.setTarget(nearestHunter);
        plugin.getLogger().info("萨满 " + shaman.getName() + " 的狗开始攻击最近的猎人 " + nearestHunter.getName());
    }
}
```

**在狗生成时调用：**
```java
// 设置狗默认攻击猎人
setDogToAttackHunters(dog, shaman);
```

### AI特性
- 🎯 **智能目标：** 自动寻找最近的猎人作为攻击目标
- 📏 **范围限制：** 只攻击50格范围内的猎人
- 🌍 **世界检查：** 只攻击同一世界的猎人
- 🔄 **动态更新：** 配合现有的战斗AI系统

## 🎮 游戏体验改进

### 萨满玩家体验
**狗的行为更智能：**
- ✅ **主动攻击：** 狗生成后立即寻找猎人攻击
- ✅ **安全复活：** 狗复活时不会卡在地里
- ✅ **数量控制：** 不会因为复活导致狗数量失控

**战术价值：**
- 🎯 **明确目标：** 狗有明确的攻击优先级
- ⚖️ **平衡复活：** 只有初始狗可以复活，保持平衡

### 敌方玩家体验
**面对狗群时：**
- 🎯 **明确威胁：** 狗会主动攻击猎人
- ⚖️ **有限复活：** 知道最多只会复活2只狗
- 🛡️ **应对策略：** 可以通过击杀狗来永久削弱萨满

## 📊 技术细节

### 复活限制机制
```java
// 数据结构
private final Map<UUID, Integer> shamanInitialDogCount = new HashMap<>();

// 初始化时记录
shamanInitialDogCount.put(shamanId, 2); // 记录初始2只狗

// 死亡时检查
Integer initialCount = shamanInitialDogCount.get(shamanId);
if (initialCount != null && initialCount > 0) {
    // 可以复活
    shamanInitialDogCount.put(shamanId, initialCount - 1); // 减少计数
} else {
    // 不能复活
}
```

### 位置生成优化
```java
// 搜索策略
1. 在萨满周围2-6格范围内随机选择位置
2. 优先向上搜索（0到+10格）
3. 如果向上找不到，向下搜索（-1到-5格）
4. 每个位置都进行安全检查
5. 生成位置稍微抬高0.5格
6. 最后备选：萨满正上方1格
```

### AI目标选择
```java
// 目标选择逻辑
1. 遍历所有在线玩家
2. 筛选出猎人角色
3. 检查是否在同一世界
4. 计算距离，选择最近的（50格内）
5. 设置为狗的攻击目标
```

## 🧪 测试建议

### 复活位置测试
1. **地下复活测试：**
   ```
   1. 让萨满在地下
   2. 让狗死亡
   3. 等待90秒复活
   4. 验证狗是否在安全位置复活
   ```

2. **复杂地形测试：**
   ```
   1. 在山地、建筑物等复杂地形测试
   2. 验证狗复活位置是否合理
   3. 确认狗不会卡在墙里或掉落
   ```

### 复活数量测试
1. **初始狗复活测试：**
   ```
   1. 击杀萨满的2只初始狗
   2. 验证都会在90秒后复活
   3. 再次击杀，验证不会再复活
   ```

2. **奖励狗复活测试：**
   ```
   1. 萨满击杀猎人获得奖励狗
   2. 击杀奖励狗
   3. 验证奖励狗不会复活
   ```

### AI目标测试
1. **默认攻击测试：**
   ```
   1. 萨满生成狗
   2. 观察狗是否主动攻击最近的猎人
   3. 验证攻击范围是否为50格
   ```

2. **目标切换测试：**
   ```
   1. 狗攻击一个猎人
   2. 萨满攻击另一个猎人
   3. 验证狗是否会切换目标
   ```

## 🎉 修复总结

成功修复了萨满狗伙伴的三个关键问题：

### 关键改进
- 🏠 **安全复活：** 改进的位置算法确保狗不会卡在地里
- 🔢 **数量控制：** 限制只有初始2只狗可以复活
- 🎯 **智能AI：** 狗默认攻击最近的猎人

### 技术价值
1. **位置算法：** 更安全、更智能的生成位置选择
2. **数量管理：** 完善的复活限制机制
3. **AI优化：** 更主动的战斗行为

### 平衡性提升
- ⚖️ **复活限制：** 防止狗数量无限增长
- 🎯 **明确目标：** 狗有明确的攻击优先级
- 🛡️ **反制机会：** 敌方可以通过击杀狗永久削弱萨满

现在萨满的狗伙伴系统更加完善：安全的复活机制、合理的数量限制、智能的攻击AI！🐕🎯✨

**重要特点：**
- 防卡地的安全复活
- 最多2只狗的复活限制
- 主动攻击猎人的智能AI
- 完善的边界情况处理
