# 职业选择GUI自动重新打开功能

## 🎯 功能需求

**用户需求：**
- 职业选择GUI如果被玩家退出，应该自动重新打开
- 实现类似Ban Pick系统的强制选择机制
- 确保玩家必须完成职业选择才能继续游戏

## 🔧 实现方案

### 1. 活跃GUI追踪系统

**在 `ProfessionSelectionGui` 中添加静态追踪：**
```java
// 静态追踪正在进行职业选择的玩家
private static final Map<UUID, ProfessionSelectionGui> activeProfessionGuis = new HashMap<>();
```

**构造函数中注册：**
```java
public ProfessionSelectionGui(UltimateManhurt plugin, Player player, GameSession gameSession) {
    super(plugin, player, "选择职业", 54);
    this.gameSession = gameSession;
    this.playerRole = gameSession.getPlayerRole(player.getUniqueId());
    
    // 将此GUI添加到活跃追踪中
    activeProfessionGuis.put(player.getUniqueId(), this);
    
    setupGui();
}
```

### 2. 职业选择完成时移除追踪

**在职业选择成功后移除：**
```java
private void handleProfessionSelection(Profession profession) {
    boolean success = plugin.getProfessionManager().setProfession(player.getUniqueId(), profession);
    
    if (success) {
        ComponentUtil.sendMessage(player, ComponentUtil.success(
            "成功选择职业: " + profession.getDisplayName()));
        playSuccessSound();
        
        // 从活跃追踪中移除（职业选择完成）
        activeProfessionGuis.remove(player.getUniqueId());
        
        // 关闭GUI
        player.closeInventory();
        
        // 通知游戏会话职业选择完成
        gameSession.onPlayerProfessionSelected(player.getUniqueId(), profession);
    }
}
```

### 3. GUI关闭事件处理

**在 `GuiListener` 中添加重新打开逻辑：**
```java
@EventHandler
public void onInventoryClose(InventoryCloseEvent event) {
    if (!(event.getPlayer() instanceof Player player)) {
        return;
    }

    BaseGui gui = plugin.getGuiManager().getOpenGui(player);
    if (gui != null && gui.isThisInventory(event.getInventory())) {
        plugin.getGuiManager().closeGui(player);
    }
    
    // 检查是否是职业选择GUI被关闭
    if (ProfessionSelectionGui.hasActiveProfessionGui(player.getUniqueId())) {
        ProfessionSelectionGui professionGui = ProfessionSelectionGui.getActiveProfessionGui(player.getUniqueId());
        
        // 检查玩家是否已经选择了职业
        if (!plugin.getProfessionManager().hasPlayerProfession(player.getUniqueId())) {
            // 玩家还没有选择职业，延迟重新打开GUI
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                if (player.isOnline() && 
                    !plugin.getProfessionManager().hasPlayerProfession(player.getUniqueId()) &&
                    ProfessionSelectionGui.hasActiveProfessionGui(player.getUniqueId())) {
                    
                    // 重新打开职业选择GUI
                    plugin.getGuiManager().openProfessionSelectionGui(player, professionGui.getGameSession());
                }
            }, 2L); // 延迟2 tick，避免冲突
        }
    }
}
```

### 4. 管理方法

**添加静态管理方法：**
```java
/**
 * 检查玩家是否有活跃的职业选择GUI
 */
public static boolean hasActiveProfessionGui(UUID playerId) {
    return activeProfessionGuis.containsKey(playerId);
}

/**
 * 获取玩家的活跃职业选择GUI
 */
public static ProfessionSelectionGui getActiveProfessionGui(UUID playerId) {
    return activeProfessionGuis.get(playerId);
}

/**
 * 移除玩家的活跃职业选择GUI
 */
public static void removeActiveProfessionGui(UUID playerId) {
    activeProfessionGuis.remove(playerId);
}

/**
 * 清理所有活跃的职业选择GUI
 */
public static void clearAllActiveProfessionGuis() {
    activeProfessionGuis.clear();
}
```

### 5. 游戏结束时清理

**在 `GameSession.cleanup()` 中添加清理：**
```java
// 清理职业系统状态
if (room.getSettings().isProfessionSystemEnabled()) {
    for (UUID playerId : playerRoles.keySet()) {
        plugin.getProfessionManager().removePlayerProfession(playerId);
        plugin.getLogger().info("已清理玩家 " + playerId + " 的职业状态");
    }
    
    // 清理活跃的职业选择GUI
    ProfessionSelectionGui.clearAllActiveProfessionGuis();
    plugin.getLogger().info("已清理所有活跃的职业选择GUI");
}
```

## 🎯 工作流程

### 正常职业选择流程
1. **GUI打开：** 玩家进入职业选择阶段，GUI自动打开并注册到追踪系统
2. **玩家选择：** 玩家点击职业，选择成功后从追踪系统移除
3. **GUI关闭：** GUI正常关闭，不会重新打开

### 玩家退出GUI流程
1. **GUI关闭：** 玩家按ESC或其他方式关闭GUI
2. **状态检查：** 系统检查玩家是否在活跃追踪中且未选择职业
3. **延迟重开：** 延迟2 tick后重新打开职业选择GUI
4. **循环保护：** 直到玩家选择职业或游戏结束

### 游戏结束清理流程
1. **状态清理：** 清理所有玩家的职业状态
2. **GUI清理：** 清理所有活跃的职业选择GUI追踪
3. **资源释放：** 确保没有内存泄漏

## 🛡️ 安全保障

### 重复打开保护
- **在线检查：** 确保玩家仍然在线
- **职业检查：** 确保玩家还没有选择职业
- **追踪检查：** 确保GUI仍在活跃追踪中

### 内存管理
- **及时清理：** 职业选择完成后立即移除追踪
- **游戏结束清理：** 游戏结束时清理所有追踪
- **静态Map管理：** 避免内存泄漏

### 延迟机制
- **2 tick延迟：** 避免与GUI关闭事件冲突
- **状态验证：** 延迟执行时再次验证所有条件

## 🧪 测试场景

### 基本功能测试
1. **正常选择：**
   ```
   1. 进入职业选择阶段
   2. 点击选择职业
   3. 确认GUI正常关闭，不会重新打开
   ```

2. **退出重开：**
   ```
   1. 进入职业选择阶段
   2. 按ESC关闭GUI
   3. 确认GUI在2 tick后自动重新打开
   ```

### 边界情况测试
1. **快速操作：**
   ```
   1. 快速多次关闭和打开GUI
   2. 确认系统稳定，没有重复GUI
   ```

2. **离线重连：**
   ```
   1. 职业选择阶段玩家离线
   2. 玩家重连后确认状态正确
   ```

3. **游戏结束：**
   ```
   1. 职业选择阶段游戏异常结束
   2. 确认所有追踪状态被正确清理
   ```

## 🎉 功能特点

### 用户体验
- **强制选择：** 确保玩家必须完成职业选择
- **无缝体验：** 自动重新打开，用户无需手动操作
- **即时反馈：** 2 tick延迟几乎无感知

### 技术实现
- **轻量级：** 使用静态Map追踪，内存占用极小
- **高效率：** 只在必要时进行检查和重新打开
- **安全性：** 多重检查确保系统稳定

### 兼容性
- **与Ban Pick一致：** 使用相同的设计模式
- **不影响其他GUI：** 只处理职业选择GUI
- **向后兼容：** 不影响现有功能

## 📊 对比Ban Pick系统

| 特性 | Ban Pick系统 | 职业选择系统 |
|------|-------------|-------------|
| **追踪方式** | 实例内Map | 静态Map |
| **重开条件** | 阶段未完成 | 职业未选择 |
| **清理时机** | 阶段完成 | 职业选择完成/游戏结束 |
| **延迟时间** | 2 tick | 2 tick |
| **状态检查** | 阶段状态 | 职业状态 |

## 🎯 总结

成功实现了职业选择GUI的自动重新打开功能：

- ✅ **强制选择：** 玩家必须完成职业选择才能继续
- ✅ **自动重开：** GUI被关闭后自动重新打开
- ✅ **安全保障：** 多重检查确保系统稳定
- ✅ **内存管理：** 及时清理避免内存泄漏
- ✅ **用户友好：** 无缝的用户体验

现在职业选择系统与Ban Pick系统一样，确保玩家必须完成选择才能继续游戏！🎮✨
