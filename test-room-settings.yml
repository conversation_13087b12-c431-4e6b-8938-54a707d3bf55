# 测试房间设置配置文件
# 用于验证装备包物品加载修复

metadata:
  name: "测试房间_20250730_092551"
  creator: "550e8400-e29b-41d4-a716-446655440000"
  created-at: "2025-07-30T09:25:51"
  version: "1.0"

# 基础设置
basic:
  game-duration-minutes: 30
  world-seed: 0
  difficulty: "NORMAL"
  spectator-game-mode: "SPECTATOR"

# 玩家设置
players:
  max-players: 10
  max-speedrunners: 1
  max-hunters: 9
  allow-spectators: true

# StartKit设置 - 这是修复的重点
start-kit:
  enabled: true
  speedrunner:
    name: "测试速通者装备"
    description: "用于测试的速通者装备包"
    template-id: "test_speedrunner"
    items:
      "0":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: DIAMOND_SWORD
        amount: 1
      "1":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: DIAMOND_PICKAXE
        amount: 1
      "2":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: GOLDEN_APPLE
        amount: 5
    extra-items:
      - ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: IRON_INGOT
        amount: 16
      - ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: DIAMOND
        amount: 8
  hunter:
    name: "测试捕猎者装备"
    description: "用于测试的捕猎者装备包"
    template-id: "test_hunter"
    items:
      "0":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: DIAMOND_SWORD
        amount: 1
      "1":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: BOW
        amount: 1
      "2":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: ARROW
        amount: 64
    extra-items:
      - ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: DIAMOND
        amount: 16
      - ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: ENDER_PEARL
        amount: 4

# 其他基本设置
game-rules:
  pvp-enabled: true
  friendly-fire: false
  keep-inventory: false
  natural-regeneration: true
  show-death-messages: true

world:
  generate-structures: true
  generate-bonus-chest: false
  enable-command-blocks: false
  do-daylight-cycle: true
  do-weather-cycle: true
  custom-spawn-logic: false

victory-mode:
  mode: "DRAGON_MODE"
  target-score: 500

milestone:
  enabled: false
