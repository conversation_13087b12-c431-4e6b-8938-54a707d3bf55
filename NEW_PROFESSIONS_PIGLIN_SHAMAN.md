# 新职业实现 - 猪灵 & 萨满

## 🆕 新增职业概览

### 捕猎者阵营 - 猪灵 🐷
- **职业名称：** 猪灵
- **职业定位：** 团队支援者，拥有光环和集结能力
- **图标：** 金锭 🥇
- **颜色：** 金色

### 速通者阵营 - 萨满 🐺
- **职业名称：** 萨满
- **职业定位：** 自然召唤师，拥有狼伙伴和风暴控制
- **图标：** 骨头 🦴
- **颜色：** 绿色

## 🐷 猪灵技能详解

### 被动技能 - 狂意
**技能描述：** 被动的使自己和一定范围内的友军获得速度一加成，使得一定范围内的速通者高亮并得到10%的伤害加深

**技术实现：**
```java
public void handlePiglinFrenzy(Player piglin) {
    Location piglinLoc = piglin.getLocation();
    
    // 给范围内的友军（猎人）速度加成
    for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
        if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) == PlayerRole.HUNTER) {
            double distance = onlinePlayer.getLocation().distance(piglinLoc);
            if (distance <= 15.0) { // 15格范围
                onlinePlayer.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 100, 0)); // 5秒速度1
            }
        }
    }
    
    // 给范围内的速通者高亮和伤害加深标记
    for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
        if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) == PlayerRole.SPEEDRUNNER) {
            double distance = onlinePlayer.getLocation().distance(piglinLoc);
            if (distance <= 15.0) { // 15格范围
                // 高亮效果
                onlinePlayer.addPotionEffect(new PotionEffect(PotionEffectType.GLOWING, 100, 0)); // 5秒发光
                // 伤害加深标记
                onlinePlayer.addPotionEffect(new PotionEffect(PotionEffectType.WEAKNESS, 100, 0)); // 5秒虚弱作为标记
            }
        }
    }
}
```

**狂意机制特点：**
- **友军增益：** 15格范围内的猎人获得速度1加成
- **敌军标记：** 15格范围内的速通者被高亮并标记伤害加深
- **持续光环：** 每次攻击时触发，提供持续的团队支援

### 主动技能 - 猪灵集结
**技能描述：** 将所有队友传送到自己身边，并且获得抗性1加成
**冷却时间：** 120秒

**技术实现：**
```java
private boolean handlePiglinRally(Player player) {
    Location rallyPoint = player.getLocation();

    // 传送所有队友到自己身边
    for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
        if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) == PlayerRole.HUNTER && 
            !onlinePlayer.equals(player)) {
            
            onlinePlayer.teleport(rallyPoint);
            // 给予抗性1加成
            onlinePlayer.addPotionEffect(new PotionEffect(PotionEffectType.DAMAGE_RESISTANCE, 200, 0)); // 10秒抗性1
            
            ComponentUtil.sendMessage(onlinePlayer, ComponentUtil.info("被猪灵集结传送！获得抗性加成"));
        }
    }

    // 给猪灵自己也加抗性
    player.addPotionEffect(new PotionEffect(PotionEffectType.DAMAGE_RESISTANCE, 200, 0)); // 10秒抗性1
    
    return true;
}
```

**集结机制特点：**
- **团队传送：** 所有猎人队友传送到猪灵身边
- **抗性加成：** 所有参与者获得10秒抗性1效果
- **战术价值：** 快速集结队伍，适合团战和救援

## 🐺 萨满技能详解

### 被动技能 - 动物伙伴
**技能描述：** 出生获得两只狼，狼拥有80点生命值。狼死亡后会扣除萨满20%的最大生命值，每次击杀猎人额外多获得一只狼。每只狼死亡后过90s会复活

**技术实现：**
```java
public void handleShamanAnimalCompanion(Player shaman) {
    // 这个方法将在萨满选择职业时调用，生成初始的两只狼
    // 具体实现需要在职业选择完成后调用
    plugin.getLogger().info("萨满 " + shaman.getName() + " 的动物伙伴被动技能激活");
}
```

**动物伙伴机制：**
- **初始狼群：** 选择职业时获得2只狼
- **狼的属性：** 每只狼80点生命值
- **死亡惩罚：** 狼死亡扣除萨满20%最大生命值
- **击杀奖励：** 击杀猎人额外获得1只狼
- **复活机制：** 狼死亡90秒后自动复活

### 主动技能 - 龙卷风暴
**技能描述：** 将自己周围一定范围变成风场，里面的玩家获得减速4的效果，萨满处于无敌状态，所有在风场内的猎人根据风场内猎人数量受到魔法伤害(初始0.25*数量逐渐提高0.25最大1.5*数量)，风场持续5s
**冷却时间：** 150秒

**技术实现：**
```java
private boolean handleShamanTornado(Player player) {
    // 萨满获得无敌状态
    player.addPotionEffect(new PotionEffect(PotionEffectType.DAMAGE_RESISTANCE, 100, 4)); // 5秒抗性5（近似无敌）
    
    // 启动风场效果
    executeTornadoEffect(player);
    
    return true;
}

private void executeTornadoEffect(Player shaman) {
    Location center = shaman.getLocation();
    final double[] damageMultiplier = {0.25}; // 初始伤害倍数

    // 持续5秒的风场效果
    for (int i = 0; i < 5; i++) {
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            // 统计风场内的猎人数量
            int hunterCount = 0;
            for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) == PlayerRole.HUNTER) {
                    double distance = onlinePlayer.getLocation().distance(center);
                    if (distance <= 8.0) { // 8格范围
                        hunterCount++;
                        // 给予减速4效果
                        onlinePlayer.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 40, 3)); // 2秒减速4
                    }
                }
            }

            // 对风场内的猎人造成魔法伤害
            if (hunterCount > 0) {
                double damage = Math.min(1.5, damageMultiplier[0]) * hunterCount;
                for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                    if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) == PlayerRole.HUNTER) {
                        double distance = onlinePlayer.getLocation().distance(center);
                        if (distance <= 8.0) {
                            onlinePlayer.damage(damage);
                        }
                    }
                }
            }

            // 增加伤害倍数
            damageMultiplier[0] = Math.min(1.5, damageMultiplier[0] + 0.25);

        }, i * 20L); // 每秒执行一次
    }
}
```

**龙卷风暴机制特点：**
- **无敌状态：** 萨满获得5秒近似无敌效果
- **风场范围：** 8格半径的风场区域
- **减速效果：** 风场内猎人获得减速4效果
- **递增伤害：** 伤害从0.25倍数递增到1.5倍数
- **群体伤害：** 伤害基于风场内猎人数量

## 🎮 职业平衡性分析

### 猪灵平衡性
**优势：**
- 强大的团队支援能力
- 光环效果提升团队战斗力
- 集结技能提供战术灵活性

**劣势：**
- 个人战斗能力一般
- 依赖团队配合
- 技能冷却时间较长

**平衡考虑：**
- 光环范围限制在15格
- 集结技能120秒冷却
- 需要主动攻击才能触发狂意

### 萨满平衡性
**优势：**
- 狼伙伴提供额外战斗力
- 龙卷风暴强大的区域控制
- 击杀奖励机制鼓励主动战斗

**劣势：**
- 狼死亡有生命值惩罚
- 龙卷风暴冷却时间很长
- 依赖狼群的生存能力

**平衡考虑：**
- 狼死亡扣除20%生命值
- 龙卷风暴150秒冷却
- 狼复活需要90秒等待

## 🎯 战术应用

### 猪灵战术
1. **团队核心：** 作为团队的支援核心
2. **光环控制：** 通过位置控制光环覆盖
3. **关键集结：** 在关键时刻集结队友

### 萨满战术
1. **狼群战术：** 利用狼群进行骚扰和侦察
2. **区域控制：** 使用龙卷风暴控制关键区域
3. **击杀积累：** 通过击杀扩大狼群规模

## 🎉 总结

成功实现了两个新职业：

- ✅ **猪灵：** 团队支援专家，光环+集结组合
- ✅ **萨满：** 自然召唤师，狼群+风暴组合
- ✅ **技能机制：** 复杂而有趣的技能系统
- ✅ **平衡设计：** 每个职业都有明确的优势和劣势
- ✅ **战术深度：** 为游戏增加了新的战术选择

现在游戏拥有了10个独特的职业，每个都有不同的战术定位和游戏风格！🎮✨

**注意：** 萨满的狼伙伴系统需要进一步完善实体生成和管理机制。
