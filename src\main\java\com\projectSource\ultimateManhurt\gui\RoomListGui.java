package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.room.RoomType;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 房间列表GUI
 */
public class RoomListGui extends BaseGui {

    private int currentPage = 0;
    private final int roomsPerPage = 28; // 7x4 区域显示房间
    private List<Room> displayedRooms = new ArrayList<>(); // 当前显示的房间列表
    
    public RoomListGui(UltimateManhurt plugin, Player player) {
        super(plugin, player, "<gold><bold>房间列表", 54);

        // 手动调用setupGui()
        setupGui();
    }
    
    @Override
    protected void setupGui() {
        // 填充边框
        fillBorder(Material.GRAY_STAINED_GLASS_PANE);
        
        // 显示房间
        displayRooms();
        
        // 控制按钮
        setupControlButtons();
    }
    
    /**
     * 获取所有可显示的房间
     */
    private List<Room> getAllRooms() {
        List<Room> allRooms = new ArrayList<>();

        // 添加玩家相关的房间（房主或成员）
        Room playerRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
        if (playerRoom != null) {
            allRooms.add(playerRoom);
        }

        // 添加其他公开房间
        for (Room room : plugin.getRoomManager().getPublicRooms()) {
            if (playerRoom == null || !room.getId().equals(playerRoom.getId())) {
                allRooms.add(room);
            }
        }

        return allRooms;
    }

    /**
     * 显示房间列表
     */
    private void displayRooms() {
        List<Room> allRooms = getAllRooms();

        int startIndex = currentPage * roomsPerPage;
        int endIndex = Math.min(startIndex + roomsPerPage, allRooms.size());

        // 保存当前页显示的房间列表
        displayedRooms.clear();
        for (int i = startIndex; i < endIndex; i++) {
            displayedRooms.add(allRooms.get(i));
        }

        // 显示房间
        for (int i = 0; i < displayedRooms.size(); i++) {
            Room room = displayedRooms.get(i);
            int slot = getSlotForIndex(i);
            setItem(slot, createRoomItem(room));
        }

        // 如果没有房间，显示提示
        if (allRooms.isEmpty()) {
            setItem(22, createItem(Material.BARRIER, "<red>暂无房间", "<gray>当前没有可用的房间"));
        }
    }
    
    /**
     * 创建房间物品
     */
    private ItemStack createRoomItem(Room room) {
        Material material = getRoomMaterial(room.getType());
        String name = "<aqua>" + room.getName();
        
        List<String> lore = new ArrayList<>();
        lore.add("<gray>类型: " + room.getType().getDisplayName());
        lore.add("<gray>玩家: <yellow>" + room.getPlayerCount() + "/" + room.getSettings().getMaxPlayers());
        lore.add("<gray>状态: " + room.getGameState().getDisplayName());
        
        if (!room.getDescription().isEmpty()) {
            lore.add("");
            lore.add("<white>" + room.getDescription());
        }
        
        lore.add("");

        // 调试信息
        UUID playerId = player.getUniqueId();
        boolean isOwner = room.isOwner(playerId);
        boolean containsPlayer = room.containsPlayer(playerId);
        boolean canJoin = room.canJoin(playerId);

        // 添加调试日志
        plugin.getLogger().info("玩家 " + player.getName() + " (" + playerId + ") 查看房间 " + room.getName() +
            " (房主: " + room.getOwnerId() + ") - isOwner: " + isOwner + ", containsPlayer: " + containsPlayer + ", canJoin: " + canJoin);
        plugin.getLogger().info("房间玩家列表: " + room.getPlayers());

        if (isOwner) {
            lore.add("<gold>👑 你是房主");
            lore.add("<yellow>点击管理房间");
        } else if (containsPlayer) {
            lore.add("<green>✓ 你已在房间中");
            lore.add("<yellow>点击进入房间设置");
        } else if (canJoin) {
            lore.add("<green>点击加入房间");
        } else {
            lore.add("<red>无法加入此房间");
        }
        
        return createItem(material, name, lore.toArray(new String[0]));
    }
    
    /**
     * 获取房间类型对应的材料
     */
    private Material getRoomMaterial(RoomType type) {
        switch (type) {
            case PUBLIC:
                return Material.EMERALD_BLOCK;
            case PRIVATE:
                return Material.DIAMOND_BLOCK;
            default:
                return Material.STONE;
        }
    }
    
    /**
     * 获取索引对应的槽位
     */
    private int getSlotForIndex(int index) {
        int row = index / 7;
        int col = index % 7;
        return (row + 1) * 9 + col + 1; // 跳过边框
    }
    
    /**
     * 设置控制按钮
     */
    private void setupControlButtons() {
        // 创建房间按钮
        setItem(48, createItem(Material.NETHER_STAR, "<green>创建房间", "<gray>点击创建新房间"));
        
        // 刷新按钮
        setItem(49, createItem(Material.COMPASS, "<yellow>刷新", "<gray>点击刷新房间列表"));
        
        // 上一页按钮
        if (currentPage > 0) {
            setItem(45, createItem(Material.ARROW, "<yellow>上一页", "<gray>点击查看上一页"));
        }
        
        // 下一页按钮
        List<Room> allRooms = new ArrayList<>(plugin.getRoomManager().getPublicRooms());
        if ((currentPage + 1) * roomsPerPage < allRooms.size()) {
            setItem(53, createItem(Material.ARROW, "<yellow>下一页", "<gray>点击查看下一页"));
        }
        
        // 关闭按钮
        setItem(50, createCloseButton());
    }
    
    @Override
    public void handleClick(InventoryClickEvent event) {
        event.setCancelled(true);
        
        if (event.getClickedInventory() != inventory) {
            return;
        }
        
        int slot = event.getSlot();
        ItemStack item = event.getCurrentItem();
        
        if (item == null || item.getType() == Material.AIR) {
            return;
        }
        
        playClickSound();
        
        // 处理控制按钮
        switch (slot) {
            case 48: // 创建房间
                handleCreateRoom();
                break;
            case 49: // 刷新
                handleRefresh();
                break;
            case 45: // 上一页
                handlePreviousPage();
                break;
            case 53: // 下一页
                handleNextPage();
                break;
            case 50: // 关闭
                handleClose();
                break;
            default:
                // 检查是否点击了房间
                handleRoomClick(slot);
                break;
        }
    }
    
    /**
     * 处理创建房间
     */
    private void handleCreateRoom() {
        plugin.getLogger().info("玩家 " + player.getName() + " 尝试创建房间");

        // 检查玩家是否已在房间中
        if (plugin.getRoomManager().isPlayerInRoom(player.getUniqueId())) {
            plugin.getLogger().info("玩家 " + player.getName() + " 已在房间中，无法创建新房间");
            sendError("你已经在一个房间中了！");
            playErrorSound();
            return;
        }

        // 打开房间类型选择界面
        String defaultRoomName = player.getName() + "的房间";
        plugin.getLogger().info("打开房间类型选择界面，默认房间名: " + defaultRoomName);

        close();
        plugin.getGuiManager().openRoomTypeSelectionGui(player, defaultRoomName);
    }
    
    /**
     * 处理刷新
     */
    private void handleRefresh() {
        refresh();
        sendSuccess("房间列表已刷新");
    }
    
    /**
     * 处理上一页
     */
    private void handlePreviousPage() {
        if (currentPage > 0) {
            currentPage--;
            refresh();
        }
    }

    /**
     * 处理下一页
     */
    private void handleNextPage() {
        List<Room> allRooms = getAllRooms();
        if ((currentPage + 1) * roomsPerPage < allRooms.size()) {
            currentPage++;
            refresh();
        }
    }
    
    /**
     * 处理关闭
     */
    private void handleClose() {
        close();
    }
    
    /**
     * 处理房间点击
     */
    private void handleRoomClick(int slot) {
        int index = getIndexForSlot(slot);
        if (index < 0 || index >= displayedRooms.size()) {
            return;
        }

        Room room = displayedRooms.get(index);

        // 检查玩家与房间的关系
        if (room.isOwner(player.getUniqueId()) || room.containsPlayer(player.getUniqueId())) {
            // 房主或已在房间中的玩家，直接打开房间设置
            sendSuccess("进入房间设置");
            playSuccessSound();
            close();
            plugin.getGuiManager().openRoomSettingsGui(player, room);
        } else if (room.canJoin(player.getUniqueId())) {
            // 可以加入房间的玩家
            boolean success = plugin.getRoomManager().joinRoom(
                player.getUniqueId(),
                room.getId(),
                com.projectSource.ultimateManhurt.game.PlayerRole.SPECTATOR
            );

            if (success) {
                sendSuccess("成功加入房间 " + room.getName());
                playSuccessSound();
                close();
                plugin.getGuiManager().openRoomSettingsGui(player, room);
            } else {
                sendError("加入房间失败");
                playErrorSound();
            }
        } else {
            sendError("无法加入房间 " + room.getName());
            playErrorSound();
        }
    }
    
    /**
     * 根据槽位获取索引
     */
    private int getIndexForSlot(int slot) {
        // 计算在7x4网格中的位置
        int row = (slot / 9) - 1;
        int col = (slot % 9) - 1;
        
        if (row < 0 || row >= 4 || col < 0 || col >= 7) {
            return -1;
        }
        
        return row * 7 + col;
    }
}
