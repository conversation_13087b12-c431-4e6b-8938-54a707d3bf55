package com.projectSource.ultimateManhurt.world;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import org.bukkit.World;
import org.bukkit.generator.ChunkGenerator;

import java.util.Random;

/**
 * 自定义世界生成器
 * 为Manhunt游戏优化世界生成
 */
public class WorldGenerator extends ChunkGenerator {
    
    private final UltimateManhurt plugin;
    
    public WorldGenerator(UltimateManhurt plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public ChunkData generateChunkData(World world, Random random, int x, int z, BiomeGrid biome) {
        // 返回null让Minecraft使用默认的世界生成器
        // 这样可以生成正常的地形、矿物、结构等
        return null;
    }
    
    @Override
    public boolean canSpawn(World world, int x, int z) {
        // 使用默认的生成规则
        return true;
    }
    
    /**
     * 检查是否应该生成结构
     */
    @Override
    public boolean shouldGenerateStructures() {
        return plugin.getConfigManager().getBoolean("world.generate-structures", true);
    }
    
    /**
     * 检查是否应该生成装饰
     */
    @Override
    public boolean shouldGenerateDecorations() {
        return true;
    }
    
    /**
     * 检查是否应该生成洞穴
     */
    @Override
    public boolean shouldGenerateCaves() {
        return true;
    }
    
    /**
     * 检查是否应该生成地牢
     */
    @Override
    public boolean shouldGenerateMobs() {
        return true;
    }
}
