# Tablist状态切换问题修复

## 问题描述

用户反馈：进入游戏后，Tablist仍然显示游戏大厅的内容，数据没有及时更新，应该切换到对游戏有用的提示。

## 问题分析

1. **状态切换不及时**: 游戏开始时没有主动为所有玩家创建游戏Tablist
2. **更新机制不完善**: 只是更新现有Tablist内容，而不是切换Tablist类型
3. **缺少状态检查**: 没有检查当前Tablist类型是否与玩家状态匹配

## 修复方案

### 1. 游戏开始时主动创建游戏Tablist

**修改文件**: `GameSession.java`

在 `actuallyStartGame()` 方法中添加：
```java
// 更新房间状态
room.setGameState(GameState.RUNNING);

// 为所有玩家创建游戏Tablist
createGameTablistForAllPlayers();

plugin.getLogger().info("游戏会话 " + sessionId + " 已开始");
```

添加新方法：
```java
/**
 * 为所有玩家创建游戏Tablist
 */
private void createGameTablistForAllPlayers() {
    for (UUID playerId : playerRoles.keySet()) {
        Player player = Bukkit.getPlayer(playerId);
        if (player != null && player.isOnline()) {
            plugin.getTablistManager().createGameTablist(player, this);
        }
    }
}
```

### 2. 游戏结束时切换回大厅Tablist

**修改文件**: `GameSession.java`

在 `cleanup()` 方法中添加：
```java
// 传送玩家回主世界
teleportPlayersToMainWorld();

// 为所有玩家创建大厅Tablist
createLobbyTablistForAllPlayers();

// 清理玩家位置数据
for (UUID playerId : playerRoles.keySet()) {
    plugin.getPlayerLocationManager().removePlayerLocation(playerId);
}
```

添加新方法：
```java
/**
 * 为所有玩家创建大厅Tablist
 */
private void createLobbyTablistForAllPlayers() {
    for (UUID playerId : playerRoles.keySet()) {
        Player player = Bukkit.getPlayer(playerId);
        if (player != null && player.isOnline()) {
            plugin.getTablistManager().createDefaultTablist(player);
        }
    }
}
```

### 3. 改进TablistManager的更新机制

**修改文件**: `TablistManager.java`

改进 `updateGameTablist()` 方法：
```java
/**
 * 更新游戏Tablist
 */
public void updateGameTablist(GameSession gameSession) {
    for (UUID playerId : gameSession.getPlayerRoles().keySet()) {
        Player player = Bukkit.getPlayer(playerId);
        if (player == null || !player.isOnline()) {
            continue;
        }
        
        PlayerTablist tablist = playerTablists.get(playerId);
        if (tablist != null) {
            // 检查是否需要切换到游戏Tablist
            if (tablist.getType() != PlayerTablist.TablistType.GAME) {
                createGameTablist(player, gameSession);
            } else {
                tablist.updateContent();
            }
        } else {
            // 如果没有Tablist，创建游戏Tablist
            createGameTablist(player, gameSession);
        }
    }
}
```

### 4. 添加调试日志

**修改文件**: `TablistManager.java`, `TablistListener.java`

在各个创建Tablist的方法中添加日志：
```java
plugin.getLogger().info("已为玩家 " + player.getName() + " 创建游戏Tablist");
plugin.getLogger().info("已为玩家 " + player.getName() + " 创建房间Tablist");
plugin.getLogger().info("已为玩家 " + player.getName() + " 创建大厅Tablist");
```

在 `TablistListener.createAppropriateTablist()` 中添加状态日志：
```java
plugin.getLogger().info("为玩家 " + player.getName() + " 创建游戏Tablist (状态: " + gameSession.getState() + ")");
plugin.getLogger().info("为玩家 " + player.getName() + " 创建房间Tablist (房间: " + room.getName() + ")");
plugin.getLogger().info("为玩家 " + player.getName() + " 创建大厅Tablist");
```

## 修复后的工作流程

### 游戏开始流程
1. 玩家在房间中等待 → 显示房间Tablist
2. 房主开始游戏 → `GameSession.startGame()`
3. 游戏倒计时 → 状态变为 `STARTING`
4. 游戏正式开始 → `actuallyStartGame()`
5. **主动为所有玩家创建游戏Tablist** → 显示游戏相关信息

### 游戏进行中
1. 定时器每tick更新 → `updateGameTablist()`
2. 检查Tablist类型是否正确
3. 如果类型不匹配，重新创建正确类型的Tablist
4. 如果类型正确，只更新内容

### 游戏结束流程
1. 游戏结束 → `endGame()`
2. 显示游戏结果
3. 清理游戏会话 → `cleanup()`
4. **主动为所有玩家创建大厅Tablist** → 显示大厅信息
5. 传送玩家回主世界

## 预期效果

1. **即时切换**: 游戏开始时立即切换到游戏Tablist
2. **正确显示**: 显示游戏时间、存活玩家、角色信息等游戏相关数据
3. **自动恢复**: 游戏结束后自动切换回大厅Tablist
4. **状态同步**: Tablist状态与玩家实际状态保持同步
5. **调试友好**: 通过日志可以追踪Tablist状态变化

## 测试建议

1. **创建房间测试**: 验证房间等待时显示房间Tablist
2. **开始游戏测试**: 验证游戏开始时立即切换到游戏Tablist
3. **游戏进行测试**: 验证游戏数据实时更新
4. **结束游戏测试**: 验证游戏结束后切换回大厅Tablist
5. **重连测试**: 验证玩家重连后Tablist状态正确
6. **多玩家测试**: 验证所有玩家的Tablist都正确切换

## 相关文件

- `src/main/java/com/projectSource/ultimateManhurt/game/GameSession.java`
- `src/main/java/com/projectSource/ultimateManhurt/tablist/TablistManager.java`
- `src/main/java/com/projectSource/ultimateManhurt/tablist/TablistListener.java`
