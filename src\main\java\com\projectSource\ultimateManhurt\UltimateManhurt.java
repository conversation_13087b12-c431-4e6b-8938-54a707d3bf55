package com.projectSource.ultimateManhurt;

import com.projectSource.ultimateManhurt.config.ConfigManager;
import com.projectSource.ultimateManhurt.game.GameManager;
import com.projectSource.ultimateManhurt.room.RoomManager;
import com.projectSource.ultimateManhurt.world.WorldManager;
import com.projectSource.ultimateManhurt.gui.GuiManager;
import com.projectSource.ultimateManhurt.scoreboard.ScoreboardManager;
import com.projectSource.ultimateManhurt.command.ManhuntCommand;
import com.projectSource.ultimateManhurt.listener.*;
import com.projectSource.ultimateManhurt.data.DataManager;
import com.projectSource.ultimateManhurt.data.PlayerLocationManager;
import com.projectSource.ultimateManhurt.cache.PlayerCacheManager;
import com.projectSource.ultimateManhurt.cache.PlayerCacheListener;
import org.bukkit.plugin.java.JavaPlugin;

public final class UltimateManhurt extends JavaPlugin {

    private static UltimateManhurt instance;

    // 核心管理器
    private ConfigManager configManager;
    private DataManager dataManager;
    private PlayerLocationManager playerLocationManager;
    private PlayerCacheManager playerCacheManager;
    private WorldManager worldManager;
    private RoomManager roomManager;
    private GameManager gameManager;
    private GuiManager guiManager;
    private ScoreboardManager scoreboardManager;
    private com.projectSource.ultimateManhurt.ranking.RankDisplayManager rankDisplayManager;
    private com.projectSource.ultimateManhurt.kit.StartKitManager startKitManager;
    private com.projectSource.ultimateManhurt.kit.CustomTemplateManager customTemplateManager;
    private com.projectSource.ultimateManhurt.tablist.TablistManager tablistManager;
    private com.projectSource.ultimateManhurt.profession.ProfessionManager professionManager;
    private com.projectSource.ultimateManhurt.room.RoomSettingsManager roomSettingsManager;

    @Override
    public void onEnable() {
        instance = this;

        // 初始化管理器
        initializeManagers();

        // 注册命令
        registerCommands();

        // 注册事件监听器
        registerListeners();

        // 启动定时任务
        startScheduledTasks();

        getLogger().info("Ultimate Manhurt 插件已启用！");
    }

    @Override
    public void onDisable() {
        // 清理资源
        if (gameManager != null) {
            gameManager.shutdown();
        }
        if (worldManager != null) {
            worldManager.shutdown();
        }
        if (dataManager != null) {
            dataManager.saveAll();
        }
        if (tablistManager != null) {
            tablistManager.shutdown();
        }
        if (professionManager != null) {
            professionManager.shutdown();
        }
        if (playerCacheManager != null) {
            playerCacheManager.shutdown();
        }

        getLogger().info("Ultimate Manhurt 插件已禁用！");
    }

    private void initializeManagers() {
        this.configManager = new ConfigManager(this);
        this.dataManager = new DataManager(this);
        this.playerLocationManager = new PlayerLocationManager(this);
        this.playerCacheManager = new PlayerCacheManager(this);
        this.worldManager = new WorldManager(this);
        this.roomManager = new RoomManager(this);
        this.gameManager = new GameManager(this);
        this.guiManager = new GuiManager(this);
        this.scoreboardManager = new ScoreboardManager(this);
        this.rankDisplayManager = new com.projectSource.ultimateManhurt.ranking.RankDisplayManager(this);
        this.startKitManager = new com.projectSource.ultimateManhurt.kit.StartKitManager(this);
        this.customTemplateManager = new com.projectSource.ultimateManhurt.kit.CustomTemplateManager(this);
        this.tablistManager = new com.projectSource.ultimateManhurt.tablist.TablistManager(this);
        this.professionManager = new com.projectSource.ultimateManhurt.profession.ProfessionManager(this);
        this.roomSettingsManager = new com.projectSource.ultimateManhurt.room.RoomSettingsManager(this);
    }

    private void registerCommands() {
        getCommand("manhunt").setExecutor(new ManhuntCommand(this));
        getCommand("gg").setExecutor(new com.projectSource.ultimateManhurt.command.GGCommand(this));
    }

    private void registerListeners() {
        getServer().getPluginManager().registerEvents(new PlayerListener(this), this);
        getServer().getPluginManager().registerEvents(new WorldListener(this), this);
        getServer().getPluginManager().registerEvents(new GameListener(this), this);
        getServer().getPluginManager().registerEvents(new GuiListener(this), this);
        getServer().getPluginManager().registerEvents(new com.projectSource.ultimateManhurt.listener.ScoreListener(this), this);
        getServer().getPluginManager().registerEvents(new com.projectSource.ultimateManhurt.tablist.TablistListener(this), this);
        getServer().getPluginManager().registerEvents(new com.projectSource.ultimateManhurt.listener.CompassListener(this), this);
        getServer().getPluginManager().registerEvents(new com.projectSource.ultimateManhurt.banpick.BanPickCraftingListener(this), this);
        getServer().getPluginManager().registerEvents(new com.projectSource.ultimateManhurt.profession.listener.ProfessionListener(this), this);
        getServer().getPluginManager().registerEvents(new com.projectSource.ultimateManhurt.profession.listener.JumpDisableListener(this), this);
        getServer().getPluginManager().registerEvents(new PlayerCacheListener(this), this);
    }

    private void startScheduledTasks() {
        // 每30分钟清理一次过期的位置数据
        getServer().getScheduler().runTaskTimerAsynchronously(this, () -> {
            playerLocationManager.cleanupExpiredLocations();
        }, 36000L, 36000L); // 30分钟 = 36000 ticks

        // 每天清理一次过期的玩家缓存
        getServer().getScheduler().runTaskTimerAsynchronously(this, () -> {
            playerCacheManager.cleanupExpiredCache();
        }, 1728000L, 1728000L); // 24小时 = 1728000 ticks
    }

    // Getter方法
    public static UltimateManhurt getInstance() {
        return instance;
    }

    public ConfigManager getConfigManager() {
        return configManager;
    }

    public DataManager getDataManager() {
        return dataManager;
    }

    public PlayerLocationManager getPlayerLocationManager() {
        return playerLocationManager;
    }

    public PlayerCacheManager getPlayerCacheManager() {
        return playerCacheManager;
    }

    public WorldManager getWorldManager() {
        return worldManager;
    }

    public RoomManager getRoomManager() {
        return roomManager;
    }

    public GameManager getGameManager() {
        return gameManager;
    }

    public GuiManager getGuiManager() {
        return guiManager;
    }

    public ScoreboardManager getScoreboardManager() {
        return scoreboardManager;
    }

    public com.projectSource.ultimateManhurt.ranking.RankDisplayManager getRankDisplayManager() {
        return rankDisplayManager;
    }

    public com.projectSource.ultimateManhurt.kit.StartKitManager getStartKitManager() {
        return startKitManager;
    }

    public com.projectSource.ultimateManhurt.kit.CustomTemplateManager getCustomTemplateManager() {
        return customTemplateManager;
    }

    public com.projectSource.ultimateManhurt.tablist.TablistManager getTablistManager() {
        return tablistManager;
    }

    public com.projectSource.ultimateManhurt.profession.ProfessionManager getProfessionManager() {
        return professionManager;
    }

    public com.projectSource.ultimateManhurt.room.RoomSettingsManager getRoomSettingsManager() {
        return roomSettingsManager;
    }
}
