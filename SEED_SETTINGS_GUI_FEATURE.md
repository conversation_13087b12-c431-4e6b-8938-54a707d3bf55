# 种子设置GUI功能实现

## 功能概述

为世界种子设置创建了一个专门的GUI界面，支持通过按钮输入种子号、预设种子选择、随机种子生成等多种设置方式。这个GUI在鼠标操作模式下提供了更直观、更方便的种子设置体验。

## 界面布局

### 📱 GUI尺寸与布局
- **尺寸**: 54格 (6行9列)
- **标题**: "世界种子设置"
- **边框**: 灰色玻璃板装饰

### 🔢 界面布局设计
```
[ ] [当前输入显示] [ ] [ ] [ ] [ ] [ ] [ ] [ ]
[ ] [1] [2] [3] [ ] [预设1] [预设2] [预设3] [ ]
[ ] [4] [5] [6] [ ] [预设4] [预设5] [预设6] [ ]
[ ] [7] [8] [9] [ ] [ ] [ ] [ ] [ ]
[ ] [±] [0] [←] [ ] [ ] [ ] [ ] [ ]
[ ] [清空] [随机] [ ] [玩家] [确认] [ ] [ ] [返回]
```

**左侧数字输入区域:**
- **数字按钮 (0-9)**: 石质按钮，点击输入对应数字
- **正负号按钮 (±)**: 红石，切换正负号
- **退格按钮 (←)**: 箭头，删除最后一位数字

**右侧预设种子区域:**
- **预设种子按钮**: 绿宝石，6个经典预设种子

**底部控制区域:**
- **清空按钮**: 屏障方块，清空所有输入
- **随机种子**: 末影珍珠，生成随机种子
- **玩家种子**: 玩家头颅，基于用户名生成
- **确认设置**: 绿色混凝土，应用当前输入
- **返回按钮**: 红色混凝土，返回房间设置

## 主要功能

### 🎯 **按钮输入种子**
1. **数字按钮**: 0-9数字按钮，点击输入
2. **正负号切换**: 支持负数种子
3. **退格功能**: 可以删除输入错误的数字
4. **清空功能**: 一键清空所有输入
5. **长度限制**: 最多18位数字，防止溢出

### 🎲 **预设种子选择**
提供6个经典预设种子：
- **经典404**: 种子值 404
- **地狱666**: 种子值 666  
- **幸运888**: 种子值 888
- **简单数字**: 种子值 1234567890
- **负数种子**: 种子值 -1234567890
- **时间种子**: 基于当前时间生成

### 🎰 **智能种子生成**
1. **随机种子**: 生成完全随机的种子值
2. **玩家种子**: 基于玩家用户名生成固定种子
3. **快速随机**: 直接设置为0（游戏随机）

### 💾 **实时显示与反馈**
- **当前输入显示**: 实时显示正在输入的种子
- **长度提示**: 显示当前输入的位数
- **操作提示**: 清晰的按钮说明和操作指引
- **错误处理**: 长度超限等错误的友好提示

## 操作方式

### 🖱️ **鼠标操作模式下的种子设置**
1. **左键点击世界种子**: 打开种子设置GUI
2. **右键点击世界种子**: 传统聊天输入方式
3. **Shift+左键**: 快速设置为随机种子

### 💬 **聊天输入模式下的种子设置**
- **任意点击世界种子**: 直接进入聊天输入状态（不打开GUI）

## 使用流程

### 📝 **按钮输入种子流程**
1. 点击数字按钮输入种子号
2. 使用±按钮切换正负号（如需要）
3. 使用退格按钮修正错误（如需要）
4. 点击"确认设置"应用种子
5. 自动返回房间设置界面

### 🎯 **预设种子使用流程**
1. 浏览预设种子选项
2. 点击想要的预设种子
3. 自动应用并返回房间设置

### 🎲 **随机种子生成流程**
1. 点击"随机种子"按钮
2. 系统生成随机种子值
3. 自动应用并返回房间设置

## 技术特性

### 🔧 **输入验证与处理**
```java
private long getCurrentSeedValue() {
    if (currentSeed.length() == 0) {
        return 0;
    }
    try {
        long value = Long.parseLong(currentSeed.toString());
        return isNegative ? -value : value;
    } catch (NumberFormatException e) {
        return 0;
    }
}
```

### 🎨 **动态界面更新**
- 实时更新当前输入显示
- 动态更新按钮状态和描述
- 智能错误提示和音效反馈

### 🔄 **与主GUI的集成**
- 无缝从房间设置GUI跳转
- 设置完成后自动返回
- 保持操作模式状态

## 用户体验优化

### ✨ **直观操作**
- 类似计算器的数字按钮布局
- 清晰的功能按钮图标
- 实时的输入反馈

### 🎯 **多样选择**
- 手动输入精确种子
- 快速选择预设种子
- 一键生成随机种子
- 个性化玩家种子

### 🛡️ **错误防护**
- 输入长度限制
- 数值溢出保护
- 友好的错误提示
- 操作撤销功能

### 🚀 **高效便捷**
- 无需记忆复杂种子号
- 支持负数种子输入
- 一键应用设置
- 快速返回主界面

## 扩展可能性

### 🔮 **未来功能**
- 种子历史记录
- 种子收藏功能
- 种子分享机制
- 种子预览功能
- 更多预设种子类别

### 🎨 **界面优化**
- 自定义预设种子
- 种子分类管理
- 搜索功能
- 导入/导出种子

## 使用建议

### 💡 **何时使用种子设置GUI（鼠标操作模式）**
- 需要输入特定的数字种子时
- 想要精确控制种子值时
- 希望使用预设种子时
- 想要避免输入错误时
- 需要生成随机或玩家种子时

### 💬 **何时使用聊天输入（聊天输入模式）**
- 已知确切的种子值时
- 希望快速输入时
- 不需要GUI辅助时
- 习惯传统输入方式时

### 🎲 **何时使用预设种子**
- 想要尝试经典种子时
- 需要快速设置时
- 不知道选什么种子时
- 想要特定类型的世界时

### ⚡ **何时使用随机生成**
- 想要完全随机的世界时
- 不在意具体种子值时
- 希望每次都有新体验时
- 快速开始游戏时
