# 近战攻击判定修复

## 🎯 问题描述

**原问题：** 屠夫的腐肉堆积被动技能和猎人的战斗专注技能只对特定武器的攻击生效，而不是对所有近战攻击生效。

**具体问题：**
- 只有剑、斧、三叉戟攻击才能触发
- 空手攻击无法触发
- 其他工具（如镐、锄等）攻击无法触发

## 🔧 修复方案

### 原逻辑问题

**原代码逻辑：**
```java
// 检查是否是特定的近战武器
private boolean isMeleeWeapon(Material material) {
    return switch (material) {
        case WOODEN_SWORD, STONE_SWORD, IRON_SWORD, GOLDEN_SWORD, DIAMOND_SWORD, NETHERITE_SWORD,
             WOODEN_AXE, STONE_AXE, IRON_AXE, GOLDEN_AXE, DIAMOND_AXE, NETHERITE_AXE,
             TRIDENT -> true;
        default -> false;
    };
}

// 使用方式
ItemStack weapon = attacker.getInventory().getItemInMainHand();
if (isMeleeWeapon(weapon.getType())) {
    // 触发技能
}
```

**问题分析：**
- 只检查特定武器类型
- 忽略了空手攻击
- 忽略了其他工具的攻击

### 新逻辑设计

**新代码逻辑：**
```java
// 检查是否是近战攻击（基于伤害来源）
private boolean isMeleeAttack(EntityDamageByEntityEvent event) {
    Entity damager = event.getDamager();
    
    // 如果伤害来源是玩家，则认为是近战攻击
    if (damager instanceof Player) {
        return true;
    }
    
    // 如果伤害来源是抛射物，则不是近战攻击
    if (damager instanceof Projectile) {
        return false;
    }
    
    // 其他情况（如TNT、岩浆等）也不认为是近战攻击
    return false;
}

// 使用方式
if (isMeleeAttack(event)) {
    // 触发技能
}
```

**优势分析：**
- ✅ **全面覆盖：** 包括所有玩家直接造成的伤害
- ✅ **简单准确：** 基于伤害来源而非武器类型
- ✅ **逻辑清晰：** 玩家直接攻击 = 近战攻击

## 📊 影响的技能

### 1. 屠夫腐肉堆积被动技能

**修复前：**
- 只有剑、斧、三叉戟攻击才能触发15%概率获得抗性三效果

**修复后：**
- 所有近战攻击都能触发，包括：
  - ✅ 空手攻击
  - ✅ 剑类武器攻击
  - ✅ 斧类武器攻击
  - ✅ 镐、锄、铲等工具攻击
  - ✅ 三叉戟近战攻击
  - ✅ 其他任何手持物品的攻击

### 2. 猎人战斗专注技能

**修复前：**
- 只有特定武器的近战攻击才能回复4点生命值

**修复后：**
- 所有近战攻击都能回复生命值，包括空手攻击

## 🎮 游戏体验改进

### 对屠夫玩家
- ✅ **更灵活的防御：** 不再依赖攻击者使用特定武器
- ✅ **更可靠的触发：** 空手攻击也能触发抗性效果
- ✅ **符合直觉：** 所有近战攻击都应该触发"腐肉堆积"

### 对猎人玩家
- ✅ **更一致的回复：** 所有近战攻击都能回复生命值
- ✅ **战术多样性：** 不必局限于特定武器类型

### 对攻击者
- ⚠️ **风险增加：** 空手攻击屠夫也可能触发抗性效果
- 💡 **策略调整：** 需要考虑所有近战攻击的风险

## 🔍 技术细节

### 攻击类型分类

**近战攻击（会触发技能）：**
- 玩家直接造成的伤害
- 包括空手、任何工具、任何武器

**非近战攻击（不会触发技能）：**
- 弓箭、弩箭等抛射物攻击
- TNT爆炸伤害
- 岩浆、跌落等环境伤害
- 生物攻击

### 判定逻辑

```java
// 判定流程
EntityDamageByEntityEvent event = ...;
Entity damager = event.getDamager();

if (damager instanceof Player) {
    // 玩家直接攻击 → 近战攻击 ✅
    return true;
} else if (damager instanceof Projectile) {
    // 抛射物攻击 → 远程攻击 ❌
    return false;
} else {
    // 其他来源 → 非近战攻击 ❌
    return false;
}
```

## 🧪 测试验证

### 测试场景

1. **空手攻击测试：**
   - 空手攻击屠夫，确认能触发腐肉堆积
   - 猎人空手攻击，确认能回复生命值

2. **工具攻击测试：**
   - 使用镐、锄、铲等工具攻击
   - 确认能正常触发相关技能

3. **武器攻击测试：**
   - 使用剑、斧等传统武器攻击
   - 确认功能保持正常

4. **远程攻击测试：**
   - 使用弓箭攻击
   - 确认不会触发近战技能

### 预期结果

**屠夫腐肉堆积：**
```
玩家A空手攻击屠夫B → 15%概率触发抗性三效果 ✅
玩家A用镐攻击屠夫B → 15%概率触发抗性三效果 ✅
玩家A射箭攻击屠夫B → 不触发 ✅
```

**猎人战斗专注：**
```
猎人空手攻击敌人 → 回复4点生命值 ✅
猎人用锄攻击敌人 → 回复4点生命值 ✅
猎人射箭攻击敌人 → 回复6点生命值（射箭回复） ✅
```

## 📝 文档更新

### 职业描述更新

**屠夫腐肉堆积：**
- 原描述："受到近战伤害有15%概率获得抗性三效果3s"
- 新描述："受到近战攻击有15%概率获得抗性三效果3s"
- 补充说明："包括所有类型的近战攻击（空手、工具、武器等）"

## 🎯 总结

这个修复让近战攻击的判定更加合理和全面：

- ✅ **逻辑更清晰：** 基于攻击方式而非武器类型
- ✅ **覆盖更全面：** 包括所有玩家直接攻击
- ✅ **体验更一致：** 符合玩家对"近战攻击"的直觉理解
- ✅ **代码更简洁：** 减少了复杂的武器类型判断

现在屠夫的腐肉堆积和猎人的战斗专注技能将对所有类型的近战攻击生效，提供更一致和可靠的游戏体验！🥊✨
