# 新的离线检测系统

## 问题分析

原来的房间自动关闭系统存在问题：
1. **检测延迟**: `Bukkit.getPlayer().isOnline()` 在玩家刚离线时可能仍返回true
2. **时机问题**: 玩家离线事件触发时，其他玩家的在线状态可能还没有更新
3. **复杂性**: 需要管理多个计时器，容易出现竞态条件

## 新方案设计

### 核心思路
- **定期检测**: 游戏开始后每5分钟检查一次所有玩家是否离线
- **简单可靠**: 不依赖玩家离线事件，避免时机问题
- **游戏内置**: 检测器作为游戏会话的一部分，生命周期管理简单

### 实现细节

#### 1. 检测器启动
```java
// 游戏开始时启动
private void startOfflineChecker() {
    long checkInterval = 5 * 60 * 20L; // 5分钟 = 6000 ticks
    
    offlineCheckTask = new BukkitRunnable() {
        @Override
        public void run() {
            checkAllPlayersOffline();
        }
    }.runTaskTimer(plugin, checkInterval, checkInterval);
}
```

#### 2. 离线检测逻辑
```java
private void checkAllPlayersOffline() {
    if (state != GameState.RUNNING) {
        return; // 只在游戏进行中检测
    }

    boolean hasOnlinePlayer = false;
    for (UUID playerId : playerRoles.keySet()) {
        Player player = Bukkit.getPlayer(playerId);
        if (player != null && player.isOnline()) {
            hasOnlinePlayer = true;
            break;
        }
    }

    if (!hasOnlinePlayer) {
        // 所有玩家都离线，强制结束游戏（不计分）
        forceEndGame(false);
    }
}
```

#### 3. 生命周期管理
- **启动**: 游戏开始时（`actuallyStartGame()`）
- **停止**: 游戏结束时（`cleanup()`）
- **自动清理**: 游戏会话结束时自动取消任务

## 优势

### 1. 可靠性
- **避免时机问题**: 不依赖玩家离线事件的即时性
- **定期检测**: 5分钟间隔确保及时发现问题
- **状态检查**: 只在游戏进行中检测，避免误判

### 2. 简单性
- **单一计时器**: 每个游戏会话只有一个检测器
- **生命周期简单**: 随游戏会话自动管理
- **无竞态条件**: 不需要复杂的计时器管理

### 3. 性能
- **低频检测**: 5分钟一次，对性能影响极小
- **早期退出**: 发现在线玩家立即停止检查
- **自动清理**: 游戏结束后自动停止

## 配置选项

目前检测间隔硬编码为5分钟，未来可以添加配置：
```yaml
game:
  offline-detection:
    enabled: true
    check-interval-minutes: 5
    force-end-on-all-offline: true
    count-score-on-force-end: false
```

## 日志记录

系统会记录以下事件：
```
[INFO]: 游戏 game_xxx 离线检测器已启动，每5分钟检查一次
[INFO]: 离线检测 - 游戏: game_xxx, 总玩家: 2, 在线: 0
[WARNING]: 游戏 game_xxx 所有玩家都离线，强制结束游戏（不计分）
[INFO]: 游戏 game_xxx 离线检测器已停止
```

## 与原系统的对比

| 特性 | 原系统 | 新系统 |
|------|--------|--------|
| 触发方式 | 玩家离线事件 | 定期检测 |
| 检测延迟 | 1分钟 | 5分钟 |
| 复杂度 | 高（多计时器） | 低（单计时器） |
| 可靠性 | 中（时机问题） | 高（定期检测） |
| 性能影响 | 中（频繁检查） | 低（低频检查） |

## 测试建议

### 1. 基本功能测试
1. 开始游戏，等待5分钟，验证检测器正常运行
2. 所有玩家离线，等待5分钟，验证游戏强制结束
3. 部分玩家离线，验证游戏继续进行

### 2. 边界情况测试
1. 游戏开始前玩家离线（不应触发检测）
2. 游戏结束后玩家离线（检测器应已停止）
3. 玩家在检测间隔内重连

### 3. 性能测试
1. 多个游戏同时进行的检测性能
2. 长时间运行的内存使用情况

## 实现文件

### 修改的文件
- `src/main/java/com/projectSource/ultimateManhurt/game/GameSession.java`
  - 添加 `offlineCheckTask` 字段
  - 添加 `startOfflineChecker()` 方法
  - 添加 `stopOfflineChecker()` 方法
  - 添加 `checkAllPlayersOffline()` 方法
  - 在游戏开始时启动检测器
  - 在游戏结束时停止检测器

### 移除的功能
- `src/main/java/com/projectSource/ultimateManhurt/room/RoomManager.java`
  - 移除所有旧的自动关闭相关方法
  - 移除计时器管理字段

- `src/main/java/com/projectSource/ultimateManhurt/listener/PlayerListener.java`
  - 移除对 `checkRoomAutoClose()` 的调用

## 未来改进

1. **可配置间隔**: 允许管理员配置检测间隔
2. **渐进式警告**: 在强制结束前发送警告消息
3. **重连宽限期**: 给玩家一定时间重连
4. **统计记录**: 记录因离线而结束的游戏统计

## 注意事项

1. **检测间隔**: 5分钟可能对某些情况来说太长，可以根据需要调整
2. **游戏状态**: 只在 `RUNNING` 状态检测，确保不会误判
3. **资源清理**: 游戏结束时确保检测器被正确停止
4. **日志级别**: 确保重要事件被记录到日志中
