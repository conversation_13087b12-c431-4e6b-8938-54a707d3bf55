package com.projectSource.ultimateManhurt.game.scoring;

/**
 * 积分里程碑
 * 定义各种可以获得分数的成就和里程碑
 */
public enum ScoreMilestone {
    
    // 基础进度里程碑 (130分)
    GET_WOOD("获得木头", 3),
    CRAFT_WORKBENCH("制作工作台", 5),
    CRAFT_WOODEN_TOOLS("制作木制工具", 8),
    MINE_STONE("挖到石头", 10),
    CRAFT_STONE_TOOLS("制作石制工具", 12),
    CRAFT_FURNACE("制作熔炉", 15),
    SMELT_IRON("冶炼铁锭", 18),
    CRAFT_IRON_TOOLS("制作铁制工具", 25),
    CRAFT_IRON_ARMOR("制作铁制盔甲", 34),

    // 重要里程碑 (210分)
    FIND_DIAMONDS("发现钻石", 40),
    CRAFT_DIAMOND_TOOLS("制作钻石工具", 50),
    CRAFT_DIAMOND_ARMOR("制作钻石盔甲", 60),
    BUILD_NETHER_PORTAL("建造下界传送门", 30),
    ENTER_NETHER("进入下界", 30),
    
    // 下界进度 (170分)
    KILL_BLAZE("击杀烈焰人", 40),
    GET_BLAZE_ROD("获得烈焰棒", 50),
    CRAFT_BREWING_STAND("制作酿造台", 30),
    CRAFT_ENDER_EYE("制作末影之眼", 50),

    // 末地准备 (130分)
    ACTIVATE_END_PORTAL("激活末地传送门", 60),
    ENTER_END("进入末地", 70),

    // 最终目标 (130分)
    DAMAGE_DRAGON("对末影龙造成伤害", 50),
    KILL_DRAGON("击杀末影龙", 80),
    
    // 奖励系统 (85分) - 重新添加击杀hunter
    SURVIVAL_BONUS("生存奖励", 5),
    NO_DEATH_BONUS("无死亡奖励", 15),
    SPEED_BONUS_NETHER("快速进入下界", 10),
    SPEED_BONUS_END("快速进入末地", 15),
    SPEED_BONUS_DRAGON("快速击杀末影龙", 20),
    EFFICIENCY_BONUS("效率奖励", 5),
    KILL_HUNTER("击杀捕猎者", 15);

    // 移除的里程碑 (不再使用):
    // RESOURCE_CONSERVATION - 难以准确判断
    // ESCAPE_HUNTER - 检测复杂，容易误判
    // COLLECT_ALL_MATERIALS - 过于复杂
    // CRAFT_FULL_SET - 与其他里程碑重复
    
    private final String displayName;
    private final int points;
    
    ScoreMilestone(String displayName, int points) {
        this.displayName = displayName;
        this.points = points;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public int getPoints() {
        return points;
    }

    /**
     * 获取默认分数（用于里程碑设置）
     */
    public int getDefaultPoints() {
        return points;
    }
    
    /**
     * 根据里程碑类型获取描述
     */
    public String getDescription() {
        return switch (this) {
            case GET_WOOD -> "收集你的第一块木头";
            case CRAFT_WORKBENCH -> "制作工作台开始你的冒险";
            case MINE_STONE -> "挖掘石头制作更好的工具";
            case CRAFT_FURNACE -> "制作熔炉准备冶炼";
            case FIND_DIAMONDS -> "发现珍贵的钻石";
            case ENTER_NETHER -> "勇敢地进入下界";
            case GET_BLAZE_ROD -> "从烈焰人那里获得烈焰棒";
            case CRAFT_ENDER_EYE -> "制作末影之眼寻找要塞";
            case ENTER_END -> "进入末地面对最终挑战";
            case KILL_DRAGON -> "击败末影龙完成终极挑战";
            case SURVIVAL_BONUS -> "成功存活5分钟";
            case NO_DEATH_BONUS -> "10分钟内没有死亡";
            case SPEED_BONUS_NETHER -> "快速进入下界获得奖励";
            case SPEED_BONUS_END -> "快速进入末地获得奖励";
            case SPEED_BONUS_DRAGON -> "快速击杀末影龙获得奖励";
            case EFFICIENCY_BONUS -> "高效完成任务获得奖励";
            case KILL_HUNTER -> "在战斗中击败捕猎者";
            default -> "完成特殊成就";
        };
    }
    
    /**
     * 获取里程碑的难度等级
     */
    public Difficulty getDifficulty() {
        if (points <= 20) return Difficulty.EASY;
        if (points <= 50) return Difficulty.MEDIUM;
        if (points <= 100) return Difficulty.HARD;
        return Difficulty.EXTREME;
    }
    
    public enum Difficulty {
        EASY("简单"),
        MEDIUM("中等"),
        HARD("困难"),
        EXTREME("极难");
        
        private final String displayName;
        
        Difficulty(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
}
