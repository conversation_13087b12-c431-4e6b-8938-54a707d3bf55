# Ultimate Manhunt 插件配置文件

# 世界设置
world:
  # 世界名称前缀（用于生成游戏世界名称）
  name-prefix: "manhunt_"

# 计分板设置
scoreboard:
  # 更新间隔（tick）
  update-interval: 20
  # 服务器IP显示
  server-ip: "mc.example.com"

# Tablist设置
tablist:
  # 更新间隔（tick）
  update-interval: 20

  # 玩家显示设置
  player-display:
    # 是否启用自定义玩家显示名称
    enabled: true
    # 是否显示延迟
    show-ping: true
    # 是否显示血量
    show-health: true
    # 是否显示角色（游戏中）
    show-role: true

    # 延迟阈值设置
    ping-thresholds:
      good: 50      # 绿色延迟阈值（毫秒）
      medium: 150   # 黄色延迟阈值（毫秒）

    # 血量阈值设置
    health-thresholds:
      excellent: 75   # 优秀血量阈值（百分比）
      good: 50        # 良好血量阈值（百分比）
      medium: 25      # 中等血量阈值（百分比）
