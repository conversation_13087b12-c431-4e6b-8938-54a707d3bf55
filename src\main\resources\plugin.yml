name: UltimateManhurt
version: '${version}'
main: com.projectSource.ultimateManhurt.UltimateManhurt
api-version: '1.21'
author: ProjectSource
description: Ultimate Manhunt plugin with talent system and battle rooms

commands:
  manhunt:
    description: Main command for Ultimate Manhunt
    usage: /<command> [subcommand]
    aliases: [mh, hunt]
    permission: ultimatemanhurt.use
  gg:
    description: Surrender command for players
    usage: /gg
    permission: ultimatemanhurt.use

permissions:
  ultimatemanhurt.*:
    description: All Ultimate Manhunt permissions
    default: op
    children:
      ultimatemanhurt.use: true
      ultimatemanhurt.admin: true
      ultimatemanhurt.room.create: true
      ultimatemanhurt.room.join: true
      ultimatemanhurt.room.manage: true
  ultimatemanhurt.use:
    description: Basic plugin usage
    default: true
  ultimatemanhurt.admin:
    description: Admin permissions
    default: op
  ultimatemanhurt.room.create:
    description: Create game rooms
    default: true
  ultimatemanhurt.room.join:
    description: Join game rooms
    default: true
  ultimatemanhurt.room.manage:
    description: Manage game rooms
    default: op
