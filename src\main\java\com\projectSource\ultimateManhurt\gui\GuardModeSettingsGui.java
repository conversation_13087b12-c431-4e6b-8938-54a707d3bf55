package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.room.RoomSettings;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

/**
 * 守卫模式设置GUI
 * 让房主配置守卫模式的各种参数
 */
public class GuardModeSettingsGui extends BaseGui {
    
    private final Room room;
    private final RoomSettings settings;
    
    public GuardModeSettingsGui(UltimateManhurt plugin, Player player, Room room) {
        super(plugin, player, "守卫模式设置", 54);
        this.room = room;
        this.settings = room.getSettings();
        setupGui();
    }

    @Override
    protected void setupGui() {
        // 清空界面
        inventory.clear();
        
        // 设置边框
        fillBorder(Material.GRAY_STAINED_GLASS_PANE);
        
        // 设置守卫模式配置项
        setupGuardModeSettings();
        
        // 设置控制按钮
        setupControlButtons();
    }
    
    /**
     * 设置守卫模式配置项
     */
    private void setupGuardModeSettings() {
        // 凋零血量设置
        setItem(10, createItem(Material.WITHER_SKELETON_SKULL,
            "<red><bold>凋零血量",
            "<gray>当前: <white>" + (int)settings.getWitherMaxHealth() + " 点",
            "<gray>范围: 100-1000（受Minecraft限制）",
            "<gray>注意: 超过1024的血量会被自动调整",
            "",
            "<yellow>左键: -50 | 右键: +50",
            "<yellow>Shift+左键: -200 | Shift+右键: +200"));

        // 减疗效果设置
        setItem(11, createItem(Material.POTION,
            "<dark_red><bold>减疗效果",
            "<gray>当前: <white>" + (int)(settings.getWitherHealingReduction() * 100) + "%",
            "<gray>攻击凋零后获得的减疗效果强度",
            "",
            "<yellow>左键: -5% | 右键: +5%",
            "<yellow>Shift+左键: -25% | Shift+右键: +25%"));

        // 攻击间隔设置
        setItem(12, createItem(Material.CLOCK,
            "<gold><bold>攻击间隔",
            "<gray>当前: <white>" + settings.getWitherAttackInterval() + " 秒",
            "<gray>凋零自动攻击的间隔时间",
            "",
            "<yellow>左键: -1秒 | 右键: +1秒",
            "<yellow>Shift+左键: -5秒 | Shift+右键: +5秒"));

        // 攻击伤害设置
        setItem(13, createItem(Material.DIAMOND_SWORD,
            "<red><bold>攻击伤害",
            "<gray>当前: <white>" + (int)settings.getWitherAttackDamage() + " 点",
            "<gray>凋零每次攻击造成的伤害",
            "",
            "<yellow>左键: -1点 | 右键: +1点",
            "<yellow>Shift+左键: -5点 | Shift+右键: +5点"));

        // 护盾持续时间设置
        setItem(14, createItem(Material.SHIELD,
            "<blue><bold>护盾持续时间",
            "<gray>当前: <white>" + (settings.getWitherShieldDuration() / 60) + " 分钟",
            "<gray>游戏开始时凋零的减伤护盾持续时间",
            "",
            "<yellow>左键: -1分钟 | 右键: +1分钟",
            "<yellow>Shift+左键: -5分钟 | Shift+右键: +5分钟"));

        // 护盾减伤比例设置
        setItem(15, createItem(Material.TOTEM_OF_UNDYING,
            "<aqua><bold>护盾减伤比例",
            "<gray>当前: <white>" + (int)(settings.getWitherShieldReduction() * 100) + "%",
            "<gray>护盾激活时的伤害减免比例",
            "",
            "<yellow>左键: -5% | 右键: +5%",
            "<yellow>Shift+左键: -25% | Shift+右键: +25%"));

        // 捕猎者出生距离设置
        setItem(16, createItem(Material.COMPASS,
            "<green><bold>捕猎者出生距离",
            "<gray>当前: <white>" + settings.getHunterSpawnDistance() + " 方块",
            "<gray>捕猎者距离中心塔的出生距离",
            "",
            "<yellow>左键: -25方块 | 右键: +25方块",
            "<yellow>Shift+左键: -100方块 | Shift+右键: +100方块"));

        // 凋零移动设置
        setItem(19, createToggleItem(Material.LEAD, "凋零可移动", settings.isWitherCanMove(),
            "<gray>设置凋零是否可以移动",
            "<gray>关闭时凋零将固定在塔中心"));

        // 凋零攻击目标设置
        setItem(20, createToggleItem(Material.TARGET, "只攻击捕猎者", settings.isWitherTargetHuntersOnly(),
            "<gray>设置凋零是否只攻击捕猎者",
            "<gray>开启时凋零不会攻击速通者"));

        // 凋零破坏地形设置
        setItem(21, createToggleItem(Material.TNT, "破坏地形", settings.isWitherDestroyBlocks(),
            "<gray>设置凋零攻击是否破坏地形",
            "<gray>关闭时凋零攻击不会破坏方块"));

        // 凋零效果持续时间
        setItem(28, createItem(Material.CLOCK,
            "<purple><bold>凋零效果时长",
            "<gray>当前: <white>" + (settings.getWitherEffectDuration() / 20.0) + " 秒",
            "<gray>范围: 1-10秒",
            "<gray>被骷髅头击中后的凋零效果持续时间",
            "",
            "<yellow>左键: -1秒 | 右键: +1秒",
            "<yellow>Shift+左键: -3秒 | Shift+右键: +3秒"));

        // 凋零效果等级
        String[] levelNames = {"I", "II", "III"};
        String currentLevel = levelNames[Math.min(settings.getWitherEffectLevel(), 2)];
        setItem(29, createItem(Material.WITHER_ROSE,
            "<purple><bold>凋零效果等级",
            "<gray>当前: <white>凋零" + currentLevel,
            "<gray>范围: I-III级",
            "<gray>凋零效果的强度等级",
            "",
            "<yellow>左键: 降低等级 | 右键: 提高等级"));
    }
    
    /**
     * 设置控制按钮
     */
    private void setupControlButtons() {
        // 重置为默认值按钮
        setItem(49, createItem(Material.REDSTONE,
            "<red><bold>重置默认值",
            "<gray>将所有守卫模式设置重置为默认值",
            "",
            "<yellow>点击重置"));

        // 返回按钮
        setItem(53, createBackButton());
    }

    /**
     * 创建开关物品
     */
    private ItemStack createToggleItem(Material material, String name, boolean enabled, String... description) {
        String status = enabled ? "<green>已启用" : "<red>已禁用";
        String action = enabled ? "<gray>点击禁用" : "<gray>点击启用";

        // 构建描述数组
        String[] lore = new String[description.length + 2];
        System.arraycopy(description, 0, lore, 0, description.length);
        lore[description.length] = "<gray>状态: " + status;
        lore[description.length + 1] = action;

        return createItem(material, "<yellow>" + name, lore);
    }

    @Override
    public void handleClick(InventoryClickEvent event) {
        // 立即取消事件，防止物品被拿起
        event.setCancelled(true);

        if (event.getClickedInventory() != inventory) {
            return;
        }

        ItemStack item = event.getCurrentItem();
        if (item == null || item.getType() == Material.AIR) {
            return;
        }

        // 播放点击音效
        playClickSound();

        int slot = event.getSlot();
        boolean isLeftClick = event.isLeftClick();
        boolean isRightClick = event.isRightClick();
        boolean isShiftClick = event.isShiftClick();

        switch (slot) {
            case 10: // 凋零血量
                handleWitherHealth(isLeftClick, isRightClick, isShiftClick);
                break;
            case 11: // 减疗效果
                handleHealingReduction(isLeftClick, isRightClick, isShiftClick);
                break;
            case 12: // 攻击间隔
                handleAttackInterval(isLeftClick, isRightClick, isShiftClick);
                break;
            case 13: // 攻击伤害
                handleAttackDamage(isLeftClick, isRightClick, isShiftClick);
                break;
            case 14: // 护盾持续时间
                handleShieldDuration(isLeftClick, isRightClick, isShiftClick);
                break;
            case 15: // 护盾减伤比例
                handleShieldReduction(isLeftClick, isRightClick, isShiftClick);
                break;
            case 16: // 捕猎者出生距离
                handleHunterSpawnDistance(isLeftClick, isRightClick, isShiftClick);
                break;
            case 19: // 凋零可移动
                settings.setWitherCanMove(!settings.isWitherCanMove());
                refresh();
                break;
            case 20: // 只攻击捕猎者
                settings.setWitherTargetHuntersOnly(!settings.isWitherTargetHuntersOnly());
                refresh();
                break;
            case 21: // 破坏地形
                settings.setWitherDestroyBlocks(!settings.isWitherDestroyBlocks());
                refresh();
                break;
            case 28: // 凋零效果时长
                handleWitherEffectDuration(isLeftClick, isRightClick, isShiftClick);
                break;
            case 29: // 凋零效果等级
                handleWitherEffectLevel(isLeftClick, isRightClick, isShiftClick);
                break;
            case 49: // 重置默认值
                resetToDefaults();
                break;
            case 53: // 返回
                handleBack();
                break;
        }
    }

    /**
     * 处理凋零血量设置
     */
    private void handleWitherHealth(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        double current = settings.getWitherMaxHealth();
        double change = isShiftClick ? 200 : 50; // 修改间隔：普通50，Shift+200
        double newValue = current;

        if (isLeftClick) {
            newValue = Math.max(100, current - change);
        } else if (isRightClick) {
            newValue = Math.min(1000, current + change); // 限制最大值为1000
        }

        if (newValue != current) {
            settings.setWitherMaxHealth(newValue);
            if (newValue == 1000 && current + change > 1000) {
                sendError("血量已达到最大值(1000点)！");
                playErrorSound();
            } else if (newValue == 100 && current - change < 100) {
                sendError("血量已达到最小值(100点)！");
                playErrorSound();
            } else {
                sendSuccess("凋零血量已设置为: " + (int)newValue + " 点");
            }
        }

        refresh();
    }

    /**
     * 处理减疗效果设置
     */
    private void handleHealingReduction(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        double current = settings.getWitherHealingReduction();
        double change = (isShiftClick ? 0.25 : 0.05);
        
        if (isLeftClick) {
            settings.setWitherHealingReduction(current - change);
        } else if (isRightClick) {
            settings.setWitherHealingReduction(current + change);
        }
        
        refresh();
    }

    /**
     * 处理攻击间隔设置
     */
    private void handleAttackInterval(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        int current = settings.getWitherAttackInterval();
        int change = isShiftClick ? 5 : 1;
        
        if (isLeftClick) {
            settings.setWitherAttackInterval(current - change);
        } else if (isRightClick) {
            settings.setWitherAttackInterval(current + change);
        }
        
        refresh();
    }

    /**
     * 处理攻击伤害设置
     */
    private void handleAttackDamage(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        double current = settings.getWitherAttackDamage();
        double change = isShiftClick ? 5 : 1;
        
        if (isLeftClick) {
            settings.setWitherAttackDamage(current - change);
        } else if (isRightClick) {
            settings.setWitherAttackDamage(current + change);
        }
        
        refresh();
    }

    /**
     * 处理护盾持续时间设置
     */
    private void handleShieldDuration(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        int current = settings.getWitherShieldDuration();
        int change = (isShiftClick ? 300 : 60); // 5分钟或1分钟
        
        if (isLeftClick) {
            settings.setWitherShieldDuration(current - change);
        } else if (isRightClick) {
            settings.setWitherShieldDuration(current + change);
        }
        
        refresh();
    }

    /**
     * 处理护盾减伤比例设置
     */
    private void handleShieldReduction(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        double current = settings.getWitherShieldReduction();
        double change = (isShiftClick ? 0.25 : 0.05);
        
        if (isLeftClick) {
            settings.setWitherShieldReduction(current - change);
        } else if (isRightClick) {
            settings.setWitherShieldReduction(current + change);
        }
        
        refresh();
    }

    /**
     * 处理捕猎者出生距离设置
     */
    private void handleHunterSpawnDistance(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        int current = settings.getHunterSpawnDistance();
        int change = isShiftClick ? 100 : 25;
        
        if (isLeftClick) {
            settings.setHunterSpawnDistance(current - change);
        } else if (isRightClick) {
            settings.setHunterSpawnDistance(current + change);
        }
        
        refresh();
    }

    /**
     * 重置为默认值
     */
    private void resetToDefaults() {
        settings.setWitherMaxHealth(2000.0);
        settings.setWitherHealingReduction(0.5);
        settings.setWitherAttackInterval(5);
        settings.setWitherAttackDamage(10.0);
        settings.setWitherShieldDuration(300);
        settings.setWitherShieldReduction(0.75);
        settings.setHunterSpawnDistance(200);
        settings.setWitherCanMove(false);
        settings.setWitherTargetHuntersOnly(true);
        settings.setWitherDestroyBlocks(false);
        
        refresh();
        ComponentUtil.sendMessage(player, ComponentUtil.success("守卫模式设置已重置为默认值"));
    }

    /**
     * 处理返回
     */
    private void handleBack() {
        close();
        
        // 返回房间设置GUI
        Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
        if (currentRoom != null) {
            plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
        } else {
            ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
            plugin.getGuiManager().openRoomListGui(player);
        }
    }

    /**
     * 处理凋零效果持续时间设置
     */
    private void handleWitherEffectDuration(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        int current = settings.getWitherEffectDuration();
        int change = isShiftClick ? 60 : 20; // Shift: 3秒, 普通: 1秒 (20 ticks = 1秒)
        int newValue = current;

        if (isLeftClick) {
            newValue = Math.max(20, current - change); // 最小1秒
        } else if (isRightClick) {
            newValue = Math.min(200, current + change); // 最大10秒
        }

        if (newValue != current) {
            settings.setWitherEffectDuration(newValue);
            double seconds = newValue / 20.0;
            if (newValue == 200 && current + change > 200) {
                sendError("凋零效果时长已达到最大值(10秒)！");
                playErrorSound();
            } else if (newValue == 20 && current - change < 20) {
                sendError("凋零效果时长已达到最小值(1秒)！");
                playErrorSound();
            } else {
                sendSuccess("凋零效果时长已设置为: " + seconds + " 秒");
            }
        }

        refresh();
    }

    /**
     * 处理凋零效果等级设置
     */
    private void handleWitherEffectLevel(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        int current = settings.getWitherEffectLevel();
        int newValue = current;

        if (isLeftClick) {
            newValue = Math.max(0, current - 1); // 最小I级(0)
        } else if (isRightClick) {
            newValue = Math.min(2, current + 1); // 最大III级(2)
        }

        if (newValue != current) {
            settings.setWitherEffectLevel(newValue);
            String[] levelNames = {"I", "II", "III"};
            String levelName = levelNames[newValue];

            if (newValue == 2 && current + 1 > 2) {
                sendError("凋零效果等级已达到最高级(III级)！");
                playErrorSound();
            } else if (newValue == 0 && current - 1 < 0) {
                sendError("凋零效果等级已达到最低级(I级)！");
                playErrorSound();
            } else {
                sendSuccess("凋零效果等级已设置为: 凋零" + levelName);
            }
        }

        refresh();
    }
}
