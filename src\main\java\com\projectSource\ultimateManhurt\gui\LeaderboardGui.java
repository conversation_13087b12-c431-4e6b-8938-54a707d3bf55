package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.data.PlayerData;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 排行榜GUI
 */
public class LeaderboardGui extends BaseGui {
    
    private LeaderboardType currentType = LeaderboardType.WIN_RATE;
    
    public LeaderboardGui(UltimateManhurt plugin, Player player) {
        super(plugin, player, "排行榜", 54);
        setupGui();
    }

    @Override
    protected void setupGui() {
        // 标题
        setItem(4, createItem(Material.GOLDEN_APPLE, "<gold><bold>排行榜",
            "<gray>查看全服玩家排名"));
        
        // 排行榜类型选择
        setItem(19, createTypeItem(Material.EMERALD, "胜率排行",
            currentType == LeaderboardType.WIN_RATE));
        setItem(20, createTypeItem(Material.DIAMOND_SWORD, "击杀排行",
            currentType == LeaderboardType.KILLS));
        setItem(21, createTypeItem(Material.EXPERIENCE_BOTTLE, "游戏数排行",
            currentType == LeaderboardType.GAMES));
        setItem(22, createTypeItem(Material.CLOCK, "游戏时长排行",
            currentType == LeaderboardType.PLAYTIME));
        setItem(23, createTypeItem(Material.DRAGON_HEAD, "最快击龙排行",
            currentType == LeaderboardType.FASTEST_DRAGON));
        setItem(24, createTypeItem(Material.TOTEM_OF_UNDYING, "最长存活排行",
            currentType == LeaderboardType.LONGEST_SURVIVAL));
        setItem(25, createTypeItem(Material.FIRE_CHARGE, "连杀排行",
            currentType == LeaderboardType.KILL_STREAK));
        
        // 显示排行榜
        displayLeaderboard();
        
        // 返回按钮
        setItem(49, createItem(Material.BARRIER, "<red>返回", "<gray>点击返回"));
        
        // 装饰性物品
        for (int i = 0; i < this.inventory.getSize(); i++) {
            if (this.inventory.getItem(i) == null) {
                setItem(i, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));
            }
        }
    }

    /**
     * 创建排行榜类型选择物品
     */
    private ItemStack createTypeItem(Material material, String name, boolean selected) {
        String status = selected ? "<green>已选择" : "<gray>点击选择";
        String prefix = selected ? "<yellow><bold>" : "<white>";

        return createItem(material, prefix + name,
            "<gray>状态: " + status,
            "",
            selected ? "<green>当前选择的排行榜类型" : "<yellow>点击切换到此排行榜"
        );
    }
    
    private void displayLeaderboard() {
        // 清除之前的排行榜显示
        for (int i = 28; i <= 34; i++) {
            setItem(i, null);
        }
        
        // 获取所有玩家数据
        List<PlayerData> allPlayerData = getAllPlayerData();
        
        // 根据当前类型排序
        List<PlayerData> sortedData = sortPlayerData(allPlayerData, currentType);
        
        // 显示前7名
        for (int i = 0; i < Math.min(7, sortedData.size()); i++) {
            PlayerData data = sortedData.get(i);
            ItemStack item = createLeaderboardItem(i + 1, data, currentType);
            setItem(28 + i, item);
        }
    }
    
    private List<PlayerData> getAllPlayerData() {
        // 从DataManager获取所有玩家数据
        return this.plugin.getDataManager().getAllPlayerData();
    }
    
    private List<PlayerData> sortPlayerData(List<PlayerData> data, LeaderboardType type) {
        return data.stream()
            .filter(playerData -> playerData.getTotalGames() > 0) // 至少玩过一局
            .sorted((a, b) -> {
                switch (type) {
                    case WIN_RATE:
                        return Double.compare(b.getWinRate(), a.getWinRate());
                    case KILLS:
                        return Integer.compare(b.getTotalKills(), a.getTotalKills());
                    case GAMES:
                        return Integer.compare(b.getTotalGames(), a.getTotalGames());
                    case PLAYTIME:
                        return Long.compare(b.getTotalPlayTime(), a.getTotalPlayTime());
                    case FASTEST_DRAGON:
                        if (a.getFastestDragonKill() == Long.MAX_VALUE && b.getFastestDragonKill() == Long.MAX_VALUE) {
                            return 0;
                        } else if (a.getFastestDragonKill() == Long.MAX_VALUE) {
                            return 1;
                        } else if (b.getFastestDragonKill() == Long.MAX_VALUE) {
                            return -1;
                        } else {
                            return Long.compare(a.getFastestDragonKill(), b.getFastestDragonKill());
                        }
                    case LONGEST_SURVIVAL:
                        return Integer.compare(b.getLongestSurvival(), a.getLongestSurvival());
                    case KILL_STREAK:
                        return Integer.compare(b.getMaxKillStreak(), a.getMaxKillStreak());
                    default:
                        return 0;
                }
            })
            .collect(Collectors.toList());
    }
    
    private ItemStack createLeaderboardItem(int rank, PlayerData data, LeaderboardType type) {
        Material material = getRankMaterial(rank);
        String rankColor = getRankColor(rank);
        
        // 使用缓存获取玩家名字
        String playerName = plugin.getPlayerCacheManager().getPlayerName(data.getPlayerId());
        
        String value = getValueString(data, type);
        
        return createItem(material, rankColor + "#" + rank + " " + playerName,
            "<gray>数值: <white>" + value,
            "<gray>总游戏: <white>" + data.getTotalGames(),
            "<gray>胜率: <yellow>" + String.format("%.1f%%", data.getWinRate() * 100),
            "",
            "<yellow>点击查看详细统计"
        );
    }
    
    private Material getRankMaterial(int rank) {
        switch (rank) {
            case 1: return Material.GOLD_INGOT;
            case 2: return Material.IRON_INGOT;
            case 3: return Material.COPPER_INGOT;
            default: return Material.COAL;
        }
    }
    
    private String getRankColor(int rank) {
        switch (rank) {
            case 1: return "<gold><bold>";
            case 2: return "<gray><bold>";
            case 3: return "<#CD7F32><bold>"; // 铜色
            default: return "<white>";
        }
    }
    
    private String getValueString(PlayerData data, LeaderboardType type) {
        switch (type) {
            case WIN_RATE:
                return String.format("%.1f%%", data.getWinRate() * 100);
            case KILLS:
                return String.valueOf(data.getTotalKills());
            case GAMES:
                return String.valueOf(data.getTotalGames());
            case PLAYTIME:
                return formatPlayTime(data.getTotalPlayTime());
            case FASTEST_DRAGON:
                return data.getFastestDragonKill() != Long.MAX_VALUE ? 
                    formatTime(data.getFastestDragonKill()) : "未记录";
            case LONGEST_SURVIVAL:
                return data.getLongestSurvival() > 0 ? 
                    formatTime(data.getLongestSurvival() * 1000L) : "未记录";
            case KILL_STREAK:
                return String.valueOf(data.getMaxKillStreak());
            default:
                return "未知";
        }
    }
    
    @Override
    public void handleClick(InventoryClickEvent event) {
        event.setCancelled(true);

        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        if (event.getClickedInventory() != this.inventory) {
            return;
        }

        ItemStack item = event.getCurrentItem();
        if (item == null || item.getType() == Material.AIR) {
            return;
        }

        this.playClickSound();
        
        int slot = event.getSlot();
        
        // 排行榜类型切换
        if (slot >= 19 && slot <= 25) {
            LeaderboardType newType = LeaderboardType.values()[slot - 19];
            if (newType != currentType) {
                currentType = newType;
                setupGui(); // 重新设置界面
            }
        }
        // 查看玩家详细统计
        else if (slot >= 28 && slot <= 34) {
            int rank = slot - 28 + 1; // 计算排名（1-7）
            List<PlayerData> allPlayerData = getAllPlayerData();
            List<PlayerData> sortedData = sortPlayerData(allPlayerData, currentType);

            if (rank <= sortedData.size()) {
                PlayerData targetData = sortedData.get(rank - 1);
                close();
                this.plugin.getGuiManager().openPlayerStatsGui(this.player, targetData.getPlayerId());
            }
        }
        // 返回按钮
        else if (slot == 49) {
            this.player.closeInventory();
        }
    }
    
    private String formatPlayTime(long milliseconds) {
        long hours = milliseconds / 3600000;
        long minutes = (milliseconds % 3600000) / 60000;
        
        if (hours > 0) {
            return String.format("%d小时%d分钟", hours, minutes);
        } else {
            return String.format("%d分钟", minutes);
        }
    }
    
    private String formatTime(long milliseconds) {
        long seconds = milliseconds / 1000;
        long minutes = seconds / 60;
        long secs = seconds % 60;
        
        if (minutes > 0) {
            return String.format("%d分%d秒", minutes, secs);
        } else {
            return String.format("%d秒", secs);
        }
    }
    
    public enum LeaderboardType {
        WIN_RATE, KILLS, GAMES, PLAYTIME, FASTEST_DRAGON, LONGEST_SURVIVAL, KILL_STREAK
    }
}
