package com.projectSource.ultimateManhurt.banpick;

import org.bukkit.Material;

/**
 * Ban Pick系统中的可选择物品
 * 原则：只包含可合成且在速通中真正有用的物品
 */
public enum BanPickItem {

    // === 移动工具类 ===
    BOAT(Material.OAK_BOAT, "船", "水上快速移动，速通必备", BanPickCategory.MOBILITY),

    // === 防御装备类 ===
    SHIELD(Material.SHIELD, "盾牌", "阻挡攻击和弹射物", BanPickCategory.DEFENSE),
    IRON_HELMET(Material.IRON_HELMET, "铁头盔", "基础头部防护", BanPickCategory.DEFENSE),
    IRON_CHESTPLATE(Material.IRON_CHESTPLATE, "铁胸甲", "基础胸部防护", BanPickCategory.DEFENSE),
    IRON_LEGGINGS(Material.IRON_LEGGINGS, "铁护腿", "基础腿部防护", BanPickCategory.DEFENSE),
    IRON_BOOTS(Material.IRON_BOOTS, "铁靴子", "基础脚部防护", BanPickCategory.DEFENSE),
    DIAMOND_HELMET(Material.DIAMOND_HELMET, "钻石头盔", "高级头部防护", BanPickCategory.DEFENSE),
    DIAMOND_CHESTPLATE(Material.DIAMOND_CHESTPLATE, "钻石胸甲", "高级胸部防护", BanPickCategory.DEFENSE),
    DIAMOND_LEGGINGS(Material.DIAMOND_LEGGINGS, "钻石护腿", "高级腿部防护", BanPickCategory.DEFENSE),
    DIAMOND_BOOTS(Material.DIAMOND_BOOTS, "钻石靴子", "高级脚部防护", BanPickCategory.DEFENSE),

    // === 战斗武器类 ===
    IRON_SWORD(Material.IRON_SWORD, "铁剑", "基础战斗武器", BanPickCategory.COMBAT),
    DIAMOND_SWORD(Material.DIAMOND_SWORD, "钻石剑", "高级战斗武器", BanPickCategory.COMBAT),
    BOW(Material.BOW, "弓", "远程攻击武器", BanPickCategory.COMBAT),
    CROSSBOW(Material.CROSSBOW, "弩", "高威力远程武器", BanPickCategory.COMBAT),
    ARROW(Material.ARROW, "箭", "弓弩弹药", BanPickCategory.COMBAT),

    // === 挖掘工具类 ===
    IRON_PICKAXE(Material.IRON_PICKAXE, "铁镐", "基础挖掘工具", BanPickCategory.TOOLS),
    DIAMOND_PICKAXE(Material.DIAMOND_PICKAXE, "钻石镐", "高效挖掘工具", BanPickCategory.TOOLS),
    IRON_AXE(Material.IRON_AXE, "铁斧", "基础砍伐工具", BanPickCategory.TOOLS),
    DIAMOND_AXE(Material.DIAMOND_AXE, "钻石斧", "高效砍伐工具", BanPickCategory.TOOLS),
    IRON_SHOVEL(Material.IRON_SHOVEL, "铁锹", "基础挖掘工具", BanPickCategory.TOOLS),
    DIAMOND_SHOVEL(Material.DIAMOND_SHOVEL, "钻石锹", "高效挖掘工具", BanPickCategory.TOOLS),

    // === 战术物品类 ===
    BED(Material.RED_BED, "床", "下界爆炸战术", BanPickCategory.TACTICAL),
    TNT(Material.TNT, "TNT", "爆炸战术工具", BanPickCategory.TACTICAL),
    WATER_BUCKET(Material.WATER_BUCKET, "水桶", "灭火、缓冲、阻挡", BanPickCategory.TACTICAL),
    LAVA_BUCKET(Material.LAVA_BUCKET, "岩浆桶", "攻击、陷阱工具", BanPickCategory.TACTICAL),

    // === 附魔相关类 ===
    ENCHANTING_TABLE(Material.ENCHANTING_TABLE, "附魔台", "装备附魔强化", BanPickCategory.ENCHANTING),
    ANVIL(Material.ANVIL, "铁砧", "装备修复和合并", BanPickCategory.ENCHANTING),
    BOOKSHELF(Material.BOOKSHELF, "书架", "提升附魔等级", BanPickCategory.ENCHANTING),

    // === 食物类 ===
    GOLDEN_APPLE(Material.GOLDEN_APPLE, "金苹果", "强力恢复食物", BanPickCategory.FOOD),
    COOKED_BEEF(Material.COOKED_BEEF, "熟牛肉", "高饱食度食物", BanPickCategory.FOOD),
    BREAD(Material.BREAD, "面包", "基础食物", BanPickCategory.FOOD),

    // === 酿造类 ===
    BREWING_STAND(Material.BREWING_STAND, "酿造台", "制作药水", BanPickCategory.BREWING),
    GLASS_BOTTLE(Material.GLASS_BOTTLE, "玻璃瓶", "装药水容器", BanPickCategory.BREWING);

    private final Material material;
    private final String displayName;
    private final String description;
    private final BanPickCategory category;
    
    BanPickItem(Material material, String displayName, String description, BanPickCategory category) {
        this.material = material;
        this.displayName = displayName;
        this.description = description;
        this.category = category;
    }
    
    public Material getMaterial() {
        return material;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public BanPickCategory getCategory() {
        return category;
    }
    
    /**
     * 根据Material获取BanPickItem
     */
    public static BanPickItem fromMaterial(Material material) {
        for (BanPickItem item : values()) {
            if (item.includesMaterial(material)) {
                return item;
            }
        }
        return null;
    }
    
    /**
     * 获取指定类别的所有物品
     */
    public static BanPickItem[] getByCategory(BanPickCategory category) {
        return java.util.Arrays.stream(values())
                .filter(item -> item.getCategory() == category)
                .toArray(BanPickItem[]::new);
    }

    /**
     * 获取该物品相关的所有材料
     * 对于代表一类物品的，返回所有变种
     */
    public Material[] getAllRelatedMaterials() {
        switch (this) {
            case BOAT:
                return new Material[] {
                    Material.OAK_BOAT, Material.SPRUCE_BOAT, Material.BIRCH_BOAT,
                    Material.JUNGLE_BOAT, Material.ACACIA_BOAT, Material.DARK_OAK_BOAT,
                    Material.MANGROVE_BOAT, Material.CHERRY_BOAT, Material.BAMBOO_RAFT
                };
            case BED:
                return new Material[] {
                    Material.WHITE_BED, Material.ORANGE_BED, Material.MAGENTA_BED,
                    Material.LIGHT_BLUE_BED, Material.YELLOW_BED, Material.LIME_BED,
                    Material.PINK_BED, Material.GRAY_BED, Material.LIGHT_GRAY_BED,
                    Material.CYAN_BED, Material.PURPLE_BED, Material.BLUE_BED,
                    Material.BROWN_BED, Material.GREEN_BED, Material.RED_BED, Material.BLACK_BED
                };
            default:
                return new Material[] { this.material };
        }
    }

    /**
     * 检查指定材料是否属于该Ban Pick物品
     */
    public boolean includesMaterial(Material material) {
        for (Material relatedMaterial : getAllRelatedMaterials()) {
            if (relatedMaterial == material) {
                return true;
            }
        }
        return false;
    }
}
