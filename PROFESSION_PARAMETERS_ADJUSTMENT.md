# 职业参数调整

## 🔄 调整概览

### 猪灵技能调整
1. **狂意范围：** 从15格增加到30格
2. **技能描述：** 在Profession.java中显示具体数值

### 萨满技能调整
1. **龙卷风暴范围：** 从8格增加到16格
2. **无敌机制：** 从抗性提升改为真正的不可攻击状态
3. **技能描述：** 在Profession.java中显示具体数值

## 🐷 猪灵狂意调整

### 范围扩大
**修改前：**
```java
if (distance <= 15.0) { // 15格范围
```

**修改后：**
```java
if (distance <= 30.0) { // 30格范围
```

### 技能描述更新
**修改前：**
```
"被动的使自己和一定范围内的友军获得速度一加成，使得一定范围内的速通者高亮并得到10%的伤害加深"
```

**修改后：**
```
"被动的使自己和30格范围内的友军获得速度一加成，使得30格范围内的速通者高亮并得到10%的伤害加深"
```

### 影响分析
- **覆盖范围：** 30格范围覆盖更大区域，光环效果更强
- **战术价值：** 猪灵可以在更远距离支援队友
- **定位强化：** 更符合团队支援核心的定位

## 🐺 萨满龙卷风暴调整

### 风场范围扩大
**修改前：**
```java
if (distance <= 8.0) { // 8格范围
```

**修改后：**
```java
if (distance <= 16.0) { // 16格范围
```

### 无敌机制重做
**修改前（抗性提升）：**
```java
// 萨满获得无敌状态
player.addPotionEffect(new PotionEffect(PotionEffectType.DAMAGE_RESISTANCE, 100, 4)); // 5秒抗性5（近似无敌）
```

**修改后（真正无敌）：**
```java
// 萨满获得真正的无敌状态（不可攻击）
long endTime = System.currentTimeMillis() + 5000; // 5秒
shamanInvulnerableStates.put(playerId, endTime);

// 5秒后自动移除无敌状态
Bukkit.getScheduler().runTaskLater(plugin, () -> {
    shamanInvulnerableStates.remove(playerId);
    ComponentUtil.sendMessage(player, ComponentUtil.warning("龙卷风暴结束，无敌状态消失"));
}, 100L); // 5秒 = 100 tick
```

### 无敌状态检查
**新增功能：**
```java
/**
 * 检查萨满是否处于龙卷风暴无敌状态
 */
public boolean isShamanInvulnerable(UUID playerId) {
    Long endTime = shamanInvulnerableStates.get(playerId);
    if (endTime == null) {
        return false;
    }
    
    // 检查是否过期
    if (System.currentTimeMillis() > endTime) {
        shamanInvulnerableStates.remove(playerId);
        return false;
    }
    
    return true;
}
```

### 伤害事件拦截
**在ProfessionListener中添加：**
```java
// 检查受害者是否是处于龙卷风暴无敌状态的萨满
if (plugin.getProfessionManager().getActiveSkillHandler().isShamanInvulnerable(victim.getUniqueId())) {
    event.setCancelled(true);
    ComponentUtil.sendMessage(attacker, ComponentUtil.warning("萨满处于龙卷风暴无敌状态，无法攻击！"));
    return;
}
```

### 技能描述更新
**修改前：**
```
"将自己周围一定范围变成风场，里面的玩家获得减速4的效果，萨满处于无敌状态，所有在风场内的猎人根据风场内猎人数量受到魔法伤害(初始0.25*数量逐渐提高0.25最大1.5*数量)，风场持续5s"
```

**修改后：**
```
"将自己周围16格范围变成风场，里面的玩家获得减速4的效果，萨满处于不可攻击状态，所有在风场内的猎人根据风场内猎人数量受到魔法伤害(初始0.25*数量逐渐提高0.25最大1.5*数量)，风场持续5s"
```

## 📊 调整影响分析

### 猪灵影响
**范围扩大效果：**
- **友军支援：** 30格范围内的猎人获得速度1加成
- **敌军标记：** 30格范围内的速通者被高亮和伤害加深
- **战术价值：** 可以在更安全的距离提供支援

**平衡考虑：**
- 范围虽然扩大，但仍需要主动攻击才能触发
- 光环效果持续时间仍然是5秒
- 增强了团队支援能力，但个人战斗力不变

### 萨满影响
**风场范围扩大：**
- **控制范围：** 从8格扩大到16格，控制能力显著增强
- **影响玩家：** 更多猎人可能被卷入风场
- **伤害潜力：** 更大范围意味着可能影响更多敌人

**无敌机制改进：**
- **真正无敌：** 完全免疫所有伤害，不再是近似无敌
- **明确反馈：** 攻击者会收到明确的无法攻击提示
- **状态管理：** 精确的时间控制和状态清理

## 🎮 游戏体验改进

### 猪灵玩家体验
- **支援范围：** 30格范围让猪灵可以在更安全的位置支援
- **战术灵活：** 不需要过于接近前线就能提供光环效果
- **团队价值：** 更强的团队支援能力

### 萨满玩家体验
- **控制能力：** 16格风场提供更强的区域控制
- **安全保障：** 真正的无敌状态确保技能期间的安全
- **战术价值：** 更大的影响范围和更可靠的保护

### 敌方玩家体验
- **猪灵应对：** 需要在更大范围内警惕猪灵的光环效果
- **萨满应对：** 面对更大的风场范围和真正的无敌状态
- **战术调整：** 需要调整对这两个职业的应对策略

## 🎯 技术实现亮点

### 状态管理系统
```java
// 萨满龙卷风暴无敌状态 <玩家UUID, 结束时间>
private final Map<UUID, Long> shamanInvulnerableStates = new HashMap<>();
```

### 精确时间控制
```java
// 检查是否过期
if (System.currentTimeMillis() > endTime) {
    shamanInvulnerableStates.remove(playerId);
    return false;
}
```

### 事件拦截机制
```java
// 在伤害事件中检查无敌状态
if (plugin.getProfessionManager().getActiveSkillHandler().isShamanInvulnerable(victim.getUniqueId())) {
    event.setCancelled(true);
    // 提供明确反馈
}
```

## 🎉 调整总结

成功完成了职业参数的精确调整：

- ✅ **猪灵范围：** 从15格扩大到30格，增强团队支援能力
- ✅ **萨满风场：** 从8格扩大到16格，增强区域控制能力
- ✅ **萨满无敌：** 从抗性提升改为真正不可攻击状态
- ✅ **数值显示：** 在Profession.java中显示所有具体数值
- ✅ **技术实现：** 完善的状态管理和事件拦截系统

### 关键改进点
1. **精确数值：** 所有技能描述都包含具体的数值参数
2. **真正无敌：** 萨满龙卷风暴期间完全免疫伤害
3. **范围优化：** 猪灵和萨满的影响范围都得到合理扩大
4. **状态管理：** 完善的时间控制和状态清理机制

现在这两个职业的技能更加强大和精确，同时保持了良好的平衡性！🎮⚖️✨

**重要特点：**
- 猪灵成为更强的团队支援核心
- 萨满拥有更强的区域控制能力
- 技能描述更加清晰和具体
- 技术实现更加完善和可靠
