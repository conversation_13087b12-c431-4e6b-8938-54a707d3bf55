package com.projectSource.ultimateManhurt.game.guard;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;

/**
 * 守卫塔结构生成器
 * 在(0,0)位置生成守卫塔结构
 */
public class TowerGenerator {
    
    /**
     * 生成守卫塔结构
     */
    public static Location generateTower(World world) {
        // 找到合适的生成位置
        Location towerLocation = findTowerLocation(world);
        
        int baseX = towerLocation.getBlockX();
        int baseY = towerLocation.getBlockY();
        int baseZ = towerLocation.getBlockZ();
        
        // 清理区域
        clearArea(world, baseX, baseY, baseZ);
        
        // 生成塔结构
        generateFoundation(world, baseX, baseY, baseZ);
        generateTowerBody(world, baseX, baseY, baseZ);
        generateTowerTop(world, baseX, baseY, baseZ);
        generateDecorations(world, baseX, baseY, baseZ);
        
        return towerLocation.clone().add(0, 2, 0); // 返回凋零生成位置
    }
    
    /**
     * 寻找塔的位置
     */
    public static Location findTowerLocation(World world) {
        Location baseLocation = new Location(world, 0.5, 0, 0.5);

        // 获取(0,0)位置的最高点
        int highestY = world.getHighestBlockYAt(0, 0);

        // 确保塔建在地面上方，即使在海底也要在水面上
        int towerY = Math.max(highestY + 1, world.getSeaLevel() + 1);

        // 如果在海底，需要在水面上建塔
        if (highestY < world.getSeaLevel()) {
            towerY = world.getSeaLevel() + 1;
            System.out.println("检测到(0,0)在海底，将在水面上建造守卫塔，高度: " + towerY);
        }

        baseLocation.setY(towerY);
        return baseLocation;
    }
    
    /**
     * 清理建造区域
     */
    private static void clearArea(World world, int centerX, int centerY, int centerZ) {
        // 清理一个7x10x7的区域
        for (int x = centerX - 3; x <= centerX + 3; x++) {
            for (int y = centerY; y <= centerY + 9; y++) {
                for (int z = centerZ - 3; z <= centerZ + 3; z++) {
                    Block block = world.getBlockAt(x, y, z);
                    if (!block.getType().isSolid() || y > centerY) {
                        block.setType(Material.AIR);
                    }
                }
            }
        }
    }
    
    /**
     * 生成地基
     */
    private static void generateFoundation(World world, int centerX, int centerY, int centerZ) {
        // 外层地基 (7x7)
        for (int x = centerX - 3; x <= centerX + 3; x++) {
            for (int z = centerZ - 3; z <= centerZ + 3; z++) {
                world.getBlockAt(x, centerY - 1, z).setType(Material.OBSIDIAN);
            }
        }
        
        // 内层地基 (5x5)
        for (int x = centerX - 2; x <= centerX + 2; x++) {
            for (int z = centerZ - 2; z <= centerZ + 2; z++) {
                world.getBlockAt(x, centerY, z).setType(Material.BLACKSTONE);
            }
        }
        
        // 中心平台 (3x3)
        for (int x = centerX - 1; x <= centerX + 1; x++) {
            for (int z = centerZ - 1; z <= centerZ + 1; z++) {
                world.getBlockAt(x, centerY + 1, z).setType(Material.POLISHED_BLACKSTONE);
            }
        }
    }
    
    /**
     * 生成塔身
     */
    private static void generateTowerBody(World world, int centerX, int centerY, int centerZ) {
        // 塔身高度为6格
        for (int y = centerY + 2; y <= centerY + 7; y++) {
            // 外墙 (5x5空心)
            for (int x = centerX - 2; x <= centerX + 2; x++) {
                for (int z = centerZ - 2; z <= centerZ + 2; z++) {
                    // 只在边缘放置方块
                    if (x == centerX - 2 || x == centerX + 2 || z == centerZ - 2 || z == centerZ + 2) {
                        world.getBlockAt(x, y, z).setType(Material.POLISHED_BLACKSTONE_BRICKS);
                    }
                }
            }
            
            // 在特定高度添加支撑
            if (y == centerY + 4) {
                // 内部支撑柱
                world.getBlockAt(centerX - 1, y, centerZ - 1).setType(Material.POLISHED_BLACKSTONE_BRICKS);
                world.getBlockAt(centerX + 1, y, centerZ - 1).setType(Material.POLISHED_BLACKSTONE_BRICKS);
                world.getBlockAt(centerX - 1, y, centerZ + 1).setType(Material.POLISHED_BLACKSTONE_BRICKS);
                world.getBlockAt(centerX + 1, y, centerZ + 1).setType(Material.POLISHED_BLACKSTONE_BRICKS);
            }
        }
    }
    
    /**
     * 生成塔顶
     */
    private static void generateTowerTop(World world, int centerX, int centerY, int centerZ) {
        int topY = centerY + 8;
        
        // 塔顶平台 (5x5)
        for (int x = centerX - 2; x <= centerX + 2; x++) {
            for (int z = centerZ - 2; z <= centerZ + 2; z++) {
                world.getBlockAt(x, topY, z).setType(Material.POLISHED_BLACKSTONE);
            }
        }
        
        // 塔顶边缘装饰
        for (int x = centerX - 2; x <= centerX + 2; x++) {
            for (int z = centerZ - 2; z <= centerZ + 2; z++) {
                // 只在边缘放置装饰
                if (x == centerX - 2 || x == centerX + 2 || z == centerZ - 2 || z == centerZ + 2) {
                    world.getBlockAt(x, topY + 1, z).setType(Material.BLACKSTONE_WALL);
                }
            }
        }
        
        // 中心信标
        world.getBlockAt(centerX, topY + 1, centerZ).setType(Material.BEACON);
        
        // 角落装饰塔
        world.getBlockAt(centerX - 2, topY + 1, centerZ - 2).setType(Material.POLISHED_BLACKSTONE_BRICKS);
        world.getBlockAt(centerX + 2, topY + 1, centerZ - 2).setType(Material.POLISHED_BLACKSTONE_BRICKS);
        world.getBlockAt(centerX - 2, topY + 1, centerZ + 2).setType(Material.POLISHED_BLACKSTONE_BRICKS);
        world.getBlockAt(centerX + 2, topY + 1, centerZ + 2).setType(Material.POLISHED_BLACKSTONE_BRICKS);
        
        world.getBlockAt(centerX - 2, topY + 2, centerZ - 2).setType(Material.BLACKSTONE_WALL);
        world.getBlockAt(centerX + 2, topY + 2, centerZ - 2).setType(Material.BLACKSTONE_WALL);
        world.getBlockAt(centerX - 2, topY + 2, centerZ + 2).setType(Material.BLACKSTONE_WALL);
        world.getBlockAt(centerX + 2, topY + 2, centerZ + 2).setType(Material.BLACKSTONE_WALL);
    }
    
    /**
     * 生成装饰元素
     */
    private static void generateDecorations(World world, int centerX, int centerY, int centerZ) {
        // 在塔身中间添加窗户
        int windowY = centerY + 5;
        
        // 四个方向的窗户
        world.getBlockAt(centerX - 2, windowY, centerZ).setType(Material.IRON_BARS);
        world.getBlockAt(centerX + 2, windowY, centerZ).setType(Material.IRON_BARS);
        world.getBlockAt(centerX, windowY, centerZ - 2).setType(Material.IRON_BARS);
        world.getBlockAt(centerX, windowY, centerZ + 2).setType(Material.IRON_BARS);
        
        // 在地基周围添加装饰柱
        int[] decorativePositions = {-3, 3};
        for (int x : decorativePositions) {
            for (int z : decorativePositions) {
                // 装饰柱
                world.getBlockAt(centerX + x, centerY, centerZ + z).setType(Material.POLISHED_BLACKSTONE_BRICKS);
                world.getBlockAt(centerX + x, centerY + 1, centerZ + z).setType(Material.POLISHED_BLACKSTONE_BRICKS);
                world.getBlockAt(centerX + x, centerY + 2, centerZ + z).setType(Material.BLACKSTONE_WALL);
                
                // 装饰火把
                if (x == -3 && z == -3) {
                    world.getBlockAt(centerX + x, centerY + 3, centerZ + z).setType(Material.SOUL_TORCH);
                } else if (x == 3 && z == -3) {
                    world.getBlockAt(centerX + x, centerY + 3, centerZ + z).setType(Material.SOUL_TORCH);
                } else if (x == -3 && z == 3) {
                    world.getBlockAt(centerX + x, centerY + 3, centerZ + z).setType(Material.SOUL_TORCH);
                } else if (x == 3 && z == 3) {
                    world.getBlockAt(centerX + x, centerY + 3, centerZ + z).setType(Material.SOUL_TORCH);
                }
            }
        }
        
        // 在塔基周围添加装饰性方块
        Material[] decorativeBlocks = {
            Material.CHISELED_POLISHED_BLACKSTONE,
            Material.GILDED_BLACKSTONE,
            Material.POLISHED_BLACKSTONE_BRICKS
        };
        
        // 随机在地基边缘放置装饰方块
        for (int x = centerX - 3; x <= centerX + 3; x++) {
            for (int z = centerZ - 3; z <= centerZ + 3; z++) {
                if ((x == centerX - 3 || x == centerX + 3 || z == centerZ - 3 || z == centerZ + 3) &&
                    Math.random() < 0.3) { // 30%概率放置装饰
                    Material decorative = decorativeBlocks[(int)(Math.random() * decorativeBlocks.length)];
                    world.getBlockAt(x, centerY, z).setType(decorative);
                }
            }
        }
        
        // 在塔内部添加一些装饰
        world.getBlockAt(centerX, centerY + 2, centerZ).setType(Material.SOUL_LANTERN);
        world.getBlockAt(centerX, centerY + 6, centerZ).setType(Material.SOUL_LANTERN);
    }
    
    /**
     * 获取凋零应该生成的位置
     */
    public static Location getWitherSpawnLocation(World world) {
        Location towerLocation = findTowerLocation(world);
        return towerLocation.clone().add(0, 2, 0); // 在塔内部，地面上方3格
    }

    /**
     * 获取速通者在塔顶的出生位置
     */
    public static Location getSpeedrunnerSpawnLocation(World world) {
        Location towerLocation = findTowerLocation(world);
        // 塔顶平台在towerLocation + 8，速通者出生在平台上方
        return towerLocation.clone().add(0, 9, 0);
    }
    
    /**
     * 检查位置是否适合建造塔
     */
    public static boolean isValidTowerLocation(World world, int x, int z) {
        // 检查周围区域是否平坦
        int baseY = world.getHighestBlockYAt(x, z);
        
        for (int checkX = x - 3; checkX <= x + 3; checkX++) {
            for (int checkZ = z - 3; checkZ <= z + 3; checkZ++) {
                int checkY = world.getHighestBlockYAt(checkX, checkZ);
                if (Math.abs(checkY - baseY) > 3) {
                    return false; // 地形变化太大
                }
            }
        }
        
        return true;
    }
}
