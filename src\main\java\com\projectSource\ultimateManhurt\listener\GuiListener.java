package com.projectSource.ultimateManhurt.listener;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.gui.BaseGui;
import com.projectSource.ultimateManhurt.profession.gui.ProfessionSelectionGui;
import com.projectSource.ultimateManhurt.game.GameState;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

/**
 * GUI事件监听器
 */
public class GuiListener implements Listener {

    private final UltimateManhurt plugin;

    // 防止无限循环重新打开GUI的保护机制
    private final Set<UUID> reopeningPlayers = new HashSet<>();

    public GuiListener(UltimateManhurt plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player player)) {
            return;
        }

        BaseGui gui = plugin.getGuiManager().getOpenGui(player);
        if (gui != null && gui.isThisInventory(event.getInventory())) {
            gui.handleClick(event);
        }
    }

    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player player)) {
            return;
        }

        BaseGui gui = plugin.getGuiManager().getOpenGui(player);
        if (gui != null && gui.isThisInventory(event.getInventory())) {
            plugin.getGuiManager().closeGui(player);
        }

        // 检查是否是职业选择GUI被关闭
        UUID playerId = player.getUniqueId();
        if (ProfessionSelectionGui.hasActiveProfessionGui(playerId)) {
            // 检查是否已经在重新打开过程中，避免无限循环
            if (reopeningPlayers.contains(playerId)) {
                plugin.getLogger().info("玩家 " + player.getName() + " 的GUI重新打开已在进行中，跳过");
                return;
            }

            ProfessionSelectionGui professionGui = ProfessionSelectionGui.getActiveProfessionGui(playerId);

            // 标记玩家正在重新打开过程中
            reopeningPlayers.add(playerId);

            // 延迟检查，给职业设置足够的时间完成
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                try {
                    // 再次检查是否还在活跃追踪中（如果职业选择成功，应该已经被移除）
                    if (ProfessionSelectionGui.hasActiveProfessionGui(playerId) &&
                        player.isOnline() &&
                        !plugin.getProfessionManager().hasPlayerProfession(playerId)) {

                        // 玩家确实还没有选择职业，重新打开GUI
                        plugin.getGuiManager().openProfessionSelectionGui(player, professionGui.getGameSession());
                        plugin.getLogger().info("重新为玩家 " + player.getName() + " 打开职业选择GUI");
                    } else {
                        plugin.getLogger().info("玩家 " + player.getName() + " 已选择职业或不再需要重新打开GUI");
                    }
                } finally {
                    // 无论如何都要移除标记，避免永久阻塞
                    reopeningPlayers.remove(playerId);
                }
            }, 5L); // 延迟5 tick，给职业设置更多时间
        }
    }
}
