package com.projectSource.ultimateManhurt.util;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextColor;
import net.kyori.adventure.text.format.TextDecoration;
import net.kyori.adventure.text.minimessage.MiniMessage;
import net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer;
import org.bukkit.entity.Player;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 现代Component API工具类
 * 提供文本组件创建、格式化和发送功能
 */
public class ComponentUtil {
    
    private static final MiniMessage MINI_MESSAGE = MiniMessage.miniMessage();
    private static final LegacyComponentSerializer LEGACY_SERIALIZER = LegacyComponentSerializer.legacyAmpersand();
    
    // 常用颜色
    public static final TextColor PRIMARY = TextColor.fromHexString("#00AAFF");
    public static final TextColor SECONDARY = TextColor.fromHexString("#FFD700");
    public static final TextColor SUCCESS = NamedTextColor.GREEN;
    public static final TextColor ERROR = NamedTextColor.RED;
    public static final TextColor WARNING = NamedTextColor.YELLOW;
    public static final TextColor INFO = NamedTextColor.AQUA;
    
    /**
     * 使用MiniMessage格式创建组件
     */
    public static Component parse(String miniMessage) {
        return MINI_MESSAGE.deserialize(miniMessage);
    }
    
    /**
     * 创建带前缀的消息
     */
    public static Component createMessage(String prefix, String message, TextColor color) {
        return Component.text()
                .append(Component.text("[" + prefix + "] ", PRIMARY, TextDecoration.BOLD))
                .append(Component.text(message, color))
                .build();
    }
    
    /**
     * 创建成功消息
     */
    public static Component success(String message) {
        return createMessage("✓", message, SUCCESS);
    }
    
    /**
     * 创建错误消息
     */
    public static Component error(String message) {
        return createMessage("✗", message, ERROR);
    }
    
    /**
     * 创建警告消息
     */
    public static Component warning(String message) {
        return createMessage("⚠", message, WARNING);
    }
    
    /**
     * 创建信息消息
     */
    public static Component info(String message) {
        return createMessage("ℹ", message, INFO);
    }
    
    /**
     * 创建渐变文本
     */
    public static Component gradient(String text, String startColor, String endColor) {
        return parse("<gradient:" + startColor + ":" + endColor + ">" + text + "</gradient>");
    }
    
    /**
     * 创建彩虹文本
     */
    public static Component rainbow(String text) {
        return parse("<rainbow>" + text + "</rainbow>");
    }
    
    /**
     * 创建闪烁文本
     */
    public static Component blinking(String text, TextColor color) {
        return Component.text(text, color, TextDecoration.OBFUSCATED);
    }
    
    /**
     * 创建标题组件
     */
    public static Component title(String text) {
        return Component.text()
                .append(Component.text("═══════════════════", SECONDARY))
                .append(Component.newline())
                .append(Component.text(text, PRIMARY, TextDecoration.BOLD))
                .append(Component.newline())
                .append(Component.text("═══════════════════", SECONDARY))
                .build();
    }
    
    /**
     * 创建分隔线
     */
    public static Component separator() {
        return Component.text("▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬", SECONDARY);
    }
    
    /**
     * 将Component转换为传统格式字符串
     */
    public static String toLegacy(Component component) {
        return LEGACY_SERIALIZER.serialize(component);
    }
    
    /**
     * 从传统格式字符串创建Component
     */
    public static Component fromLegacy(String legacy) {
        return LEGACY_SERIALIZER.deserialize(legacy);
    }
    
    /**
     * 向玩家发送消息
     */
    public static void sendMessage(Player player, Component message) {
        player.sendMessage(message);
    }
    
    /**
     * 向多个玩家发送消息
     */
    public static void sendMessage(List<Player> players, Component message) {
        players.forEach(player -> sendMessage(player, message));
    }
    
    /**
     * 向玩家发送标题
     */
    public static void sendTitle(Player player, Component title, Component subtitle, int fadeIn, int stay, int fadeOut) {
        player.showTitle(net.kyori.adventure.title.Title.title(title, subtitle, 
                net.kyori.adventure.title.Title.Times.times(
                        java.time.Duration.ofMillis(fadeIn * 50),
                        java.time.Duration.ofMillis(stay * 50),
                        java.time.Duration.ofMillis(fadeOut * 50)
                )));
    }
    
    /**
     * 向玩家发送Action Bar
     */
    public static void sendActionBar(Player player, Component message) {
        player.sendActionBar(message);
    }
    
    /**
     * 创建可点击的组件
     */
    public static Component clickable(String text, String command, String hover) {
        return Component.text(text, PRIMARY)
                .clickEvent(net.kyori.adventure.text.event.ClickEvent.runCommand(command))
                .hoverEvent(net.kyori.adventure.text.event.HoverEvent.showText(Component.text(hover)));
    }
    
    /**
     * 创建建议命令的组件
     */
    public static Component suggestCommand(String text, String command, String hover) {
        return Component.text(text, PRIMARY)
                .clickEvent(net.kyori.adventure.text.event.ClickEvent.suggestCommand(command))
                .hoverEvent(net.kyori.adventure.text.event.HoverEvent.showText(Component.text(hover)));
    }
}
