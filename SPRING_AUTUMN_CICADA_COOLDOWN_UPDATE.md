# 春秋蝉冷却机制更新

## 更新内容

修改了职业方源的春秋蝉被动技能的冷却机制，现在冷却期间也可以使用，但概率会根据冷却进度递减。

## 新的冷却机制

### 概率计算公式
```
最终概率 = 基础概率 × 冷却进度

其中：
- 基础概率 = 初始100% × 0.8^触发次数（最低20.9%）
- 冷却进度 = (总冷却时间 - 剩余冷却时间) / 总冷却时间
```

### 示例计算

假设360秒冷却，已经过了180秒：
- 冷却进度 = (360 - 180) / 360 = 0.5 (50%)
- 如果基础概率是100%，最终概率 = 100% × 0.5 = 50%
- 如果基础概率是80%（第一次触发后），最终概率 = 80% × 0.5 = 40%

### 概率特性
- 概率可以降到非常低的值（接近0%）
- 冷却刚开始时概率几乎为0，随着时间推移逐渐增加

## 代码修改

### 1. PassiveSkillHandler.java
- 移除了冷却期间直接返回false的逻辑
- 添加了基于冷却进度的概率调整
- 优化了冷却时间设置逻辑（只在非冷却状态下设置新冷却）
- 更新了触发消息，显示实际使用的概率

### 2. Profession.java
- 更新了春秋蝉的技能描述，说明新的冷却机制

## 使用体验改进

### 玩家反馈
- 触发时会显示实际使用的概率
- 如果在冷却中触发，会显示剩余冷却时间
- 如果不在冷却中触发，会显示下次的基础概率

### 平衡性考虑
- 冷却期间仍可使用，但概率大幅降低
- 避免了完全无法使用的挫败感
- 保持了技能的战略价值和平衡性

## 测试建议

1. **基础功能测试**：
   - 创建时光分身
   - 将血量降到13%以下
   - 验证触发概率和消息显示

2. **冷却期间测试**：
   - 触发一次春秋蝉后立即再次测试
   - 验证概率是否按冷却进度递减
   - 检查消息是否正确显示概率和剩余时间

3. **边界情况测试**：
   - 测试冷却刚开始时（概率接近0%）
   - 测试冷却即将结束时（概率接近基础概率）
   - 测试多次触发后的概率递减

## 预期效果

这个更新让春秋蝉在保持平衡性的同时提供了更好的用户体验：
- 不再有完全无法使用的"死区"
- 概率机制更加平滑和可预测，可以降到接近0%
- 玩家可以更好地规划技能使用时机
- 冷却刚开始时概率极低，但仍有微小的触发可能
