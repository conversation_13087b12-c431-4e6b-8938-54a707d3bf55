# Ban Pick机制详细流程说明

## 🎯 Ban Pick概念

Ban Pick是一种策略性的预选阶段，让两个阵营在游戏开始前互相禁用和锁定关键物品，增加游戏的策略深度和观赏性。

## 📋 完整流程

### 阶段1: 游戏启动检查
```
房主点击开始游戏
    ↓
检查房间设置中是否启用Ban Pick
    ↓
如果启用 → 进入Ban Pick阶段
如果禁用 → 直接开始游戏
```

### 阶段2: Ban Pick轮次流程
```
第1轮: 捕猎者Ban (30秒)
    ↓
第2轮: 速通者Ban (30秒)
    ↓
第3轮: 捕猎者Pick (30秒)
    ↓
第4轮: 速通者Pick (30秒)
    ↓
第5轮: 捕猎者Ban (30秒)
    ↓
第6轮: 速通者Ban (30秒)
    ↓
第7轮: 捕猎者Pick (30秒)
    ↓
第8轮: 速通者Pick (30秒)
    ↓
显示最终结果 → 开始游戏
```

## 🗳️ 投票机制

### 投票规则
1. **轮次限制**: 只有当前轮次的阵营可以投票
2. **时间限制**: 每个轮次30秒倒计时
3. **多数决定**: 同阵营玩家投票，得票最多的物品生效
4. **可修改**: 玩家可以在时间内修改自己的投票
5. **提前结束**: 如果所有该阵营玩家都投票完成，可提前进入下一轮

### 投票示例
```
当前轮次: 捕猎者Ban第1轮
参与玩家: Hunter1, Hunter2, Hunter3

投票情况:
Hunter1 → 船
Hunter2 → 船  
Hunter3 → 钻石镐

结果: 船被禁用 (2票 vs 1票)
```

## 🚫 Ban机制 (禁用)

### Ban的作用
- **禁止合成**: 被Ban的物品无法在游戏中合成
- **禁止使用**: 即使通过其他方式获得也无法使用
- **永久生效**: 整局游戏中都被禁用

### Ban策略考虑
- **限制对手**: 禁用对手依赖的关键物品
- **保护自己**: 防止对手获得优势物品
- **战术针对**: 根据地图和战术选择Ban目标

### Ban优先级建议
1. **高影响物品**: 船、床、打火石
2. **效率工具**: 钻石镐、钻石斧
3. **战斗装备**: 钻石剑、弩
4. **特殊功能**: 末影之眼、附魔台

## ✅ Pick机制 (锁定)

### Pick的作用
- **确保可用**: 被Pick的物品保证可以合成和使用
- **优先权**: 即使对手想Ban也无法禁用
- **战略保障**: 确保关键战术的可执行性

### Pick策略考虑
- **核心需求**: 锁定自己战术的核心物品
- **效率优先**: 选择能提升效率的工具
- **应对Ban**: 预测对手可能Ban的物品并提前Pick

### Pick优先级建议
1. **核心工具**: 钻石镐 (挖掘效率)
2. **移动工具**: 船 (水域优势)
3. **战斗装备**: 盾牌、钻石剑
4. **特殊功能**: 末影之眼 (寻找要塞)

## 🎮 实际游戏影响

### 被Ban物品的处理
```java
// 检查物品是否被Ban
if (banPickManager.getBannedItems().contains(targetItem)) {
    // 阻止合成
    event.setCancelled(true);
    player.sendMessage("该物品已被禁用！");
}
```

### 被Pick物品的保护
```java
// Pick的物品不能被Ban
if (hunterPickedItems.contains(item) || speedrunnerPickedItems.contains(item)) {
    return false; // 不能选择已被Pick的物品
}
```

## 📊 战略分析

### 经典Ban Pick策略

#### 捕猎者视角
**Ban策略**:
- Ban船 → 限制速通者水域逃跑
- Ban床 → 阻止下界爆炸战术
- Ban钻石工具 → 降低速通效率

**Pick策略**:
- Pick弩 → 确保远程攻击能力
- Pick盾牌 → 提升防御能力
- Pick钻石剑 → 确保战斗优势

#### 速通者视角
**Ban策略**:
- Ban弩 → 减少远程威胁
- Ban盾牌 → 降低捕猎者防御
- Ban钻石剑 → 减少近战威胁

**Pick策略**:
- Pick钻石镐 → 确保挖掘效率
- Pick船 → 保证移动能力
- Pick末影之眼 → 确保寻找要塞

### 高级策略

#### 心理博弈
- **虚张声势**: 投票给不重要的物品误导对手
- **最后时刻**: 在倒计时最后几秒改变投票
- **团队配合**: 同阵营玩家协调投票策略

#### 适应性策略
- **地图分析**: 根据地图特点调整Ban Pick
- **对手分析**: 根据对手习惯调整策略
- **实时调整**: 根据前几轮结果调整后续选择

## ⚙️ 技术实现细节

### 状态管理
```java
public enum BanPickPhase {
    WAITING,           // 等待开始
    HUNTER_BAN_1,      // 捕猎者第1轮Ban
    SPEEDRUNNER_BAN_1, // 速通者第1轮Ban
    HUNTER_PICK_1,     // 捕猎者第1轮Pick
    SPEEDRUNNER_PICK_1,// 速通者第1轮Pick
    // ... 更多轮次
    COMPLETED          // 完成
}
```

### 投票处理
```java
public boolean vote(Player player, BanPickItem item) {
    // 1. 检查是否是该玩家的回合
    // 2. 检查物品是否可选择
    // 3. 记录投票
    // 4. 检查是否所有人都投票完成
    // 5. 如果完成则立即进入下一阶段
}
```

### 结果应用
```java
private void applyPhaseResult(BanPickItem item) {
    if (currentPhase.isBanPhase()) {
        bannedItems.add(item);        // 添加到禁用列表
    } else if (currentPhase.isPickPhase()) {
        if (currentPhase.isHunterTurn()) {
            hunterPickedItems.add(item);  // 捕猎者锁定
        } else {
            speedrunnerPickedItems.add(item); // 速通者锁定
        }
    }
}
```

## 🎯 配置选项

### 房间设置
- `banPickEnabled`: 是否启用Ban Pick
- `banPickPhaseTimeSeconds`: 每个阶段的时间 (默认30秒)

### 可扩展配置 (未来)
- 轮次数量配置
- 每轮Ban/Pick数量
- 特殊规则 (如首轮双Ban)

## 📈 游戏价值

### 竞技价值
- **策略深度**: 增加游戏前期的策略博弈
- **观赏性**: 为观众提供分析和预测的乐趣
- **平衡性**: 让弱势方有机会通过Ban Pick扳回劣势

### 教育价值
- **物品认知**: 帮助玩家了解各物品的重要性
- **战术思考**: 培养玩家的战略思维
- **团队协作**: 促进同阵营玩家的沟通合作

## 总结

Ban Pick机制通过8轮的策略性选择，让玩家在游戏开始前就开始博弈，大大增强了Manhunt的竞技性和观赏性。每个选择都可能影响游戏的最终结果，让每一局游戏都充满变数和惊喜！
