# 新职业实现：方源和暗夜领主

## 概述

本次更新为游戏添加了两个全新的职业：
- **方源**（速通者职业）- 时光掌控者，能够操控时间回溯和创造时光分身
- **暗夜领主**（猎人职业）- 黑夜的主宰，在夜晚获得强大力量并能操控黑暗

## 职业详情

### 方源 (FANGYUAN) - 速通者职业

**基本信息：**
- 显示名称：方源
- 描述：时光掌控者，能够操控时间回溯和创造时光分身
- 图标：时钟 (Material.CLOCK)
- 颜色：金色 (NamedTextColor.GOLD)
- 表情符号：⏰

**被动技能 - 春秋蝉：**
- 当血量降低到13%时，有80%概率回到上一个创建光阴分身的地方
- 360秒冷却时间
- 冷却期间概率会递减（例如：180秒时概率为40%）
- 使用伪随机分布确保公平性

**主动技能 - 春秋必成：**
- 600秒冷却时间
- 第一次使用：记录所有玩家的背包状态、血量、饥饿条、位置（创建新时光分身会摧毁旧的）
- 第二次使用：所有玩家回到记录的状态（时光分身保留，供春秋蝉使用）
- 技能在第一次使用时不进入冷却，等待第二次使用后才进入冷却

### 暗夜领主 (NIGHT_LORD) - 猎人职业

**基本信息：**
- 显示名称：暗夜领主
- 描述：黑夜的主宰，在夜晚获得强大力量并能操控黑暗
- 图标：下界合金头盔 (Material.NETHERITE_HELMET)
- 颜色：深蓝色 (NamedTextColor.DARK_BLUE)
- 表情符号：🌙

**被动技能 - 暗夜猎影：**
- 在夜晚时被动获得以下效果：
  - 速度2 (5秒持续时间)
  - 生命恢复1 (5秒持续时间)
  - 夜视 (10秒持续时间)
- 每5秒检查一次时间并应用效果
- 无冷却时间

**主动技能 - 暗月升起：**
- 120秒冷却时间
- 持续30秒的效果：
  - 将世界时间设置为夜晚 (18000)
  - 暗夜领主获得飞行能力
- 效果结束后：
  - 移除飞行能力
  - 恢复世界时间为白天 (1000)

## 技术实现

### 文件修改

1. **Profession.java**
   - 添加了两个新的职业枚举值
   - 包含完整的技能描述和属性

2. **ActiveSkillHandler.java**
   - 添加了新的状态管理变量
   - 实现了PlayerTimeState和PlayerSnapshot数据类
   - 添加了handleFangyuanTimeRewind()方法
   - 添加了handleNightLordDarkMoon()方法
   - 添加了公共访问方法供被动技能使用

3. **PassiveSkillHandler.java**
   - 添加了新的状态管理变量
   - 实现了handleFangyuanSpringAutumnCicada()方法
   - 实现了handleNightLordNightHunting()方法
   - 添加了夜晚检测任务管理

4. **ProfessionManager.java**
   - 在职业设置和移除时添加了新职业的特殊处理
   - 暗夜领主会自动启动夜晚检测任务

5. **ProfessionListener.java**
   - 添加了方源春秋蝉被动技能的伤害事件处理

### 核心功能

#### 时光分身系统
- 使用PlayerTimeState类存储游戏会话的时光状态
- PlayerSnapshot类保存单个玩家的完整状态（位置、血量、饥饿、背包等）
- 支持所有玩家同时回溯到记录状态

#### 夜晚检测系统
- 使用BukkitTask定时检查世界时间
- 自动应用夜晚增益效果
- 职业切换时自动清理任务

#### 世界时间操控
- 通过GameWorld.setTime()方法控制世界时间
- 支持临时改变世界时间并自动恢复

### 平衡性考虑

1. **方源平衡性：**
   - 春秋蝉有较长的冷却时间（360秒）
   - 冷却期间概率递减，避免频繁触发
   - 需要先创建时光分身才能触发被动技能

2. **暗夜领主平衡性：**
   - 暗月升起持续时间有限（30秒）
   - 较长的冷却时间（120秒）
   - 被动技能只在夜晚生效

## 使用方法

### 启用新职业
1. 确保房间设置中启用了职业系统
2. 新职业会自动出现在职业选择GUI中
3. 根据角色（速通者/猎人）选择对应职业

### 方源使用技巧
1. 优先使用春秋必成创建时光分身
2. 在危险情况下依靠春秋蝉被动保命
3. 合理利用时光回溯帮助团队

### 暗夜领主使用技巧
1. 在夜晚时更加积极进攻
2. 合理使用暗月升起控制战场节奏
3. 利用飞行能力进行空中追击

## 测试建议

1. **功能测试：**
   - 测试春秋必成的状态记录和恢复
   - 测试春秋蝉的触发条件和概率
   - 测试暗月升起的世界时间改变和飞行

2. **平衡性测试：**
   - 观察新职业在实际游戏中的表现
   - 收集玩家反馈调整数值
   - 监控胜率变化

3. **兼容性测试：**
   - 确保与现有职业系统兼容
   - 测试多人游戏中的同步性
   - 验证离线重连后的状态恢复

## 注意事项

1. 方源的时光分身功能会影响所有玩家，使用时需要考虑团队配合
2. 暗夜领主的暗月升起会改变世界时间，影响所有玩家的游戏体验
3. 两个职业都有较强的控制能力，需要在实际游戏中观察平衡性

## 后续优化

1. 可以考虑添加更多视觉效果和音效
2. 根据玩家反馈调整技能数值
3. 可能需要添加更多的平衡性机制
