package com.projectSource.ultimateManhurt.tablist;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.room.Room;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.TextComponent;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextColor;
import org.bukkit.Bukkit;
import org.bukkit.World;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;

import java.awt.*;

/**
 * Tablist工具类
 * 包含构建Tablist各部分内容的静态方法
 */
public class TablistUtil {

    /**
     * 构建玩家显示名称
     */
    public static Component buildPlayerDisplayName(Player player, UltimateManhurt plugin, 
                                                   GameSession gameSession, Room room) {
        TextComponent.Builder nameBuilder = Component.text();

        boolean showPing = plugin.getConfigManager().getBoolean("tablist.player-display.show-ping", true);
        boolean showHealth = plugin.getConfigManager().getBoolean("tablist.player-display.show-health", true);
        boolean showRole = plugin.getConfigManager().getBoolean("tablist.player-display.show-role", true);
        boolean showProfession = plugin.getConfigManager().getBoolean("tablist.player-display.show-profession", true);

        // 添加延迟指示器
        if (showPing) {
            nameBuilder.append(buildPingIndicator(player, plugin));
            nameBuilder.append(Component.text(" "));
        }

        // 添加角色指示器（游戏中）
        if (showRole && gameSession != null) {
            nameBuilder.append(buildRoleIndicator(player, gameSession));
            nameBuilder.append(Component.text(" "));
        }

        // 添加职业指示器（游戏中且启用职业系统）
        if (showProfession && gameSession != null &&
            gameSession.getRoom().getSettings().isProfessionSystemEnabled()) {
            Component professionIndicator = buildProfessionIndicator(player, plugin);
            if (!professionIndicator.equals(Component.empty())) {
                nameBuilder.append(professionIndicator);
                nameBuilder.append(Component.text(" "));
            }
        }

        // 添加玩家名称（带权限颜色）
        nameBuilder.append(Component.text(player.getName(), getPlayerNameColor(player)));

        // 添加血量指示器
        if (showHealth) {
            nameBuilder.append(Component.text(" "));
            nameBuilder.append(buildHealthIndicator(player, plugin));
        }

        return nameBuilder.build();
    }

    /**
     * 构建游戏底部信息
     */
    public static Component buildGameFooter(Player player, GameSession gameSession, UltimateManhurt plugin) {
        return Component.text()
                .append(Component.newline())
                .append(Component.text("⏱ 游戏时间 ", TextColor.color(0x87CEEB)))
                .append(buildGameTimeBar(gameSession))
                .append(Component.newline())
                .append(Component.text("👥 存活玩家 ", TextColor.color(0x87CEEB)))
                .append(buildAlivePlayersBar(gameSession))
                .append(Component.newline())
                .append(Component.text("📡 延迟 ", TextColor.color(0x87CEEB)))
                .append(buildPingBar(player))
                .append(Component.newline())
                .append(Component.text("🔘 TPS ", TextColor.color(0x87CEEB)))
                .append(buildTpsBar())
                .append(Component.newline())
                .append(Component.text("💾 内存 ", TextColor.color(0x87CEEB)))
                .append(buildMemoryBar())
                .append(Component.newline())
                .build();
    }

    /**
     * 构建房间底部信息
     */
    public static Component buildRoomFooter(Player player, Room room, UltimateManhurt plugin) {
        return Component.text()
                .append(Component.newline())
                .append(Component.text("🏠 房间信息 ", TextColor.color(0x87CEEB)))
                .append(Component.text(room.getName(), NamedTextColor.WHITE))
                .append(Component.newline())
                .append(Component.text("👥 玩家数量 ", TextColor.color(0x87CEEB)))
                .append(buildRoomPlayersBar(room))
                .append(Component.newline())
                .append(Component.text("⚔ 速通者 ", TextColor.color(0x87CEEB)))
                .append(buildRoleCountBar(room, PlayerRole.SPEEDRUNNER))
                .append(Component.newline())
                .append(Component.text("🏹 猎人 ", TextColor.color(0x87CEEB)))
                .append(buildRoleCountBar(room, PlayerRole.HUNTER))
                .append(Component.newline())
                .append(Component.text("📡 延迟 ", TextColor.color(0x87CEEB)))
                .append(buildPingBar(player))
                .append(Component.newline())
                .build();
    }

    /**
     * 构建大厅底部信息
     */
    public static Component buildLobbyFooter(Player player, UltimateManhurt plugin) {
        return Component.text()
                .append(Component.newline())
                .append(Component.text("👥 在线玩家 ", TextColor.color(0x87CEEB)))
                .append(buildOnlinePlayersBar())
                .append(Component.newline())
                .append(Component.text("📡 延迟 ", TextColor.color(0x87CEEB)))
                .append(buildPingBar(player))
                .append(Component.newline())
                .append(Component.text("🔘 TPS ", TextColor.color(0x87CEEB)))
                .append(buildTpsBar())
                .append(Component.newline())
                .append(Component.text("💾 内存 ", TextColor.color(0x87CEEB)))
                .append(buildMemoryBar())
                .append(Component.newline())
                .append(Component.text("🎯 实体 ", TextColor.color(0x87CEEB)))
                .append(buildEntityBar())
                .append(Component.newline())
                .build();
    }

    /**
     * 构建延迟指示器
     */
    private static Component buildPingIndicator(Player player, UltimateManhurt plugin) {
        int ping = player.getPing();

        int goodThreshold = plugin.getConfigManager().getInt("tablist.player-display.ping-thresholds.good", 50);
        int mediumThreshold = plugin.getConfigManager().getInt("tablist.player-display.ping-thresholds.medium", 150);

        TextColor pingColor;
        String pingSymbol = "●";

        if (ping < goodThreshold) {
            pingColor = NamedTextColor.GREEN;
        } else if (ping < mediumThreshold) {
            pingColor = NamedTextColor.YELLOW;
        } else {
            pingColor = NamedTextColor.RED;
        }

        return Component.text()
                .append(Component.text(pingSymbol, pingColor))
                .append(Component.text(ping + "ms", NamedTextColor.GRAY))
                .build();
    }

    /**
     * 构建角色指示器
     */
    private static Component buildRoleIndicator(Player player, GameSession gameSession) {
        PlayerRole role = gameSession.getPlayerRole(player.getUniqueId());
        if (role == null) {
            return Component.empty();
        }

        String roleSymbol;
        TextColor roleColor;

        switch (role) {
            case SPEEDRUNNER:
                roleSymbol = "⚔";
                roleColor = NamedTextColor.GREEN;
                break;
            case HUNTER:
                roleSymbol = "🏹";
                roleColor = NamedTextColor.RED;
                break;
            case SPECTATOR:
                roleSymbol = "👁";
                roleColor = NamedTextColor.GRAY;
                break;
            default:
                return Component.empty();
        }

        return Component.text(roleSymbol, roleColor);
    }

    /**
     * 构建职业指示器
     */
    private static Component buildProfessionIndicator(Player player, UltimateManhurt plugin) {
        com.projectSource.ultimateManhurt.profession.Profession profession =
            plugin.getProfessionManager().getPlayerProfession(player.getUniqueId());

        if (profession == null) {
            return Component.empty();
        }

        return Component.text(profession.getEmoji(), profession.getColor());
    }

    /**
     * 构建血量指示器
     */
    private static Component buildHealthIndicator(Player player, UltimateManhurt plugin) {
        double health = player.getHealth();
        double maxHealth = player.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
        double healthPercentage = (health / maxHealth) * 100;

        double excellentThreshold = plugin.getConfigManager().getDouble("tablist.player-display.health-thresholds.excellent", 75);
        double goodThreshold = plugin.getConfigManager().getDouble("tablist.player-display.health-thresholds.good", 50);
        double mediumThreshold = plugin.getConfigManager().getDouble("tablist.player-display.health-thresholds.medium", 25);

        TextColor healthColor;
        if (healthPercentage > excellentThreshold) {
            healthColor = NamedTextColor.GREEN;
        } else if (healthPercentage > goodThreshold) {
            healthColor = NamedTextColor.YELLOW;
        } else if (healthPercentage > mediumThreshold) {
            healthColor = NamedTextColor.GOLD;
        } else {
            healthColor = NamedTextColor.RED;
        }

        return Component.text()
                .append(Component.text("❤", healthColor))
                .append(Component.text(String.format("%.0f%%", healthPercentage), healthColor))
                .build();
    }

    /**
     * 获取玩家名称颜色（基于段位）
     */
    private static TextColor getPlayerNameColor(Player player) {
        // 获取玩家段位并返回对应颜色
        try {
            com.projectSource.ultimateManhurt.data.PlayerData playerData =
                com.projectSource.ultimateManhurt.UltimateManhurt.getInstance()
                    .getDataManager().getPlayerData(player.getUniqueId());

            if (playerData != null) {
                int elo = com.projectSource.ultimateManhurt.ranking.EloSystem.getPlayerElo(playerData);
                com.projectSource.ultimateManhurt.ranking.EloSystem.Rank rank =
                    com.projectSource.ultimateManhurt.ranking.EloSystem.getRank(elo);

                // 根据段位返回颜色
                switch (rank) {
                    case GRANDMASTER:
                        return TextColor.color(0xFF0000); // 红色
                    case MASTER:
                        return TextColor.color(0x800080); // 紫色
                    case DIAMOND:
                        return TextColor.color(0x00FFFF); // 青色
                    case PLATINUM:
                        return TextColor.color(0x0080FF); // 蓝色
                    case GOLD:
                        return TextColor.color(0xFFD700); // 金色
                    case SILVER:
                        return TextColor.color(0xC0C0C0); // 银色
                    case BRONZE:
                        return TextColor.color(0xCD7F32); // 铜色
                    case IRON:
                    default:
                        return TextColor.color(0x808080); // 灰色
                }
            }
        } catch (Exception e) {
            // 如果获取段位失败，使用默认颜色
        }

        return NamedTextColor.WHITE; // 默认白色
    }

    /**
     * 构建游戏时间条
     */
    private static Component buildGameTimeBar(GameSession gameSession) {
        long gameTime = gameSession.getGameTime();
        long maxTime = gameSession.getRoom().getSettings().getGameDurationMinutes() * 60 * 1000L;
        
        double ratio = Math.min((double) gameTime / maxTime, 1.0);
        int barLength = 10;

        TextComponent.Builder barBuilder = Component.text();
        TextColor barColor = calculateGradient(ratio);

        for (int i = 0; i < barLength; i++) {
            double segmentRatio = (double) i / barLength;
            if (segmentRatio < ratio) {
                double fill = Math.min(1.0, (ratio - segmentRatio) * barLength);
                barBuilder.append(Component.text(getUnicodeBlock(fill)).color(barColor));
            } else {
                barBuilder.append(Component.text("▁").color(NamedTextColor.DARK_GRAY));
            }
        }

        long minutes = gameTime / 60000;
        long seconds = (gameTime % 60000) / 1000;
        String timeDisplay = String.format(" %02d:%02d", minutes, seconds);

        return barBuilder.append(Component.text(timeDisplay).color(NamedTextColor.GRAY)).build();
    }

    /**
     * 构建存活玩家条
     */
    private static Component buildAlivePlayersBar(GameSession gameSession) {
        int alivePlayers = gameSession.getAlivePlayers().size();
        int totalPlayers = gameSession.getPlayerRoles().size();

        double ratio = totalPlayers > 0 ? (double) alivePlayers / totalPlayers : 0.0;
        int barLength = 10;

        TextComponent.Builder barBuilder = Component.text();
        TextColor barColor = calculateGradient(1.0 - ratio); // 反向：存活越多越好

        for (int i = 0; i < barLength; i++) {
            double segmentRatio = (double) i / barLength;
            if (segmentRatio < ratio) {
                double fill = Math.min(1.0, (ratio - segmentRatio) * barLength);
                barBuilder.append(Component.text(getUnicodeBlock(fill)).color(barColor));
            } else {
                barBuilder.append(Component.text("▁").color(NamedTextColor.DARK_GRAY));
            }
        }

        String display = String.format(" %d/%d", alivePlayers, totalPlayers);
        return barBuilder.append(Component.text(display).color(NamedTextColor.GRAY)).build();
    }

    /**
     * 构建房间玩家条
     */
    private static Component buildRoomPlayersBar(Room room) {
        int currentPlayers = room.getPlayers().size();
        int maxPlayers = room.getSettings().getMaxPlayers();

        double ratio = maxPlayers > 0 ? (double) currentPlayers / maxPlayers : 0.0;
        int barLength = 10;

        TextComponent.Builder barBuilder = Component.text();
        TextColor barColor = calculateGradient(1.0 - ratio); // 反向：玩家越多越好

        for (int i = 0; i < barLength; i++) {
            double segmentRatio = (double) i / barLength;
            if (segmentRatio < ratio) {
                double fill = Math.min(1.0, (ratio - segmentRatio) * barLength);
                barBuilder.append(Component.text(getUnicodeBlock(fill)).color(barColor));
            } else {
                barBuilder.append(Component.text("▁").color(NamedTextColor.DARK_GRAY));
            }
        }

        String display = String.format(" %d/%d", currentPlayers, maxPlayers);
        return barBuilder.append(Component.text(display).color(NamedTextColor.GRAY)).build();
    }

    /**
     * 构建角色数量条
     */
    private static Component buildRoleCountBar(Room room, PlayerRole role) {
        long currentCount = room.getPlayersWithRoles().values().stream()
                .filter(r -> r == role)
                .count();

        int maxCount;
        switch (role) {
            case SPEEDRUNNER:
                maxCount = room.getSettings().getMaxSpeedrunners();
                break;
            case HUNTER:
                maxCount = room.getSettings().getMaxHunters();
                break;
            default:
                maxCount = 1;
        }

        double ratio = maxCount > 0 ? (double) currentCount / maxCount : 0.0;
        int barLength = 10;

        TextComponent.Builder barBuilder = Component.text();
        TextColor barColor = calculateGradient(1.0 - ratio); // 反向：角色越多越好

        for (int i = 0; i < barLength; i++) {
            double segmentRatio = (double) i / barLength;
            if (segmentRatio < ratio) {
                double fill = Math.min(1.0, (ratio - segmentRatio) * barLength);
                barBuilder.append(Component.text(getUnicodeBlock(fill)).color(barColor));
            } else {
                barBuilder.append(Component.text("▁").color(NamedTextColor.DARK_GRAY));
            }
        }

        String display = String.format(" %d/%d", (int) currentCount, maxCount);
        return barBuilder.append(Component.text(display).color(NamedTextColor.GRAY)).build();
    }

    /**
     * 构建延迟条
     */
    private static Component buildPingBar(Player player) {
        int ping = player.getPing();
        double ratio = Math.min(ping / 200.0, 1.0);
        int barLength = 10;

        TextComponent.Builder bar = Component.text();
        TextColor barColor = calculateGradient(ratio);

        for (int i = 0; i < barLength; i++) {
            double segmentRatio = (double) i / barLength;
            if (segmentRatio < ratio) {
                double fill = Math.min(1.0, (ratio - segmentRatio) * barLength);
                bar.append(Component.text(getUnicodeBlock(fill)).color(barColor));
            } else {
                bar.append(Component.text("▁").color(NamedTextColor.DARK_GRAY));
            }
        }

        return bar.append(Component.text(" " + ping + "ms")
                .color(NamedTextColor.GRAY)).build();
    }

    /**
     * 构建TPS条
     */
    private static Component buildTpsBar() {
        double currentTps = Math.min(Bukkit.getTPS()[0], 20.0);
        double ratio = currentTps / 20.0;
        int barLength = 10;

        TextColor color = calculateGradient(1.0 - ratio);

        TextComponent.Builder barBuilder = Component.text();
        for (int i = 0; i < barLength; i++) {
            double segmentRatio = (double) i / barLength;
            if (segmentRatio < ratio) {
                double fill = Math.min(1.0, (ratio - segmentRatio) * barLength);
                barBuilder.append(Component.text(getUnicodeBlock(fill)).color(color));
            } else {
                barBuilder.append(Component.text("▁").color(NamedTextColor.DARK_GRAY));
            }
        }

        return barBuilder.append(Component.text(" " + String.format("%.1f", currentTps))
                .color(NamedTextColor.GRAY)).build();
    }

    /**
     * 构建内存使用条
     */
    private static Component buildMemoryBar() {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;

        double usageRatio = (double) usedMemory / maxMemory;

        int barLength = 10;
        TextColor barColor = calculateGradient(usageRatio);

        TextComponent.Builder barBuilder = Component.text();
        for (int i = 0; i < barLength; i++) {
            double segmentRatio = (double) i / barLength;
            if (segmentRatio < usageRatio) {
                double fill = Math.min(1.0, (usageRatio - segmentRatio) * barLength);
                barBuilder.append(Component.text(getUnicodeBlock(fill)).color(barColor));
            } else {
                barBuilder.append(Component.text("▁").color(NamedTextColor.DARK_GRAY));
            }
        }

        long usedMB = usedMemory / 1024 / 1024;
        long maxMB = maxMemory / 1024 / 1024;
        String display = String.format(" %d/%d MB", usedMB, maxMB);

        return barBuilder.append(
                Component.text(display).color(NamedTextColor.GRAY)
        ).build();
    }

    /**
     * 构建在线玩家条
     */
    private static Component buildOnlinePlayersBar() {
        int onlinePlayers = Bukkit.getOnlinePlayers().size();
        int maxPlayers = Bukkit.getMaxPlayers();

        double ratio = 0.0;
        if (maxPlayers > 0) {
            ratio = (double) onlinePlayers / maxPlayers;
        }

        int barLength = 10;
        TextComponent.Builder barBuilder = Component.text();
        TextColor barColor = calculateGradient(1.0 - ratio); // 反向：玩家越多越好

        for (int i = 0; i < barLength; i++) {
            double segmentRatio = (double) i / barLength;
            if (segmentRatio < ratio) {
                double fill = Math.min(1.0, (ratio - segmentRatio) * barLength);
                barBuilder.append(
                        Component.text(getUnicodeBlock(fill)).color(barColor)
                );
            } else {
                barBuilder.append(
                        Component.text("▁").color(NamedTextColor.DARK_GRAY)
                );
            }
        }

        String display = String.format(" %d/%d", onlinePlayers, maxPlayers);
        return barBuilder.append(
                Component.text(display).color(NamedTextColor.GRAY)
        ).build();
    }

    /**
     * 构建实体条
     */
    private static Component buildEntityBar() {
        int totalEntities = 0;
        int totalItems = 0;

        for (World world : Bukkit.getWorlds()) {
            for (Entity entity : world.getEntities()) {
                totalEntities++;
                if (entity instanceof org.bukkit.entity.Item) {
                    totalItems++;
                }
            }
        }

        double ratio = 0.0;
        if (totalEntities > 0) {
            ratio = (double) totalItems / totalEntities;
        }

        int barLength = 10;
        TextComponent.Builder barBuilder = Component.text();
        TextColor barColor = calculateGradient(ratio);

        for (int i = 0; i < barLength; i++) {
            double segmentRatio = (double) i / barLength;
            if (segmentRatio < ratio) {
                double fill = Math.min(1.0, (ratio - segmentRatio) * barLength);
                barBuilder.append(
                        Component.text(getUnicodeBlock(fill)).color(barColor)
                );
            } else {
                barBuilder.append(
                        Component.text("▁").color(NamedTextColor.DARK_GRAY)
                );
            }
        }

        String display = String.format(" %d/%d", totalItems, totalEntities);
        return barBuilder.append(
                Component.text(display).color(NamedTextColor.GRAY)
        ).build();
    }

    /**
     * 获取Unicode块字符
     */
    private static String getUnicodeBlock(double fill) {
        String[] blocks = {
                " ",    // 0/8 - 空
                "▁",    // 1/8 - 轻阴影
                "▂",    // 2/8 - 下四分之一
                "▃",    // 3/8 - 下八分之三
                "▄",    // 4/8 - 下半部
                "▅",    // 5/8 - 下八分之五
                "▆",    // 6/8 - 下四分之三
                "▇",    // 7/8 - 下八分之七
                "█"     // 8/8 - 完整块
        };
        int index = (int) Math.round(fill * (blocks.length - 1));
        return blocks[Math.min(Math.max(index, 0), blocks.length - 1)];
    }

    /**
     * 计算渐变颜色
     */
    public static TextColor calculateGradient(double ratio) {
        ratio = Math.max(0.0, Math.min(1.0, ratio));

        Color startColor;
        Color endColor;
        double newRatio;

        if (ratio <= 0.5) {
            // 0.0 ~ 0.5: 绿色到黄色
            startColor = Color.GREEN;   // #00FF00
            endColor = Color.YELLOW;    // #FFFF00
            newRatio = ratio / 0.5;
        } else {
            // 0.5 ~ 1.0: 黄色到红色
            startColor = Color.YELLOW;  // #FFFF00
            endColor = Color.RED;       // #FF0000
            newRatio = (ratio - 0.5) / 0.5;
        }

        int r = (int) Math.round(startColor.getRed() + (endColor.getRed() - startColor.getRed()) * newRatio);
        int g = (int) Math.round(startColor.getGreen() + (endColor.getGreen() - startColor.getGreen()) * newRatio);
        int b = (int) Math.round(startColor.getBlue() + (endColor.getBlue() - startColor.getBlue()) * newRatio);

        return TextColor.color(r, g, b);
    }
}
