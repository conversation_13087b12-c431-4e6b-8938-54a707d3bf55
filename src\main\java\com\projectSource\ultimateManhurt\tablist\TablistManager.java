package com.projectSource.ultimateManhurt.tablist;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.room.Room;
import net.kyori.adventure.text.Component;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Tablist管理器
 * 负责管理玩家的Tab列表显示，包括头部、底部和玩家名称显示
 */
public class TablistManager {

    private final UltimateManhurt plugin;
    private final Map<UUID, PlayerTablist> playerTablists = new ConcurrentHashMap<>();
    private BukkitTask updateTask;

    public TablistManager(UltimateManhurt plugin) {
        this.plugin = plugin;
        initialize();
    }

    /**
     * 初始化Tablist管理器
     */
    private void initialize() {
        startUpdateTask();
        plugin.getLogger().info("Tablist管理器已初始化");
    }

    /**
     * 启动更新任务
     */
    private void startUpdateTask() {
        int updateInterval = plugin.getConfigManager().getInt("tablist.update-interval", 20);

        updateTask = new BukkitRunnable() {
            @Override
            public void run() {
                updateAllTablists();
            }
        }.runTaskTimer(plugin, 0L, updateInterval);
    }

    /**
     * 更新所有Tablist
     */
    private void updateAllTablists() {
        for (PlayerTablist tablist : playerTablists.values()) {
            tablist.update();
        }
    }

    /**
     * 为玩家创建游戏Tablist
     */
    public void createGameTablist(Player player, GameSession gameSession) {
        UUID playerId = player.getUniqueId();

        // 移除现有Tablist
        removeTablist(player);

        // 创建新Tablist
        PlayerTablist tablist = new PlayerTablist(plugin, player, gameSession);
        playerTablists.put(playerId, tablist);

        // 显示Tablist
        tablist.show();

        plugin.getLogger().info("已为玩家 " + player.getName() + " 创建游戏Tablist");
    }

    /**
     * 为玩家创建房间Tablist（等待状态）
     */
    public void createRoomTablist(Player player, Room room) {
        UUID playerId = player.getUniqueId();

        // 移除现有Tablist
        removeTablist(player);

        // 创建新Tablist
        PlayerTablist tablist = new PlayerTablist(plugin, player, room);
        playerTablists.put(playerId, tablist);

        // 显示Tablist
        tablist.show();

        plugin.getLogger().info("已为玩家 " + player.getName() + " 创建房间Tablist");
    }

    /**
     * 创建默认Tablist（大厅状态）
     */
    public void createDefaultTablist(Player player) {
        UUID playerId = player.getUniqueId();

        // 移除现有Tablist
        removeTablist(player);

        // 创建新Tablist
        PlayerTablist tablist = new PlayerTablist(plugin, player);
        playerTablists.put(playerId, tablist);

        // 显示Tablist
        tablist.show();

        plugin.getLogger().info("已为玩家 " + player.getName() + " 创建大厅Tablist");
    }

    /**
     * 移除玩家的Tablist
     */
    public void removeTablist(Player player) {
        UUID playerId = player.getUniqueId();
        PlayerTablist tablist = playerTablists.remove(playerId);

        if (tablist != null) {
            tablist.hide();
        }
    }

    /**
     * 更新游戏Tablist
     */
    public void updateGameTablist(GameSession gameSession) {
        for (UUID playerId : gameSession.getPlayerRoles().keySet()) {
            Player player = Bukkit.getPlayer(playerId);
            if (player == null || !player.isOnline()) {
                continue;
            }

            PlayerTablist tablist = playerTablists.get(playerId);
            if (tablist != null) {
                // 检查是否需要切换到游戏Tablist
                if (tablist.getType() != PlayerTablist.TablistType.GAME) {
                    createGameTablist(player, gameSession);
                } else {
                    tablist.updateContent();
                }
            } else {
                // 如果没有Tablist，创建游戏Tablist
                createGameTablist(player, gameSession);
            }
        }
    }

    /**
     * 更新房间Tablist
     */
    public void updateRoomTablist(Room room) {
        for (UUID playerId : room.getPlayers()) {
            PlayerTablist tablist = playerTablists.get(playerId);
            if (tablist != null) {
                tablist.updateContent();
            }
        }
    }

    /**
     * 更新所有玩家的显示名称
     */
    public void updateAllPlayerDisplayNames() {
        if (!plugin.getConfigManager().getBoolean("tablist.player-display.enabled", true)) {
            return;
        }

        for (Player player : Bukkit.getOnlinePlayers()) {
            if (player.isOnline()) {
                PlayerTablist tablist = playerTablists.get(player.getUniqueId());
                if (tablist != null) {
                    Component displayName = TablistUtil.buildPlayerDisplayName(player, plugin, tablist.getGameSession(), tablist.getRoom());
                    player.playerListName(displayName);
                }
            }
        }
    }

    /**
     * 获取玩家的Tablist
     */
    public PlayerTablist getPlayerTablist(UUID playerId) {
        return playerTablists.get(playerId);
    }

    /**
     * 检查玩家是否有Tablist
     */
    public boolean hasTablist(UUID playerId) {
        return playerTablists.containsKey(playerId);
    }

    /**
     * 关闭Tablist管理器
     */
    public void shutdown() {
        if (updateTask != null) {
            updateTask.cancel();
            updateTask = null;
        }

        // 清理所有Tablist
        for (PlayerTablist tablist : playerTablists.values()) {
            tablist.hide();
        }
        playerTablists.clear();

        plugin.getLogger().info("Tablist管理器已关闭");
    }
}
