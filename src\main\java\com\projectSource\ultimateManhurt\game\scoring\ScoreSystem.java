package com.projectSource.ultimateManhurt.game.scoring;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.game.rules.WinCondition;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 积分系统
 * 允许速通者通过完成里程碑获得分数，达到目标分数即可获胜
 */
public class ScoreSystem {
    
    private final UltimateManhurt plugin;
    private final GameSession gameSession;
    private final Map<UUID, Integer> playerScores = new ConcurrentHashMap<>();
    private final Map<UUID, Long> lastSurvivalBonus = new ConcurrentHashMap<>();
    private final Map<UUID, Long> lastNoDeathCheck = new ConcurrentHashMap<>();
    private final Map<UUID, Long> lastDeathTime = new ConcurrentHashMap<>();
    private final long gameStartTime;
    private final Set<String> completedMilestones = ConcurrentHashMap.newKeySet();

    // 效率追踪
    private final Map<UUID, Integer> playerMilestoneCount = new ConcurrentHashMap<>();
    private final Map<UUID, Integer> playerResourceUsage = new ConcurrentHashMap<>();
    
    // 积分设置
    private int targetScore = 500; // 目标分数（可在房间设置中配置）
    private boolean scoreVictoryEnabled = true; // 是否启用积分胜利
    
    public ScoreSystem(UltimateManhurt plugin, GameSession gameSession) {
        this.plugin = plugin;
        this.gameSession = gameSession;
        this.gameStartTime = System.currentTimeMillis();
        
        // 从房间设置中读取配置
        this.targetScore = gameSession.getRoom().getSettings().getTargetScore();
        this.scoreVictoryEnabled = gameSession.getRoom().getSettings().getVictoryMode().requiresScoreSystem();
        
        // 初始化所有速通者的分数
        for (UUID playerId : gameSession.getPlayersByRole(PlayerRole.SPEEDRUNNER)) {
            playerScores.put(playerId, 0);
            lastSurvivalBonus.put(playerId, System.currentTimeMillis());
            lastNoDeathCheck.put(playerId, System.currentTimeMillis());
            playerMilestoneCount.put(playerId, 0);
            playerResourceUsage.put(playerId, 0);
        }

        // 启动生存奖励定时器
        startSurvivalBonusTimer();
    }
    
    /**
     * 添加分数
     */
    public void addScore(UUID playerId, ScoreMilestone milestone) {
        if (!scoreVictoryEnabled) {
            return;
        }

        PlayerRole role = gameSession.getPlayerRole(playerId);
        if (role != PlayerRole.SPEEDRUNNER) {
            return; // 只有速通者可以获得分数
        }

        // 检查里程碑是否启用
        MilestoneSettings milestoneSettings = gameSession.getRoom().getSettings().getMilestoneSettings();
        if (milestoneSettings != null && !milestoneSettings.isMilestoneEnabled(milestone)) {
            return; // 里程碑被禁用，不给分数
        }

        // 获取自定义分数
        int milestonePoints = milestoneSettings != null ?
            milestoneSettings.getMilestoneScore(milestone) : milestone.getPoints();

        int currentScore = playerScores.getOrDefault(playerId, 0);
        int newScore = currentScore + milestonePoints;
        playerScores.put(playerId, newScore);

        // 更新里程碑计数
        int milestoneCount = playerMilestoneCount.getOrDefault(playerId, 0) + 1;
        playerMilestoneCount.put(playerId, milestoneCount);

        // 检查效率奖励
        checkEfficiencyBonus(playerId);

        Player player = Bukkit.getPlayer(playerId);
        if (player != null && player.isOnline()) {
            // 通知玩家获得分数
            ComponentUtil.sendMessage(player, ComponentUtil.success(
                "🏆 " + milestone.getDisplayName() + " +" + milestonePoints + "分 (总分: " + newScore + "/" + targetScore + ")"
            ));

            // 播放音效
            player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.0f);
        }

        // 通知其他玩家
        broadcastScoreUpdate(player, milestone, milestonePoints, newScore);

        // 只在积分模式下检查积分胜利条件
        if (scoreVictoryEnabled) {
            checkScoreVictory(playerId, newScore);
        }

        plugin.getLogger().info("玩家 " + (player != null ? player.getName() : playerId) +
            " 获得 " + milestonePoints + " 分 (" + milestone.getDisplayName() + "), 总分: " + newScore);
    }
    
    /**
     * 检查积分胜利条件
     */
    private void checkScoreVictory(UUID playerId, int score) {
        if (score >= targetScore) {
            Player player = Bukkit.getPlayer(playerId);
            String playerName = player != null ? player.getName() : "Unknown";
            
            // 广播胜利消息给所有玩家
            for (UUID pid : gameSession.getPlayerRoles().keySet()) {
                Player p = Bukkit.getPlayer(pid);
                if (p != null && p.isOnline()) {
                    ComponentUtil.sendMessage(p, ComponentUtil.parse(
                        "<green><bold>" + playerName + " 达到目标分数 " + targetScore + "！速通者获胜！</bold>"
                    ));
                }
            }
            
            // 结束游戏
            gameSession.endGame(WinCondition.SPEEDRUNNER_SCORE_VICTORY);
        }
    }
    
    /**
     * 广播分数更新
     */
    private void broadcastScoreUpdate(Player scorer, ScoreMilestone milestone, int milestonePoints, int newScore) {
        String message = "<yellow>" + scorer.getName() + " 完成了 " + milestone.getDisplayName() +
            " (+" + milestonePoints + "分, 总分: " + newScore + "/" + targetScore + ")";

        for (UUID playerId : gameSession.getPlayerRoles().keySet()) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline() && !player.equals(scorer)) {
                ComponentUtil.sendMessage(player, ComponentUtil.parse(message));
            }
        }
    }
    
    /**
     * 处理生存奖励
     */
    public void processSurvivalBonus() {
        if (!scoreVictoryEnabled) {
            return;
        }
        
        long currentTime = System.currentTimeMillis();
        
        for (UUID playerId : gameSession.getPlayersByRole(PlayerRole.SPEEDRUNNER)) {
            if (!gameSession.isPlayerAlive(playerId)) {
                continue; // 死亡玩家不获得生存奖励
            }
            
            Long lastBonus = lastSurvivalBonus.get(playerId);
            if (lastBonus != null && currentTime - lastBonus >= 300000) { // 5分钟
                addScore(playerId, ScoreMilestone.SURVIVAL_BONUS);
                lastSurvivalBonus.put(playerId, currentTime);
            }

            // 检查无死亡奖励（每10分钟）
            Long lastNoDeathBonus = lastNoDeathCheck.get(playerId);
            if (lastNoDeathBonus != null && currentTime - lastNoDeathBonus >= 600000) { // 10分钟
                // 检查玩家是否在这10分钟内死亡过
                if (!hasPlayerDiedRecently(playerId)) {
                    addScore(playerId, ScoreMilestone.NO_DEATH_BONUS);
                }
                lastNoDeathCheck.put(playerId, currentTime);
            }
        }
    }
    
    /**
     * 获取玩家分数
     */
    public int getPlayerScore(UUID playerId) {
        return playerScores.getOrDefault(playerId, 0);
    }
    
    /**
     * 获取所有分数
     */
    public Map<UUID, Integer> getAllScores() {
        return new ConcurrentHashMap<>(playerScores);
    }
    
    /**
     * 获取目标分数
     */
    public int getTargetScore() {
        return targetScore;
    }
    
    /**
     * 是否启用积分胜利
     */
    public boolean isScoreVictoryEnabled() {
        return scoreVictoryEnabled;
    }
    
    /**
     * 获取最高分玩家
     */
    public UUID getTopScorer() {
        return playerScores.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse(null);
    }
    
    /**
     * 获取最高分
     */
    public int getTopScore() {
        return playerScores.values().stream()
            .mapToInt(Integer::intValue)
            .max()
            .orElse(0);
    }

    /**
     * 记录玩家死亡
     */
    public void recordPlayerDeath(UUID playerId) {
        lastDeathTime.put(playerId, System.currentTimeMillis());
    }

    /**
     * 检查玩家最近是否死亡过
     */
    private boolean hasPlayerDiedRecently(UUID playerId) {
        Long deathTime = lastDeathTime.get(playerId);
        if (deathTime == null) {
            return false; // 从未死亡
        }

        Long lastCheck = lastNoDeathCheck.get(playerId);
        if (lastCheck == null) {
            return false;
        }

        // 检查在上次检查之后是否死亡过
        return deathTime > lastCheck;
    }

    /**
     * 检查速度奖励
     */
    public void checkSpeedBonus(UUID playerId, ScoreMilestone milestone) {
        if (!scoreVictoryEnabled) return;

        long currentTime = System.currentTimeMillis();
        long gameTime = currentTime - gameStartTime;

        // 根据不同里程碑检查速度奖励
        switch (milestone) {
            case ENTER_NETHER:
                // 15分钟内进入下界给予速度奖励
                if (gameTime <= 900000) { // 15分钟
                    String speedKey = playerId + ":SPEED_NETHER";
                    if (!completedMilestones.contains(speedKey)) {
                        addScore(playerId, ScoreMilestone.SPEED_BONUS_NETHER);
                        completedMilestones.add(speedKey);
                    }
                }
                break;
            case ENTER_END:
                // 30分钟内进入末地给予速度奖励
                if (gameTime <= 1800000) { // 30分钟
                    String speedKey = playerId + ":SPEED_END";
                    if (!completedMilestones.contains(speedKey)) {
                        addScore(playerId, ScoreMilestone.SPEED_BONUS_END);
                        completedMilestones.add(speedKey);
                    }
                }
                break;
            case KILL_DRAGON:
                // 45分钟内击杀末影龙给予速度奖励
                if (gameTime <= 2700000) { // 45分钟
                    String speedKey = playerId + ":SPEED_DRAGON";
                    if (!completedMilestones.contains(speedKey)) {
                        addScore(playerId, ScoreMilestone.SPEED_BONUS_DRAGON);
                        completedMilestones.add(speedKey);
                    }
                }
                break;
            default:
                break;
        }
    }

    /**
     * 检查效率奖励
     */
    private void checkEfficiencyBonus(UUID playerId) {
        if (!scoreVictoryEnabled) return;

        int milestoneCount = playerMilestoneCount.getOrDefault(playerId, 0);
        long gameTime = System.currentTimeMillis() - gameStartTime;

        // 效率奖励：在30分钟内完成15个里程碑
        if (gameTime <= 1800000 && milestoneCount >= 15) { // 30分钟，15个里程碑
            String efficiencyKey = playerId + ":EFFICIENCY_BONUS";
            if (!completedMilestones.contains(efficiencyKey)) {
                addScore(playerId, ScoreMilestone.EFFICIENCY_BONUS);
                completedMilestones.add(efficiencyKey);
            }
        }
    }

    /**
     * 记录资源使用（用于资源节约奖励）
     */
    public void recordResourceUsage(UUID playerId, int amount) {
        int currentUsage = playerResourceUsage.getOrDefault(playerId, 0);
        playerResourceUsage.put(playerId, currentUsage + amount);

        // 检查资源节约奖励
        checkResourceConservationBonus(playerId);
    }

    /**
     * 检查资源节约奖励 (已移除)
     * 移除了RESOURCE_CONSERVATION里程碑，因为难以准确判断
     */
    private void checkResourceConservationBonus(UUID playerId) {
        // 方法已废弃，但保留以避免调用错误
    }

    /**
     * 启动生存奖励定时器
     */
    private void startSurvivalBonusTimer() {
        if (!scoreVictoryEnabled) {
            return;
        }

        // 每分钟检查一次生存奖励
        Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            processSurvivalBonus();
        }, 1200L, 1200L); // 1分钟 = 1200 ticks
    }
}
