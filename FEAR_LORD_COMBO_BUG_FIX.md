# 恐惧魔王影压连击Bug修复

## 🐛 Bug描述

**问题现象**：
恐惧魔王的影压技能出现连击次数疯狂增长的bug，一次影压攻击会触发多次命中判定，导致连击次数异常增加。

**日志表现**：
```
[17:33:18] [ℹ] 影压发射！当前连击: 0，伤害: 2.0
[17:33:19] [ℹ] 影压命中1个目标！连击次数: 1
[17:33:19] [ℹ] 影压命中1个目标！连击次数: 2
[17:33:19] [ℹ] 影压命中1个目标！连击次数: 3
...连续触发多次...
[17:33:19] [ℹ] 影压命中1个目标！连击次数: 21
```

**影响**：
- 连击次数异常增长
- 伤害计算错误（伤害 = 2 + 连击次数 × 1.5）
- 游戏平衡性被破坏

## 🔍 Bug分析

### 根本原因
凋灵之首的`ProjectileHitEvent`被多次触发，导致同一个凋灵之首的命中处理被执行多次。

### 触发机制
1. **发射凋灵之首**：恐惧魔王使用影压技能
2. **多次命中事件**：凋灵之首在飞行过程中多次触发ProjectileHitEvent
3. **重复处理**：每次事件都会增加连击次数
4. **连击爆炸**：连击次数在短时间内疯狂增长

### 技术原因
```java
// 问题代码：没有防重复处理机制
@EventHandler(priority = EventPriority.HIGH)
public void onProjectileHit(ProjectileHitEvent event) {
    if (event.getEntity() instanceof WitherSkull witherSkull) {
        handleWitherSkullHit(witherSkull, event); // 可能被多次调用
    }
}
```

## 🔧 修复方案

### 1. 添加防重复处理机制

**核心思路**：为每个凋灵之首添加"已处理"标记，确保每个凋灵之首只能被处理一次。

**实现代码**：
```java
private void handleWitherSkullHit(WitherSkull witherSkull, ProjectileHitEvent event) {
    // 检查是否是恐惧魔王的影压技能
    if (!witherSkull.hasMetadata("fear_lord_shadow_strike")) {
        return;
    }

    // 🔧 新增：检查是否已经处理过（防止重复处理）
    if (witherSkull.hasMetadata("fear_lord_processed")) {
        return;
    }

    // 🔧 新增：标记为已处理
    witherSkull.setMetadata("fear_lord_processed", 
        new FixedMetadataValue(plugin, true));

    // 取消原生的凋灵之首伤害和效果
    event.setCancelled(true);
    
    // ... 后续处理逻辑
}
```

### 2. 增强日志记录

**目的**：便于调试和监控连击次数变化。

**实现代码**：
```java
if (hitTarget) {
    int currentCombo = activeHandler.getFearLordComboCount(shooterId);
    int newCombo = currentCombo + 1;
    activeHandler.setFearLordComboCount(shooterId, newCombo);
    
    // 🔧 新增：详细日志记录
    plugin.getLogger().info("恐惧魔王 " + shooter.getName() + 
        " 影压命中，连击从 " + currentCombo + " 增加到 " + newCombo);
} else {
    activeHandler.setFearLordComboCount(shooterId, 0);
    
    // 🔧 新增：重置日志记录
    plugin.getLogger().info("恐惧魔王 " + shooter.getName() + 
        " 影压未命中，连击重置为0");
}
```

## ✅ 修复效果

### 修复前
```
发射影压 → 凋灵之首飞行 → 多次触发ProjectileHitEvent → 连击疯狂增长
连击: 0 → 1 → 2 → 3 → ... → 21 (一次攻击)
```

### 修复后
```
发射影压 → 凋灵之首飞行 → 只处理一次命中 → 连击正常增长
连击: 0 → 1 (一次攻击，正确)
```

## 🧪 测试验证

### 测试步骤
1. **基础测试**：
   - 恐惧魔王发射影压
   - 观察连击次数是否只增加1
   - 验证伤害计算是否正确

2. **连续测试**：
   - 连续发射多次影压
   - 验证连击次数是否按预期累积
   - 测试未命中时连击是否正确重置

3. **边界测试**：
   - 测试同时命中多个目标的情况
   - 验证连击次数仍然只增加1

### 预期结果
- ✅ 每次影压命中只增加1次连击
- ✅ 伤害计算正确：2 + 连击次数 × 1.5
- ✅ 未命中时连击正确重置为0
- ✅ 日志记录清晰，便于调试

## 🔒 防护机制

### 元数据标记系统
```java
// 发射时添加技能标记
witherSkull.setMetadata("fear_lord_shadow_strike", 
    new FixedMetadataValue(plugin, damage));

// 命中时添加处理标记
witherSkull.setMetadata("fear_lord_processed", 
    new FixedMetadataValue(plugin, true));
```

### 双重检查机制
1. **技能检查**：确认是恐惧魔王的影压技能
2. **处理检查**：确认未被重复处理

### 事件取消机制
```java
// 取消原生凋灵之首效果，避免干扰
event.setCancelled(true);
```

## 📋 相关文件修改

### 修改的文件
- `src/main/java/com/projectSource/ultimateManhurt/profession/listener/ProfessionListener.java`

### 修改的方法
- `handleWitherSkullHit()` - 添加防重复处理机制
- 连击次数更新逻辑 - 增强日志记录

### 新增的机制
- 元数据标记系统防止重复处理
- 详细的日志记录便于调试

## 🎯 技术要点

### 1. 元数据管理
- **标记类型**：使用FixedMetadataValue存储布尔值
- **生命周期**：随凋灵之首实体自动清理
- **性能影响**：最小，只在命中时检查

### 2. 事件处理优化
- **优先级**：使用EventPriority.HIGH确保及时处理
- **事件取消**：防止原生凋灵之首效果干扰
- **异常处理**：UUID解析异常的安全处理

### 3. 状态管理
- **连击计数**：使用Map<UUID, Integer>存储
- **线程安全**：在主线程中操作，无需额外同步
- **内存管理**：玩家离线时自动清理状态

## 🎉 总结

这次修复成功解决了恐惧魔王影压技能的连击bug：

🎯 **问题解决**: 防止了重复处理导致的连击爆炸
🔧 **机制完善**: 添加了完整的防护机制
📊 **监控增强**: 增加了详细的日志记录
⚖️ **平衡恢复**: 恢复了技能的正常平衡性

现在恐惧魔王的影压技能应该能够正常工作，连击次数按预期增长！
