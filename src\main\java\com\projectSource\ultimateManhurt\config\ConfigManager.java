package com.projectSource.ultimateManhurt.config;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.logging.Level;

/**
 * 配置管理器
 * 负责加载和保存插件配置
 */
public class ConfigManager {
    
    private final UltimateManhurt plugin;
    private FileConfiguration config;
    private FileConfiguration messages;
    private File configFile;
    private File messagesFile;
    
    public ConfigManager(UltimateManhurt plugin) {
        this.plugin = plugin;
        loadConfigs();
    }
    
    /**
     * 加载所有配置文件
     */
    private void loadConfigs() {
        // 创建插件数据文件夹
        if (!plugin.getDataFolder().exists()) {
            plugin.getDataFolder().mkdirs();
        }
        
        // 加载主配置文件
        configFile = new File(plugin.getDataFolder(), "config.yml");
        if (!configFile.exists()) {
            plugin.saveDefaultConfig();
        }
        config = plugin.getConfig();
        
        // 加载消息配置文件
        messagesFile = new File(plugin.getDataFolder(), "messages.yml");
        if (!messagesFile.exists()) {
            plugin.saveResource("messages.yml", false);
        }
        messages = YamlConfiguration.loadConfiguration(messagesFile);
    }
    
    /**
     * 重新加载配置
     */
    public void reloadConfigs() {
        plugin.reloadConfig();
        config = plugin.getConfig();
        messages = YamlConfiguration.loadConfiguration(messagesFile);
    }
    
    /**
     * 保存配置
     */
    public void saveConfigs() {
        try {
            config.save(configFile);
            messages.save(messagesFile);
        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "无法保存配置文件", e);
        }
    }
    
    /**
     * 获取主配置
     */
    public FileConfiguration getConfig() {
        return config;
    }
    
    /**
     * 获取消息配置
     */
    public FileConfiguration getMessages() {
        return messages;
    }
    
    /**
     * 获取配置值
     */
    public Object get(String path) {
        return config.get(path);
    }
    
    /**
     * 获取字符串配置
     */
    public String getString(String path) {
        return config.getString(path);
    }
    
    /**
     * 获取字符串配置（带默认值）
     */
    public String getString(String path, String defaultValue) {
        return config.getString(path, defaultValue);
    }
    
    /**
     * 获取整数配置
     */
    public int getInt(String path) {
        return config.getInt(path);
    }
    
    /**
     * 获取整数配置（带默认值）
     */
    public int getInt(String path, int defaultValue) {
        return config.getInt(path, defaultValue);
    }
    
    /**
     * 获取布尔配置
     */
    public boolean getBoolean(String path) {
        return config.getBoolean(path);
    }
    
    /**
     * 获取布尔配置（带默认值）
     */
    public boolean getBoolean(String path, boolean defaultValue) {
        return config.getBoolean(path, defaultValue);
    }
    
    /**
     * 获取双精度配置
     */
    public double getDouble(String path) {
        return config.getDouble(path);
    }
    
    /**
     * 获取双精度配置（带默认值）
     */
    public double getDouble(String path, double defaultValue) {
        return config.getDouble(path, defaultValue);
    }
    
    /**
     * 设置配置值
     */
    public void set(String path, Object value) {
        config.set(path, value);
    }
    
    /**
     * 获取消息
     */
    public String getMessage(String path) {
        return messages.getString(path, "消息未找到: " + path);
    }
    
    /**
     * 获取消息（带默认值）
     */
    public String getMessage(String path, String defaultValue) {
        return messages.getString(path, defaultValue);
    }
    
    /**
     * 设置消息
     */
    public void setMessage(String path, String message) {
        messages.set(path, message);
    }
}
