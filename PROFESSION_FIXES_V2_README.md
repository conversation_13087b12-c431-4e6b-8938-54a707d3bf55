# 职业系统修复 V2

## 修复的问题

### ✅ 1. 末影人被动技能优化

**问题：**
- 末影人被动技能不生效
- 传送位置不合理，容易卡在奇怪的位置
- 传送距离太近

**修复方案：**

#### 🔧 传送距离优化
- **原距离：** 8-16格
- **新距离：** 15-35格（更远更安全）

#### 🔧 安全位置检测增强
```java
// 原逻辑：简单检查脚下有方块
// 新逻辑：全面安全检查
private boolean isLocationSafe(Location loc) {
    // 检查Y坐标范围（-60 到 320）
    // 检查危险方块（岩浆、火焰、仙人掌等）
    // 检查基本安全性（脚下有方块，头部空气）
}
```

#### 🔧 寻找算法改进
- **尝试次数：** 10次 → 20次
- **搜索方式：** 从上往下寻找，避免卡在地下
- **备用方案：** 如果找不到安全位置，向上传送10格

#### 🔧 危险方块检测
新增危险方块检测，避免传送到：
- 岩浆 (LAVA)
- 火焰 (FIRE, SOUL_FIRE)
- 岩浆块 (MAGMA_BLOCK)
- 仙人掌 (CACTUS)
- 甜浆果丛 (SWEET_BERRY_BUSH)
- 凋零玫瑰 (WITHER_ROSE)
- 细雪 (POWDER_SNOW)

### ✅ 2. 屠夫钩子伤害增强

**新功能：** 屠夫钩子对速通者造成伤害

#### 🎯 伤害机制
- **目标检测：** 自动识别被钩中的玩家角色
- **伤害条件：** 捕猎者使用钩子命中速通者
- **伤害数值：** 6.0点伤害（3颗心）
- **伤害来源：** 正确标记伤害来源为使用者

#### 💬 消息反馈
- **对捕猎者：** "钩子命中 [玩家名]！造成伤害"
- **对速通者：** "被 [玩家名] 的钩子拉住并受到伤害！"
- **对其他角色：** 保持原有消息（无伤害）

## 技术实现

### 末影人传送优化

```java
private Location findSafeLocation(Player player) {
    Location playerLoc = player.getLocation();
    
    // 增加传送距离和尝试次数
    for (int attempts = 0; attempts < 20; attempts++) {
        double angle = random.nextDouble() * 2 * Math.PI;
        double distance = 15 + random.nextDouble() * 20; // 15-35格距离
        
        double x = playerLoc.getX() + Math.cos(angle) * distance;
        double z = playerLoc.getZ() + Math.sin(angle) * distance;
        
        // 从高处开始寻找，避免卡在地下
        Location testLoc = new Location(playerLoc.getWorld(), x, playerLoc.getY() + 10, z);
        
        // 从上往下寻找安全位置
        for (int y = (int) playerLoc.getY() + 15; y >= (int) playerLoc.getY() - 15; y--) {
            testLoc.setY(y);
            
            if (isSafeLocation(testLoc)) {
                Location safeLoc = testLoc.clone().add(0.5, 0.1, 0.5);
                
                if (isLocationSafe(safeLoc)) {
                    return safeLoc;
                }
            }
        }
    }
    
    // 备用方案：向上传送
    Location upLoc = playerLoc.clone().add(0, 10, 0);
    if (isLocationSafe(upLoc)) {
        return upLoc;
    }
    
    return null;
}
```

### 屠夫钩子伤害

```java
// 检查目标是否是速通者，如果是则造成伤害
if (playerRole == PlayerRole.HUNTER && targetRole == PlayerRole.SPEEDRUNNER) {
    double damage = 6.0; // 3颗心的伤害
    targetPlayer.damage(damage, player); // 指定伤害来源
    
    ComponentUtil.sendMessage(player, ComponentUtil.info("钩子命中 " + targetPlayer.getName() + "！造成伤害"));
    ComponentUtil.sendMessage(targetPlayer, ComponentUtil.warning("被 " + player.getName() + " 的钩子拉住并受到伤害！"));
}
```

## 调试功能

### 末影人被动技能调试
添加了临时调试日志来跟踪问题：
```java
plugin.getLogger().info("检测到末影人 " + victim.getName() + " 受到攻击，尝试触发闪烁");
```

## 平衡性调整

### 末影人
- **传送距离增加：** 提高生存能力
- **安全性提升：** 减少传送到危险位置的概率
- **冷却时间不变：** 保持45秒平衡

### 屠夫
- **攻击性增强：** 钩子现在对速通者有伤害
- **策略性提升：** 钩子不仅是控制技能，也是伤害技能
- **平衡考虑：** 只对敌对角色造成伤害

## 测试建议

### 末影人被动技能测试
1. 设置末影人职业：`/manhunt professiontest set ENDERMAN`
2. 让其他玩家攻击
3. 观察传送距离和位置安全性
4. 检查冷却时间是否正常

### 屠夫钩子测试
1. 设置屠夫职业：`/manhunt professiontest set BUTCHER`
2. 对速通者使用钩子技能
3. 确认造成伤害且显示正确消息
4. 对同阵营玩家测试确认无伤害

## 文件修改列表

1. **PassiveSkillHandler.java**
   - 重写 `findSafeLocation` 方法
   - 新增 `isLocationSafe` 方法
   - 新增 `isDangerousBlock` 方法

2. **ActiveSkillHandler.java**
   - 修改 `handleButcherHook` 方法
   - 添加角色检测和伤害逻辑

3. **ProfessionListener.java**
   - 添加末影人被动技能调试日志

4. **Profession.java**
   - 更新屠夫钩子技能描述

5. **PROFESSION_SYSTEM_README.md**
   - 更新屠夫钩子技能说明

## 注意事项

1. **调试日志：** 测试完成后记得移除调试日志
2. **性能影响：** 增加的安全检查可能略微影响性能，但在可接受范围内
3. **平衡性：** 屠夫钩子的伤害可能需要根据实际游戏体验进行调整
4. **兼容性：** 所有修改都向后兼容，不影响现有功能
