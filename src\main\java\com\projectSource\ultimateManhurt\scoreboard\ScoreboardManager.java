package com.projectSource.ultimateManhurt.scoreboard;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.util.ColorUtil;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import com.projectSource.ultimateManhurt.util.TimeUtil;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.scoreboard.*;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 计分板管理器
 * 负责游戏计分板的显示和更新
 */
public class ScoreboardManager {

    private final UltimateManhurt plugin;
    private final Map<UUID, GameScoreboard> playerScoreboards = new ConcurrentHashMap<>();
    private BukkitTask updateTask;

    public ScoreboardManager(UltimateManhurt plugin) {
        this.plugin = plugin;
        initialize();
    }

    /**
     * 初始化计分板管理器
     */
    private void initialize() {
        startUpdateTask();
        plugin.getLogger().info("计分板管理器已初始化");
    }

    /**
     * 启动更新任务
     */
    private void startUpdateTask() {
        int updateInterval = plugin.getConfigManager().getInt("scoreboard.update-interval", 20);

        updateTask = new BukkitRunnable() {
            @Override
            public void run() {
                updateAllScoreboards();
            }
        }.runTaskTimer(plugin, 0L, updateInterval);
    }

    /**
     * 更新所有计分板
     */
    private void updateAllScoreboards() {
        for (GameScoreboard gameScoreboard : playerScoreboards.values()) {
            gameScoreboard.update();
        }
    }

    /**
     * 为玩家创建游戏计分板
     */
    public void createGameScoreboard(Player player, GameSession gameSession) {
        UUID playerId = player.getUniqueId();

        // 移除现有计分板
        removeScoreboard(player);

        // 创建新计分板
        GameScoreboard gameScoreboard = new GameScoreboard(plugin, player, gameSession);
        playerScoreboards.put(playerId, gameScoreboard);

        // 显示计分板
        gameScoreboard.show();
    }

    /**
     * 移除玩家的计分板
     */
    public void removeScoreboard(Player player) {
        UUID playerId = player.getUniqueId();
        GameScoreboard gameScoreboard = playerScoreboards.remove(playerId);

        if (gameScoreboard != null) {
            gameScoreboard.hide();
        }
    }

    /**
     * 更新游戏计分板
     */
    public void updateGameScoreboard(GameSession gameSession) {
        for (UUID playerId : gameSession.getPlayerRoles().keySet()) {
            GameScoreboard gameScoreboard = playerScoreboards.get(playerId);
            if (gameScoreboard != null) {
                gameScoreboard.updateContent();
            }
        }
    }

    /**
     * 关闭计分板管理器
     */
    public void shutdown() {
        if (updateTask != null) {
            updateTask.cancel();
            updateTask = null;
        }

        // 清理所有计分板
        for (GameScoreboard gameScoreboard : playerScoreboards.values()) {
            gameScoreboard.hide();
        }
        playerScoreboards.clear();

        plugin.getLogger().info("计分板管理器已关闭");
    }
}
