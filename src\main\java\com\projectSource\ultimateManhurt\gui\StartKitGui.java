package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.kit.StartKit;
import com.projectSource.ultimateManhurt.kit.StartKitTemplate;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.List;

/**
 * StartKit管理GUI
 * 房主可以管理速通者和捕猎者的装备包
 */
public class StartKitGui extends BaseGui {
    
    private final Room room;
    
    public StartKitGui(UltimateManhurt plugin, Player player, Room room) {
        super(plugin, player, "装备包管理 - " + room.getName(), 54);
        this.room = room;
        setupGui();
    }
    
    @Override
    protected void setupGui() {
        // 检查权限
        if (!room.isOwner(this.player.getUniqueId())) {
            setItem(22, createItem(Material.BARRIER, "<red>权限不足", 
                "<gray>只有房主可以管理装备包"));
            return;
        }
        
        // 标题
        setItem(4, createItem(Material.CHEST, "<gold><bold>装备包管理",
            "<gray>管理速通者和捕猎者的起始装备"));
        
        // 启用/禁用装备包
        Material toggleMaterial = room.getSettings().isStartKitEnabled() ? Material.LIME_DYE : Material.GRAY_DYE;
        String toggleStatus = room.getSettings().isStartKitEnabled() ? "<green>已启用" : "<red>已禁用";
        setItem(13, createItem(toggleMaterial, "<yellow><bold>装备包状态",
            "<gray>当前状态: " + toggleStatus,
            "",
            "<yellow>点击切换启用/禁用装备包"));
        
        // 速通者装备包管理
        setupSpeedrunnerKit();
        
        // 捕猎者装备包管理
        setupHunterKit();
        
        // 预设模板
        setupTemplates();
        
        // 控制按钮
        setupControlButtons();
        
        // 装饰性物品
        fillEmptySlots();
    }
    
    /**
     * 设置速通者装备包区域
     */
    private void setupSpeedrunnerKit() {
        StartKit speedrunnerKit = room.getSettings().getSpeedrunnerKit();
        
        // 速通者装备包信息
        List<String> speedrunnerLore = new ArrayList<>();
        speedrunnerLore.add("<gray>当前装备包: <white>" + speedrunnerKit.getName());
        speedrunnerLore.add("<gray>描述: <white>" + speedrunnerKit.getDescription());
        speedrunnerLore.add("<gray>装备摘要: <white>" + speedrunnerKit.getSummary());
        speedrunnerLore.add("");
        speedrunnerLore.add("<yellow>左键: 编辑装备包");
        speedrunnerLore.add("<yellow>右键: 选择模板");
        speedrunnerLore.add("<yellow>Shift+左键: 预览装备包");
        
        setItem(20, createItem(Material.IRON_SWORD, "<green><bold>速通者装备包",
            speedrunnerLore.toArray(new String[0])));
        
        // 速通者装备包状态指示器
        Material statusMaterial = speedrunnerKit.isEmpty() ? Material.RED_CONCRETE : Material.GREEN_CONCRETE;
        String statusText = speedrunnerKit.isEmpty() ? "<red>空装备包" : "<green>已配置";
        setItem(29, createItem(statusMaterial, statusText,
            "<gray>物品数量: <white>" + speedrunnerKit.getItemCount()));
    }
    
    /**
     * 设置捕猎者装备包区域
     */
    private void setupHunterKit() {
        StartKit hunterKit = room.getSettings().getHunterKit();
        
        // 捕猎者装备包信息
        List<String> hunterLore = new ArrayList<>();
        hunterLore.add("<gray>当前装备包: <white>" + hunterKit.getName());
        hunterLore.add("<gray>描述: <white>" + hunterKit.getDescription());
        hunterLore.add("<gray>装备摘要: <white>" + hunterKit.getSummary());
        hunterLore.add("");
        hunterLore.add("<yellow>左键: 编辑装备包");
        hunterLore.add("<yellow>右键: 选择模板");
        hunterLore.add("<yellow>Shift+左键: 预览装备包");
        
        setItem(24, createItem(Material.BOW, "<red><bold>捕猎者装备包",
            hunterLore.toArray(new String[0])));
        
        // 捕猎者装备包状态指示器
        Material statusMaterial = hunterKit.isEmpty() ? Material.RED_CONCRETE : Material.GREEN_CONCRETE;
        String statusText = hunterKit.isEmpty() ? "<red>空装备包" : "<green>已配置";
        setItem(33, createItem(statusMaterial, statusText,
            "<gray>物品数量: <white>" + hunterKit.getItemCount()));
    }
    
    /**
     * 设置预设模板区域
     */
    private void setupTemplates() {
        // 预设模板标题
        setItem(37, createItem(Material.BOOKSHELF, "<blue><bold>预设模板",
            "<gray>快速应用预设的装备包模板",
            "",
            "<yellow>点击查看所有模板"));
        
        // 显示几个常用模板
        setItem(38, createItem(Material.WOODEN_SWORD, "<gray>基础装备包",
            "<gray>最基本的生存装备"));
        
        setItem(39, createItem(Material.STONE_SWORD, "<green>默认速通者",
            "<gray>适合新手的速通者装备"));
        
        setItem(40, createItem(Material.IRON_SWORD, "<red>默认捕猎者",
            "<gray>适合新手的捕猎者装备"));
        
        setItem(41, createItem(Material.DIAMOND_SWORD, "<color:#8d5caa>专业装备包",
            "<gray>适合有经验的玩家"));

        setItem(42, createItem(Material.BARRIER, "<gray>空装备包",
            "<gray>不提供任何起始装备"));
    }

    /**
     * 设置控制按钮
     */
    private void setupControlButtons() {
        // 保存设置
        setItem(45, createItem(Material.EMERALD, "<green><bold>保存设置",
            "<gray>保存当前装备包配置"));

        // 重置为默认
        setItem(46, createItem(Material.REDSTONE, "<yellow><bold>重置默认",
            "<gray>重置为默认装备包配置"));

        // 复制装备包
        setItem(47, createItem(Material.PAPER, "<blue><bold>复制装备包",
            "<gray>将速通者装备包复制给捕猎者",
            "<gray>或将捕猎者装备包复制给速通者"));

        // 测试装备包
        setItem(48, createItem(Material.ENDER_PEARL, "<color:#8d5caa><bold>测试装备包",
            "<gray>预览装备包内容"));
        
        // 返回
        setItem(53, createItem(Material.ARROW, "<red>返回",
            "<gray>返回房间设置"));
    }
    
    /**
     * 填充空槽位
     */
    private void fillEmptySlots() {
        for (int i = 0; i < inventory.getSize(); i++) {
            if (inventory.getItem(i) == null) {
                setItem(i, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));
            }
        }
    }
    
    @Override
    public void handleClick(InventoryClickEvent event) {
        event.setCancelled(true);
        
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        if (event.getClickedInventory() != this.inventory) {
            return;
        }
        
        ItemStack item = event.getCurrentItem();
        if (item == null || item.getType() == Material.AIR) {
            return;
        }
        
        this.playClickSound();
        
        int slot = event.getSlot();
        boolean isShiftClick = event.isShiftClick();
        boolean isRightClick = event.isRightClick();
        
        switch (slot) {
            case 13: // 切换装备包启用状态
                handleToggleStartKit();
                break;
            case 20: // 速通者装备包
                handleSpeedrunnerKit(isShiftClick, isRightClick);
                break;
            case 24: // 捕猎者装备包
                handleHunterKit(isShiftClick, isRightClick);
                break;
            case 37: // 查看所有模板
                handleViewAllTemplates();
                break;
            case 38: // 基础装备包
                handleApplyTemplate("basic");
                break;
            case 39: // 默认速通者
                handleApplyTemplate("default_speedrunner");
                break;
            case 40: // 默认捕猎者
                handleApplyTemplate("default_hunter");
                break;
            case 41: // 专业装备包
                handleViewProTemplates();
                break;
            case 42: // 空装备包
                handleApplyTemplate("empty");
                break;
            case 45: // 保存设置
                handleSave();
                break;
            case 46: // 重置默认
                handleReset();
                break;
            case 47: // 复制装备包
                handleCopyKit();
                break;
            case 48: // 测试装备包
                handleTestKit();
                break;
            case 53: // 返回
                handleBack();
                break;
        }
    }
    
    /**
     * 切换装备包启用状态
     */
    private void handleToggleStartKit() {
        boolean currentStatus = room.getSettings().isStartKitEnabled();
        room.getSettings().setStartKitEnabled(!currentStatus);
        
        String statusText = !currentStatus ? "启用" : "禁用";
        sendSuccess("已" + statusText + "装备包功能");
        
        refresh();
    }
    
    /**
     * 处理速通者装备包点击
     */
    private void handleSpeedrunnerKit(boolean isShiftClick, boolean isRightClick) {
        if (isShiftClick) {
            // 预览装备包
            plugin.getStartKitManager().previewKit(player, room.getSettings().getSpeedrunnerKit());
        } else if (isRightClick) {
            // 选择模板
            close();
            plugin.getGuiManager().openStartKitTemplateGui(player, room, PlayerRole.SPEEDRUNNER);
        } else {
            // 编辑装备包
            close();
            plugin.getGuiManager().openStartKitEditorGui(player, room, PlayerRole.SPEEDRUNNER);
        }
    }
    
    /**
     * 处理捕猎者装备包点击
     */
    private void handleHunterKit(boolean isShiftClick, boolean isRightClick) {
        if (isShiftClick) {
            // 预览装备包
            plugin.getStartKitManager().previewKit(player, room.getSettings().getHunterKit());
        } else if (isRightClick) {
            // 选择模板
            close();
            plugin.getGuiManager().openStartKitTemplateGui(player, room, PlayerRole.HUNTER);
        } else {
            // 编辑装备包
            close();
            plugin.getGuiManager().openStartKitEditorGui(player, room, PlayerRole.HUNTER);
        }
    }
    
    /**
     * 查看所有模板
     */
    private void handleViewAllTemplates() {
        close();
        plugin.getGuiManager().openStartKitTemplateGui(player, room, null);
    }
    
    /**
     * 应用模板
     */
    private void handleApplyTemplate(String templateId) {
        // 这里需要让用户选择应用到哪个角色
        close();
        plugin.getGuiManager().openStartKitTemplateApplyGui(player, room, templateId);
    }
    
    /**
     * 查看专业模板
     */
    private void handleViewProTemplates() {
        close();
        plugin.getGuiManager().openStartKitProTemplateGui(player, room);
    }
    
    /**
     * 保存设置
     */
    private void handleSave() {
        sendSuccess("装备包设置已保存");
        playSuccessSound();
    }
    
    /**
     * 重置为默认
     */
    private void handleReset() {
        room.getSettings().setSpeedrunnerKit(StartKitTemplate.getDefaultSpeedrunnerKit());
        room.getSettings().setHunterKit(StartKitTemplate.getDefaultHunterKit());
        
        sendSuccess("已重置为默认装备包");
        refresh();
    }
    
    /**
     * 复制装备包
     */
    private void handleCopyKit() {
        close();
        plugin.getGuiManager().openStartKitCopyGui(player, room);
    }
    
    /**
     * 测试装备包
     */
    private void handleTestKit() {
        close();
        plugin.getGuiManager().openStartKitTestGui(player, room);
    }
    
    /**
     * 返回房间设置
     */
    private void handleBack() {
        close();
        plugin.getGuiManager().openRoomSettingsGui(player, room);
    }
}
