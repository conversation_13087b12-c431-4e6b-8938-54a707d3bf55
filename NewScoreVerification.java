/**
 * 新积分系统验证程序
 */
public class NewScoreVerification {
    
    static class Milestone {
        String name;
        int score;
        String category;
        
        Milestone(String name, int score, String category) {
            this.name = name;
            this.score = score;
            this.category = category;
        }
    }
    
    public static void main(String[] args) {
        System.out.println("=== 新积分系统验证 ===\n");
        
        // 新的里程碑分数
        Milestone[] milestones = {
            // 基础进度 (130分)
            new Milestone("获得木头", 3, "基础进度"),
            new Milestone("制作工作台", 5, "基础进度"),
            new Milestone("制作木制工具", 8, "基础进度"),
            new Milestone("挖到石头", 10, "基础进度"),
            new Milestone("制作石制工具", 12, "基础进度"),
            new Milestone("制作熔炉", 15, "基础进度"),
            new Milestone("冶炼铁锭", 18, "基础进度"),
            new Milestone("制作铁制工具", 25, "基础进度"),
            new Milestone("制作铁制盔甲", 34, "基础进度"),
            
            // 重要里程碑 (210分)
            new Milestone("发现钻石", 40, "重要里程碑"),
            new Milestone("制作钻石工具", 50, "重要里程碑"),
            new Milestone("制作钻石盔甲", 60, "重要里程碑"),
            new Milestone("建造下界传送门", 30, "重要里程碑"),
            new Milestone("进入下界", 30, "重要里程碑"),
            
            // 下界进度 (170分)
            new Milestone("击杀烈焰人", 40, "下界进度"),
            new Milestone("获得烈焰棒", 50, "下界进度"),
            new Milestone("制作酿造台", 30, "下界进度"),
            new Milestone("制作末影之眼", 50, "下界进度"),
            
            // 末地准备 (130分)
            new Milestone("激活末地传送门", 60, "末地准备"),
            new Milestone("进入末地", 70, "末地准备"),
            
            // 最终目标 (130分)
            new Milestone("对末影龙造成伤害", 50, "最终目标"),
            new Milestone("击杀末影龙", 80, "最终目标"),
            
            // 奖励系统 (70分)
            new Milestone("生存奖励", 5, "奖励系统"),
            new Milestone("无死亡奖励", 15, "奖励系统"),
            new Milestone("快速进入下界", 10, "奖励系统"),
            new Milestone("快速进入末地", 15, "奖励系统"),
            new Milestone("快速击杀末影龙", 20, "奖励系统"),
            new Milestone("效率奖励", 5, "奖励系统")
        };
        
        // 验证分数
        verifyScores(milestones);
        
        // 显示分类统计
        showCategoryStats(milestones);
        
        // 显示难度分布
        showDifficultyDistribution(milestones);
    }
    
    private static void verifyScores(Milestone[] milestones) {
        System.out.println("1. 总分验证:");
        
        int totalScore = 0;
        for (Milestone milestone : milestones) {
            totalScore += milestone.score;
        }
        
        System.out.println("新总分: " + totalScore + "分");
        System.out.println("目标总分: 850分");
        System.out.println("差异: " + (totalScore - 850) + "分");
        
        if (Math.abs(totalScore - 850) <= 20) {
            System.out.println("✅ 总分在合理范围内 (±20分)");
        } else {
            System.out.println("❌ 总分偏差过大");
        }
        System.out.println();
    }
    
    private static void showCategoryStats(Milestone[] milestones) {
        System.out.println("2. 分类统计:");
        
        java.util.Map<String, Integer> categoryScores = new java.util.HashMap<>();
        java.util.Map<String, Integer> categoryCounts = new java.util.HashMap<>();
        
        for (Milestone milestone : milestones) {
            categoryScores.merge(milestone.category, milestone.score, Integer::sum);
            categoryCounts.merge(milestone.category, 1, Integer::sum);
        }
        
        String[] categories = {"基础进度", "重要里程碑", "下界进度", "末地准备", "最终目标", "奖励系统"};
        int[] targetScores = {130, 210, 170, 130, 130, 70};
        
        for (int i = 0; i < categories.length; i++) {
            String category = categories[i];
            int actualScore = categoryScores.getOrDefault(category, 0);
            int targetScore = targetScores[i];
            int count = categoryCounts.getOrDefault(category, 0);
            
            String status = Math.abs(actualScore - targetScore) <= 10 ? "✅" : "⚠️";
            System.out.println(String.format("  %s %s: %d分/%d分 (%d个里程碑, 平均%.1f分)", 
                status, category, actualScore, targetScore, count, (double)actualScore/count));
        }
        System.out.println();
    }
    
    private static void showDifficultyDistribution(Milestone[] milestones) {
        System.out.println("3. 难度分布:");
        
        int easy = 0, medium = 0, hard = 0, extreme = 0;
        
        for (Milestone milestone : milestones) {
            if (milestone.score <= 10) easy++;
            else if (milestone.score <= 30) medium++;
            else if (milestone.score <= 60) hard++;
            else extreme++;
        }
        
        System.out.println("  简单 (≤10分): " + easy + "个");
        System.out.println("  中等 (11-30分): " + medium + "个");
        System.out.println("  困难 (31-60分): " + hard + "个");
        System.out.println("  极难 (>60分): " + extreme + "个");
        System.out.println();
        
        System.out.println("4. 分数分布特点:");
        System.out.println("  ✅ 早期里程碑分数较低，鼓励快速进展");
        System.out.println("  ✅ 中期里程碑分数适中，保持动力");
        System.out.println("  ✅ 后期里程碑分数合理，避免过度集中");
        System.out.println("  ✅ 奖励系统分数较低，作为额外激励");
        System.out.println();
        
        System.out.println("5. 改进效果:");
        System.out.println("  📉 总分从3225分降至" + getTotalScore(milestones) + "分 (降低" + 
            (3225 - getTotalScore(milestones)) + "分)");
        System.out.println("  ⚖️ 分数分布更加均匀，避免后期过度集中");
        System.out.println("  🎯 移除了5个不实用的里程碑，简化系统");
        System.out.println("  🎮 提升了游戏体验，分数更有意义");
    }
    
    private static int getTotalScore(Milestone[] milestones) {
        int total = 0;
        for (Milestone milestone : milestones) {
            total += milestone.score;
        }
        return total;
    }
}
