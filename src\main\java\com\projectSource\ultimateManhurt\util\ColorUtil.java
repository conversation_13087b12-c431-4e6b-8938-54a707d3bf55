package com.projectSource.ultimateManhurt.util;

import net.kyori.adventure.text.format.TextColor;
import java.awt.Color;
import java.util.ArrayList;
import java.util.List;

/**
 * 颜色工具类
 * 提供颜色计算、渐变生成等功能
 */
public class ColorUtil {
    
    // 预定义颜色方案
    public static final TextColor[] RAINBOW_COLORS = {
        TextColor.fromHexString("#FF0000"), // 红
        TextColor.fromHexString("#FF8000"), // 橙
        TextColor.fromHexString("#FFFF00"), // 黄
        TextColor.fromHexString("#80FF00"), // 黄绿
        TextColor.fromHexString("#00FF00"), // 绿
        TextColor.fromHexString("#00FF80"), // 青绿
        TextColor.fromHexString("#00FFFF"), // 青
        TextColor.fromHexString("#0080FF"), // 天蓝
        TextColor.fromHexString("#0000FF"), // 蓝
        TextColor.fromHexString("#8000FF"), // 蓝紫
        TextColor.fromHexString("#FF00FF"), // 紫
        TextColor.fromHexString("#FF0080")  // 紫红
    };
    
    public static final TextColor[] FIRE_COLORS = {
        TextColor.fromHexString("#FF4500"), // 橙红
        TextColor.fromHexString("#FF6347"), // 番茄红
        TextColor.fromHexString("#FF7F50"), // 珊瑚色
        TextColor.fromHexString("#FFA500"), // 橙色
        TextColor.fromHexString("#FFD700")  // 金色
    };
    
    public static final TextColor[] ICE_COLORS = {
        TextColor.fromHexString("#E0FFFF"), // 浅青色
        TextColor.fromHexString("#B0E0E6"), // 粉蓝色
        TextColor.fromHexString("#87CEEB"), // 天蓝色
        TextColor.fromHexString("#4682B4"), // 钢蓝色
        TextColor.fromHexString("#1E90FF")  // 道奇蓝
    };
    
    /**
     * 创建两个颜色之间的渐变
     */
    public static List<TextColor> createGradient(TextColor start, TextColor end, int steps) {
        List<TextColor> gradient = new ArrayList<>();
        
        Color startColor = new Color(start.red(), start.green(), start.blue());
        Color endColor = new Color(end.red(), end.green(), end.blue());
        
        for (int i = 0; i < steps; i++) {
            float ratio = (float) i / (steps - 1);
            
            int red = (int) (startColor.getRed() * (1 - ratio) + endColor.getRed() * ratio);
            int green = (int) (startColor.getGreen() * (1 - ratio) + endColor.getGreen() * ratio);
            int blue = (int) (startColor.getBlue() * (1 - ratio) + endColor.getBlue() * ratio);
            
            gradient.add(TextColor.color(red, green, blue));
        }
        
        return gradient;
    }
    
    /**
     * 创建多个颜色之间的渐变
     */
    public static List<TextColor> createMultiGradient(TextColor[] colors, int totalSteps) {
        if (colors.length < 2) {
            throw new IllegalArgumentException("至少需要两个颜色");
        }
        
        List<TextColor> result = new ArrayList<>();
        int stepsPerSegment = totalSteps / (colors.length - 1);
        
        for (int i = 0; i < colors.length - 1; i++) {
            List<TextColor> segment = createGradient(colors[i], colors[i + 1], stepsPerSegment);
            if (i > 0) {
                segment.remove(0); // 移除重复的起始颜色
            }
            result.addAll(segment);
        }
        
        return result;
    }
    
    /**
     * 获取彩虹渐变
     */
    public static List<TextColor> getRainbowGradient(int steps) {
        return createMultiGradient(RAINBOW_COLORS, steps);
    }
    
    /**
     * 获取火焰渐变
     */
    public static List<TextColor> getFireGradient(int steps) {
        return createMultiGradient(FIRE_COLORS, steps);
    }
    
    /**
     * 获取冰霜渐变
     */
    public static List<TextColor> getIceGradient(int steps) {
        return createMultiGradient(ICE_COLORS, steps);
    }
    
    /**
     * 根据百分比获取颜色（红到绿）
     */
    public static TextColor getPercentageColor(double percentage) {
        percentage = Math.max(0, Math.min(1, percentage)); // 限制在0-1之间
        
        if (percentage <= 0.5) {
            // 红到黄
            int red = 255;
            int green = (int) (255 * percentage * 2);
            return TextColor.color(red, green, 0);
        } else {
            // 黄到绿
            int red = (int) (255 * (1 - percentage) * 2);
            int green = 255;
            return TextColor.color(red, green, 0);
        }
    }
    
    /**
     * 获取健康值颜色
     */
    public static TextColor getHealthColor(double health, double maxHealth) {
        double percentage = health / maxHealth;
        return getPercentageColor(percentage);
    }
    
    /**
     * 混合两个颜色
     */
    public static TextColor blendColors(TextColor color1, TextColor color2, float ratio) {
        ratio = Math.max(0, Math.min(1, ratio));
        
        int red = (int) (color1.red() * (1 - ratio) + color2.red() * ratio);
        int green = (int) (color1.green() * (1 - ratio) + color2.green() * ratio);
        int blue = (int) (color1.blue() * (1 - ratio) + color2.blue() * ratio);
        
        return TextColor.color(red, green, blue);
    }
    
    /**
     * 从十六进制字符串创建颜色
     */
    public static TextColor fromHex(String hex) {
        if (!hex.startsWith("#")) {
            hex = "#" + hex;
        }
        return TextColor.fromHexString(hex);
    }
    
    /**
     * 将颜色转换为十六进制字符串
     */
    public static String toHex(TextColor color) {
        return String.format("#%02X%02X%02X", color.red(), color.green(), color.blue());
    }
    
    /**
     * 获取随机颜色
     */
    public static TextColor randomColor() {
        return RAINBOW_COLORS[(int) (Math.random() * RAINBOW_COLORS.length)];
    }
    
    /**
     * 获取对比色（黑或白）
     */
    public static TextColor getContrastColor(TextColor color) {
        // 计算亮度
        double brightness = (color.red() * 0.299 + color.green() * 0.587 + color.blue() * 0.114) / 255;
        return brightness > 0.5 ? TextColor.color(0, 0, 0) : TextColor.color(255, 255, 255);
    }
}
