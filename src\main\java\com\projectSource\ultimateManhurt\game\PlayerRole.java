package com.projectSource.ultimateManhurt.game;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextColor;

/**
 * 玩家角色枚举
 */
public enum PlayerRole {
    SPEEDRUNNER("速通者", "需要在时间限制内击败末影龙", NamedTextColor.GREEN, "🏃"),
    HUNTER("捕猎者", "阻止速通者完成目标", NamedTextColor.RED, "🗡"),
    SPECTATOR("观察者", "观看游戏进行", NamedTextColor.GRAY, "👁");
    
    private final String displayName;
    private final String description;
    private final TextColor color;
    private final String emoji;
    
    PlayerRole(String displayName, String description, TextColor color, String emoji) {
        this.displayName = displayName;
        this.description = description;
        this.color = color;
        this.emoji = emoji;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public TextColor getColor() {
        return color;
    }
    
    public String getEmoji() {
        return emoji;
    }
    
    /**
     * 获取带颜色的显示名称组件
     */
    public Component getDisplayComponent() {
        return Component.text(emoji + " " + displayName, color);
    }
    
    /**
     * 获取详细信息组件
     */
    public Component getDetailComponent() {
        return Component.text()
                .append(getDisplayComponent())
                .append(Component.text(" - ", NamedTextColor.GRAY))
                .append(Component.text(description, NamedTextColor.WHITE))
                .build();
    }
    
    /**
     * 检查是否为游戏参与者（非观察者）
     */
    public boolean isPlayer() {
        return this != SPECTATOR;
    }
    
    /**
     * 检查是否可以造成伤害
     */
    public boolean canDealDamage() {
        return this == HUNTER || this == SPEEDRUNNER;
    }
    
    /**
     * 检查是否可以被其他玩家攻击
     */
    public boolean canBeDamaged() {
        return this == HUNTER || this == SPEEDRUNNER;
    }
    
    /**
     * 获取对立角色
     */
    public PlayerRole getOpposite() {
        return switch (this) {
            case SPEEDRUNNER -> HUNTER;
            case HUNTER -> SPEEDRUNNER;
            default -> SPECTATOR;
        };
    }
}
