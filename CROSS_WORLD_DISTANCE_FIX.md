# 跨世界距离计算异常修复

## 🚨 问题描述

### 错误信息
```
java.lang.IllegalArgumentException: Cannot measure distance between world and manhunt_room_1753526733205_227_1753526923076
	at org.bukkit.Location.distanceSquared(Location.java:577)
	at org.bukkit.Location.distance(Location.java:560)
	at PassiveSkillHandler.applyPiglinAura(PassiveSkillHandler.java:750)
```

### 问题原因
**根本原因：** Bukkit的 `Location.distance()` 方法无法计算不同世界之间两个位置的距离

**触发场景：**
1. **猪灵光环任务：** 猪灵在游戏世界，其他玩家在大厅世界
2. **狗战斗AI：** 萨满的狗在一个世界，目标在另一个世界
3. **伤害加深处理：** 猪灵和受害者在不同世界

### 影响范围
- 猪灵光环系统持续报错
- 萨满狗战斗AI可能失效
- 猪灵伤害加深功能异常

## 🔧 修复方案

### 核心修复策略
在所有距离计算前添加**世界一致性检查**，确保只在同一世界的实体之间计算距离。

### 修复位置

#### 1. 猪灵光环 - 友军速度加成
**修复前：**
```java
for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
    if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) == PlayerRole.HUNTER) {
        double distance = onlinePlayer.getLocation().distance(piglinLoc); // ❌ 可能跨世界
        if (distance <= 30.0) {
            // 应用速度效果
        }
    }
}
```

**修复后：**
```java
for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
    if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) == PlayerRole.HUNTER) {
        // ✅ 检查是否在同一世界
        if (!onlinePlayer.getWorld().equals(piglinLoc.getWorld())) {
            continue; // 不在同一世界，跳过
        }
        
        double distance = onlinePlayer.getLocation().distance(piglinLoc); // ✅ 安全的距离计算
        if (distance <= 30.0) {
            // 应用速度效果
        }
    }
}
```

#### 2. 猪灵光环 - 速通者高亮
**修复前：**
```java
for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
    if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) == PlayerRole.SPEEDRUNNER) {
        double distance = onlinePlayer.getLocation().distance(piglinLoc); // ❌ 可能跨世界
        if (distance <= 30.0) {
            // 应用高亮效果
        }
    }
}
```

**修复后：**
```java
for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
    if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) == PlayerRole.SPEEDRUNNER) {
        // ✅ 检查是否在同一世界
        if (!onlinePlayer.getWorld().equals(piglinLoc.getWorld())) {
            continue; // 不在同一世界，跳过
        }
        
        double distance = onlinePlayer.getLocation().distance(piglinLoc); // ✅ 安全的距离计算
        if (distance <= 30.0) {
            // 应用高亮效果
        }
    }
}
```

#### 3. 萨满狗战斗AI
**修复前：**
```java
for (Wolf dog : dogs) {
    if (dog != null && !dog.isDead() && dog.isValid()) {
        double distance = dog.getLocation().distance(target.getLocation()); // ❌ 可能跨世界
        if (distance <= 50.0) {
            dog.setTarget(target);
        }
    }
}
```

**修复后：**
```java
for (Wolf dog : dogs) {
    if (dog != null && !dog.isDead() && dog.isValid()) {
        // ✅ 检查狗是否在同一世界
        if (!dog.getWorld().equals(target.getWorld())) {
            continue; // 不在同一世界，跳过
        }
        
        double distance = dog.getLocation().distance(target.getLocation()); // ✅ 安全的距离计算
        if (distance <= 50.0) {
            dog.setTarget(target);
        }
    }
}
```

#### 4. 猪灵光环伤害加深
**修复前：**
```java
// 检查距离
double distance = victim.getLocation().distance(onlinePlayer.getLocation()); // ❌ 可能跨世界
if (distance <= 30.0) {
    // 增加伤害
}
```

**修复后：**
```java
// ✅ 检查是否在同一世界
if (!victim.getWorld().equals(onlinePlayer.getWorld())) {
    continue; // 不在同一世界，跳过
}

// 检查距离
double distance = victim.getLocation().distance(onlinePlayer.getLocation()); // ✅ 安全的距离计算
if (distance <= 30.0) {
    // 增加伤害
}
```

## 🛡️ 修复效果

### 错误消除
- ✅ **完全消除：** `IllegalArgumentException: Cannot measure distance between world` 错误
- ✅ **任务稳定：** 猪灵光环任务不再崩溃
- ✅ **功能正常：** 所有距离相关功能正常工作

### 逻辑优化
- ✅ **性能提升：** 跳过不必要的跨世界计算
- ✅ **逻辑合理：** 只在同一世界的实体间应用效果
- ✅ **代码健壮：** 增强了代码的容错性

## 🎯 技术细节

### 世界检查方法
```java
// 检查两个实体是否在同一世界
if (!entity1.getWorld().equals(entity2.getWorld())) {
    continue; // 不在同一世界，跳过处理
}
```

### 为什么会出现跨世界情况
1. **游戏流程：** 玩家从大厅世界传送到游戏世界
2. **任务持续：** 猪灵光环任务在后台持续运行
3. **状态不同步：** 部分玩家已在游戏世界，部分仍在大厅
4. **距离计算：** 尝试计算不同世界玩家间的距离

### Bukkit距离计算限制
```java
// Bukkit的Location.distance()方法要求：
// 1. 两个Location必须在同一个World中
// 2. 否则抛出IllegalArgumentException
public double distance(Location o) {
    return Math.sqrt(distanceSquared(o));
}

public double distanceSquared(Location o) {
    if (o == null) {
        throw new IllegalArgumentException("Cannot measure distance to a null location");
    } else if (o.getWorld() == null || getWorld() == null) {
        throw new IllegalArgumentException("Cannot measure distance to a null world");
    } else if (o.getWorld() != getWorld()) {
        throw new IllegalArgumentException("Cannot measure distance between " + getWorld().getName() + " and " + o.getWorld().getName());
    }
    // ... 距离计算逻辑
}
```

## 🧪 测试验证

### 测试场景
1. **跨世界传送测试：**
   ```
   1. 猪灵在游戏世界
   2. 其他玩家在大厅世界
   3. 验证不再出现距离计算错误
   ```

2. **正常游戏测试：**
   ```
   1. 所有玩家在同一游戏世界
   2. 验证猪灵光环正常工作
   3. 验证萨满狗AI正常工作
   ```

3. **边界情况测试：**
   ```
   1. 玩家在传送过程中
   2. 世界切换时的状态
   3. 验证系统稳定性
   ```

### 预期结果
- ❌ **错误消失：** 不再出现跨世界距离计算异常
- ✅ **功能正常：** 同世界内的所有功能正常工作
- ✅ **性能稳定：** 任务不再因异常而中断

## 📊 修复统计

### 修复位置统计
| 文件 | 方法 | 修复点 | 影响功能 |
|------|------|--------|----------|
| **PassiveSkillHandler.java** | `applyPiglinAura()` | 2处 | 猪灵光环效果 |
| **PassiveSkillHandler.java** | `commandShamanDogsToAttack()` | 1处 | 萨满狗战斗AI |
| **ProfessionListener.java** | `handlePiglinAuraDamageBoost()` | 1处 | 猪灵伤害加深 |
| **总计** | - | **4处** | 3个核心功能 |

### 代码模式
**统一的修复模式：**
```java
// 在所有距离计算前添加世界检查
if (!entity1.getWorld().equals(entity2.getWorld())) {
    continue; // 跳过跨世界处理
}

// 安全的距离计算
double distance = entity1.getLocation().distance(entity2.getLocation());
```

## 🎉 修复总结

成功修复了跨世界距离计算导致的系统异常：

### 关键改进
- ✅ **异常消除：** 完全解决 `IllegalArgumentException` 错误
- ✅ **逻辑优化：** 只在同一世界内应用效果，更符合游戏逻辑
- ✅ **性能提升：** 避免不必要的跨世界计算
- ✅ **代码健壮：** 增强了系统的容错能力

### 影响功能
1. **猪灵光环系统：** 稳定运行，不再崩溃
2. **萨满狗战斗AI：** 正常工作，智能攻击
3. **猪灵伤害加深：** 准确计算，效果正常

### 技术价值
- **防御性编程：** 在所有距离计算前进行世界检查
- **异常处理：** 优雅处理跨世界场景
- **系统稳定性：** 提升了整体系统的稳定性

现在系统可以稳定运行，不再出现跨世界距离计算的异常错误！🌍✨

**重要特点：**
- 完全消除跨世界距离计算异常
- 保持所有功能的正常运行
- 提升系统稳定性和性能
- 增强代码的健壮性
