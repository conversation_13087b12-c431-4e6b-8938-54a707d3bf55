# 世界种子操作模式说明

## 功能概述

世界种子设置现在根据当前的操作模式提供不同的交互方式，为用户提供最适合的操作体验。

## 操作模式对比

### 🖱️ 鼠标操作模式

当用户选择鼠标操作模式时，世界种子提供多种便捷的设置方式：

#### 操作方式
- **左键点击**: 打开专用的种子设置GUI
- **右键点击**: 传统的聊天输入方式
- **Shift+左键**: 快速设置为随机种子

#### 种子设置GUI特性
- 📱 **按钮输入**: 计算器式的数字按钮布局
- 🎯 **预设种子**: 6个经典预设种子一键应用
- 🎲 **智能生成**: 随机种子和玩家专属种子
- ✨ **实时反馈**: 输入过程中的即时显示和验证
- 🛡️ **错误防护**: 输入长度限制和溢出保护

#### 适用场景
- 需要精确输入复杂种子时
- 想要尝试预设的经典种子时
- 不确定种子格式或担心输入错误时
- 希望使用随机生成功能时

### 💬 聊天输入模式

当用户选择聊天输入模式时，世界种子提供直接的输入体验：

#### 操作方式
- **任意点击**: 直接进入聊天输入状态

#### 聊天输入特性
- ⚡ **快速输入**: 无需打开额外界面
- 🎯 **精确设置**: 直接输入已知的种子值
- 📝 **传统体验**: 熟悉的聊天栏输入方式
- 🔄 **即时应用**: 输入完成后立即生效

#### 适用场景
- 已知确切的种子值时
- 希望快速输入时
- 不需要GUI辅助功能时
- 习惯传统输入方式时

## 技术实现

### 模式判断逻辑
```java
private void handleWorldSeed(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
    if (currentOperationMode == OperationMode.MOUSE) {
        // 鼠标操作模式 - 提供多种选择
        if (isShiftClick && isLeftClick) {
            // 快速随机种子
            settings.setWorldSeed(0);
            sendSuccess("世界种子已设置为随机");
            refresh();
        } else if (isLeftClick) {
            // 打开种子设置GUI
            plugin.getGuiManager().openSeedSettingsGui(player, room);
        } else if (isRightClick) {
            // 聊天输入
            handleWorldSeedChatInput();
        }
    } else {
        // 聊天输入模式 - 直接进入聊天输入
        handleWorldSeedChatInput();
    }
}
```

### 界面描述动态更新
```java
String worldSeedDesc = currentOperationMode == OperationMode.MOUSE ?
    "<gray>左键: 打开种子设置GUI\n<gray>右键: 在聊天栏输入种子\n<gray>Shift+左键: 快速随机种子" :
    "<gray>点击后在聊天栏输入种子值";
```

## 用户体验优化

### 🎯 **模式一致性**
- 操作方式与当前选择的模式完全一致
- 界面描述实时反映当前模式的操作方法
- 用户无需记忆不同模式下的不同操作

### 🔄 **灵活切换**
- 用户可以随时通过模式切换按钮改变操作方式
- 切换后立即生效，无需重新打开界面
- 保持用户的操作习惯和偏好

### ⚡ **高效操作**
- 鼠标操作模式：功能丰富，适合复杂操作
- 聊天输入模式：简洁直接，适合快速输入
- 每种模式都针对特定使用场景优化

## 设计理念

### 🎨 **用户为中心**
- 不同用户有不同的操作习惯
- 提供多种方式满足不同需求
- 让用户自主选择最适合的操作方式

### 🔧 **功能分层**
- 基础功能：聊天输入（两种模式都支持）
- 增强功能：种子设置GUI（鼠标操作模式专享）
- 快捷功能：Shift+左键快速随机（鼠标操作模式）

### 📱 **界面适配**
- 鼠标操作模式：充分利用GUI的视觉和交互优势
- 聊天输入模式：保持简洁，减少界面切换
- 两种模式都提供清晰的操作指引

## 使用建议

### 🖱️ **推荐使用鼠标操作模式的情况**
- 第一次设置种子，不熟悉种子格式
- 想要尝试不同的预设种子
- 需要生成随机或个性化种子
- 希望有视觉反馈和错误防护

### 💬 **推荐使用聊天输入模式的情况**
- 已经知道确切的种子值
- 习惯使用聊天栏输入
- 希望快速完成设置
- 不需要额外的GUI功能

### 🔄 **模式切换建议**
- 根据当前任务选择合适的模式
- 复杂操作时使用鼠标操作模式
- 简单输入时使用聊天输入模式
- 可以随时切换，无需担心数据丢失

## 总结

通过为不同操作模式提供不同的交互方式，世界种子设置功能实现了：

1. **🎯 精准匹配**: 每种模式都提供最适合的操作方式
2. **🔄 灵活选择**: 用户可以根据需要自由切换
3. **⚡ 高效操作**: 减少不必要的界面切换和操作步骤
4. **🛡️ 用户友好**: 清晰的操作指引和错误防护
5. **📱 体验一致**: 与整体的操作模式设计保持一致

这种设计既保留了传统聊天输入的简洁性，又充分发挥了GUI界面的功能优势，为用户提供了最佳的种子设置体验。
