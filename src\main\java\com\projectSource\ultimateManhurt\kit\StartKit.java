package com.projectSource.ultimateManhurt.kit;

import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.io.Serializable;
import java.util.*;

/**
 * 起始装备包
 * 包含玩家游戏开始时获得的物品
 */
public class StartKit implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private String name;                                    // 装备包名称
    private String description;                             // 装备包描述
    private Map<Integer, ItemStack> items;                  // 物品映射 (槽位 -> 物品)
    private List<ItemStack> extraItems;                     // 额外物品（放入背包）
    private String templateId;                              // 模板ID（用于自定义模板）
    private String creatorUUID;                             // 创建者UUID（房主）
    private boolean isCustomTemplate;                       // 是否为自定义模板
    
    public StartKit(String name, String description) {
        this.name = name;
        this.description = description;
        this.items = new HashMap<>();
        this.extraItems = new ArrayList<>();
        this.templateId = null;
        this.creatorUUID = null;
        this.isCustomTemplate = false;
    }
    
    /**
     * 复制构造函数
     */
    public StartKit(StartKit other) {
        this.name = other.name;
        this.description = other.description;
        this.items = new HashMap<>();
        this.extraItems = new ArrayList<>();
        
        // 深拷贝物品
        for (Map.Entry<Integer, ItemStack> entry : other.items.entrySet()) {
            this.items.put(entry.getKey(), entry.getValue().clone());
        }
        
        for (ItemStack item : other.extraItems) {
            this.extraItems.add(item.clone());
        }
    }
    
    // 基础getter和setter
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getCreatorUUID() {
        return creatorUUID;
    }

    public void setCreatorUUID(String creatorUUID) {
        this.creatorUUID = creatorUUID;
    }

    public boolean isCustomTemplate() {
        return isCustomTemplate;
    }

    public void setCustomTemplate(boolean customTemplate) {
        isCustomTemplate = customTemplate;
    }
    
    public Map<Integer, ItemStack> getItems() {
        return new HashMap<>(items);
    }
    
    public List<ItemStack> getExtraItems() {
        return new ArrayList<>(extraItems);
    }
    
    /**
     * 设置指定槽位的物品
     */
    public void setItem(int slot, ItemStack item) {
        if (item == null || item.getType() == Material.AIR) {
            items.remove(slot);
        } else {
            items.put(slot, item.clone());
        }
    }
    
    /**
     * 获取指定槽位的物品
     */
    public ItemStack getItem(int slot) {
        ItemStack item = items.get(slot);
        return item != null ? item.clone() : null;
    }
    
    /**
     * 添加额外物品
     */
    public void addExtraItem(ItemStack item) {
        if (item != null && item.getType() != Material.AIR) {
            extraItems.add(item.clone());
        }
    }
    
    /**
     * 移除额外物品
     */
    public void removeExtraItem(int index) {
        if (index >= 0 && index < extraItems.size()) {
            extraItems.remove(index);
        }
    }
    
    /**
     * 清空装备包
     */
    public void clear() {
        items.clear();
        extraItems.clear();
    }
    
    /**
     * 检查装备包是否为空
     */
    public boolean isEmpty() {
        return items.isEmpty() && extraItems.isEmpty();
    }
    
    /**
     * 获取装备包中的物品总数
     */
    public int getItemCount() {
        int count = items.size();
        for (ItemStack item : extraItems) {
            count += item.getAmount();
        }
        return count;
    }
    
    /**
     * 获取装备包摘要
     */
    public String getSummary() {
        if (isEmpty()) {
            return "空装备包";
        }

        // 统计主要物品
        Map<Material, Integer> materialCount = new HashMap<>();
        
        for (ItemStack item : items.values()) {
            materialCount.merge(item.getType(), item.getAmount(), Integer::sum);
        }
        
        for (ItemStack item : extraItems) {
            materialCount.merge(item.getType(), item.getAmount(), Integer::sum);
        }
        
        // 构建摘要
        List<String> itemSummaries = new ArrayList<>();
        for (Map.Entry<Material, Integer> entry : materialCount.entrySet()) {
            String itemName = entry.getKey().name().toLowerCase().replace('_', ' ');
            if (entry.getValue() > 1) {
                itemSummaries.add(itemName + " x" + entry.getValue());
            } else {
                itemSummaries.add(itemName);
            }
        }
        
        if (itemSummaries.size() <= 3) {
            return String.join(", ", itemSummaries);
        } else {
            return String.join(", ", itemSummaries.subList(0, 3)) + " 等" + materialCount.size() + "种物品";
        }
    }
    

    
    /**
     * 创建物品
     */
    public static ItemStack createItem(Material material, int amount, String displayName, List<String> lore, Map<Enchantment, Integer> enchantments) {
        ItemStack item = new ItemStack(material, amount);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            if (displayName != null) {
                meta.displayName(net.kyori.adventure.text.Component.text(displayName));
            }

            if (lore != null && !lore.isEmpty()) {
                List<net.kyori.adventure.text.Component> componentLore = new ArrayList<>();
                for (String line : lore) {
                    componentLore.add(net.kyori.adventure.text.Component.text(line));
                }
                meta.lore(componentLore);
            }
            
            if (enchantments != null) {
                for (Map.Entry<Enchantment, Integer> entry : enchantments.entrySet()) {
                    meta.addEnchant(entry.getKey(), entry.getValue(), true);
                }
            }
            
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    @Override
    public String toString() {
        return "StartKit{" +
                "name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", items=" + items.size() +
                ", extraItems=" + extraItems.size() +
                '}';
    }
}
