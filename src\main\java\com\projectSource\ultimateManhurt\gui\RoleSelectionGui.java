package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.ranking.EloSystem;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.room.RoomType;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 角色选择GUI（天梯模式专用）
 */
public class RoleSelectionGui extends BaseGui {
    
    private final Room room;
    
    public RoleSelectionGui(UltimateManhurt plugin, Player player, Room room) {
        super(plugin, player, "选择阵营 - " + room.getName(), 27);
        this.room = room;
        setupGui();
    }
    
    @Override
    protected void setupGui() {
        // 阵营选择对所有房间类型都可用
        
        // 获取玩家ELO信息
        int playerElo = EloSystem.getPlayerElo(this.plugin.getDataManager().getPlayerData(this.player.getUniqueId()));
        EloSystem.Rank playerRank = EloSystem.getRank(playerElo);
        
        // 标题信息
        setItem(4, createItem(Material.GOLDEN_APPLE, "<gold><bold>选择你的阵营",
            "<gray>你的段位: " + playerRank.getIcon() + " " + playerRank.getColoredName(),
            "<gray>ELO: <yellow>" + playerElo,
            "",
            "<yellow>选择你想要的阵营进行天梯对战"
        ));
        
        // 速通者选项
        List<String> speedrunnerLore = new ArrayList<>();
        speedrunnerLore.add("<gray>角色: <green>速通者");
        speedrunnerLore.add("<gray>目标: <white>击败末影龙或达到目标分数");
        speedrunnerLore.add("<gray>难度: <yellow>较高 (ELO奖励 +20%)");
        speedrunnerLore.add("");
        
        // 显示当前速通者列表和平均ELO
        List<UUID> speedrunners = new ArrayList<>(room.getPlayersByRole(PlayerRole.SPEEDRUNNER));
        int maxSpeedrunners = room.getSettings().getMaxSpeedrunners();
        speedrunnerLore.add("<gray>当前人数: <white>" + speedrunners.size() + "/" + maxSpeedrunners);

        if (!speedrunners.isEmpty()) {
            int avgElo = calculateAverageElo(speedrunners);
            speedrunnerLore.add("<gray>平均ELO: <yellow>" + avgElo);

            // 显示当前速通者列表
            speedrunnerLore.add("<gray>当前成员:");
            for (UUID playerId : speedrunners) {
                Player speedrunner = this.plugin.getServer().getPlayer(playerId);
                if (speedrunner != null) {
                    int elo = com.projectSource.ultimateManhurt.ranking.EloSystem.getPlayerElo(
                        this.plugin.getDataManager().getPlayerData(playerId));
                    com.projectSource.ultimateManhurt.ranking.EloSystem.Rank rank =
                        com.projectSource.ultimateManhurt.ranking.EloSystem.getRank(elo);
                    speedrunnerLore.add("<gray>  • " + rank.getIcon() + " " + speedrunner.getName() + " (" + elo + ")");
                }
            }
        } else {
            speedrunnerLore.add("<gray>暂无成员");
        }
        speedrunnerLore.add("");

        // 检查是否可以加入速通者
        boolean canJoinSpeedrunner = canJoinRole(PlayerRole.SPEEDRUNNER);
        if (canJoinSpeedrunner) {
            speedrunnerLore.add("<green>点击加入速通者阵营");
        } else {
            speedrunnerLore.add("<red>速通者阵营已满 (" + speedrunners.size() + "/" + maxSpeedrunners + ")");
        }
        
        setItem(11, createItem(Material.EMERALD, "<green><bold>速通者阵营", speedrunnerLore.toArray(new String[0])));
        
        // 捕猎者选项
        List<String> hunterLore = new ArrayList<>();
        hunterLore.add("<gray>角色: <red>捕猎者");
        hunterLore.add("<gray>目标: <white>阻止速通者获胜");
        hunterLore.add("<gray>难度: <yellow>标准 (ELO奖励 标准)");
        hunterLore.add("");
        
        // 显示当前捕猎者列表和平均ELO
        List<UUID> hunters = new ArrayList<>(room.getPlayersByRole(PlayerRole.HUNTER));
        int maxHunters = room.getSettings().getMaxHunters();
        hunterLore.add("<gray>当前人数: <white>" + hunters.size() + "/" + maxHunters);

        if (!hunters.isEmpty()) {
            int avgElo = calculateAverageElo(hunters);
            hunterLore.add("<gray>平均ELO: <yellow>" + avgElo);

            // 显示当前捕猎者列表
            hunterLore.add("<gray>当前成员:");
            for (UUID playerId : hunters) {
                Player hunter = this.plugin.getServer().getPlayer(playerId);
                if (hunter != null) {
                    int elo = com.projectSource.ultimateManhurt.ranking.EloSystem.getPlayerElo(
                        this.plugin.getDataManager().getPlayerData(playerId));
                    com.projectSource.ultimateManhurt.ranking.EloSystem.Rank rank =
                        com.projectSource.ultimateManhurt.ranking.EloSystem.getRank(elo);
                    hunterLore.add("<gray>  • " + rank.getIcon() + " " + hunter.getName() + " (" + elo + ")");
                }
            }
        } else {
            hunterLore.add("<gray>暂无成员");
        }
        hunterLore.add("");

        // 检查是否可以加入捕猎者
        boolean canJoinHunter = canJoinRole(PlayerRole.HUNTER);
        if (canJoinHunter) {
            hunterLore.add("<red>点击加入捕猎者阵营");
        } else {
            hunterLore.add("<red>捕猎者阵营已满 (" + hunters.size() + "/" + maxHunters + ")");
        }

        setItem(15, createItem(Material.REDSTONE, "<red><bold>捕猎者阵营", hunterLore.toArray(new String[0])));
        
        // 显示当前玩家角色
        PlayerRole currentRole = room.getPlayerRole(player.getUniqueId());
        if (currentRole != null && currentRole != PlayerRole.SPECTATOR) {
            setItem(13, createItem(Material.COMPASS, "<yellow>当前阵营",
                "<gray>你当前是: " + currentRole.getDisplayName(),
                "<gray>可以点击上方切换阵营"
            ));
        }
        
        // 阵营平衡分析
        com.projectSource.ultimateManhurt.room.TeamBalanceManager balanceManager =
            new com.projectSource.ultimateManhurt.room.TeamBalanceManager(this.plugin);
        com.projectSource.ultimateManhurt.room.TeamBalanceManager.BalanceAnalysis analysis =
            balanceManager.analyzeBalance(room);

        // 平衡状态显示
        Material balanceIcon;
        switch (analysis.balanceLevel) {
            case EXCELLENT:
                balanceIcon = Material.EMERALD_BLOCK;
                break;
            case GOOD:
                balanceIcon = Material.GOLD_BLOCK;
                break;
            case FAIR:
                balanceIcon = Material.IRON_BLOCK;
                break;
            case POOR:
                balanceIcon = Material.REDSTONE_BLOCK;
                break;
            case UNPLAYABLE:
                balanceIcon = Material.BARRIER;
                break;
            default:
                balanceIcon = Material.STONE;
                break;
        }

        List<String> balanceLore = new ArrayList<>();
        balanceLore.add("<gray>平衡状态: " + analysis.getBalanceColor() + analysis.getBalanceDescription());
        balanceLore.add("<gray>速通者: <white>" + analysis.speedrunnerCount + "/" + analysis.maxSpeedrunners);
        balanceLore.add("<gray>捕猎者: <white>" + analysis.hunterCount + "/" + analysis.maxHunters);

        // 所有房间类型都显示ELO差距
        if (analysis.eloDifference > 0) {
            balanceLore.add("<gray>ELO差距: <white>" + analysis.eloDifference);
        }

        balanceLore.add("");
        balanceLore.add("<gray>建议:");
        for (String recommendation : analysis.recommendations) {
            balanceLore.add("<gray>• " + recommendation);
        }

        setItem(22, createItem(balanceIcon, analysis.getBalanceColor() + "阵营平衡分析",
            balanceLore.toArray(new String[0])));
        
        // 返回按钮
        setItem(26, createItem(Material.ARROW, "<yellow>返回房间设置", 
            "<gray>点击返回房间设置界面"));
        
        // 装饰性物品
        for (int i = 0; i < inventory.getSize(); i++) {
            if (inventory.getItem(i) == null) {
                setItem(i, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));
            }
        }
    }
    
    @Override
    public void handleClick(InventoryClickEvent event) {
        event.setCancelled(true);
        
        if (event.getClickedInventory() != inventory) {
            return;
        }
        
        ItemStack item = event.getCurrentItem();
        if (item == null || item.getType() == Material.AIR) {
            return;
        }
        
        playClickSound();
        
        int slot = event.getSlot();
        
        switch (slot) {
            case 11: // 速通者阵营
                handleRoleSelection(PlayerRole.SPEEDRUNNER);
                break;
            case 15: // 捕猎者阵营
                handleRoleSelection(PlayerRole.HUNTER);
                break;
            case 26: // 返回
                close();
                this.plugin.getGuiManager().openRoomSettingsGui(this.player, room);
                break;
        }
    }
    
    /**
     * 处理角色选择
     */
    private void handleRoleSelection(PlayerRole role) {
        if (!canJoinRole(role)) {
            sendError("无法加入" + role.getDisplayName() + "阵营：阵营已满");
            playErrorSound();
            return;
        }
        
        // 设置玩家角色
        boolean success = room.setPlayerRole(player.getUniqueId(), role);
        
        if (success) {
            sendSuccess("成功加入" + role.getDisplayName() + "阵营！");
            playSuccessSound();
            
            // 通知房间内其他玩家
            String message = player.getName() + " 加入了" + role.getDisplayName() + "阵营";
            for (UUID playerId : room.getPlayers()) {
                Player roomPlayer = this.plugin.getServer().getPlayer(playerId);
                if (roomPlayer != null && !roomPlayer.equals(this.player)) {
                    ComponentUtil.sendMessage(roomPlayer, ComponentUtil.info(message));
                }
            }
            
            // 刷新界面
            setupGui();
        } else {
            sendError("加入阵营失败，请稍后再试");
            playErrorSound();
        }
    }
    
    /**
     * 检查是否可以加入指定角色
     */
    private boolean canJoinRole(PlayerRole role) {
        int currentCount = room.getPlayerCount(role);
        int maxCount = (role == PlayerRole.SPEEDRUNNER) ? 
            room.getSettings().getMaxSpeedrunners() : room.getSettings().getMaxHunters();
        
        return currentCount < maxCount;
    }
    
    /**
     * 计算玩家列表的平均ELO
     */
    private int calculateAverageElo(List<UUID> playerIds) {
        if (playerIds.isEmpty()) return 1200; // 返回默认ELO

        int totalElo = 0;
        int validPlayers = 0;

        for (UUID playerId : playerIds) {
            com.projectSource.ultimateManhurt.data.PlayerData playerData =
                this.plugin.getDataManager().getPlayerData(playerId);
            if (playerData != null) {
                totalElo += EloSystem.getPlayerElo(playerData);
                validPlayers++;
            }
        }

        return validPlayers > 0 ? totalElo / validPlayers : 1200; // 默认ELO
    }
}
