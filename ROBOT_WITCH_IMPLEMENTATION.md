# 机器人 & 女巫职业实现报告

## 🤖 机器人 (Robot) - 速通者职业

### 基本信息
- **图标**: 铁镐 (扳手效果)
- **颜色**: 灰色
- **表情**: 🔧
- **定位**: 建筑工程师，擅长建造和陷阱设置

### 被动技能：分则能成
**描述**: 每放置一组方块获得一个金苹果

**实现机制**:
- 监听BlockPlaceEvent事件
- 检查放置的方块数量是否≥64个
- 给予1个金苹果作为奖励
- 播放升级音效和提示消息

### 主动技能：陷阱 (15秒冷却)
**描述**: 在原地生成隐形地雷

**技能特性**:
- **感应范围**: 5格
- **感应时间**: 1.5秒
- **触发条件**: 猎人进入范围且不潜行
- **地雷限制**: 最多同时存在9个
- **连击机制**: 45秒内重复触发伤害叠加

**爆炸机制**:
- 基础爆炸强度: 2.0
- 连击加成: 每次连击+0.5强度
- 地雷现形时显示火焰粒子效果

## 🧙‍♀️ 女巫 (Witch) - 猎人职业

### 基本信息
- **图标**: 酿造台
- **颜色**: 深紫色
- **表情**: 🧙‍♀️
- **定位**: 药水专家，掌握治疗和诅咒的双重力量

### 被动技能：药水专精
**描述**: 水瓶转为魔药或毒药，提供随机效果

**魔药效果** (友军使用):
1. 饱和 10秒
2. 速度II 15秒
3. 瞬间治疗III
4. 伤害吸收II 15秒
5. 抗性提升I 11秒
6. 抗火I 60秒
7. 水下呼吸 60秒
8. 无效果

**毒药效果** (敌军受到):
1. 瞬间伤害III
2. 中毒II 18秒
3. 虚弱II 11秒
4. 迟缓II 15秒
5. 无效果

### 主动技能：巫毒诅咒 (60秒冷却)
**描述**: 8秒内6格敌人每秒失去5%当前生命值

**技能特性**:
- **作用范围**: 6格
- **持续时间**: 8秒
- **伤害类型**: 真实伤害（直接减少血量）
- **伤害量**: 每秒5%当前生命值
- **特殊效果**: 禁止回血，血量低于25%最大生命值时立即死亡

## 🔧 技术实现

### 新增数据结构
```java
// 机器人地雷系统
private final Map<UUID, Set<Location>> robotTraps = new HashMap<>();
private final Map<Location, UUID> trapOwners = new HashMap<>();
private final Map<Location, Long> trapTriggerTimes = new HashMap<>();
private final Map<UUID, Integer> trapComboCount = new HashMap<>();

// 女巫巫毒诅咒系统
private final Map<UUID, Long> voodooAffectedPlayers = new HashMap<>();
```

### 事件监听
1. **BlockPlaceEvent**: 监听机器人方块放置
2. **PlayerMoveEvent**: 监听地雷触发检测
3. **PlayerInteractEvent**: 监听女巫药水使用
4. **EntityDamageByEntityEvent**: 禁用巫毒诅咒期间的回血

### 职业枚举更新
```java
ROBOT(
    "机器人", "建筑工程师，擅长建造和陷阱设置",
    PlayerRole.SPEEDRUNNER, Material.IRON_PICKAXE, NamedTextColor.GRAY, "🔧",
    "分则能成", "每放置一组方块获得一个金苹果", 0,
    "陷阱", "副手持剑右键，在原地生成隐形地雷...", 15
),

WITCH(
    "女巫", "药水专家，掌握治疗和诅咒的双重力量",
    PlayerRole.HUNTER, Material.BREWING_STAND, NamedTextColor.DARK_PURPLE, "🧙‍♀️",
    "药水专精", "水瓶转为魔药（友军增益）或毒药（敌军负面）...", 0,
    "巫毒诅咒", "副手持剑右键，8秒内6格敌人每秒失去5%当前生命值...", 60
)
```

## 🎮 游戏平衡

### 机器人平衡性
- **优势**: 建造奖励，区域控制能力
- **劣势**: 需要大量资源，地雷数量限制
- **反制**: 潜行可避免地雷，地雷有1.5秒感应时间

### 女巫平衡性
- **优势**: 强力的持续伤害，团队支援
- **劣势**: 技能冷却时间长，需要接近敌人
- **反制**: 保持距离，快速击杀

## ✅ 实现完成

- ✅ 职业枚举定义
- ✅ 主动技能实现
- ✅ 被动技能实现
- ✅ 事件监听器
- ✅ 地雷系统
- ✅ 巫毒诅咒系统
- ✅ 药水转换系统
- ✅ 回血禁用机制
- ✅ 连击系统
- ✅ 音效和粒子效果

两个新职业已完全实现并集成到现有的职业系统中！🎉
