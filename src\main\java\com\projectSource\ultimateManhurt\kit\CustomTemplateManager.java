package com.projectSource.ultimateManhurt.kit;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.io.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 自定义模板管理器
 * 负责房主自定义模板的保存、加载和管理
 */
public class CustomTemplateManager {
    
    private final UltimateManhurt plugin;
    private final Map<String, StartKit> customTemplates = new ConcurrentHashMap<>();
    private final File templatesFolder;
    
    public CustomTemplateManager(UltimateManhurt plugin) {
        this.plugin = plugin;
        this.templatesFolder = new File(plugin.getDataFolder(), "custom_templates");
        
        // 创建模板文件夹
        if (!templatesFolder.exists()) {
            templatesFolder.mkdirs();
        }
        
        // 加载已保存的模板
        loadCustomTemplates();
    }
    
    /**
     * 保存自定义模板
     */
    public boolean saveCustomTemplate(Player player, StartKit kit, String templateName) {
        if (templateName == null || templateName.trim().isEmpty()) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("模板名称不能为空"));
            return false;
        }
        
        // 验证模板名称
        if (!isValidTemplateName(templateName)) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("模板名称包含非法字符"));
            return false;
        }
        
        // 检查是否已存在同名模板
        String templateId = generateTemplateId(player.getUniqueId().toString(), templateName);
        if (customTemplates.containsKey(templateId)) {
            ComponentUtil.sendMessage(player, ComponentUtil.warning("已存在同名模板，将覆盖原模板"));
        }
        
        // 设置模板信息
        StartKit template = new StartKit(kit);
        template.setName(templateName);
        template.setTemplateId(templateId);
        template.setCreatorUUID(player.getUniqueId().toString());
        template.setCustomTemplate(true);
        
        // 保存到内存
        customTemplates.put(templateId, template);
        
        // 保存到文件
        if (saveTemplateToFile(template)) {
            ComponentUtil.sendMessage(player, ComponentUtil.success("模板 \"" + templateName + "\" 保存成功"));
            plugin.getLogger().info("玩家 " + player.getName() + " 保存了自定义模板: " + templateName);
            return true;
        } else {
            ComponentUtil.sendMessage(player, ComponentUtil.error("模板保存失败"));
            return false;
        }
    }
    
    /**
     * 删除自定义模板
     */
    public boolean deleteCustomTemplate(Player player, String templateId) {
        StartKit template = customTemplates.get(templateId);
        if (template == null) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("模板不存在"));
            return false;
        }
        
        // 检查权限（只有创建者可以删除）
        if (!template.getCreatorUUID().equals(player.getUniqueId().toString())) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("只有模板创建者可以删除模板"));
            return false;
        }
        
        // 从内存中删除
        customTemplates.remove(templateId);
        
        // 从文件中删除
        File templateFile = new File(templatesFolder, templateId + ".yml");
        if (templateFile.exists()) {
            templateFile.delete();
        }
        
        ComponentUtil.sendMessage(player, ComponentUtil.success("模板 \"" + template.getName() + "\" 删除成功"));
        return true;
    }
    
    /**
     * 获取玩家的自定义模板
     */
    public List<StartKit> getPlayerTemplates(UUID playerUUID) {
        List<StartKit> playerTemplates = new ArrayList<>();
        String playerUUIDStr = playerUUID.toString();
        
        for (StartKit template : customTemplates.values()) {
            if (playerUUIDStr.equals(template.getCreatorUUID())) {
                playerTemplates.add(template);
            }
        }
        
        return playerTemplates;
    }
    
    /**
     * 获取所有自定义模板
     */
    public Map<String, StartKit> getAllCustomTemplates() {
        return new HashMap<>(customTemplates);
    }
    
    /**
     * 根据ID获取模板
     */
    public StartKit getTemplate(String templateId) {
        return customTemplates.get(templateId);
    }
    
    /**
     * 生成模板ID
     */
    private String generateTemplateId(String playerUUID, String templateName) {
        return "custom_" + playerUUID.substring(0, 8) + "_" + templateName.replaceAll("[^a-zA-Z0-9_]", "_");
    }
    
    /**
     * 验证模板名称
     */
    private boolean isValidTemplateName(String name) {
        if (name.length() > 32) return false;
        if (name.length() < 1) return false;
        
        // 不允许的字符
        String invalidChars = "<>:\"/\\|?*";
        for (char c : invalidChars.toCharArray()) {
            if (name.indexOf(c) != -1) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 保存模板到文件
     */
    private boolean saveTemplateToFile(StartKit template) {
        try {
            File templateFile = new File(templatesFolder, template.getTemplateId() + ".yml");

            YamlConfiguration config = new YamlConfiguration();

            // 保存基本信息
            config.set("name", template.getName());
            config.set("description", template.getDescription());
            config.set("templateId", template.getTemplateId());
            config.set("creatorUUID", template.getCreatorUUID());
            config.set("isCustomTemplate", template.isCustomTemplate());

            // 保存主要物品
            Map<String, Object> itemsMap = new HashMap<>();
            for (Map.Entry<Integer, ItemStack> entry : template.getItems().entrySet()) {
                itemsMap.put(entry.getKey().toString(), entry.getValue().serialize());
            }
            config.set("items", itemsMap);

            // 保存额外物品
            List<Map<String, Object>> extraItemsList = new ArrayList<>();
            for (ItemStack item : template.getExtraItems()) {
                extraItemsList.add(item.serialize());
            }
            config.set("extraItems", extraItemsList);

            // 保存到文件
            config.save(templateFile);
            return true;

        } catch (Exception e) {
            plugin.getLogger().severe("保存自定义模板失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 从文件加载模板
     */
    private void loadCustomTemplates() {
        if (!templatesFolder.exists()) {
            return;
        }

        File[] templateFiles = templatesFolder.listFiles((dir, name) -> name.endsWith(".yml"));
        if (templateFiles == null) {
            return;
        }

        int loadedCount = 0;
        for (File file : templateFiles) {
            try {
                StartKit template = loadTemplateFromYaml(file);
                if (template != null) {
                    customTemplates.put(template.getTemplateId(), template);
                    loadedCount++;
                }
            } catch (Exception e) {
                plugin.getLogger().warning("加载自定义模板失败: " + file.getName() + " - " + e.getMessage());
                e.printStackTrace();
            }
        }

        if (loadedCount > 0) {
            plugin.getLogger().info("加载了 " + loadedCount + " 个自定义模板");
        }
    }

    /**
     * 从YAML文件加载模板
     */
    private StartKit loadTemplateFromYaml(File file) throws Exception {
        YamlConfiguration config = YamlConfiguration.loadConfiguration(file);

        // 加载基本信息
        String name = config.getString("name");
        String description = config.getString("description", "");
        String templateId = config.getString("templateId");
        String creatorUUID = config.getString("creatorUUID");
        boolean isCustomTemplate = config.getBoolean("isCustomTemplate", false);

        if (name == null || templateId == null || creatorUUID == null) {
            plugin.getLogger().warning("模板文件格式错误: " + file.getName());
            return null;
        }

        // 创建模板
        StartKit template = new StartKit(name, description);
        template.setTemplateId(templateId);
        template.setCreatorUUID(creatorUUID);
        template.setCustomTemplate(isCustomTemplate);

        // 加载主要物品
        if (config.contains("items")) {
            ConfigurationSection itemsSection = config.getConfigurationSection("items");
            if (itemsSection != null) {
                for (String slotKey : itemsSection.getKeys(false)) {
                    try {
                        int slot = Integer.parseInt(slotKey);
                        ConfigurationSection itemSection = itemsSection.getConfigurationSection(slotKey);
                        if (itemSection != null) {
                            Map<String, Object> itemData = itemSection.getValues(true);
                            ItemStack item = ItemStack.deserialize(itemData);
                            template.setItem(slot, item);
                        }
                    } catch (Exception e) {
                        plugin.getLogger().warning("加载物品失败 (槽位 " + slotKey + "): " + e.getMessage());
                    }
                }
            }
        }

        // 加载额外物品
        if (config.contains("extraItems")) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> extraItemsList = (List<Map<String, Object>>) config.getList("extraItems");
            if (extraItemsList != null) {
                for (Map<String, Object> itemData : extraItemsList) {
                    try {
                        ItemStack item = ItemStack.deserialize(itemData);
                        template.addExtraItem(item);
                    } catch (Exception e) {
                        plugin.getLogger().warning("加载额外物品失败: " + e.getMessage());
                    }
                }
            }
        }

        return template;
    }
    
    /**
     * 获取模板统计信息
     */
    public String getTemplateStats() {
        int totalTemplates = customTemplates.size();
        Set<String> creators = new HashSet<>();
        
        for (StartKit template : customTemplates.values()) {
            creators.add(template.getCreatorUUID());
        }
        
        return String.format("总模板数: %d, 创建者数: %d", totalTemplates, creators.size());
    }
    
    /**
     * 清理过期模板（可选功能）
     */
    public void cleanupOldTemplates() {
        // 这里可以实现清理逻辑，比如删除很久没有使用的模板
        // 为了简化，暂时不实现
    }
    
    /**
     * 导出模板
     */
    public boolean exportTemplate(String templateId, File exportFile) {
        StartKit template = customTemplates.get(templateId);
        if (template == null) {
            return false;
        }

        try {
            YamlConfiguration config = new YamlConfiguration();

            // 保存基本信息
            config.set("name", template.getName());
            config.set("description", template.getDescription());
            config.set("templateId", template.getTemplateId());
            config.set("creatorUUID", template.getCreatorUUID());
            config.set("isCustomTemplate", template.isCustomTemplate());

            // 保存主要物品
            Map<String, Object> itemsMap = new HashMap<>();
            for (Map.Entry<Integer, ItemStack> entry : template.getItems().entrySet()) {
                itemsMap.put(entry.getKey().toString(), entry.getValue().serialize());
            }
            config.set("items", itemsMap);

            // 保存额外物品
            List<Map<String, Object>> extraItemsList = new ArrayList<>();
            for (ItemStack item : template.getExtraItems()) {
                extraItemsList.add(item.serialize());
            }
            config.set("extraItems", extraItemsList);

            // 保存到文件
            config.save(exportFile);
            return true;

        } catch (Exception e) {
            plugin.getLogger().severe("导出模板失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 导入模板
     */
    public boolean importTemplate(File importFile, Player importer) {
        try {
            StartKit template = loadTemplateFromYaml(importFile);
            if (template == null) {
                ComponentUtil.sendMessage(importer, ComponentUtil.error("模板文件格式错误"));
                return false;
            }

            // 重新设置创建者信息
            template.setCreatorUUID(importer.getUniqueId().toString());
            template.setTemplateId(generateTemplateId(importer.getUniqueId().toString(), template.getName()));

            customTemplates.put(template.getTemplateId(), template);
            saveTemplateToFile(template);

            ComponentUtil.sendMessage(importer, ComponentUtil.success("模板导入成功: " + template.getName()));
            return true;

        } catch (Exception e) {
            ComponentUtil.sendMessage(importer, ComponentUtil.error("模板导入失败"));
            plugin.getLogger().severe("导入模板失败: " + e.getMessage());
            return false;
        }
    }
}
