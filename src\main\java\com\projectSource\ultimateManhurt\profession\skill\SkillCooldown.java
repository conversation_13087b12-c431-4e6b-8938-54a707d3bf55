package com.projectSource.ultimateManhurt.profession.skill;

import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 技能冷却管理器
 */
public class SkillCooldown {
    
    // 玩家技能冷却时间映射 <玩家UUID, <技能名称, 冷却结束时间>>
    private final ConcurrentHashMap<UUID, ConcurrentHashMap<String, Long>> playerCooldowns;

    // 玩家技能最大冷却时间映射 <玩家UUID, <技能名称, 最大冷却时间(秒)>>
    private final ConcurrentHashMap<UUID, ConcurrentHashMap<String, Integer>> playerMaxCooldowns;
    
    public SkillCooldown() {
        this.playerCooldowns = new ConcurrentHashMap<>();
        this.playerMaxCooldowns = new ConcurrentHashMap<>();
    }
    
    /**
     * 设置技能冷却
     */
    public void setCooldown(UUID playerId, String skillName, int cooldownSeconds) {
        playerCooldowns.computeIfAbsent(playerId, k -> new ConcurrentHashMap<>())
                .put(skillName, System.currentTimeMillis() + (cooldownSeconds * 1000L));

        // 同时记录最大冷却时间
        playerMaxCooldowns.computeIfAbsent(playerId, k -> new ConcurrentHashMap<>())
                .put(skillName, cooldownSeconds);
    }
    
    /**
     * 检查技能是否在冷却中
     */
    public boolean isOnCooldown(UUID playerId, String skillName) {
        ConcurrentHashMap<String, Long> playerSkills = playerCooldowns.get(playerId);
        if (playerSkills == null) {
            return false;
        }
        
        Long cooldownEnd = playerSkills.get(skillName);
        if (cooldownEnd == null) {
            return false;
        }
        
        if (System.currentTimeMillis() >= cooldownEnd) {
            // 冷却已结束，移除记录
            playerSkills.remove(skillName);
            if (playerSkills.isEmpty()) {
                playerCooldowns.remove(playerId);
            }
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取剩余冷却时间（秒）
     */
    public int getRemainingCooldown(UUID playerId, String skillName) {
        ConcurrentHashMap<String, Long> playerSkills = playerCooldowns.get(playerId);
        if (playerSkills == null) {
            return 0;
        }
        
        Long cooldownEnd = playerSkills.get(skillName);
        if (cooldownEnd == null) {
            return 0;
        }
        
        long remaining = cooldownEnd - System.currentTimeMillis();
        if (remaining <= 0) {
            // 冷却已结束，移除记录
            playerSkills.remove(skillName);
            if (playerSkills.isEmpty()) {
                playerCooldowns.remove(playerId);
            }
            return 0;
        }
        
        return (int) Math.ceil(remaining / 1000.0);
    }

    /**
     * 获取技能的最大冷却时间（秒）
     */
    public int getMaxCooldown(UUID playerId, String skillName) {
        ConcurrentHashMap<String, Integer> playerSkills = playerMaxCooldowns.get(playerId);
        if (playerSkills == null) {
            return 0;
        }

        Integer maxCooldown = playerSkills.get(skillName);
        return maxCooldown != null ? maxCooldown : 0;
    }

    /**
     * 清除玩家的所有技能冷却
     */
    public void clearPlayerCooldowns(UUID playerId) {
        playerCooldowns.remove(playerId);
        playerMaxCooldowns.remove(playerId);
    }
    
    /**
     * 清除指定玩家的指定技能冷却
     */
    public void clearSkillCooldown(UUID playerId, String skillName) {
        ConcurrentHashMap<String, Long> playerSkills = playerCooldowns.get(playerId);
        if (playerSkills != null) {
            playerSkills.remove(skillName);
            if (playerSkills.isEmpty()) {
                playerCooldowns.remove(playerId);
            }
        }

        ConcurrentHashMap<String, Integer> playerMaxSkills = playerMaxCooldowns.get(playerId);
        if (playerMaxSkills != null) {
            playerMaxSkills.remove(skillName);
            if (playerMaxSkills.isEmpty()) {
                playerMaxCooldowns.remove(playerId);
            }
        }
    }
    
    /**
     * 清理所有过期的冷却记录
     */
    public void cleanupExpiredCooldowns() {
        long currentTime = System.currentTimeMillis();

        playerCooldowns.entrySet().removeIf(playerEntry -> {
            UUID playerId = playerEntry.getKey();
            ConcurrentHashMap<String, Long> playerSkills = playerEntry.getValue();

            // 移除过期的技能冷却
            playerSkills.entrySet().removeIf(skillEntry -> {
                String skillName = skillEntry.getKey();
                boolean expired = skillEntry.getValue() <= currentTime;

                // 如果技能过期，同时清理最大冷却时间记录
                if (expired) {
                    ConcurrentHashMap<String, Integer> playerMaxSkills = playerMaxCooldowns.get(playerId);
                    if (playerMaxSkills != null) {
                        playerMaxSkills.remove(skillName);
                        if (playerMaxSkills.isEmpty()) {
                            playerMaxCooldowns.remove(playerId);
                        }
                    }
                }

                return expired;
            });

            // 如果玩家没有任何技能在冷却中，移除整个玩家记录
            return playerSkills.isEmpty();
        });
    }
}
