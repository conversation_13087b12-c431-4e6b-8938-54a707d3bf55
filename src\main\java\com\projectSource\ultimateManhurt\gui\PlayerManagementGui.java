package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import net.kyori.adventure.text.Component;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.SkullMeta;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 玩家管理GUI
 */
public class PlayerManagementGui extends BaseGui {
    
    private final Room room;
    
    public PlayerManagementGui(UltimateManhurt plugin, Player player, Room room) {
        super(plugin, player,
            (room != null && room.isOwner(player.getUniqueId()) ?
                "<blue><bold>玩家管理 - " + room.getName() :
                "<gray><bold>玩家列表 - " + (room != null ? room.getName() : "未知房间")), 54);
        this.room = room;

        // 手动调用setupGui()
        setupGui();
    }
    
    @Override
    protected void setupGui() {
        // 检查房间是否存在
        if (room == null) {
            setItem(22, createItem(Material.BARRIER, "<red>房间不存在", "<gray>房间可能已被删除"));
            return;
        }
        
        // 检查是否为房间成员
        if (!room.containsPlayer(player.getUniqueId()) && !room.isOwner(player.getUniqueId())) {
            setItem(22, createItem(Material.BARRIER, "<red>权限不足", "<gray>只有房间成员可以查看玩家列表"));
            return;
        }
        
        // 填充边框
        fillBorder(Material.GRAY_STAINED_GLASS_PANE);
        
        // 显示玩家列表
        displayPlayers();
        
        // 显示角色统计
        displayRoleStats();
        
        // 控制按钮
        setupControlButtons();
    }
    
    /**
     * 显示玩家列表
     */
    private void displayPlayers() {
        List<UUID> playerList = new ArrayList<>(room.getPlayers());
        
        int startSlot = 10; // 从第二行开始
        int currentSlot = startSlot;
        
        for (int i = 0; i < playerList.size() && i < 28; i++) { // 最多显示28个玩家
            UUID playerId = playerList.get(i);
            Player roomPlayer = Bukkit.getPlayer(playerId);
            
            if (roomPlayer != null) {
                PlayerRole role = room.getPlayerRole(playerId);
                ItemStack playerItem = createPlayerItem(roomPlayer, role);
                setItem(currentSlot, playerItem);
            }
            
            currentSlot++;
            // 跳过边框位置
            if (currentSlot % 9 == 8) {
                currentSlot += 2;
            }
            if (currentSlot >= 45) {
                break;
            }
        }
        
        // 如果没有其他玩家，显示提示
        if (playerList.size() <= 1) {
            setItem(22, createItem(Material.BARRIER, "<yellow>暂无其他玩家", 
                "<gray>邀请朋友加入房间吧！"));
        }
    }
    
    /**
     * 创建玩家物品
     */
    private ItemStack createPlayerItem(Player roomPlayer, PlayerRole role) {
        ItemStack item = new ItemStack(Material.PLAYER_HEAD);
        SkullMeta meta = (SkullMeta) item.getItemMeta();

        if (meta != null) {
            meta.setOwningPlayer(roomPlayer);

            String roleName = role != null ? role.getDisplayName() : "未分配";
            String roleColor = role != null ? getRoleColor(role) : "<gray>";

            meta.displayName(ComponentUtil.parse("<white>" + roomPlayer.getName()));

            List<Component> lore = new ArrayList<>();
            lore.add(Component.empty());
            lore.add(ComponentUtil.parse("<gray>当前角色: " + roleColor + roleName));
            lore.add(Component.empty());

            // 根据查看者权限显示不同操作提示
            boolean isViewerOwner = room.isOwner(player.getUniqueId());
            if (isViewerOwner) {
                lore.add(ComponentUtil.parse("<yellow>左键: 设置为速通者"));
                lore.add(ComponentUtil.parse("<red>右键: 设置为捕猎者"));
                lore.add(ComponentUtil.parse("<gray>Shift+左键: 设置为观察者"));
            } else {
                lore.add(ComponentUtil.parse("<gray>只有房主可以修改角色"));
            }

            lore.add(Component.empty());
            if (room.isOwner(roomPlayer.getUniqueId())) {
                lore.add(ComponentUtil.parse("<gold>👑 房主"));
            }

            meta.lore(lore);
            item.setItemMeta(meta);
        }

        return item;
    }
    
    /**
     * 获取角色颜色
     */
    private String getRoleColor(PlayerRole role) {
        return switch (role) {
            case SPEEDRUNNER -> "<green>";
            case HUNTER -> "<red>";
            case SPECTATOR -> "<gray>";
        };
    }
    
    /**
     * 显示角色统计
     */
    private void displayRoleStats() {
        int speedrunners = room.getPlayerCount(PlayerRole.SPEEDRUNNER);
        int hunters = room.getPlayerCount(PlayerRole.HUNTER);
        int spectators = room.getPlayerCount(PlayerRole.SPECTATOR);
        
        // 速通者统计
        setItem(1, createItem(Material.EMERALD, "<green>速通者", 
            "<gray>当前数量: <white>" + speedrunners,
            "<gray>需要击败末影龙获胜"));
        
        // 捕猎者统计
        setItem(2, createItem(Material.REDSTONE, "<red>捕猎者", 
            "<gray>当前数量: <white>" + hunters,
            "<gray>需要阻止速通者获胜"));
        
        // 观察者统计
        setItem(3, createItem(Material.GRAY_DYE, "<gray>观察者", 
            "<gray>当前数量: <white>" + spectators,
            "<gray>观看游戏进行"));
    }
    
    /**
     * 设置控制按钮
     */
    private void setupControlButtons() {
        // 返回房间设置
        setItem(49, createItem(Material.ARROW, "<yellow>返回房间设置", "<gray>点击返回房间设置界面"));
        
        // 刷新
        setItem(48, createItem(Material.COMPASS, "<green>刷新", "<gray>点击刷新玩家列表"));
    }
    
    @Override
    public void handleClick(InventoryClickEvent event) {
        event.setCancelled(true);
        
        if (event.getClickedInventory() != inventory) {
            return;
        }
        
        // 检查房间是否存在
        if (room == null) {
            sendError("房间不存在或已被删除！");
            playErrorSound();
            close();
            plugin.getGuiManager().openRoomListGui(player);
            return;
        }
        
        boolean isOwner = room.isOwner(player.getUniqueId());
        int slot = event.getSlot();

        // 非房主只能点击特定按钮
        if (!isOwner) {
            switch (slot) {
                case 48: // 刷新
                case 53: // 返回
                    break; // 允许点击
                default:
                    sendError("只有房主可以管理玩家角色！");
                    playErrorSound();
                    return;
            }
        }

        ItemStack item = event.getCurrentItem();
        
        if (item == null || item.getType() == Material.AIR) {
            return;
        }
        
        playClickSound();
        
        // 处理控制按钮
        switch (slot) {
            case 48: // 刷新
                refresh();
                break;
            case 49: // 返回
                close();
                plugin.getGuiManager().openRoomSettingsGui(player, room);
                break;
            default:
                // 检查是否点击了玩家头像
                if (item.getType() == Material.PLAYER_HEAD) {
                    handlePlayerClick(event, slot);
                }
                break;
        }
    }
    
    /**
     * 处理玩家点击
     */
    private void handlePlayerClick(InventoryClickEvent event, int slot) {
        ItemStack item = event.getCurrentItem();
        if (item == null || !(item.getItemMeta() instanceof SkullMeta)) {
            return;
        }
        
        SkullMeta meta = (SkullMeta) item.getItemMeta();
        if (meta.getOwningPlayer() == null) {
            return;
        }
        
        Player targetPlayer = Bukkit.getPlayer(meta.getOwningPlayer().getUniqueId());
        if (targetPlayer == null) {
            sendError("玩家不在线！");
            playErrorSound();
            return;
        }
        
        // 房主可以设置自己的角色，但不能被其他人设置
        if (room.isOwner(targetPlayer.getUniqueId()) && !targetPlayer.equals(player)) {
            sendError("不能修改房主的角色！只有房主自己可以设置。");
            playErrorSound();
            return;
        }
        
        PlayerRole newRole = null;
        String actionName = "";
        
        // 根据点击类型确定角色
        if (event.isShiftClick() && event.isLeftClick()) {
            newRole = PlayerRole.SPECTATOR;
            actionName = "观察者";
        } else if (event.isLeftClick()) {
            newRole = PlayerRole.SPEEDRUNNER;
            actionName = "速通者";
        } else if (event.isRightClick()) {
            newRole = PlayerRole.HUNTER;
            actionName = "捕猎者";
        }
        
        if (newRole != null) {
            if (room.setPlayerRole(targetPlayer.getUniqueId(), newRole)) {
                sendSuccess("已将 " + targetPlayer.getName() + " 设置为 " + actionName);
                ComponentUtil.sendMessage(targetPlayer, ComponentUtil.info("你的角色已被设置为 " + newRole.getDisplayName()));
                playSuccessSound();
                refresh(); // 刷新界面
            } else {
                sendError("设置角色失败！可能是角色数量限制。");
                playErrorSound();
            }
        }
    }
}
