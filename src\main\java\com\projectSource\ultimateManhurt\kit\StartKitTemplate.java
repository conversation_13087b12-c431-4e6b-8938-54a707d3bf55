package com.projectSource.ultimateManhurt.kit;

import com.projectSource.ultimateManhurt.game.PlayerRole;
import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.inventory.ItemStack;

import java.util.*;

/**
 * StartKit模板管理器
 * 提供预设的装备包模板
 */
public class StartKitTemplate {
    
    /**
     * 获取默认的速通者装备包
     */
    public static StartKit getDefaultSpeedrunnerKit() {
        StartKit kit = new StartKit("默认速通者装备", "适合新手的速通者起始装备");
        
        // 主手武器
        kit.setItem(0, new ItemStack(Material.STONE_SWORD));
        
        // 工具
        kit.setItem(1, new ItemStack(Material.STONE_PICKAXE));
        kit.setItem(2, new ItemStack(Material.STONE_AXE));
        kit.setItem(3, new ItemStack(Material.STONE_SHOVEL));
        
        // 食物
        kit.setItem(4, new ItemStack(Material.BREAD, 16));
        
        // 基础材料
        kit.setItem(5, new ItemStack(Material.COBBLESTONE, 64));
        kit.setItem(6, new ItemStack(Material.OAK_LOG, 32));
        
        // 额外物品
        kit.addExtraItem(new ItemStack(Material.COAL, 16));
        kit.addExtraItem(new ItemStack(Material.IRON_INGOT, 8));
        
        return kit;
    }
    
    /**
     * 获取默认的捕猎者装备包
     */
    public static StartKit getDefaultHunterKit() {
        StartKit kit = new StartKit("默认捕猎者装备", "适合新手的捕猎者起始装备");
        
        // 主手武器
        kit.setItem(0, new ItemStack(Material.IRON_SWORD));
        
        // 远程武器
        kit.setItem(1, new ItemStack(Material.BOW));
        kit.setItem(2, new ItemStack(Material.ARROW, 32));
        
        // 工具
        kit.setItem(3, new ItemStack(Material.IRON_PICKAXE));
        kit.setItem(4, new ItemStack(Material.IRON_AXE));
        
        // 食物
        kit.setItem(5, new ItemStack(Material.COOKED_BEEF, 16));
        
        // 追踪工具
        kit.setItem(6, new ItemStack(Material.COMPASS));
        
        // 基础材料
        kit.setItem(7, new ItemStack(Material.COBBLESTONE, 32));
        
        // 额外物品
        kit.addExtraItem(new ItemStack(Material.IRON_INGOT, 16));
        kit.addExtraItem(new ItemStack(Material.DIAMOND, 4));
        
        return kit;
    }
    
    /**
     * 获取专业速通者装备包
     */
    public static StartKit getProSpeedrunnerKit() {
        StartKit kit = new StartKit("专业速通者装备", "适合有经验的速通者");
        
        // 高级武器
        Map<Enchantment, Integer> swordEnchants = new HashMap<>();
        swordEnchants.put(Enchantment.SHARPNESS, 2);
        swordEnchants.put(Enchantment.UNBREAKING, 1);
        ItemStack sword = StartKit.createItem(Material.IRON_SWORD, 1, null, null, swordEnchants);
        kit.setItem(0, sword);
        
        // 高级工具
        Map<Enchantment, Integer> pickaxeEnchants = new HashMap<>();
        pickaxeEnchants.put(Enchantment.EFFICIENCY, 2);
        pickaxeEnchants.put(Enchantment.UNBREAKING, 1);
        ItemStack pickaxe = StartKit.createItem(Material.IRON_PICKAXE, 1, null, null, pickaxeEnchants);
        kit.setItem(1, pickaxe);
        
        kit.setItem(2, new ItemStack(Material.IRON_AXE));
        kit.setItem(3, new ItemStack(Material.IRON_SHOVEL));
        
        // 优质食物
        kit.setItem(4, new ItemStack(Material.GOLDEN_APPLE, 4));
        kit.setItem(5, new ItemStack(Material.COOKED_BEEF, 32));
        
        // 建筑材料
        kit.setItem(6, new ItemStack(Material.COBBLESTONE, 64));
        kit.setItem(7, new ItemStack(Material.OAK_PLANKS, 64));
        
        // 特殊物品
        kit.setItem(8, new ItemStack(Material.ENDER_PEARL, 2));
        
        // 额外物品
        kit.addExtraItem(new ItemStack(Material.IRON_INGOT, 32));
        kit.addExtraItem(new ItemStack(Material.DIAMOND, 8));
        kit.addExtraItem(new ItemStack(Material.GOLD_INGOT, 16));
        kit.addExtraItem(new ItemStack(Material.BLAZE_POWDER, 8));
        
        return kit;
    }
    
    /**
     * 获取专业捕猎者装备包
     */
    public static StartKit getProHunterKit() {
        StartKit kit = new StartKit("专业捕猎者装备", "适合有经验的捕猎者");
        
        // 高级武器
        Map<Enchantment, Integer> swordEnchants = new HashMap<>();
        swordEnchants.put(Enchantment.SHARPNESS, 3);
        swordEnchants.put(Enchantment.UNBREAKING, 2);
        ItemStack sword = StartKit.createItem(Material.DIAMOND_SWORD, 1, null, null, swordEnchants);
        kit.setItem(0, sword);
        
        // 高级弓箭
        Map<Enchantment, Integer> bowEnchants = new HashMap<>();
        bowEnchants.put(Enchantment.POWER, 2);
        bowEnchants.put(Enchantment.INFINITY, 1);
        ItemStack bow = StartKit.createItem(Material.BOW, 1, null, null, bowEnchants);
        kit.setItem(1, bow);
        kit.setItem(2, new ItemStack(Material.ARROW, 1)); // 无限弓只需要1支箭
        
        // 高级工具
        Map<Enchantment, Integer> pickaxeEnchants = new HashMap<>();
        pickaxeEnchants.put(Enchantment.EFFICIENCY, 3);
        pickaxeEnchants.put(Enchantment.UNBREAKING, 2);
        ItemStack pickaxe = StartKit.createItem(Material.DIAMOND_PICKAXE, 1, null, null, pickaxeEnchants);
        kit.setItem(3, pickaxe);
        
        kit.setItem(4, new ItemStack(Material.DIAMOND_AXE));
        
        // 优质食物
        kit.setItem(5, new ItemStack(Material.GOLDEN_APPLE, 8));
        kit.setItem(6, new ItemStack(Material.COOKED_BEEF, 64));
        
        // 追踪工具
        kit.setItem(7, new ItemStack(Material.COMPASS));
        kit.setItem(8, new ItemStack(Material.CLOCK));
        
        // 额外物品
        kit.addExtraItem(new ItemStack(Material.DIAMOND, 32));
        kit.addExtraItem(new ItemStack(Material.IRON_INGOT, 64));
        kit.addExtraItem(new ItemStack(Material.GOLD_INGOT, 32));
        kit.addExtraItem(new ItemStack(Material.ENDER_PEARL, 8));
        kit.addExtraItem(new ItemStack(Material.BLAZE_ROD, 4));
        
        return kit;
    }
    
    /**
     * 获取空装备包
     */
    public static StartKit getEmptyKit() {
        return new StartKit("空装备包", "不提供任何起始装备");
    }
    
    /**
     * 获取基础装备包
     */
    public static StartKit getBasicKit() {
        StartKit kit = new StartKit("基础装备包", "最基本的生存装备");
        
        // 基础工具
        kit.setItem(0, new ItemStack(Material.WOODEN_SWORD));
        kit.setItem(1, new ItemStack(Material.WOODEN_PICKAXE));
        kit.setItem(2, new ItemStack(Material.WOODEN_AXE));
        
        // 基础食物
        kit.setItem(3, new ItemStack(Material.APPLE, 8));
        
        // 基础材料
        kit.setItem(4, new ItemStack(Material.OAK_LOG, 16));
        
        return kit;
    }
    
    /**
     * 获取所有预设模板
     */
    public static Map<String, StartKit> getAllTemplates() {
        Map<String, StartKit> templates = new LinkedHashMap<>();
        
        templates.put("empty", getEmptyKit());
        templates.put("basic", getBasicKit());
        templates.put("default_speedrunner", getDefaultSpeedrunnerKit());
        templates.put("default_hunter", getDefaultHunterKit());
        templates.put("pro_speedrunner", getProSpeedrunnerKit());
        templates.put("pro_hunter", getProHunterKit());
        
        return templates;
    }
    
    /**
     * 根据角色获取推荐模板
     */
    public static List<StartKit> getRecommendedTemplates(PlayerRole role) {
        List<StartKit> templates = new ArrayList<>();
        
        templates.add(getEmptyKit());
        templates.add(getBasicKit());
        
        if (role == PlayerRole.SPEEDRUNNER) {
            templates.add(getDefaultSpeedrunnerKit());
            templates.add(getProSpeedrunnerKit());
        } else if (role == PlayerRole.HUNTER) {
            templates.add(getDefaultHunterKit());
            templates.add(getProHunterKit());
        }
        
        return templates;
    }
    
    /**
     * 获取模板的显示名称
     */
    public static String getTemplateDisplayName(String templateId) {
        switch (templateId) {
            case "empty": return "空装备包";
            case "basic": return "基础装备包";
            case "default_speedrunner": return "默认速通者装备";
            case "default_hunter": return "默认捕猎者装备";
            case "pro_speedrunner": return "专业速通者装备";
            case "pro_hunter": return "专业捕猎者装备";
            default: return "未知模板";
        }
    }
}
