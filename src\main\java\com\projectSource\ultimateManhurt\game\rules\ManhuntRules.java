package com.projectSource.ultimateManhurt.game.rules;

import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.game.VictoryMode;
import com.projectSource.ultimateManhurt.room.RoomSettings;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import com.projectSource.ultimateManhurt.util.CompassUtil;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.entity.Player;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.player.PlayerRespawnEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.BookMeta;
import org.bukkit.persistence.PersistentDataType;

import java.util.*;

/**
 * Manhunt游戏规则
 * 定义游戏的核心规则和逻辑
 */
public class ManhuntRules {
    
    private final GameSession gameSession;
    private final RoomSettings settings;
    
    public ManhuntRules(GameSession gameSession) {
        this.gameSession = gameSession;
        this.settings = gameSession.getRoom().getSettings();
    }
    
    /**
     * 检查玩家是否可以对另一个玩家造成伤害
     */
    public boolean canDamagePlayer(Player attacker, Player victim) {
        if (!settings.isPvpEnabled()) {
            return false;
        }

        UUID attackerId = attacker.getUniqueId();
        UUID victimId = victim.getUniqueId();

        PlayerRole attackerRole = gameSession.getPlayerRole(attackerId);
        PlayerRole victimRole = gameSession.getPlayerRole(victimId);

        if (attackerRole == null || victimRole == null) {
            return false;
        }

        // 观察者不能攻击也不能被攻击
        if (attackerRole == PlayerRole.SPECTATOR || victimRole == PlayerRole.SPECTATOR) {
            return false;
        }

        // 检查友军伤害
        if (attackerRole == victimRole && !settings.isFriendlyFire()) {
            return false;
        }

        // 检查豁免设置：hunter不能在开局豁免时间内伤害speedrunner
        if (attackerRole == PlayerRole.HUNTER && victimRole == PlayerRole.SPEEDRUNNER) {
            if (gameSession.getImmunityManager().isImmunityActive()) {
                // 给攻击者发送提示消息
                long remainingTime = gameSession.getImmunityManager().getRemainingImmunityTime();
                com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(attacker,
                    com.projectSource.ultimateManhurt.util.ComponentUtil.warning(
                        "速通者还在豁免期内，无法造成伤害！剩余时间: " + remainingTime + "秒"));
                return false;
            }
        }

        return true;
    }
    
    /**
     * 处理玩家伤害事件
     */
    public void handlePlayerDamage(EntityDamageByEntityEvent event) {
        if (!(event.getEntity() instanceof Player victim)) {
            return;
        }
        
        if (!(event.getDamager() instanceof Player attacker)) {
            return;
        }
        
        if (!canDamagePlayer(attacker, victim)) {
            event.setCancelled(true);
            return;
        }
        
        // 记录伤害统计
        gameSession.recordDamage(attacker.getUniqueId(), victim.getUniqueId(), event.getFinalDamage());
    }
    
    /**
     * 处理玩家死亡事件
     */
    public void handlePlayerDeath(PlayerDeathEvent event) {
        Player player = event.getEntity();
        UUID playerId = player.getUniqueId();
        PlayerRole role = gameSession.getPlayerRole(playerId);
        
        if (role == null) {
            return;
        }
        
        // 记录死亡
        gameSession.recordDeath(playerId);
        
        // 处理物品掉落
        if (settings.isKeepInventory()) {
            event.setKeepInventory(true);
            event.getDrops().clear();
        } else {
            // 即使不保留背包，也要保护特殊物品不掉落
            protectSpecialItems(event, role);
        }
        
        // 检查游戏结束条件
        checkGameEndConditions(role, playerId);
        
        // 设置重生位置
        setRespawnLocation(player, role);
    }

    /**
     * 保护特殊物品不掉落
     * 速通者的里程碑指南书和猎人的指南针在死亡时不会掉落
     */
    private void protectSpecialItems(PlayerDeathEvent event, PlayerRole role) {
        Player player = event.getEntity();
        List<ItemStack> drops = event.getDrops();
        List<ItemStack> protectedItems = new ArrayList<>();

        // 遍历掉落物品，找出需要保护的特殊物品
        Iterator<ItemStack> iterator = drops.iterator();
        while (iterator.hasNext()) {
            ItemStack item = iterator.next();

            if (shouldProtectItem(item, role)) {
                protectedItems.add(item.clone());
                iterator.remove(); // 从掉落列表中移除
            }
        }

        // 将保护的物品重新添加到玩家背包
        if (!protectedItems.isEmpty()) {
            // 延迟添加，确保玩家重生后再给予物品
            org.bukkit.Bukkit.getScheduler().runTaskLater(gameSession.getPlugin(), () -> {
                if (player.isOnline()) {
                    for (ItemStack protectedItem : protectedItems) {
                        // 尝试添加到背包，如果背包满了会掉落到地上
                        player.getInventory().addItem(protectedItem);
                    }

                    // 通知玩家
                    if (protectedItems.size() > 0) {
                        ComponentUtil.sendMessage(player, ComponentUtil.info(
                            "🛡️ 特殊物品已保护：" + protectedItems.size() + "个物品未掉落"));
                    }
                }
            }, 20L); // 1秒后执行
        }
    }

    /**
     * 判断物品是否应该被保护
     */
    private boolean shouldProtectItem(ItemStack item, PlayerRole role) {
        if (item == null || item.getType() == Material.AIR) {
            return false;
        }

        // 使用通用的保护物品检测
        if (CompassUtil.isProtectedItem(item)) {
            String itemType = CompassUtil.getProtectedItemType(item);

            // 速通者的里程碑指南书
            if (role == PlayerRole.SPEEDRUNNER && "speedrunner_guide_book".equals(itemType)) {
                return true;
            }

            // 猎人的指南针
            if (role == PlayerRole.HUNTER && "hunter_compass".equals(itemType)) {
                return true;
            }
        }

        // 兼容性检查：对于没有特殊标识的旧物品
        if (role == PlayerRole.SPEEDRUNNER && isLegacySpeedrunnerGuideBook(item)) {
            return true;
        }

        if (role == PlayerRole.HUNTER && isLegacyHunterCompass(item)) {
            return true;
        }

        return false;
    }

    /**
     * 判断是否为旧版速通者的里程碑指南书（兼容性）
     */
    private boolean isLegacySpeedrunnerGuideBook(ItemStack item) {
        if (item.getType() != Material.WRITTEN_BOOK) {
            return false;
        }

        BookMeta meta = (BookMeta) item.getItemMeta();
        if (meta == null) {
            return false;
        }

        // 检查书籍标题和作者
        return "里程碑指南".equals(meta.getTitle()) &&
               "UltimateManhurt".equals(meta.getAuthor());
    }

    /**
     * 判断是否为旧版猎人的指南针（兼容性）
     */
    private boolean isLegacyHunterCompass(ItemStack item) {
        // 对于没有特殊标识的普通指南针，也进行保护
        // 这确保了向后兼容性
        return item.getType() == Material.COMPASS;
    }
    
    /**
     * 处理玩家重生事件
     */
    public void handlePlayerRespawn(PlayerRespawnEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();
        PlayerRole role = gameSession.getPlayerRole(playerId);

        if (role == null) {
            return;
        }

        // 检查是否允许重生
        boolean canRespawn = true;
        if (role == PlayerRole.SPEEDRUNNER && !settings.isAllowSpeedrunnerRespawn()) {
            canRespawn = false;
        } else if (role == PlayerRole.HUNTER && !settings.isAllowHunterRespawn()) {
            canRespawn = false;
        }

        // 检查生命数限制
        if (canRespawn) {
            int lives = (role == PlayerRole.SPEEDRUNNER) ? settings.getSpeedrunnerLives() : settings.getHunterLives();
            if (lives > 0) {
                int deaths = gameSession.getPlayerDeaths(playerId);
                if (deaths >= lives) {
                    canRespawn = false;
                }
            }
        }

        if (!canRespawn) {
            // 不允许重生，设置为观察者模式
            org.bukkit.Bukkit.getScheduler().runTask(com.projectSource.ultimateManhurt.UltimateManhurt.getInstance(), () -> {
                player.setGameMode(org.bukkit.GameMode.SPECTATOR);
                com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(player,
                    com.projectSource.ultimateManhurt.util.ComponentUtil.error("你的生命已耗尽，现在是观察者模式"));
            });
            event.setRespawnLocation(gameSession.getGameWorld().getSpectatorSpawn());
            return;
        }

        // 根据角色设置重生位置
        switch (role) {
            case SPEEDRUNNER:
                event.setRespawnLocation(gameSession.getGameWorld().getSpeedrunnerSpawn());
                break;
            case HUNTER:
                event.setRespawnLocation(gameSession.getGameWorld().getHunterSpawn());
                break;
            case SPECTATOR:
                event.setRespawnLocation(gameSession.getGameWorld().getSpectatorSpawn());
                break;
        }

        // 恢复玩家存活状态（延迟执行，确保重生完成）
        // 只有在允许重生的情况下才恢复存活状态
        if (canRespawn) {
            org.bukkit.Bukkit.getScheduler().runTaskLater(com.projectSource.ultimateManhurt.UltimateManhurt.getInstance(), () -> {
                gameSession.revivePlayer(playerId);
                com.projectSource.ultimateManhurt.UltimateManhurt.getInstance().getLogger().info(
                    "玩家 " + player.getName() + " 重生，恢复存活状态");
            }, 1L); // 1 tick后执行
        }

        // 显示剩余生命数
        if (role != PlayerRole.SPECTATOR) {
            int lives = (role == PlayerRole.SPEEDRUNNER) ? settings.getSpeedrunnerLives() : settings.getHunterLives();
            if (lives > 0) {
                int deaths = gameSession.getPlayerDeaths(playerId);
                int remainingLives = lives - deaths;
                if (remainingLives > 0) {
                    com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(player,
                        com.projectSource.ultimateManhurt.util.ComponentUtil.warning("剩余生命: " + remainingLives));
                }
            }
        }
    }
    
    /**
     * 检查游戏结束条件
     */
    private void checkGameEndConditions(PlayerRole deadPlayerRole, UUID deadPlayerId) {
        // 添加调试日志
        org.bukkit.entity.Player deadPlayer = org.bukkit.Bukkit.getPlayer(deadPlayerId);
        String playerName = deadPlayer != null ? deadPlayer.getName() : "Unknown";

        com.projectSource.ultimateManhurt.UltimateManhurt.getInstance().getLogger().info(
            "检查游戏结束条件 - 死亡玩家: " + playerName + " (" + deadPlayerRole + ")" +
            ", speedrunnerLives: " + settings.getSpeedrunnerLives() +
            ", hunterLives: " + settings.getHunterLives()
        );

        if (deadPlayerRole == PlayerRole.SPEEDRUNNER) {
            // 统一使用生命数系统
            if (settings.getSpeedrunnerLives() > 0) {
                com.projectSource.ultimateManhurt.UltimateManhurt.getInstance().getLogger().info("速通者生命数: " + settings.getSpeedrunnerLives());

                // 使用生命数系统
                int currentDeaths = gameSession.getPlayerDeaths(deadPlayerId);
                com.projectSource.ultimateManhurt.UltimateManhurt.getInstance().getLogger().info("玩家 " + deadPlayerId + " 当前死亡次数: " + currentDeaths);

                if (currentDeaths >= settings.getSpeedrunnerLives()) {
                    // 该速通者生命耗尽，检查是否还有其他活着的速通者
                    boolean hasAliveSpeedrunners = gameSession.getPlayersByRole(PlayerRole.SPEEDRUNNER)
                            .stream()
                            .anyMatch(playerId -> {
                                if (playerId.equals(deadPlayerId)) return false; // 当前死亡的玩家
                                int playerDeaths = gameSession.getPlayerDeaths(playerId);
                                boolean isAlive = gameSession.isPlayerAlive(playerId) && playerDeaths < settings.getSpeedrunnerLives();
                                com.projectSource.ultimateManhurt.UltimateManhurt.getInstance().getLogger().info("检查玩家 " + playerId + " - 死亡次数: " + playerDeaths + ", 是否存活: " + isAlive);
                                return isAlive;
                            });

                    com.projectSource.ultimateManhurt.UltimateManhurt.getInstance().getLogger().info("是否还有存活的速通者: " + hasAliveSpeedrunners);

                    if (!hasAliveSpeedrunners) {
                        com.projectSource.ultimateManhurt.UltimateManhurt.getInstance().getLogger().info("所有速通者生命耗尽，游戏结束");
                        gameSession.endGame(WinCondition.HUNTER_KILL_SPEEDRUNNER);
                    } else {
                        com.projectSource.ultimateManhurt.UltimateManhurt.getInstance().getLogger().info("还有速通者存活，游戏继续");
                    }
                } else {
                    com.projectSource.ultimateManhurt.UltimateManhurt.getInstance().getLogger().info("玩家还有生命，游戏继续");
                }
            } else {
                com.projectSource.ultimateManhurt.UltimateManhurt.getInstance().getLogger().info("速通者有无限生命，不会因死亡结束游戏");
            }
        }

        // 检查捕猎者死亡（统一使用生命数系统）
        if (deadPlayerRole == PlayerRole.HUNTER) {
            if (settings.getHunterLives() > 0) {
                // 使用生命数系统
                int currentDeaths = gameSession.getPlayerDeaths(deadPlayerId);
                if (currentDeaths >= settings.getHunterLives()) {
                    // 该捕猎者生命耗尽，检查是否还有其他活着的捕猎者
                    boolean hasAliveHunters = gameSession.getPlayersByRole(PlayerRole.HUNTER)
                            .stream()
                            .anyMatch(playerId -> {
                                if (playerId.equals(deadPlayerId)) return false; // 当前死亡的玩家
                                return gameSession.isPlayerAlive(playerId) &&
                                       gameSession.getPlayerDeaths(playerId) < settings.getHunterLives();
                            });

                    if (!hasAliveHunters) {
                        gameSession.endGame(WinCondition.SPEEDRUNNER_SURVIVE);
                    }
                }
            }
            // 如果 hunterLives = 0（无限生命），则捕猎者死亡不会结束游戏
        }
    }
    
    /**
     * 设置重生位置
     */
    private void setRespawnLocation(Player player, PlayerRole role) {
        // 重生位置在handlePlayerRespawn中设置
        // 这里可以添加额外的重生逻辑
    }
    
    /**
     * 检查末影龙死亡
     */
    public void handleDragonDeath() {
        // 速通者击败末影龙，游戏结束
        gameSession.endGame(WinCondition.SPEEDRUNNER_KILL_DRAGON);
    }

    /**
     * 检查守卫凋零死亡
     */
    public void handleWitherDeath() {
        // 捕猎者击败守卫凋零，游戏结束
        gameSession.endGame(WinCondition.HUNTER_KILL_WITHER);
    }

    /**
     * 检查时间到
     */
    public void handleTimeUp() {
        // 根据胜利模式判断时间耗尽的胜负
        VictoryMode victoryMode = settings.getVictoryMode();

        // 记录日志用于调试
        gameSession.getPlugin().getLogger().info("游戏时间耗尽，当前胜利模式: " + victoryMode.getDisplayName());

        switch (victoryMode) {
            case LIFE_MODE:
                // 生命模式：时间耗尽速通者获胜（成功存活）
                gameSession.getPlugin().getLogger().info("生命模式时间耗尽 → 速通者获胜（成功存活）");
                gameSession.endGame(WinCondition.SPEEDRUNNER_SURVIVE);
                break;

            case DRAGON_MODE:
                // 末影龙模式：时间耗尽捕猎者获胜（速通者未能击败末影龙）
                gameSession.getPlugin().getLogger().info("末影龙模式时间耗尽 → 捕猎者获胜（速通者未能击败末影龙）");
                gameSession.endGame(WinCondition.HUNTER_TIMEOUT);
                break;

            case SCORE_MODE:
                // 积分模式：时间耗尽捕猎者获胜（速通者未能达到目标积分）
                gameSession.getPlugin().getLogger().info("积分模式时间耗尽 → 捕猎者获胜（速通者未能达到目标积分）");
                gameSession.endGame(WinCondition.HUNTER_TIMEOUT);
                break;

            case HYBRID_MODE:
                // 混合模式：时间耗尽捕猎者获胜（速通者未能完成任何胜利条件）
                gameSession.getPlugin().getLogger().info("混合模式时间耗尽 → 捕猎者获胜（速通者未能完成任何胜利条件）");
                gameSession.endGame(WinCondition.HUNTER_TIMEOUT);
                break;

            case GUARD_MODE:
                // 守卫模式：时间耗尽速通者获胜（成功守卫凋零）
                gameSession.getPlugin().getLogger().info("守卫模式时间耗尽 → 速通者获胜（成功守卫凋零）");
                gameSession.endGame(WinCondition.SPEEDRUNNER_GUARD_SUCCESS);
                break;

            default:
                // 兜底逻辑：使用设置中的配置
                gameSession.getPlugin().getLogger().info("使用兜底逻辑，timeoutHuntersWin: " + settings.isTimeoutHuntersWin());
                if (settings.isTimeoutHuntersWin()) {
                    gameSession.endGame(WinCondition.HUNTER_TIMEOUT);
                } else {
                    gameSession.endGame(WinCondition.SPEEDRUNNER_SURVIVE);
                }
                break;
        }
    }
    
    /**
     * 检查玩家是否可以使用末影珍珠
     */
    public boolean canUseEnderPearl(Player player) {
        if (!settings.isEnderPearlCooldown()) {
            return true;
        }
        
        UUID playerId = player.getUniqueId();
        long lastUse = gameSession.getLastEnderPearlUse(playerId);
        long cooldown = settings.getEnderPearlCooldownSeconds() * 1000L;
        
        return System.currentTimeMillis() - lastUse >= cooldown;
    }
    
    /**
     * 记录末影珍珠使用
     */
    public void recordEnderPearlUse(Player player) {
        UUID playerId = player.getUniqueId();
        gameSession.setLastEnderPearlUse(playerId, System.currentTimeMillis());
    }
    
    /**
     * 检查玩家是否可以进入传送门
     */
    public boolean canEnterPortal(Player player) {
        if (!settings.isNetherPortalDelay()) {
            return true;
        }
        
        UUID playerId = player.getUniqueId();
        long lastUse = gameSession.getLastPortalUse(playerId);
        long delay = settings.getNetherPortalDelaySeconds() * 1000L;
        
        return System.currentTimeMillis() - lastUse >= delay;
    }
    
    /**
     * 记录传送门使用
     */
    public void recordPortalUse(Player player) {
        UUID playerId = player.getUniqueId();
        gameSession.setLastPortalUse(playerId, System.currentTimeMillis());
    }
    
    /**
     * 检查指南针追踪
     */
    public boolean isCompassTrackingEnabled() {
        return settings.isCompassTracking();
    }
    
    /**
     * 获取指南针更新间隔
     */
    public int getCompassUpdateInterval() {
        return settings.getCompassUpdateInterval();
    }
    
    /**
     * 检查是否应该显示死亡消息
     */
    public boolean shouldShowDeathMessage(Player player) {
        return settings.isShowDeathMessages();
    }
    
    /**
     * 检查是否启用自然回血
     */
    public boolean isNaturalRegenerationEnabled() {
        return settings.isNaturalRegeneration();
    }
    
    /**
     * 获取游戏难度
     */
    public org.bukkit.Difficulty getDifficulty() {
        return settings.getDifficulty();
    }
    
    /**
     * 检查是否允许末影龙重生
     */
    public boolean isDragonRespawnAllowed() {
        return settings.isDragonRespawn();
    }
}
