package com.projectSource.ultimateManhurt.ranking;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.data.PlayerData;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import net.kyori.adventure.text.Component;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.scoreboard.Scoreboard;
import org.bukkit.scoreboard.Team;

import java.util.UUID;

/**
 * 段位显示管理器
 * 负责在游戏中显示玩家段位
 */
public class RankDisplayManager implements Listener {
    
    private final UltimateManhurt plugin;
    
    public RankDisplayManager(UltimateManhurt plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }
    
    /**
     * 玩家加入时设置段位显示
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();

        // 延迟1tick执行，确保PlayerListener先处理完数据加载
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            if (player.isOnline()) {
                updatePlayerRankDisplay(player);
            }
        }, 1L);
    }
    
    /**
     * 更新玩家段位显示
     */
    public void updatePlayerRankDisplay(Player player) {
        PlayerData playerData = plugin.getDataManager().getPlayerData(player.getUniqueId());
        if (playerData == null) return;
        
        int elo = EloSystem.getPlayerElo(playerData);
        EloSystem.Rank rank = EloSystem.getRank(elo);
        
        // 更新玩家显示名称
        updatePlayerDisplayName(player, rank);
        
        // 更新Tab列表显示
        updateTabListDisplay(player, rank, elo);
        
        // 更新计分板团队（用于名称前缀）
        updateScoreboardTeam(player, rank);
    }
    
    /**
     * 更新玩家显示名称
     */
    private void updatePlayerDisplayName(Player player, EloSystem.Rank rank) {
        Component displayName = ComponentUtil.parse(
            rank.getIcon() + " " + rank.getColor() + player.getName()
        );
        player.displayName(displayName);
    }
    
    /**
     * 更新Tab列表显示
     */
    private void updateTabListDisplay(Player player, EloSystem.Rank rank, int elo) {
        Component tabName = ComponentUtil.parse(
            rank.getIcon() + " " + rank.getColor() + player.getName() + " <gray>(" + elo + ")"
        );
        player.playerListName(tabName);
    }
    
    /**
     * 更新计分板团队（用于聊天前缀）
     */
    private void updateScoreboardTeam(Player player, EloSystem.Rank rank) {
        Scoreboard scoreboard = plugin.getServer().getScoreboardManager().getMainScoreboard();
        
        // 获取或创建段位团队
        String teamName = "rank_" + rank.name().toLowerCase();
        Team team = scoreboard.getTeam(teamName);
        
        if (team == null) {
            team = scoreboard.registerNewTeam(teamName);
            // 设置团队前缀
            team.prefix(ComponentUtil.parse(rank.getIcon() + " " + rank.getColor()));
            // 设置团队后缀（显示段位名称）
            team.suffix(ComponentUtil.parse(" <gray>[" + rank.getDisplayName() + "]"));
        }
        
        // 将玩家添加到对应段位团队
        team.addEntry(player.getName());
    }
    
    /**
     * 获取玩家段位显示文本
     */
    public String getPlayerRankText(UUID playerId) {
        PlayerData playerData = plugin.getDataManager().getPlayerData(playerId);
        if (playerData == null) return "";
        
        int elo = EloSystem.getPlayerElo(playerData);
        EloSystem.Rank rank = EloSystem.getRank(elo);
        
        return rank.getIcon() + " " + rank.getColoredName() + " (" + elo + ")";
    }
    
    /**
     * 获取玩家段位组件
     */
    public Component getPlayerRankComponent(UUID playerId) {
        PlayerData playerData = plugin.getDataManager().getPlayerData(playerId);
        if (playerData == null) {
            return ComponentUtil.parse("<gray>未排名");
        }
        
        int elo = EloSystem.getPlayerElo(playerData);
        EloSystem.Rank rank = EloSystem.getRank(elo);
        
        return ComponentUtil.parse(rank.getIcon() + " " + rank.getColoredName() + " <gray>(" + elo + ")");
    }
    
    /**
     * 在聊天中显示段位
     */
    public Component formatChatMessage(Player player, String message) {
        PlayerData playerData = plugin.getDataManager().getPlayerData(player.getUniqueId());
        if (playerData == null) {
            return ComponentUtil.parse("<gray>" + player.getName() + ": <white>" + message);
        }
        
        int elo = EloSystem.getPlayerElo(playerData);
        EloSystem.Rank rank = EloSystem.getRank(elo);
        
        return ComponentUtil.parse(
            rank.getIcon() + " " + rank.getColor() + player.getName() + 
            " <gray>[" + rank.getDisplayName() + "]: <white>" + message
        );
    }
    
    /**
     * 清理玩家的团队信息
     */
    public void cleanupPlayerTeam(Player player) {
        Scoreboard scoreboard = plugin.getServer().getScoreboardManager().getMainScoreboard();
        
        // 从所有段位团队中移除玩家
        for (EloSystem.Rank rank : EloSystem.Rank.values()) {
            String teamName = "rank_" + rank.name().toLowerCase();
            Team team = scoreboard.getTeam(teamName);
            if (team != null) {
                team.removeEntry(player.getName());
            }
        }
    }
    
    /**
     * 批量更新所有在线玩家的段位显示
     */
    public void updateAllPlayersRankDisplay() {
        for (Player player : plugin.getServer().getOnlinePlayers()) {
            updatePlayerRankDisplay(player);
        }
    }
}
