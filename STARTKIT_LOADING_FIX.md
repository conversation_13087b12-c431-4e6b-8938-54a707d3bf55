# StartKit装备包加载修复

## 问题描述

在加载房间设置配置文件时，出现了以下错误：

```
[WARN]: [UltimateManhurt] 加载装备包物品失败: class org.bukkit.configuration.MemorySection cannot be cast to class java.util.Map
```

## 问题原因

在`RoomSettingsManager.loadStartKit()`方法中，当从YAML配置文件读取装备包物品数据时，Bukkit的配置系统返回的是`MemorySection`对象，而不是`Map<String, Object>`对象。代码尝试直接将`MemorySection`强制转换为`Map`，导致了`ClassCastException`。

## 修复方案

### 1. 修复主要物品加载

**原代码问题**:
```java
Map<String, Object> itemsMap = config.getConfigurationSection(path + ".items").getValues(false);
for (Map.Entry<String, Object> entry : itemsMap.entrySet()) {
    @SuppressWarnings("unchecked")
    Map<String, Object> itemData = (Map<String, Object>) entry.getValue(); // 这里会出错
    ItemStack item = ItemStack.deserialize(itemData);
}
```

**修复后代码**:
```java
var itemsSection = config.getConfigurationSection(path + ".items");
if (itemsSection != null) {
    for (String slotKey : itemsSection.getKeys(false)) {
        try {
            int slot = Integer.parseInt(slotKey);
            var itemSection = itemsSection.getConfigurationSection(slotKey);
            if (itemSection != null) {
                Map<String, Object> itemData = itemSection.getValues(true);
                ItemStack item = ItemStack.deserialize(itemData);
                kit.setItem(slot, item);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("加载装备包物品失败: " + e.getMessage());
        }
    }
}
```

### 2. 修复额外物品加载

**原代码问题**:
```java
for (Object itemObj : extraItemsList) {
    @SuppressWarnings("unchecked")
    Map<String, Object> itemData = (Map<String, Object>) itemObj; // 这里会出错
    ItemStack item = ItemStack.deserialize(itemData);
}
```

**修复后代码**:
```java
for (Object itemObj : extraItemsList) {
    try {
        Map<String, Object> itemData;
        if (itemObj instanceof MemorySection) {
            // 如果是MemorySection，获取其值
            itemData = ((MemorySection) itemObj).getValues(true);
        } else {
            // 否则尝试直接转换
            @SuppressWarnings("unchecked")
            Map<String, Object> castedData = (Map<String, Object>) itemObj;
            itemData = castedData;
        }
        ItemStack item = ItemStack.deserialize(itemData);
        kit.addExtraItem(item);
    } catch (Exception e) {
        plugin.getLogger().warning("加载装备包额外物品失败: " + e.getMessage());
    }
}
```

## 技术细节

### MemorySection vs Map

- **MemorySection**: Bukkit配置系统中用于表示配置节的类
- **Map**: Java标准的键值对集合接口
- **问题**: 两者不兼容，不能直接强制转换

### 解决方法

1. **使用getConfigurationSection()**: 获取配置节对象
2. **使用getValues(true)**: 将配置节转换为Map对象
3. **类型检查**: 在转换前检查对象类型

### 添加的导入

```java
import org.bukkit.configuration.MemorySection;
```

## 修复文件

- **文件**: `src/main/java/com/projectSource/ultimateManhurt/room/RoomSettingsManager.java`
- **方法**: `loadStartKit(YamlConfiguration config, String path)`
- **行数**: 456-498行

## 测试验证

创建了测试配置文件 `test-room-settings.yml` 来验证修复效果，包含：
- 速通者装备包（3个主要物品 + 2个额外物品）
- 捕猎者装备包（3个主要物品 + 2个额外物品）

## 预期结果

修复后，加载配置文件时应该：
1. ✅ 不再出现ClassCastException错误
2. ✅ 成功加载装备包中的所有物品
3. ✅ 正确设置物品到指定槽位
4. ✅ 正确添加额外物品到装备包

## 兼容性

- ✅ 向后兼容现有配置文件
- ✅ 支持新的配置文件格式
- ✅ 不影响其他功能

## 相关功能

此修复影响以下功能：
- 房间设置配置文件加载
- 装备包系统
- 配置文件管理GUI
- 配置文件预览功能
