# 莱娜闪避Map监控系统

## 🔍 **问题进展**

从最新的日志中，我确认了关键信息：

### 确认的事实
```
22:07:11 - 设置效果标记: 过期时间: 1753711636957, 当前标记数量: 1
22:07:11 - 验证设置结果: 验证过期时间: 1753711636957, 设置成功: true
22:07:12 - 检查前状态: 当前标记总数: 0, 标记内容: []
```

**关键发现**：
1. ✅ 标记确实被正确设置了（`设置成功: true`）
2. ❌ 但在1秒内就完全消失了（从1个变成0个）
3. 🔍 没有看到任何"停止光环"或"清理标记"的日志

这说明有**隐藏的清理操作**在我们不知道的地方发生！

## 🔧 **新的监控系统**

我创建了一个监控包装类来捕获所有对`forestBlessingEffectPlayers`的操作：

### Map操作监控
```java
private final Map<UUID, Long> forestBlessingEffectPlayers = new ConcurrentHashMap<UUID, Long>() {
    @Override
    public Long put(UUID key, Long value) {
        // 记录PUT操作和调用栈
        plugin.getLogger().info("[森之祝福-MAP] PUT操作 - 玩家: " + playerName + 
            ", 值: " + value + 
            ", 调用栈: " + Thread.currentThread().getStackTrace()[2].getMethodName());
        return super.put(key, value);
    }
    
    @Override
    public Long remove(Object key) {
        // 记录REMOVE操作和调用栈
        Long removed = super.remove(key);
        plugin.getLogger().info("[森之祝福-MAP] REMOVE操作 - 玩家: " + playerName + 
            ", 移除的值: " + removed + 
            ", 调用栈: " + Thread.currentThread().getStackTrace()[2].getMethodName());
        return removed;
    }
    
    @Override
    public void clear() {
        // 记录CLEAR操作和调用栈
        plugin.getLogger().info("[森之祝福-MAP] CLEAR操作 - 清理前大小: " + size() + 
            ", 调用栈: " + Thread.currentThread().getStackTrace()[2].getMethodName());
        super.clear();
    }
};
```

## 📊 **新的日志格式**

### 正常操作会显示：
```
[森之祝福-MAP] PUT操作 - 玩家: Faith_bian, 值: 1753711636957, 调用栈: setForestBlessingEffect
[森之祝福-MAP] REMOVE操作 - 玩家: Faith_bian, 移除的值: 1753711636957, 调用栈: hasForestBlessingEffect
```

### 如果有意外清理会显示：
```
[森之祝福-MAP] CLEAR操作 - 清理前大小: 1, 调用栈: someUnknownMethod
```
或
```
[森之祝福-MAP] REMOVE操作 - 玩家: Faith_bian, 移除的值: 1753711636957, 调用栈: someUnknownMethod
```

## 🎯 **监控目标**

这个系统将帮助我们发现：

### 1. 谁在清理Map
- 通过调用栈信息，我们能知道是哪个方法在操作Map
- 可能的嫌疑方法：
  - `clearPlayerStates`
  - `removePlayerProfession`
  - `endGame`
  - `shutdown`
  - 其他未知的清理方法

### 2. 什么时候清理
- 精确的时间戳
- 与设置操作的时间间隔

### 3. 清理的内容
- 是单个玩家的标记被移除，还是整个Map被清空
- 被移除的具体值

## 🔍 **预期发现**

基于当前的症状，我预期会看到以下之一：

### 情况1：单个移除
```
[森之祝福-MAP] PUT操作 - 玩家: Faith_bian, 值: XXX, 调用栈: setForestBlessingEffect
[森之祝福-MAP] REMOVE操作 - 玩家: Faith_bian, 移除的值: XXX, 调用栈: [某个未知方法]
```

### 情况2：整体清空
```
[森之祝福-MAP] PUT操作 - 玩家: Faith_bian, 值: XXX, 调用栈: setForestBlessingEffect
[森之祝福-MAP] CLEAR操作 - 清理前大小: 1, 调用栈: [某个未知方法]
```

## 📋 **测试步骤**

### 1. 重新测试
请重新进行相同的测试，现在我们应该能看到：
- 每次PUT操作的详细信息
- 任何REMOVE或CLEAR操作的调用栈
- 精确的操作时机

### 2. 关注新日志
特别关注带有`[森之祝福-MAP]`前缀的日志，这些将告诉我们：
- 谁在操作Map
- 什么时候操作
- 操作的具体内容

### 3. 分析调用栈
调用栈信息将帮助我们定位到具体的问题代码位置

## 🎉 **解决方案预期**

一旦我们找到了清理Map的罪魁祸首，我们就可以：

### 1. 修复清理逻辑
- 如果是意外清理，我们可以移除或修改清理逻辑
- 如果是必要清理，我们可以添加保护机制

### 2. 优化时序
- 确保清理操作不会影响正在使用的效果标记
- 可能需要延迟清理或添加引用计数

### 3. 增强稳定性
- 添加防护机制，避免类似问题再次发生
- 优化效果标记的生命周期管理

## 🚨 **重要提醒**

这次测试非常关键，因为监控系统将直接告诉我们问题的根源。请：

1. **完整记录**：提供所有带有`[森之祝福-MAP]`前缀的日志
2. **注意时序**：观察操作的时间顺序
3. **分析调用栈**：调用栈信息是解决问题的关键

现在我们终于有了追踪问题的利器！🔍🎯✨
