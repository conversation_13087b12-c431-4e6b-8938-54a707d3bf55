#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff8ceb3b840, pid=25304, tid=30220
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# Problematic frame:
# V  [jvm.dll+0x3bb840]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://github.com/adoptium/adoptium-support/issues
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\d7c25a54a6ee316104bfe6acdb8443cd\redhat.java\ss_ws --pipe=\\.\pipe\lsp-ddb019f1dafdbb5104923aafe477f265-sock

Host: Genuine Intel(R) 0000, 32 cores, 31G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4768)
Time: Sun Jul 27 10:52:42 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4768) elapsed time: 1.311705 seconds (0d 0h 0m 1s)

---------------  T H R E A D  ---------------

Current thread (0x0000016c75b92fe0):  JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=30220, stack(0x0000006a44e00000,0x0000006a44f00000) (1024K)]


Current CompileTask:
C2:1311 2909       4       org.lombokweb.asm.ClassReader::readMethod (1070 bytes)

Stack: [0x0000006a44e00000,0x0000006a44f00000],  sp=0x0000006a44efce50,  free space=1011k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x3bb840]
V  [jvm.dll+0x3b4cd8]
V  [jvm.dll+0x1e0070]
V  [jvm.dll+0x247c42]
V  [jvm.dll+0x2470cf]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


siginfo: EXCEPTION_ACCESS_VIOLATION (0xc0000005), reading address 0xffffffffffffffff


Registers:
RAX=0x0000000000000000, RBX=0x000000000000002d, RCX=0x0000000000000000, RDX=0x00107ff8cf4163b0
RSP=0x0000006a44efce50, RBP=0x00007ff8cf4163b0, RSI=0x0000000000000027, RDI=0x0000006a44efcea0
R8 =0x0000000000002640, R9 =0x0000000000000004, R10=0x0000016c75c28238, R11=0x0000000000000002
R12=0x0000000000000000, R13=0x0000000000000000, R14=0x0000006a44efcfd0, R15=0x00000000000008ea
RIP=0x00007ff8ceb3b840, EFLAGS=0x0000000000010246

XMM[0]=0x0000000000000000 0x0000000000000000
XMM[1]=0x0000000000000000 0x0000000000000000
XMM[2]=0x0000000000000000 0x0000000000000000
XMM[3]=0x0000000000000000 0x0000000000000000
XMM[4]=0x0000000000000000 0x0000000000000000
XMM[5]=0x0000000000000000 0x0000000000000000
XMM[6]=0x0000000000000000 0x0000000000000000
XMM[7]=0x0000000000000000 0x0000000000000000
XMM[8]=0x0000000000000000 0x0000000000000000
XMM[9]=0x0000000000000000 0x0000000000000000
XMM[10]=0x0000000000000000 0x0000000000000000
XMM[11]=0x0000000000000000 0x0000000000000000
XMM[12]=0x0000000000000000 0x0000000000000000
XMM[13]=0x0000000000000000 0x0000000000000000
XMM[14]=0x0000000000000000 0x0000000000000000
XMM[15]=0x0000000000000000 0x0000000000000000
  MXCSR=0x00001fa0


Register to memory mapping:

RAX=0x0 is null
RBX=0x000000000000002d is an unknown value
RCX=0x0 is null
RDX=0x00107ff8cf4163b0 is an unknown value
RSP=0x0000006a44efce50 is pointing into the stack for thread: 0x0000016c75b92fe0
RBP=0x00007ff8cf4163b0 jvm.dll
RSI=0x0000000000000027 is an unknown value
RDI=0x0000006a44efcea0 is pointing into the stack for thread: 0x0000016c75b92fe0
R8 =0x0000000000002640 is an unknown value
R9 =0x0000000000000004 is an unknown value
R10=0x0000016c75c28238 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
R11=0x0000000000000002 is an unknown value
R12=0x0 is null
R13=0x0 is null
R14=0x0000006a44efcfd0 is pointing into the stack for thread: 0x0000016c75b92fe0
R15=0x00000000000008ea is an unknown value

Top of Stack: (sp=0x0000006a44efce50)
0x0000006a44efce50:   00000000000032d8 00007ff8ceb3b994
0x0000006a44efce60:   0000016c003977c0 0000016c75c9e900
0x0000006a44efce70:   0000000000000018 00007ff8ceb34cd8
0x0000006a44efce80:   00000000000008ea 0000000000000001
0x0000006a44efce90:   0000000000000027 0000006a44efcfd0
0x0000006a44efcea0:   0000000000000000 0000000200002657
0x0000006a44efceb0:   0000003100000027 00107ff8cf4163b0
0x0000006a44efcec0:   0000016c7e848b20 0000016c01034290
0x0000006a44efced0:   0000016c7e120e80 0000000000000000
0x0000006a44efcee0:   0000016c7e120e70 0000006a44efd450
0x0000006a44efcef0:   0000000000000001 00007ff8ce960070
0x0000006a44efcf00:   0000006a44efd370 0000006a44efd000
0x0000006a44efcf10:   0000016c7e121258 0000016c7e120e70
0x0000006a44efcf20:   0000016c00000000 0000000000000000
0x0000006a44efcf30:   0000000000000007 0000016c7e120e70
0x0000006a44efcf40:   0000016c79477bf0 0000016c7947b560
0x0000006a44efcf50:   0000016c7947fbd8 0000000000d14080
0x0000006a44efcf60:   0000016c7e120e80 0000000000000000
0x0000006a44efcf70:   0000000000000000 0000000000000000
0x0000006a44efcf80:   0000000000000010 0000000000007600
0x0000006a44efcf90:   0000016c75c9e9b0 00007ff8cf17eee0
0x0000006a44efcfa0:   0000000000000000 0000006a44efe850
0x0000006a44efcfb0:   0000000000000000 00007ff8cf17eee0
0x0000006a44efcfc0:   0000000000007f00 00007ff8ce844fee
0x0000006a44efcfd0:   00007ff80000000c 0000006a44efe850
0x0000006a44efcfe0:   0000016c00fdb050 0000016c75c9e901
0x0000006a44efcff0:   0000016c0140f050 0000016c000032dc
0x0000006a44efd000:   0000006a44efcf30 0000016c7e120e80
0x0000006a44efd010:   00007ff8cf17eeb0 000000000000000d
0x0000006a44efd020:   0000006a44efe850 0000006a44efd370
0x0000006a44efd030:   0000016c00002484 0000016c75c9e9b0
0x0000006a44efd040:   000000000000000a 0000006a44efe850 

Instructions: (pc=0x00007ff8ceb3b840)
0x00007ff8ceb3b740:   48 8b 8e d8 08 00 00 48 85 c9 75 0c e8 2f 09 00
0x00007ff8ceb3b750:   00 48 8b 8e d8 08 00 00 48 8b 01 0f 57 c0 48 89
0x00007ff8ceb3b760:   86 d8 08 00 00 0f 11 01 0f 11 41 10 0f 10 45 00
0x00007ff8ceb3b770:   0f 11 01 0f 10 4d 10 0f 11 49 10 48 8b 43 08 49
0x00007ff8ceb3b780:   89 0c 06 48 8d 0d 26 ac 8d 00 ff c7 ba 70 00 00
0x00007ff8ceb3b790:   00 3b bb 90 00 00 00 0f 82 63 ff ff ff 4c 8b 74
0x00007ff8ceb3b7a0:   24 60 4c 8b 64 24 58 48 8b 6c 24 50 48 8b c3 48
0x00007ff8ceb3b7b0:   83 c4 20 41 5f 41 5d 5f 5e 5b c3 cc cc cc cc cc
0x00007ff8ceb3b7c0:   48 89 74 24 18 57 48 83 ec 20 8b 51 0c 48 8b f9
0x00007ff8ceb3b7d0:   83 fa 04 73 29 48 8b 49 18 0f 1f 80 00 00 00 00
0x00007ff8ceb3b7e0:   8b c2 44 8d 4a 01 48 83 3c c1 00 4c 8d 14 c1 0f
0x00007ff8ceb3b7f0:   85 91 00 00 00 41 8b d1 41 83 f9 04 72 e2 48 89
0x00007ff8ceb3b800:   5c 24 30 8b 5f 10 48 89 6c 24 38 3b 5f 14 73 5f
0x00007ff8ceb3b810:   48 8d 2d 99 ab 8d 00 66 0f 1f 84 00 00 00 00 00
0x00007ff8ceb3b820:   48 8b 47 20 8b cb 48 8b 14 c8 48 3b d5 74 39 33
0x00007ff8ceb3b830:   c0 48 89 57 18 33 c9 66 0f 1f 84 00 00 00 00 00
0x00007ff8ceb3b840:   48 83 3c 11 00 44 8d 48 01 75 78 48 83 c1 08 41
0x00007ff8ceb3b850:   8b c1 41 83 f9 04 72 e8 48 8b 4f 28 48 85 c9 74
0x00007ff8ceb3b860:   07 8b d3 e8 58 01 00 00 ff c3 3b 5f 14 72 b1 33
0x00007ff8ceb3b870:   c0 48 8b 5c 24 30 48 8b 6c 24 38 48 8b 74 24 40
0x00007ff8ceb3b880:   48 83 c4 20 5f c3 8b 47 10 48 8b 74 24 40 44 8d
0x00007ff8ceb3b890:   04 82 41 c1 e0 06 41 81 c0 00 ff ff ff 44 89 47
0x00007ff8ceb3b8a0:   08 49 8b 12 48 0f bc c2 44 89 4f 0c 8b c8 48 d3
0x00007ff8ceb3b8b0:   ea 48 ff ca 41 03 c0 48 89 17 89 47 08 48 83 c4
0x00007ff8ceb3b8c0:   20 5f c3 8b c8 44 8d 04 98 41 c1 e0 06 8d 43 01
0x00007ff8ceb3b8d0:   44 89 47 08 48 8b 14 ca 89 47 10 48 0f bc c2 44
0x00007ff8ceb3b8e0:   89 4f 0c 8b c8 48 d3 ea 48 ff ca 41 03 c0 48 89
0x00007ff8ceb3b8f0:   17 89 47 08 e9 78 ff ff ff cc cc cc cc cc cc cc
0x00007ff8ceb3b900:   40 53 48 83 ec 20 65 48 8b 04 25 58 00 00 00 8b
0x00007ff8ceb3b910:   0d 53 28 92 00 48 8b 1c c8 b8 70 00 00 00 80 3c
0x00007ff8ceb3b920:   18 00 75 05 e8 57 b7 58 00 b8 20 00 00 00 48 8b
0x00007ff8ceb3b930:   04 18 48 8b 88 60 06 00 00 48 8b 99 80 00 00 00 


Stack slot to memory mapping:

stack at sp + 0 slots: 0x00000000000032d8 is an unknown value
stack at sp + 1 slots: 0x00007ff8ceb3b994 jvm.dll
stack at sp + 2 slots: 0x0000016c003977c0 points into unknown readable memory: 0x0000016c7fd14b40 | 40 4b d1 7f 6c 01 00 00
stack at sp + 3 slots: 0x0000016c75c9e900 points into unknown readable memory: 0x0000ffff00000001 | 01 00 00 00 ff ff 00 00
stack at sp + 4 slots: 0x0000000000000018 is an unknown value
stack at sp + 5 slots: 0x00007ff8ceb34cd8 jvm.dll
stack at sp + 6 slots: 0x00000000000008ea is an unknown value
stack at sp + 7 slots: 0x0000000000000001 is an unknown value


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000016c7f14a290, length=62, elements={
0x0000016c1cc4eff0, 0x0000016c75b64f70, 0x0000016c75b65730, 0x0000016c75b6c2c0,
0x0000016c75b6cf70, 0x0000016c75b90700, 0x0000016c1ccf4650, 0x0000016c1cd01aa0,
0x0000016c75b951f0, 0x0000016c75b94450, 0x0000016c1ccf4ce0, 0x0000016c75b96660,
0x0000016c75b92fe0, 0x0000016c75b958c0, 0x0000016c75b93d80, 0x0000016c1ccf5370,
0x0000016c75b936b0, 0x0000016c75b95f90, 0x0000016c75b94b20, 0x0000016c1ccf6db0,
0x0000016c1ccf6090, 0x0000016c1ccf5a00, 0x0000016c1ccf6720, 0x0000016c1ccf7440,
0x0000016c1ccf3fc0, 0x0000016c7e775100, 0x0000016c7e773d50, 0x0000016c7e7764b0,
0x0000016c7e775e20, 0x0000016c7e776b40, 0x0000016c7e7743e0, 0x0000016c7e7736c0,
0x0000016c7e774a70, 0x0000016c7e777860, 0x0000016c7e777ef0, 0x0000016c7e7771d0,
0x0000016c7e778580, 0x0000016c7e778c10, 0x0000016c7e775790, 0x0000016c7e7792a0,
0x0000016c7e77ace0, 0x0000016c7e779930, 0x0000016c7e779fc0, 0x0000016c7e77a650,
0x0000016c7f62a900, 0x0000016c7f628830, 0x0000016c7f62af90, 0x0000016c7f62c340,
0x0000016c7f629be0, 0x0000016c7f62b620, 0x0000016c7f62bcb0, 0x0000016c7f62a270,
0x0000016c7f629550, 0x0000016c7f628ec0, 0x0000016c7f62eaa0, 0x0000016c7f62dd80,
0x0000016c7f62d6f0, 0x0000016c7f62fe50, 0x0000016c7f62c9d0, 0x0000016c7f62d060,
0x0000016c7f62f130, 0x0000016c7f62e410
}

Java Threads: ( => current thread )
  0x0000016c1cc4eff0 JavaThread "main"                              [_thread_in_Java, id=10684, stack(0x0000006a43f00000,0x0000006a44000000) (1024K)]
  0x0000016c75b64f70 JavaThread "Reference Handler"          daemon [_thread_blocked, id=42412, stack(0x0000006a44300000,0x0000006a44400000) (1024K)]
  0x0000016c75b65730 JavaThread "Finalizer"                  daemon [_thread_blocked, id=14036, stack(0x0000006a44400000,0x0000006a44500000) (1024K)]
  0x0000016c75b6c2c0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=30080, stack(0x0000006a44500000,0x0000006a44600000) (1024K)]
  0x0000016c75b6cf70 JavaThread "Attach Listener"            daemon [_thread_blocked, id=19300, stack(0x0000006a44600000,0x0000006a44700000) (1024K)]
  0x0000016c75b90700 JavaThread "Service Thread"             daemon [_thread_blocked, id=27772, stack(0x0000006a44700000,0x0000006a44800000) (1024K)]
  0x0000016c1ccf4650 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=43900, stack(0x0000006a44800000,0x0000006a44900000) (1024K)]
  0x0000016c1cd01aa0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=35552, stack(0x0000006a44900000,0x0000006a44a00000) (1024K)]
  0x0000016c75b951f0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=16412, stack(0x0000006a44a00000,0x0000006a44b00000) (1024K)]
  0x0000016c75b94450 JavaThread "C1 CompilerThread1"         daemon [_thread_blocked, id=41468, stack(0x0000006a44b00000,0x0000006a44c00000) (1024K)]
  0x0000016c1ccf4ce0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=31864, stack(0x0000006a44c00000,0x0000006a44d00000) (1024K)]
  0x0000016c75b96660 JavaThread "C1 CompilerThread2"         daemon [_thread_blocked, id=30460, stack(0x0000006a44d00000,0x0000006a44e00000) (1024K)]
=>0x0000016c75b92fe0 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=30220, stack(0x0000006a44e00000,0x0000006a44f00000) (1024K)]
  0x0000016c75b958c0 JavaThread "C1 CompilerThread3"         daemon [_thread_blocked, id=11256, stack(0x0000006a44f00000,0x0000006a45000000) (1024K)]
  0x0000016c75b93d80 JavaThread "C1 CompilerThread4"         daemon [_thread_blocked, id=27048, stack(0x0000006a45000000,0x0000006a45100000) (1024K)]
  0x0000016c1ccf5370 JavaThread "Notification Thread"        daemon [_thread_blocked, id=29756, stack(0x0000006a45100000,0x0000006a45200000) (1024K)]
  0x0000016c75b936b0 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=27956, stack(0x0000006a45200000,0x0000006a45300000) (1024K)]
  0x0000016c75b95f90 JavaThread "C2 CompilerThread3"         daemon [_thread_blocked, id=19468, stack(0x0000006a45900000,0x0000006a45a00000) (1024K)]
  0x0000016c75b94b20 JavaThread "C2 CompilerThread4"         daemon [_thread_in_native, id=32472, stack(0x0000006a45a00000,0x0000006a45b00000) (1024K)]
  0x0000016c1ccf6db0 JavaThread "Active Thread: Equinox Container: fea47154-bc83-4156-8cc6-55d516862ea2"        [_thread_blocked, id=41992, stack(0x0000006a45b00000,0x0000006a45c00000) (1024K)]
  0x0000016c1ccf6090 JavaThread "Framework Event Dispatcher: Equinox Container: fea47154-bc83-4156-8cc6-55d516862ea2" daemon [_thread_blocked, id=18604, stack(0x0000006a45d00000,0x0000006a45e00000) (1024K)]
  0x0000016c1ccf5a00 JavaThread "Start Level: Equinox Container: fea47154-bc83-4156-8cc6-55d516862ea2" daemon [_thread_blocked, id=26668, stack(0x0000006a45e00000,0x0000006a45f00000) (1024K)]
  0x0000016c1ccf6720 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=45368, stack(0x0000006a45f00000,0x0000006a46000000) (1024K)]
  0x0000016c1ccf7440 JavaThread "SCR Component Registry"     daemon [_thread_blocked, id=18500, stack(0x0000006a46000000,0x0000006a46100000) (1024K)]
  0x0000016c1ccf3fc0 JavaThread "Worker-JM"                         [_thread_blocked, id=41624, stack(0x0000006a46100000,0x0000006a46200000) (1024K)]
  0x0000016c7e775100 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=21596, stack(0x0000006a46400000,0x0000006a46500000) (1024K)]
  0x0000016c7e773d50 JavaThread "Worker-0: Initialize After Load"        [_thread_in_native, id=32080, stack(0x0000006a46500000,0x0000006a46600000) (1024K)]
  0x0000016c7e7764b0 JavaThread "Worker-1"                          [_thread_blocked, id=40716, stack(0x0000006a46600000,0x0000006a46700000) (1024K)]
  0x0000016c7e775e20 JavaThread "Thread-2"                   daemon [_thread_in_native, id=41608, stack(0x0000006a47000000,0x0000006a47100000) (1024K)]
  0x0000016c7e776b40 JavaThread "Thread-3"                   daemon [_thread_in_native, id=25196, stack(0x0000006a47100000,0x0000006a47200000) (1024K)]
  0x0000016c7e7743e0 JavaThread "Thread-4"                   daemon [_thread_in_native, id=9596, stack(0x0000006a47200000,0x0000006a47300000) (1024K)]
  0x0000016c7e7736c0 JavaThread "Thread-5"                   daemon [_thread_in_native, id=27880, stack(0x0000006a47300000,0x0000006a47400000) (1024K)]
  0x0000016c7e774a70 JavaThread "Thread-6"                   daemon [_thread_in_native, id=28224, stack(0x0000006a47400000,0x0000006a47500000) (1024K)]
  0x0000016c7e777860 JavaThread "Thread-7"                   daemon [_thread_in_native, id=41148, stack(0x0000006a47500000,0x0000006a47600000) (1024K)]
  0x0000016c7e777ef0 JavaThread "Thread-8"                   daemon [_thread_in_native, id=42292, stack(0x0000006a47600000,0x0000006a47700000) (1024K)]
  0x0000016c7e7771d0 JavaThread "Thread-9"                   daemon [_thread_in_native, id=24096, stack(0x0000006a47700000,0x0000006a47800000) (1024K)]
  0x0000016c7e778580 JavaThread "Thread-10"                  daemon [_thread_in_native, id=27724, stack(0x0000006a47800000,0x0000006a47900000) (1024K)]
  0x0000016c7e778c10 JavaThread "Thread-11"                  daemon [_thread_in_native, id=8936, stack(0x0000006a47900000,0x0000006a47a00000) (1024K)]
  0x0000016c7e775790 JavaThread "Thread-12"                  daemon [_thread_in_native, id=31040, stack(0x0000006a47a00000,0x0000006a47b00000) (1024K)]
  0x0000016c7e7792a0 JavaThread "Thread-13"                  daemon [_thread_in_native, id=5212, stack(0x0000006a47b00000,0x0000006a47c00000) (1024K)]
  0x0000016c7e77ace0 JavaThread "Thread-14"                  daemon [_thread_in_native, id=37440, stack(0x0000006a47c00000,0x0000006a47d00000) (1024K)]
  0x0000016c7e779930 JavaThread "Thread-15"                  daemon [_thread_in_native, id=17784, stack(0x0000006a47d00000,0x0000006a47e00000) (1024K)]
  0x0000016c7e779fc0 JavaThread "Thread-16"                  daemon [_thread_in_native, id=45588, stack(0x0000006a47e00000,0x0000006a47f00000) (1024K)]
  0x0000016c7e77a650 JavaThread "Thread-17"                  daemon [_thread_in_native, id=37904, stack(0x0000006a47f00000,0x0000006a48000000) (1024K)]
  0x0000016c7f62a900 JavaThread "Thread-18"                  daemon [_thread_in_native, id=39552, stack(0x0000006a48000000,0x0000006a48100000) (1024K)]
  0x0000016c7f628830 JavaThread "Thread-19"                  daemon [_thread_in_native, id=39836, stack(0x0000006a48100000,0x0000006a48200000) (1024K)]
  0x0000016c7f62af90 JavaThread "Thread-20"                  daemon [_thread_in_native, id=39876, stack(0x0000006a48200000,0x0000006a48300000) (1024K)]
  0x0000016c7f62c340 JavaThread "Thread-21"                  daemon [_thread_in_native, id=13228, stack(0x0000006a48300000,0x0000006a48400000) (1024K)]
  0x0000016c7f629be0 JavaThread "Thread-22"                  daemon [_thread_in_native, id=18004, stack(0x0000006a48400000,0x0000006a48500000) (1024K)]
  0x0000016c7f62b620 JavaThread "Thread-23"                  daemon [_thread_in_native, id=39976, stack(0x0000006a48500000,0x0000006a48600000) (1024K)]
  0x0000016c7f62bcb0 JavaThread "Thread-24"                  daemon [_thread_in_native, id=26032, stack(0x0000006a48600000,0x0000006a48700000) (1024K)]
  0x0000016c7f62a270 JavaThread "Thread-25"                  daemon [_thread_in_native, id=24108, stack(0x0000006a48700000,0x0000006a48800000) (1024K)]
  0x0000016c7f629550 JavaThread "Thread-26"                  daemon [_thread_in_native, id=24396, stack(0x0000006a48800000,0x0000006a48900000) (1024K)]
  0x0000016c7f628ec0 JavaThread "Thread-27"                  daemon [_thread_in_native, id=37260, stack(0x0000006a48900000,0x0000006a48a00000) (1024K)]
  0x0000016c7f62eaa0 JavaThread "Thread-28"                  daemon [_thread_in_native, id=30532, stack(0x0000006a48a00000,0x0000006a48b00000) (1024K)]
  0x0000016c7f62dd80 JavaThread "Thread-29"                  daemon [_thread_in_native, id=9348, stack(0x0000006a48b00000,0x0000006a48c00000) (1024K)]
  0x0000016c7f62d6f0 JavaThread "Thread-30"                  daemon [_thread_in_native, id=33048, stack(0x0000006a48c00000,0x0000006a48d00000) (1024K)]
  0x0000016c7f62fe50 JavaThread "Thread-31"                  daemon [_thread_in_native, id=44392, stack(0x0000006a48d00000,0x0000006a48e00000) (1024K)]
  0x0000016c7f62c9d0 JavaThread "Thread-32"                  daemon [_thread_in_native, id=39156, stack(0x0000006a48e00000,0x0000006a48f00000) (1024K)]
  0x0000016c7f62d060 JavaThread "Thread-33"                  daemon [_thread_in_native, id=15760, stack(0x0000006a48f00000,0x0000006a49000000) (1024K)]
  0x0000016c7f62f130 JavaThread "Thread-34"                  daemon [_thread_in_native, id=8896, stack(0x0000006a49000000,0x0000006a49100000) (1024K)]
  0x0000016c7f62e410 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=27768, stack(0x0000006a49100000,0x0000006a49200000) (1024K)]
Total: 62

Other Threads:
  0x0000016c1ccf1900 VMThread "VM Thread"                           [id=39240, stack(0x0000006a44200000,0x0000006a44300000) (1024K)]
  0x0000016c1cadca00 WatcherThread "VM Periodic Task Thread"        [id=21200, stack(0x0000006a44100000,0x0000006a44200000) (1024K)]
  0x0000016c1cc6d520 WorkerThread "GC Thread#0"                     [id=616, stack(0x0000006a44000000,0x0000006a44100000) (1024K)]
  0x0000016c7e250980 WorkerThread "GC Thread#1"                     [id=43276, stack(0x0000006a45300000,0x0000006a45400000) (1024K)]
  0x0000016c7e28d320 WorkerThread "GC Thread#2"                     [id=25656, stack(0x0000006a45400000,0x0000006a45500000) (1024K)]
  0x0000016c7e28d6c0 WorkerThread "GC Thread#3"                     [id=30676, stack(0x0000006a45500000,0x0000006a45600000) (1024K)]
  0x0000016c7e28da60 WorkerThread "GC Thread#4"                     [id=23484, stack(0x0000006a45600000,0x0000006a45700000) (1024K)]
  0x0000016c7951f0a0 WorkerThread "GC Thread#5"                     [id=8772, stack(0x0000006a45700000,0x0000006a45800000) (1024K)]
  0x0000016c799675d0 WorkerThread "GC Thread#6"                     [id=30260, stack(0x0000006a45800000,0x0000006a45900000) (1024K)]
  0x0000016c79967970 WorkerThread "GC Thread#7"                     [id=19332, stack(0x0000006a45c00000,0x0000006a45d00000) (1024K)]
  0x0000016c79967d10 WorkerThread "GC Thread#8"                     [id=29016, stack(0x0000006a46200000,0x0000006a46300000) (1024K)]
  0x0000016c79967230 WorkerThread "GC Thread#9"                     [id=18748, stack(0x0000006a46300000,0x0000006a46400000) (1024K)]
  0x0000016c79966af0 WorkerThread "GC Thread#10"                    [id=24636, stack(0x0000006a46700000,0x0000006a46800000) (1024K)]
  0x0000016c799680b0 WorkerThread "GC Thread#11"                    [id=3240, stack(0x0000006a46800000,0x0000006a46900000) (1024K)]
  0x0000016c79966750 WorkerThread "GC Thread#12"                    [id=28372, stack(0x0000006a46900000,0x0000006a46a00000) (1024K)]
  0x0000016c7e1a2d50 WorkerThread "GC Thread#13"                    [id=29512, stack(0x0000006a46a00000,0x0000006a46b00000) (1024K)]
  0x0000016c7e1a29b0 WorkerThread "GC Thread#14"                    [id=30296, stack(0x0000006a46b00000,0x0000006a46c00000) (1024K)]
  0x0000016c7e1a4310 WorkerThread "GC Thread#15"                    [id=23440, stack(0x0000006a46d00000,0x0000006a46e00000) (1024K)]
  0x0000016c7f136ae0 WorkerThread "GC Thread#16"                    [id=25376, stack(0x0000006a49200000,0x0000006a49300000) (1024K)]
  0x0000016c7f1375c0 WorkerThread "GC Thread#17"                    [id=2284, stack(0x0000006a49300000,0x0000006a49400000) (1024K)]
  0x0000016c7f135c60 WorkerThread "GC Thread#18"                    [id=22844, stack(0x0000006a49400000,0x0000006a49500000) (1024K)]
  0x0000016c7f136000 WorkerThread "GC Thread#19"                    [id=8168, stack(0x0000006a49500000,0x0000006a49600000) (1024K)]
  0x0000016c7f1363a0 WorkerThread "GC Thread#20"                    [id=30884, stack(0x0000006a49600000,0x0000006a49700000) (1024K)]
  0x0000016c7f136e80 WorkerThread "GC Thread#21"                    [id=16180, stack(0x0000006a49700000,0x0000006a49800000) (1024K)]
Total: 24

Threads with active compile tasks:
C1 CompilerThread2  1337 3519       3       sun.nio.fs.NativeBuffers::releaseNativeBuffer (97 bytes)
C2 CompilerThread1  1337 2909       4       org.lombokweb.asm.ClassReader::readMethod (1070 bytes)
C1 CompilerThread3  1337 3524       1       java.util.Spliterators$ArraySpliterator::characteristics (5 bytes)
Total: 3

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ff8cf42ce88] MethodCompileQueue_lock - owner thread: 0x0000016c75b96660
[0x00007ff8cf42e208] CodeCache_lock - owner thread: 0x0000016c75b96660
[0x00007ff8cf42e388] Compile_lock - owner thread: 0x0000016c75b96660

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000016c34000000-0x0000016c34ba0000-0x0000016c34ba0000), size 12189696, SharedBaseAddress: 0x0000016c34000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000016c35000000-0x0000016c75000000, reserved size: 1073741824
Narrow klass base: 0x0000016c34000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 32 total, 32 available
 Memory: 32555M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 23

Heap:
 PSYoungGen      total 26624K, used 4107K [0x00000000d5580000, 0x00000000d7700000, 0x0000000100000000)
  eden space 23040K, 2% used [0x00000000d5580000,0x00000000d5606740,0x00000000d6c00000)
  from space 3584K, 99% used [0x00000000d7100000,0x00000000d747c5a8,0x00000000d7480000)
  to   space 5120K, 0% used [0x00000000d6c00000,0x00000000d6c00000,0x00000000d7100000)
 ParOldGen       total 68608K, used 9800K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 14% used [0x0000000080000000,0x0000000080992388,0x0000000084300000)
 Metaspace       used 26586K, committed 27392K, reserved 1114112K
  class space    used 2543K, committed 2880K, reserved 1048576K

Card table byte_map: [0x0000016c1c5f0000,0x0000016c1ca00000] _byte_map_base: 0x0000016c1c1f0000

Marking Bits: (ParMarkBitMap*) 0x00007ff8cf4331f0
 Begin Bits: [0x0000016c2f0f0000, 0x0000016c310f0000)
 End Bits:   [0x0000016c310f0000, 0x0000016c330f0000)

Polling page: 0x0000016c1a810000

Metaspace:

Usage:
  Non-class:     23.48 MB used.
      Class:      2.48 MB used.
       Both:     25.96 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      23.94 MB ( 37%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       2.81 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      26.75 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  7.86 MB
       Class:  13.03 MB
        Both:  20.89 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 35.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 3.
num_arena_births: 546.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 428.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 17.
num_chunks_taken_from_freelist: 1551.
num_chunk_merges: 7.
num_chunk_splits: 1014.
num_chunks_enlarged: 674.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=118848Kb used=1667Kb max_used=1667Kb free=117180Kb
 bounds [0x0000016c27a30000, 0x0000016c27ca0000, 0x0000016c2ee40000]
CodeHeap 'profiled nmethods': size=118848Kb used=7120Kb max_used=7120Kb free=111727Kb
 bounds [0x0000016c1fe40000, 0x0000016c20540000, 0x0000016c27250000]
CodeHeap 'non-nmethods': size=8064Kb used=3595Kb max_used=3619Kb free=4468Kb
 bounds [0x0000016c27250000, 0x0000016c275e0000, 0x0000016c27a30000]
 total_blobs=4158 nmethods=3523 adapters=537
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 1.307 Thread 0x0000016c75b951f0 3438       3       org.eclipse.core.runtime.Path::hasTrailingSeparator (13 bytes)
Event: 1.307 Thread 0x0000016c75b936b0 3439       4       java.lang.String::lastIndexOf (12 bytes)
Event: 1.307 Thread 0x0000016c75b94450 3440       3       org.eclipse.core.internal.resources.ResourceInfo::getType (10 bytes)
Event: 1.307 Thread 0x0000016c75b951f0 nmethod 3438 0x0000016c204ea310 code [0x0000016c204ea4c0, 0x0000016c204ea638]
Event: 1.307 Thread 0x0000016c75b951f0 3441       3       org.eclipse.core.internal.resources.ResourceInfo::getBits (6 bytes)
Event: 1.307 Thread 0x0000016c75b94450 nmethod 3440 0x0000016c204ea710 code [0x0000016c204ea8c0, 0x0000016c204eaa10]
Event: 1.307 Thread 0x0000016c75b951f0 nmethod 3441 0x0000016c204eab10 code [0x0000016c204eaca0, 0x0000016c204ead98]
Event: 1.307 Thread 0x0000016c75b93d80 3442       3       org.eclipse.core.internal.resources.Resource::getFlags (13 bytes)
Event: 1.307 Thread 0x0000016c75b94450 3443       3       org.eclipse.core.runtime.Path::getFileExtension (42 bytes)
Event: 1.307 Thread 0x0000016c75b96660 3445       3       org.eclipse.core.internal.watson.ElementTreeIterator::requestPath (100 bytes)
Event: 1.307 Thread 0x0000016c75b94b20 3444       4       java.lang.String::indexOf (37 bytes)
Event: 1.307 Thread 0x0000016c75b93d80 nmethod 3442 0x0000016c204eae10 code [0x0000016c204eafc0, 0x0000016c204eb1d8]
Event: 1.307 Thread 0x0000016c75b958c0 3446       3       org.eclipse.core.internal.resources.ResourceProxy::requestFullPath (25 bytes)
Event: 1.307 Thread 0x0000016c75b93d80 3447       3       org.eclipse.core.internal.resources.ResourceInfo::getType (8 bytes)
Event: 1.307 Thread 0x0000016c75b951f0 3448       3       org.eclipse.core.internal.resources.Resource::isMember (65 bytes)
Event: 1.307 Thread 0x0000016c75b958c0 nmethod 3446 0x0000016c204eb290 code [0x0000016c204eb440, 0x0000016c204eb670]
Event: 1.308 Thread 0x0000016c75b93d80 nmethod 3447 0x0000016c204eb790 code [0x0000016c204eb940, 0x0000016c204ebb08]
Event: 1.308 Thread 0x0000016c75b958c0 3451       3       org.eclipse.core.runtime.Path::computeSegments (163 bytes)
Event: 1.308 Thread 0x0000016c75b93d80 3453       3       org.eclipse.core.runtime.Path::canonicalize (62 bytes)
Event: 1.308 Thread 0x0000016c75b951f0 nmethod 3448 0x0000016c204ebc10 code [0x0000016c204ebdc0, 0x0000016c204ec050]

GC Heap History (16 events):
Event: 0.289 GC heap before
{Heap before GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 25587K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 99% used [0x00000000d5580000,0x00000000d6e7cff8,0x00000000d6e80000)
  from space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 0K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080000000,0x0000000084300000)
 Metaspace       used 4153K, committed 4416K, reserved 1114112K
  class space    used 443K, committed 576K, reserved 1048576K
}
Event: 0.291 GC heap after
{Heap after GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 3520K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 85% used [0x00000000d6e80000,0x00000000d71f0220,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 16K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080004000,0x0000000084300000)
 Metaspace       used 4153K, committed 4416K, reserved 1114112K
  class space    used 443K, committed 576K, reserved 1048576K
}
Event: 0.524 GC heap before
{Heap before GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 29120K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 85% used [0x00000000d6e80000,0x00000000d71f0220,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 16K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080004000,0x0000000084300000)
 Metaspace       used 7906K, committed 8192K, reserved 1114112K
  class space    used 802K, committed 960K, reserved 1048576K
}
Event: 0.527 GC heap after
{Heap after GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 4077K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d7280000,0x00000000d767b5f0,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 937K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 1% used [0x0000000080000000,0x00000000800ea698,0x0000000084300000)
 Metaspace       used 7906K, committed 8192K, reserved 1114112K
  class space    used 802K, committed 960K, reserved 1048576K
}
Event: 0.720 GC heap before
{Heap before GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 29677K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d7280000,0x00000000d767b5f0,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 937K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 1% used [0x0000000080000000,0x00000000800ea698,0x0000000084300000)
 Metaspace       used 12425K, committed 12928K, reserved 1114112K
  class space    used 1281K, committed 1472K, reserved 1048576K
}
Event: 0.722 GC heap after
{Heap after GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 4077K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d6e80000,0x00000000d727b4f8,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 2479K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 3% used [0x0000000080000000,0x000000008026bf08,0x0000000084300000)
 Metaspace       used 12425K, committed 12928K, reserved 1114112K
  class space    used 1281K, committed 1472K, reserved 1048576K
}
Event: 0.892 GC heap before
{Heap before GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 29677K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d6e80000,0x00000000d727b4f8,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 2479K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 3% used [0x0000000080000000,0x000000008026bf08,0x0000000084300000)
 Metaspace       used 16325K, committed 17024K, reserved 1114112K
  class space    used 1682K, committed 1984K, reserved 1048576K
}
Event: 0.894 GC heap after
{Heap after GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 4078K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d7280000,0x00000000d767b8b0,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 4445K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 6% used [0x0000000080000000,0x0000000080457598,0x0000000084300000)
 Metaspace       used 16325K, committed 17024K, reserved 1114112K
  class space    used 1682K, committed 1984K, reserved 1048576K
}
Event: 0.986 GC heap before
{Heap before GC invocations=5 (full 0):
 PSYoungGen      total 29696K, used 29678K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d7280000,0x00000000d767b8b0,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 4445K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 6% used [0x0000000080000000,0x0000000080457598,0x0000000084300000)
 Metaspace       used 19600K, committed 20288K, reserved 1114112K
  class space    used 1892K, committed 2240K, reserved 1048576K
}
Event: 0.987 GC heap after
{Heap after GC invocations=5 (full 0):
 PSYoungGen      total 29184K, used 4095K [0x00000000d5580000, 0x00000000d7800000, 0x0000000100000000)
  eden space 25088K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e00000)
  from space 4096K, 99% used [0x00000000d6e80000,0x00000000d727ff40,0x00000000d7280000)
  to   space 5120K, 0% used [0x00000000d7300000,0x00000000d7300000,0x00000000d7800000)
 ParOldGen       total 68608K, used 4653K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 6% used [0x0000000080000000,0x000000008048b598,0x0000000084300000)
 Metaspace       used 19600K, committed 20288K, reserved 1114112K
  class space    used 1892K, committed 2240K, reserved 1048576K
}
Event: 1.019 GC heap before
{Heap before GC invocations=6 (full 0):
 PSYoungGen      total 29184K, used 14005K [0x00000000d5580000, 0x00000000d7800000, 0x0000000100000000)
  eden space 25088K, 39% used [0x00000000d5580000,0x00000000d5f2d8b8,0x00000000d6e00000)
  from space 4096K, 99% used [0x00000000d6e80000,0x00000000d727ff40,0x00000000d7280000)
  to   space 5120K, 0% used [0x00000000d7300000,0x00000000d7300000,0x00000000d7800000)
 ParOldGen       total 68608K, used 4653K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 6% used [0x0000000080000000,0x000000008048b598,0x0000000084300000)
 Metaspace       used 20899K, committed 21504K, reserved 1114112K
  class space    used 2001K, committed 2304K, reserved 1048576K
}
Event: 1.021 GC heap after
{Heap after GC invocations=6 (full 0):
 PSYoungGen      total 30208K, used 4679K [0x00000000d5580000, 0x00000000d7800000, 0x0000000100000000)
  eden space 25088K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e00000)
  from space 5120K, 91% used [0x00000000d7300000,0x00000000d7791ed8,0x00000000d7800000)
  to   space 5120K, 0% used [0x00000000d6e00000,0x00000000d6e00000,0x00000000d7300000)
 ParOldGen       total 68608K, used 4661K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 6% used [0x0000000080000000,0x000000008048d598,0x0000000084300000)
 Metaspace       used 20899K, committed 21504K, reserved 1114112K
  class space    used 2001K, committed 2304K, reserved 1048576K
}
Event: 1.021 GC heap before
{Heap before GC invocations=7 (full 1):
 PSYoungGen      total 30208K, used 4679K [0x00000000d5580000, 0x00000000d7800000, 0x0000000100000000)
  eden space 25088K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e00000)
  from space 5120K, 91% used [0x00000000d7300000,0x00000000d7791ed8,0x00000000d7800000)
  to   space 5120K, 0% used [0x00000000d6e00000,0x00000000d6e00000,0x00000000d7300000)
 ParOldGen       total 68608K, used 4661K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 6% used [0x0000000080000000,0x000000008048d598,0x0000000084300000)
 Metaspace       used 20899K, committed 21504K, reserved 1114112K
  class space    used 2001K, committed 2304K, reserved 1048576K
}
Event: 1.033 GC heap after
{Heap after GC invocations=7 (full 1):
 PSYoungGen      total 30208K, used 0K [0x00000000d5580000, 0x00000000d7800000, 0x0000000100000000)
  eden space 25088K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e00000)
  from space 5120K, 0% used [0x00000000d7300000,0x00000000d7300000,0x00000000d7800000)
  to   space 5120K, 0% used [0x00000000d6e00000,0x00000000d6e00000,0x00000000d7300000)
 ParOldGen       total 68608K, used 8895K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 12% used [0x0000000080000000,0x00000000808afe58,0x0000000084300000)
 Metaspace       used 20886K, committed 21504K, reserved 1114112K
  class space    used 1997K, committed 2304K, reserved 1048576K
}
Event: 1.130 GC heap before
{Heap before GC invocations=8 (full 1):
 PSYoungGen      total 30208K, used 25088K [0x00000000d5580000, 0x00000000d7800000, 0x0000000100000000)
  eden space 25088K, 100% used [0x00000000d5580000,0x00000000d6e00000,0x00000000d6e00000)
  from space 5120K, 0% used [0x00000000d7300000,0x00000000d7300000,0x00000000d7800000)
  to   space 5120K, 0% used [0x00000000d6e00000,0x00000000d6e00000,0x00000000d7300000)
 ParOldGen       total 68608K, used 8895K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 12% used [0x0000000080000000,0x00000000808afe58,0x0000000084300000)
 Metaspace       used 22982K, committed 23680K, reserved 1114112K
  class space    used 2166K, committed 2432K, reserved 1048576K
}
Event: 1.131 GC heap after
{Heap after GC invocations=8 (full 1):
 PSYoungGen      total 27136K, used 2376K [0x00000000d5580000, 0x00000000d7480000, 0x0000000100000000)
  eden space 24576K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6d80000)
  from space 2560K, 92% used [0x00000000d6e00000,0x00000000d70522b0,0x00000000d7080000)
  to   space 3584K, 0% used [0x00000000d7100000,0x00000000d7100000,0x00000000d7480000)
 ParOldGen       total 68608K, used 8903K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 12% used [0x0000000080000000,0x00000000808b1e58,0x0000000084300000)
 Metaspace       used 22982K, committed 23680K, reserved 1114112K
  class space    used 2166K, committed 2432K, reserved 1048576K
}

Dll operation events (10 events):
Event: 0.004 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.019 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.046 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.047 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.048 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.049 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.056 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.092 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 0.553 Loaded shared library C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 0.847 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-92903040\jna17652515640555504344.dll

Deoptimization events (20 events):
Event: 1.163 Thread 0x0000016c1ccf5a00 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000016c27ba7c2c relative=0x0000000000000a8c
Event: 1.163 Thread 0x0000016c1ccf5a00 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000016c27ba7c2c method=java.util.regex.Pattern$BmpCharPropertyGreedy.match(Ljava/util/regex/Matcher;ILjava/lang/CharSequence;)Z @ 12 c2
Event: 1.163 Thread 0x0000016c1ccf5a00 DEOPT PACKING pc=0x0000016c27ba7c2c sp=0x0000006a45efdb00
Event: 1.163 Thread 0x0000016c1ccf5a00 DEOPT UNPACKING pc=0x0000016c272a3aa2 sp=0x0000006a45efd9e8 mode 2
Event: 1.260 Thread 0x0000016c1ccf5a00 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000016c27ad2a44 relative=0x0000000000000124
Event: 1.260 Thread 0x0000016c1ccf5a00 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000016c27ad2a44 method=jdk.internal.org.objectweb.asm.SymbolTable.put(Ljdk/internal/org/objectweb/asm/SymbolTable$Entry;)Ljdk/internal/org/objectweb/asm/SymbolTable$Entry; @ 13 c2
Event: 1.260 Thread 0x0000016c1ccf5a00 DEOPT PACKING pc=0x0000016c27ad2a44 sp=0x0000006a45efbed0
Event: 1.260 Thread 0x0000016c1ccf5a00 DEOPT UNPACKING pc=0x0000016c272a3aa2 sp=0x0000006a45efbe30 mode 2
Event: 1.264 Thread 0x0000016c1ccf5a00 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000016c27b39c68 relative=0x0000000000000388
Event: 1.264 Thread 0x0000016c1ccf5a00 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000016c27b39c68 method=java.lang.Integer.parseInt(Ljava/lang/String;I)I @ 119 c2
Event: 1.264 Thread 0x0000016c1ccf5a00 DEOPT PACKING pc=0x0000016c27b39c68 sp=0x0000006a45efcd80
Event: 1.264 Thread 0x0000016c1ccf5a00 DEOPT UNPACKING pc=0x0000016c272a3aa2 sp=0x0000006a45efcce8 mode 2
Event: 1.268 Thread 0x0000016c1ccf5a00 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000016c27bb62cc relative=0x00000000000000ac
Event: 1.268 Thread 0x0000016c1ccf5a00 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000016c27bb62cc method=java.lang.invoke.LambdaForm$BasicType.basicType(C)Ljava/lang/invoke/LambdaForm$BasicType; @ 1 c2
Event: 1.268 Thread 0x0000016c1ccf5a00 DEOPT PACKING pc=0x0000016c27bb62cc sp=0x0000006a45efbfe0
Event: 1.268 Thread 0x0000016c1ccf5a00 DEOPT UNPACKING pc=0x0000016c272a3aa2 sp=0x0000006a45efbf78 mode 2
Event: 1.297 Thread 0x0000016c7e773d50 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000016c27ba5a64 relative=0x0000000000001844
Event: 1.297 Thread 0x0000016c7e773d50 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000016c27ba5a64 method=java.util.Properties.loadConvert([CIILjava/lang/StringBuilder;)Ljava/lang/String; @ 477 c2
Event: 1.297 Thread 0x0000016c7e773d50 DEOPT PACKING pc=0x0000016c27ba5a64 sp=0x0000006a465fd820
Event: 1.297 Thread 0x0000016c7e773d50 DEOPT UNPACKING pc=0x0000016c272a3aa2 sp=0x0000006a465fd820 mode 2

Classes loaded (20 events):
Event: 1.289 Loading class sun/nio/ch/AsynchronousChannelGroupImpl done
Event: 1.289 Loading class sun/nio/ch/Iocp done
Event: 1.289 Loading class sun/nio/ch/Iocp$CompletionStatus
Event: 1.289 Loading class sun/nio/ch/Iocp$CompletionStatus done
Event: 1.289 Loading class sun/nio/ch/ThreadPool
Event: 1.290 Loading class sun/nio/ch/ThreadPool done
Event: 1.290 Loading class sun/nio/ch/Iocp$EventHandlerTask
Event: 1.290 Loading class sun/nio/ch/Iocp$EventHandlerTask done
Event: 1.290 Loading class sun/nio/ch/AsynchronousChannelGroupImpl$2
Event: 1.290 Loading class sun/nio/ch/AsynchronousChannelGroupImpl$2 done
Event: 1.291 Loading class sun/nio/ch/AsynchronousChannelGroupImpl$1
Event: 1.291 Loading class sun/nio/ch/AsynchronousChannelGroupImpl$1 done
Event: 1.291 Loading class sun/nio/ch/Invoker
Event: 1.291 Loading class sun/nio/ch/Invoker done
Event: 1.291 Loading class sun/nio/ch/Invoker$1
Event: 1.291 Loading class sun/nio/ch/Invoker$1 done
Event: 1.292 Loading class sun/nio/ch/Invoker$GroupAndInvokeCount
Event: 1.292 Loading class sun/nio/ch/Invoker$GroupAndInvokeCount done
Event: 1.296 Loading class sun/nio/ch/PendingIoCache
Event: 1.296 Loading class sun/nio/ch/PendingIoCache done

Classes unloaded (7 events):
Event: 1.023 Thread 0x0000016c1ccf1900 Unloading class 0x0000016c35189000 'java/lang/invoke/LambdaForm$MH+0x0000016c35189000'
Event: 1.023 Thread 0x0000016c1ccf1900 Unloading class 0x0000016c35188c00 'java/lang/invoke/LambdaForm$MH+0x0000016c35188c00'
Event: 1.023 Thread 0x0000016c1ccf1900 Unloading class 0x0000016c35188800 'java/lang/invoke/LambdaForm$MH+0x0000016c35188800'
Event: 1.023 Thread 0x0000016c1ccf1900 Unloading class 0x0000016c35188400 'java/lang/invoke/LambdaForm$MH+0x0000016c35188400'
Event: 1.023 Thread 0x0000016c1ccf1900 Unloading class 0x0000016c35188000 'java/lang/invoke/LambdaForm$BMH+0x0000016c35188000'
Event: 1.023 Thread 0x0000016c1ccf1900 Unloading class 0x0000016c35187c00 'java/lang/invoke/LambdaForm$DMH+0x0000016c35187c00'
Event: 1.023 Thread 0x0000016c1ccf1900 Unloading class 0x0000016c35186c00 'java/lang/invoke/LambdaForm$DMH+0x0000016c35186c00'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.878 Thread 0x0000016c7e773d50 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d69feea0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d69feea0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.878 Thread 0x0000016c7e773d50 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6a027c0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d6a027c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.883 Thread 0x0000016c1ccf5a00 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6be6ab0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d6be6ab0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.899 Thread 0x0000016c1ccf5a00 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d55c3198}: 'double java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x00000000d55c3198) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.899 Thread 0x0000016c1ccf5a00 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d55c6858}: 'double java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000d55c6858) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.007 Thread 0x0000016c1ccf5a00 Implicit null exception at 0x0000016c27b6a5f5 to 0x0000016c27b6a6d0
Event: 1.120 Thread 0x0000016c1ccf5a00 Exception <a 'java/lang/ClassNotFoundException'{0x00000000d6c62238}: com/sun/org/apache/xerces/internal/impl/msg/spi/DOMMessagesProvider> (0x00000000d6c62238) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 1.122 Thread 0x0000016c1ccf5a00 Exception <a 'java/lang/ClassNotFoundException'{0x00000000d6c8f510}: com/sun/org/apache/xerces/internal/impl/msg/spi/XMLSerializerMessagesProvider> (0x00000000d6c8f510) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 1.123 Thread 0x0000016c1ccf5a00 Exception <a 'java/lang/ClassNotFoundException'{0x00000000d6ca95f0}: com/sun/org/apache/xerces/internal/impl/msg/spi/XMLMessagesProvider> (0x00000000d6ca95f0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 1.136 Thread 0x0000016c1ccf5a00 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d55e58a8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000d55e58a8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.159 Thread 0x0000016c1ccf5a00 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d580c060}> (0x00000000d580c060) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1.215 Thread 0x0000016c1ccf5a00 Exception <a 'java/lang/ClassNotFoundException'{0x00000000d5e0dec0}: com/sun/org/apache/xml/internal/serializer/spi/XMLEntitiesProvider> (0x00000000d5e0dec0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 1.253 Thread 0x0000016c1ccf5a00 Exception <a 'java/io/FileNotFoundException'{0x00000000d6186110}> (0x00000000d6186110) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1.262 Thread 0x0000016c1ccf5a00 Exception <a 'java/lang/UnsatisfiedLinkError'{0x00000000d627fed8}: 找不到指定的程序。

> (0x00000000d627fed8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 539]
Event: 1.263 Thread 0x0000016c1ccf5a00 Exception <a 'java/io/FileNotFoundException'{0x00000000d6296380}> (0x00000000d6296380) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1.266 Thread 0x0000016c1ccf5a00 Exception <a 'java/io/FileNotFoundException'{0x00000000d630fa10}> (0x00000000d630fa10) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1.268 Thread 0x0000016c1ccf5a00 Exception <a 'java/lang/UnsatisfiedLinkError'{0x00000000d633dbc8}: 找不到指定的程序。

> (0x00000000d633dbc8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 539]
Event: 1.284 Thread 0x0000016c1cc4eff0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d66165a0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long, java.lang.Object)'> (0x00000000d66165a0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.284 Thread 0x0000016c1cc4eff0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6619c08}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, java.lang.Object)'> (0x00000000d6619c08) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.284 Thread 0x0000016c1cc4eff0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6660928}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, long)'> (0x00000000d6660928) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 0.887 Executing VM operation: ICBufferFull
Event: 0.887 Executing VM operation: ICBufferFull done
Event: 0.892 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 0.894 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 0.986 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 0.987 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 1.019 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold)
Event: 1.033 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold) done
Event: 1.100 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.100 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.130 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 1.131 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 1.152 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.152 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.177 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.177 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.178 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.178 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.221 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.221 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 1.293 Thread 0x0000016c1cc4eff0 Thread added: 0x0000016c7e779930
Event: 1.293 Thread 0x0000016c1cc4eff0 Thread added: 0x0000016c7e779fc0
Event: 1.294 Thread 0x0000016c1cc4eff0 Thread added: 0x0000016c7e77a650
Event: 1.294 Thread 0x0000016c1cc4eff0 Thread added: 0x0000016c7f62a900
Event: 1.294 Thread 0x0000016c1cc4eff0 Thread added: 0x0000016c7f628830
Event: 1.294 Thread 0x0000016c1cc4eff0 Thread added: 0x0000016c7f62af90
Event: 1.294 Thread 0x0000016c1cc4eff0 Thread added: 0x0000016c7f62c340
Event: 1.295 Thread 0x0000016c1cc4eff0 Thread added: 0x0000016c7f629be0
Event: 1.295 Thread 0x0000016c1cc4eff0 Thread added: 0x0000016c7f62b620
Event: 1.295 Thread 0x0000016c1cc4eff0 Thread added: 0x0000016c7f62bcb0
Event: 1.295 Thread 0x0000016c1cc4eff0 Thread added: 0x0000016c7f62a270
Event: 1.295 Thread 0x0000016c1cc4eff0 Thread added: 0x0000016c7f629550
Event: 1.295 Thread 0x0000016c1cc4eff0 Thread added: 0x0000016c7f628ec0
Event: 1.295 Thread 0x0000016c1cc4eff0 Thread added: 0x0000016c7f62eaa0
Event: 1.295 Thread 0x0000016c1cc4eff0 Thread added: 0x0000016c7f62dd80
Event: 1.295 Thread 0x0000016c1cc4eff0 Thread added: 0x0000016c7f62d6f0
Event: 1.295 Thread 0x0000016c1cc4eff0 Thread added: 0x0000016c7f62fe50
Event: 1.296 Thread 0x0000016c1cc4eff0 Thread added: 0x0000016c7f62c9d0
Event: 1.296 Thread 0x0000016c1cc4eff0 Thread added: 0x0000016c7f62d060
Event: 1.296 Thread 0x0000016c1cc4eff0 Thread added: 0x0000016c7f62f130


Dynamic libraries:
0x00007ff7f45a0000 - 0x00007ff7f45ae000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff9c8720000 - 0x00007ff9c8987000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff9c70c0000 - 0x00007ff9c7189000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff9c5f60000 - 0x00007ff9c6350000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff9c59d0000 - 0x00007ff9c5b1b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff9aeb90000 - 0x00007ff9aeba8000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff9ae810000 - 0x00007ff9ae82e000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff9c7190000 - 0x00007ff9c7355000 	C:\WINDOWS\System32\USER32.dll
0x00007ff9c5b20000 - 0x00007ff9c5b47000 	C:\WINDOWS\System32\win32u.dll
0x00007ff9b9f70000 - 0x00007ff9ba20a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4768_none_3e0c112ce331287c\COMCTL32.dll
0x00007ff9c7090000 - 0x00007ff9c70bb000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff9c66b0000 - 0x00007ff9c6759000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff9c5e20000 - 0x00007ff9c5f58000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff9c6360000 - 0x00007ff9c6403000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff9c65c0000 - 0x00007ff9c65ef000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff9b8fb0000 - 0x00007ff9b8fbc000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff943fa0000 - 0x00007ff94402d000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007ff8ce780000 - 0x00007ff8cf510000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff9c6760000 - 0x00007ff9c6814000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff9c6600000 - 0x00007ff9c66a6000 	C:\WINDOWS\System32\sechost.dll
0x00007ff9c6b60000 - 0x00007ff9c6c78000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff9c69e0000 - 0x00007ff9c6a54000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff9c56b0000 - 0x00007ff9c570e000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff9bdd00000 - 0x00007ff9bdd35000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff9bdd50000 - 0x00007ff9bdd5b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff9c5690000 - 0x00007ff9c56a4000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff9c4590000 - 0x00007ff9c45ab000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff9b8ef0000 - 0x00007ff9b8efa000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff9b4c30000 - 0x00007ff9b4e71000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff9c7b10000 - 0x00007ff9c7e95000 	C:\WINDOWS\System32\combase.dll
0x00007ff9c7ea0000 - 0x00007ff9c7f80000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff9b5250000 - 0x00007ff9b5293000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff9c6410000 - 0x00007ff9c64a9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff9b8eb0000 - 0x00007ff9b8ebf000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff9ae0d0000 - 0x00007ff9ae0ef000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ff9c73c0000 - 0x00007ff9c7b0d000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff9c5ca0000 - 0x00007ff9c5e13000 	C:\WINDOWS\System32\wintypes.dll
0x00007ff9c3410000 - 0x00007ff9c3c6f000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff9c64b0000 - 0x00007ff9c65a5000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff9c7020000 - 0x00007ff9c708a000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff9c5770000 - 0x00007ff9c5799000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff9a6cc0000 - 0x00007ff9a6cd8000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ff9b8d40000 - 0x00007ff9b8d50000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ff9c0500000 - 0x00007ff9c061e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff9c4b00000 - 0x00007ff9c4b6b000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff99d730000 - 0x00007ff99d746000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ff9b8d20000 - 0x00007ff9b8d30000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ff9b6580000 - 0x00007ff9b65c5000 	C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ff9c6830000 - 0x00007ff9c69d0000 	C:\WINDOWS\System32\ole32.dll
0x00007ff9c4ee0000 - 0x00007ff9c4efb000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff9c44f0000 - 0x00007ff9c452b000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff9c4ba0000 - 0x00007ff9c4bcb000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff9c5740000 - 0x00007ff9c5766000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ff9c4d40000 - 0x00007ff9c4d4c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff9c3f80000 - 0x00007ff9c3fb3000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff9c6820000 - 0x00007ff9c682a000 	C:\WINDOWS\System32\NSI.dll
0x00007ff9b5f50000 - 0x00007ff9b5f99000 	C:\Users\<USER>\AppData\Local\Temp\jna-92903040\jna17652515640555504344.dll
0x00007ff9c86d0000 - 0x00007ff9c86d8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff9c04d0000 - 0x00007ff9c04ef000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ff9c04a0000 - 0x00007ff9c04c5000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4768_none_3e0c112ce331287c;c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-92903040

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar 
java_command: c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\d7c25a54a6ee316104bfe6acdb8443cd\redhat.java\ss_ws --pipe=\\.\pipe\lsp-ddb019f1dafdbb5104923aafe477f265-sock
java_class_path (initial): c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 15                                        {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 8192380                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 121732930                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 121732930                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Zulu\zulu-24\
PATH=C:\Program Files\Zulu\zulu-24\bin\;C:\Users\<USER>\.jdks\openjdk-23.0.1\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files\Git\cmd;C:\Program Files\Go\bin;C:\TDM-GCC-64\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;;C:\Program Files\JetBrains\IntelliJ IDEA 2024.3.1.1\bin;;C:\Program Files\JetBrains\GoLand 2024.3.2.1\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin
USERNAME=alice
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4768)
OS uptime: 3 days 23:59 hours
Hyper-V role detected

CPU: total 32 (initial active 32) (16 cores per cpu, 2 threads per core) family 6 model 183 stepping 1 microcode 0x12b, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv, serialize, rdtscp, rdpid, fsrm, f16c, cet_ibt, cet_ss
Processor Information for processor 0
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 1
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 2
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 3
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 4
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 5
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 6
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 7
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 8
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 9
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 10
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 11
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 12
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 13
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 14
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 15
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 16
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 17
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 18
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 19
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 20
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 21
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 22
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 23
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 24
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 25
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 26
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 27
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 28
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 29
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 30
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 31
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700

Memory: 4k page, system-wide physical 32555M (6515M free)
TotalPageFile size 45596M (AvailPageFile size 1423M)
current process WorkingSet (physical memory assigned to process): 172M, peak: 172M
current process commit charge ("private bytes"): 355M, peak: 355M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
