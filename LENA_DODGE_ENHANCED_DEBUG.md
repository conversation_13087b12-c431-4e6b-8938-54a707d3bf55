# 莱娜闪避增强调试日志

## 🔍 **问题分析**

从你最新的日志中，我发现了一个关键问题：

### 当前问题
```
[森之祝福] 设置效果标记给玩家: <PERSON>_bian, 过期时间: 1753711001643, 当前标记数量: 1
[森之祝福] 效果检查结果 - 玩家: Faith_bian, 有效果: false, 当前标记总数: 0
```

**问题分析**：
1. 效果标记确实被设置了（标记数量: 1）
2. 但是在检查时却显示没有效果（有效果: false, 当前标记总数: 0）
3. 这说明在`hasForestBlessingEffect`检查过程中，标记被移除了

## 🔧 **增强调试日志**

我添加了更详细的调试信息来精确定位问题：

### 1. 检查前状态日志
```java
plugin.getLogger().info("[森之祝福] 检查前状态 - 玩家: " + victim.getName() + 
    ", 当前标记总数: " + forestBlessingEffectPlayers.size() + 
    ", 标记内容: " + forestBlessingEffectPlayers.keySet());
```

### 2. 详细效果检查日志
```java
plugin.getLogger().info("[森之祝福] 详细效果检查 - 玩家: " + playerName + 
    ", 过期时间: " + expiryTime + 
    ", 当前时间: " + currentTime + 
    ", 时间差: " + (expiryTime != null ? (expiryTime - currentTime) : "无标记"));
```

### 3. 不同情况的具体日志

**无标记情况**：
```java
plugin.getLogger().info("[森之祝福] 玩家 " + playerName + " 没有效果标记");
```

**效果过期情况**：
```java
plugin.getLogger().info("[森之祝福] 效果过期，移除标记: " + playerName + 
    ", 过期时间: " + expiryTime + 
    ", 当前时间: " + currentTime + 
    ", 过期了: " + (currentTime - expiryTime) + "ms");
```

**效果有效情况**：
```java
plugin.getLogger().info("[森之祝福] 效果有效 - 玩家: " + playerName + 
    ", 剩余时间: " + (expiryTime - currentTime) + "ms");
```

## 📊 **预期的新日志格式**

### 正常工作时应该看到：
```
[森之祝福] 设置效果标记给玩家: Faith_bian, 过期时间: 1753711001643, 当前标记数量: 1
[森之祝福] 检查前状态 - 玩家: Faith_bian, 当前标记总数: 1, 标记内容: [cdeca090-56e8-335d-b4b1-9324cdde9083]
[森之祝福] 详细效果检查 - 玩家: Faith_bian, 过期时间: 1753711001643, 当前时间: 1753711000000, 时间差: 1643
[森之祝福] 效果有效 - 玩家: Faith_bian, 剩余时间: 1643ms
[森之祝福] 效果检查结果 - 玩家: Faith_bian, 有效果: true, 检查后标记总数: 1
```

### 如果效果过期会看到：
```
[森之祝福] 设置效果标记给玩家: Faith_bian, 过期时间: 1753711001643, 当前标记数量: 1
[森之祝福] 检查前状态 - 玩家: Faith_bian, 当前标记总数: 1, 标记内容: [cdeca090-56e8-335d-b4b1-9324cdde9083]
[森之祝福] 详细效果检查 - 玩家: Faith_bian, 过期时间: 1753711001643, 当前时间: 1753711006643, 时间差: -5000
[森之祝福] 效果过期，移除标记: Faith_bian, 过期时间: 1753711001643, 当前时间: 1753711006643, 过期了: 5000ms
[森之祝福] 效果检查结果 - 玩家: Faith_bian, 有效果: false, 检查后标记总数: 0
```

## 🎯 **可能的问题原因**

基于当前的症状，我怀疑可能的原因：

### 1. 时间戳问题
- 系统时间可能有问题
- 过期时间计算错误
- 时区或时间精度问题

### 2. 并发问题
- 多线程访问导致的竞态条件
- 标记被其他地方意外移除

### 3. UUID匹配问题
- UUID在不同地方可能不一致
- 字符串比较问题

## 📋 **测试步骤**

请重新测试并观察以下信息：

### 1. 时间戳分析
- 观察"过期时间"和"当前时间"的数值
- 计算时间差是否合理（应该是正数，表示还未过期）

### 2. UUID一致性
- 检查"标记内容"中的UUID是否与玩家UUID一致
- 确认UUID格式是否正确

### 3. 时序分析
- 观察设置标记和检查标记之间的时间间隔
- 确认是否在5秒有效期内

## 🔍 **关键信息**

请特别关注新日志中的这些信息：

1. **标记内容**：`标记内容: [UUID列表]` - 确认UUID是否正确
2. **时间差**：`时间差: XXXXms` - 应该是正数，表示剩余时间
3. **剩余时间**：`剩余时间: XXXXms` - 如果看到这个，说明效果应该有效

## 🎉 **期望结果**

修复成功后，你应该看到：

1. **效果设置**：正确设置5秒过期时间
2. **效果检查**：显示有效果，剩余时间为正数
3. **闪避测试**：进入概率检查阶段
4. **闪避结果**：35%概率的成功/失败

请重新测试并提供完整的新日志，这次我们应该能精确定位问题所在！🔍✨
