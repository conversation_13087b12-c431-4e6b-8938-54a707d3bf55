/**
 * 手动验证ELO计算逻辑的简单程序
 */
public class EloCalculationVerification {
    
    private static final int BASE_STAKE = 25;
    
    public static void main(String[] args) {
        System.out.println("=== ELO计算逻辑验证 ===\n");
        
        // 测试场景1：2个速通者 vs 3个猎人，速通者获胜
        System.out.println("场景1: 2个速通者 vs 3个猎人，速通者获胜");
        testScenario(2, 3, "SPEEDRUNNER");
        
        // 测试场景2：1个速通者 vs 2个猎人，猎人获胜
        System.out.println("\n场景2: 1个速通者 vs 2个猎人，猎人获胜");
        testScenario(1, 2, "HUNTER");
        
        // 测试场景3：3个速通者 vs 3个猎人，速通者获胜
        System.out.println("\n场景3: 3个速通者 vs 3个猎人，速通者获胜");
        testScenario(3, 3, "SPEEDRUNNER");
        
        // 测试场景4：2个速通者 vs 4个猎人，猎人获胜
        System.out.println("\n场景4: 2个速通者 vs 4个猎人，猎人获胜");
        testScenario(2, 4, "HUNTER");
    }
    
    private static void testScenario(int speedrunnerCount, int hunterCount, String winner) {
        int totalStake = (speedrunnerCount + hunterCount) * BASE_STAKE;
        
        System.out.println("速通者人数: " + speedrunnerCount);
        System.out.println("猎人人数: " + hunterCount);
        System.out.println("总赌注池: " + totalStake + "分");
        System.out.println("获胜方: " + (winner.equals("SPEEDRUNNER") ? "速通者" : "猎人"));
        
        if (winner.equals("SPEEDRUNNER")) {
            // 速通者获胜
            int speedrunnerGain = totalStake / speedrunnerCount;
            int hunterLoss = -speedrunnerGain; // 猎人扣除对方平分后的分数
            
            System.out.println("速通者每人获得: +" + speedrunnerGain + "分");
            System.out.println("猎人每人扣除: " + hunterLoss + "分");
            
            // 验证总分平衡
            int totalGain = speedrunnerGain * speedrunnerCount;
            int totalLoss = Math.abs(hunterLoss) * hunterCount;
            System.out.println("总获得分数: " + totalGain + "分");
            System.out.println("总扣除分数: " + totalLoss + "分");
            System.out.println("分数平衡: " + (totalGain == totalLoss ? "✓" : "✗"));
            
        } else {
            // 猎人获胜
            int hunterGain = totalStake / hunterCount;
            int speedrunnerLoss = -BASE_STAKE; // 速通者只扣自己的25分
            
            System.out.println("猎人每人获得: +" + hunterGain + "分");
            System.out.println("速通者每人扣除: " + speedrunnerLoss + "分");
            
            // 验证分数流动
            int totalGain = hunterGain * hunterCount;
            int totalLoss = Math.abs(speedrunnerLoss) * speedrunnerCount;
            System.out.println("总获得分数: " + totalGain + "分");
            System.out.println("总扣除分数: " + totalLoss + "分");
            System.out.println("分数来源: 赌注池(" + totalStake + "分)");
        }
    }
}
