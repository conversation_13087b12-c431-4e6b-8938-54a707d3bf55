package com.projectSource.ultimateManhurt.room;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.event.ClickEvent;
import net.kyori.adventure.text.event.HoverEvent;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 房间邀请管理器
 * 处理私人房间的邀请发送、接受、拒绝等功能
 */
public class InviteManager {
    
    private final UltimateManhurt plugin;
    
    // 邀请数据：被邀请者UUID -> 邀请信息
    private final Map<UUID, RoomInvite> pendingInvites = new ConcurrentHashMap<>();
    
    // 邀请冷却：邀请者UUID -> 上次邀请时间
    private final Map<UUID, Long> inviteCooldowns = new ConcurrentHashMap<>();
    
    // 邀请设置
    private static final long INVITE_EXPIRE_TIME = 60000; // 60秒过期
    private static final long INVITE_COOLDOWN = 5000; // 5秒冷却
    private static final int MAX_INVITES_PER_ROOM = 10; // 每个房间最多10个待处理邀请
    
    public InviteManager(UltimateManhurt plugin) {
        this.plugin = plugin;
        startCleanupTask();
    }
    
    /**
     * 发送房间邀请
     */
    public boolean sendInvite(Player inviter, Player target, Room room) {
        UUID inviterUUID = inviter.getUniqueId();
        UUID targetUUID = target.getUniqueId();
        
        // 检查权限
        if (!canSendInvite(inviter, target, room)) {
            return false;
        }
        
        // 检查冷却时间
        if (isOnCooldown(inviterUUID)) {
            long remainingTime = getRemainingCooldown(inviterUUID);
            ComponentUtil.sendMessage(inviter, ComponentUtil.error(
                "邀请冷却中，请等待 " + (remainingTime / 1000) + " 秒"));
            return false;
        }
        
        // 检查目标是否已有待处理邀请
        if (pendingInvites.containsKey(targetUUID)) {
            ComponentUtil.sendMessage(inviter, ComponentUtil.error(
                target.getName() + " 已有待处理的房间邀请"));
            return false;
        }
        
        // 检查房间邀请数量限制
        long roomInviteCount = pendingInvites.values().stream()
            .filter(invite -> invite.getRoomId().equals(room.getId()))
            .count();
        
        if (roomInviteCount >= MAX_INVITES_PER_ROOM) {
            ComponentUtil.sendMessage(inviter, ComponentUtil.error(
                "该房间待处理邀请过多，请稍后再试"));
            return false;
        }
        
        // 创建邀请
        RoomInvite invite = new RoomInvite(
            inviterUUID, 
            targetUUID, 
            room.getId(), 
            room.getName(),
            System.currentTimeMillis()
        );
        
        pendingInvites.put(targetUUID, invite);
        inviteCooldowns.put(inviterUUID, System.currentTimeMillis());
        
        // 发送邀请消息
        sendInviteMessage(inviter, target, room);
        
        // 给邀请者反馈
        ComponentUtil.sendMessage(inviter, ComponentUtil.success(
            "已向 " + target.getName() + " 发送房间邀请"));
        
        return true;
    }
    
    /**
     * 接受邀请
     */
    public boolean acceptInvite(Player player) {
        UUID playerUUID = player.getUniqueId();
        RoomInvite invite = pendingInvites.get(playerUUID);
        
        if (invite == null) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("没有待处理的房间邀请"));
            return false;
        }
        
        // 检查邀请是否过期
        if (invite.isExpired()) {
            pendingInvites.remove(playerUUID);
            ComponentUtil.sendMessage(player, ComponentUtil.error("邀请已过期"));
            return false;
        }
        
        // 获取房间
        Room room = plugin.getRoomManager().getRoom(invite.getRoomId());
        if (room == null) {
            pendingInvites.remove(playerUUID);
            ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
            return false;
        }
        
        // 检查房间是否已满
        if (room.isFull()) {
            pendingInvites.remove(playerUUID);
            ComponentUtil.sendMessage(player, ComponentUtil.error("房间已满"));
            return false;
        }
        
        // 检查玩家是否已在其他房间
        if (plugin.getRoomManager().isPlayerInRoom(playerUUID)) {
            pendingInvites.remove(playerUUID);
            ComponentUtil.sendMessage(player, ComponentUtil.error("你已经在一个房间中了"));
            return false;
        }
        
        // 加入房间（默认为观察者角色，房主可以后续分配角色）
        boolean success = room.addPlayer(playerUUID, PlayerRole.SPECTATOR);
        if (success) {
            pendingInvites.remove(playerUUID);

            // 通知邀请者
            Player inviter = plugin.getServer().getPlayer(invite.getInviterUUID());
            if (inviter != null) {
                ComponentUtil.sendMessage(inviter, ComponentUtil.success(
                    player.getName() + " 接受了你的房间邀请"));
            }

            // 通知被邀请者
            ComponentUtil.sendMessage(player, ComponentUtil.success(
                "成功加入房间: " + room.getName()));

            // 打开房间设置界面
            plugin.getGuiManager().openRoomSettingsGui(player, room);

            return true;
        } else {
            ComponentUtil.sendMessage(player, ComponentUtil.error("加入房间失败"));
            return false;
        }
    }
    
    /**
     * 拒绝邀请
     */
    public boolean declineInvite(Player player) {
        UUID playerUUID = player.getUniqueId();
        RoomInvite invite = pendingInvites.remove(playerUUID);
        
        if (invite == null) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("没有待处理的房间邀请"));
            return false;
        }
        
        // 通知邀请者
        Player inviter = plugin.getServer().getPlayer(invite.getInviterUUID());
        if (inviter != null) {
            ComponentUtil.sendMessage(inviter, ComponentUtil.warning(
                player.getName() + " 拒绝了你的房间邀请"));
        }
        
        // 通知被邀请者
        ComponentUtil.sendMessage(player, ComponentUtil.info("已拒绝房间邀请"));
        
        return true;
    }
    
    /**
     * 检查是否可以发送邀请
     */
    private boolean canSendInvite(Player inviter, Player target, Room room) {
        // 检查房间类型
        if (room.getType() != RoomType.PRIVATE) {
            ComponentUtil.sendMessage(inviter, ComponentUtil.error("只有私人房间可以发送邀请"));
            return false;
        }
        
        // 检查是否为房主
        if (!room.isOwner(inviter.getUniqueId())) {
            ComponentUtil.sendMessage(inviter, ComponentUtil.error("只有房主可以邀请玩家"));
            return false;
        }
        
        // 检查目标玩家是否在线
        if (!target.isOnline()) {
            ComponentUtil.sendMessage(inviter, ComponentUtil.error("目标玩家不在线"));
            return false;
        }
        
        // 检查是否邀请自己
        if (inviter.getUniqueId().equals(target.getUniqueId())) {
            ComponentUtil.sendMessage(inviter, ComponentUtil.error("不能邀请自己"));
            return false;
        }
        
        // 检查目标是否已在房间中
        if (room.containsPlayer(target.getUniqueId())) {
            ComponentUtil.sendMessage(inviter, ComponentUtil.error(target.getName() + " 已经在房间中"));
            return false;
        }
        
        // 检查目标是否在其他房间中
        if (plugin.getRoomManager().isPlayerInRoom(target.getUniqueId())) {
            ComponentUtil.sendMessage(inviter, ComponentUtil.error(target.getName() + " 已在其他房间中"));
            return false;
        }
        
        return true;
    }
    
    /**
     * 发送邀请消息
     */
    private void sendInviteMessage(Player inviter, Player target, Room room) {
        Component message = Component.text()
            .append(ComponentUtil.parse("<yellow>━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"))
            .append(Component.newline())
            .append(ComponentUtil.parse("<gold><bold>房间邀请"))
            .append(Component.newline())
            .append(ComponentUtil.parse("<gray>邀请者: <white>" + inviter.getName()))
            .append(Component.newline())
            .append(ComponentUtil.parse("<gray>房间名: <white>" + room.getName()))
            .append(Component.newline())
            .append(ComponentUtil.parse("<gray>房间类型: " + room.getType().getColor() + room.getType().getDisplayName()))
            .append(Component.newline())
            .append(ComponentUtil.parse("<gray>当前人数: <white>" + room.getPlayerCount() + "/" + room.getSettings().getMaxPlayers()))
            .append(Component.newline())
            .append(Component.newline())
            .append(Component.text("[接受]", NamedTextColor.GREEN)
                .clickEvent(ClickEvent.runCommand("/manhunt accept"))
                .hoverEvent(HoverEvent.showText(Component.text("点击接受邀请", NamedTextColor.GREEN))))
            .append(Component.text("  "))
            .append(Component.text("[拒绝]", NamedTextColor.RED)
                .clickEvent(ClickEvent.runCommand("/manhunt decline"))
                .hoverEvent(HoverEvent.showText(Component.text("点击拒绝邀请", NamedTextColor.RED))))
            .append(Component.newline())
            .append(ComponentUtil.parse("<gray>邀请将在60秒后过期"))
            .append(Component.newline())
            .append(ComponentUtil.parse("<yellow>━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"))
            .build();
        
        target.sendMessage(message);
    }
    
    /**
     * 检查冷却时间
     */
    private boolean isOnCooldown(UUID playerUUID) {
        Long lastInvite = inviteCooldowns.get(playerUUID);
        if (lastInvite == null) return false;
        
        return System.currentTimeMillis() - lastInvite < INVITE_COOLDOWN;
    }
    
    /**
     * 获取剩余冷却时间
     */
    private long getRemainingCooldown(UUID playerUUID) {
        Long lastInvite = inviteCooldowns.get(playerUUID);
        if (lastInvite == null) return 0;
        
        long elapsed = System.currentTimeMillis() - lastInvite;
        return Math.max(0, INVITE_COOLDOWN - elapsed);
    }
    
    /**
     * 获取玩家的待处理邀请
     */
    public RoomInvite getPendingInvite(UUID playerUUID) {
        return pendingInvites.get(playerUUID);
    }
    
    /**
     * 清理过期邀请
     */
    private void startCleanupTask() {
        new BukkitRunnable() {
            @Override
            public void run() {
                long currentTime = System.currentTimeMillis();
                
                // 清理过期邀请
                pendingInvites.entrySet().removeIf(entry -> 
                    entry.getValue().isExpired());
                
                // 清理过期冷却
                inviteCooldowns.entrySet().removeIf(entry -> 
                    currentTime - entry.getValue() > INVITE_COOLDOWN);
            }
        }.runTaskTimer(plugin, 20L * 10, 20L * 10); // 每10秒清理一次
    }
    
    /**
     * 玩家离线时清理邀请
     */
    public void onPlayerQuit(UUID playerUUID) {
        pendingInvites.remove(playerUUID);
        inviteCooldowns.remove(playerUUID);
    }
    
    /**
     * 房间删除时清理相关邀请
     */
    public void onRoomDeleted(String roomId) {
        pendingInvites.entrySet().removeIf(entry ->
            entry.getValue().getRoomId().equals(roomId));
    }
}
