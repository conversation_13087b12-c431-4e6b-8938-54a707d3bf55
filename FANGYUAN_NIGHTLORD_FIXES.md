# 方源和暗夜领主技能修复

## 修复内容

### 1. 春秋必成内置冷却修复

**问题：** 春秋必成在创建时光分身后可以立即使用第二次效果，缺乏合理的间隔。

**解决方案：**
- 添加了3秒内置冷却机制
- 在创建时光分身后，3秒内无法再次使用春秋必成
- 显示剩余冷却时间提示

**技术实现：**
```java
// 新增状态管理
private final Map<UUID, Long> fangyuanLastCreateTime = new HashMap<>();

// 检查3秒内置冷却
Long lastCreateTime = fangyuanLastCreateTime.get(playerId);
if (lastCreateTime != null) {
    long timeSinceLastCreate = System.currentTimeMillis() - lastCreateTime;
    if (timeSinceLastCreate < 3000) { // 3秒内置冷却
        long remainingTime = (3000 - timeSinceLastCreate) / 1000 + 1;
        ComponentUtil.sendMessage(player, ComponentUtil.warning("春秋必成内置冷却中，还需等待 " + remainingTime + " 秒"));
        return false;
    }
}

// 记录创建时间
fangyuanLastCreateTime.put(playerId, System.currentTimeMillis());
```

### 2. 春秋蝉冷却逻辑修复

**问题：** 春秋蝉被动技能会先设置冷却时间，然后进行概率检查。如果概率检查失败，冷却已经被设置，但技能实际上没有触发。

**解决方案：**
- 先进行概率检查，只有在概率检查通过后才设置冷却时间
- 确保只有真正触发技能时才进入冷却

**修复前：**
```java
// 错误的顺序：先设置冷却，再检查概率
if (!checkAndSetPassiveSkillCooldown(player, skillName, cooldownTime)) {
    return false; // 在冷却中，不能触发
}

// 伪随机检查（80%概率）
if (!pseudoRandom.checkTrigger(playerId, "fangyuan_spring_autumn_cicada", 0.8)) {
    return false; // 概率检查失败，但冷却已经设置了！
}
```

**修复后：**
```java
// 正确的顺序：先检查冷却，再检查概率，最后设置冷却
if (plugin.getProfessionManager().getSkillCooldown().isOnCooldown(playerId, skillName)) {
    return false; // 在冷却中，不能触发
}

// 伪随机检查（80%概率）
if (!pseudoRandom.checkTrigger(playerId, "fangyuan_spring_autumn_cicada", 0.8)) {
    return false; // 概率检查失败，不设置冷却
}

// 概率检查通过后，设置冷却时间
plugin.getProfessionManager().getSkillCooldown().setCooldown(playerId, skillName, cooldownTime);
plugin.getProfessionManager().getSkillBossBarManager().createSkillBossBar(player);
```

### 3. 暗夜领主夜视效果闪烁修复

**问题：** 暗夜领主的夜视效果持续时间为10秒，但检查间隔为5秒，导致在效果即将结束时可能出现短暂的闪烁。

**解决方案：**
- 延长所有夜晚增益效果的持续时间
- 确保效果持续时间大于检查间隔的2倍以上

**修复前：**
```java
if (isNight) {
    player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 100, 1, false, false)); // 速度2，5秒
    player.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, 100, 0, false, false)); // 生命恢复1，5秒
    player.addPotionEffect(new PotionEffect(PotionEffectType.NIGHT_VISION, 200, 0, false, false)); // 夜视，10秒
}
```

**修复后：**
```java
if (isNight) {
    // 延长持续时间避免闪烁
    player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 140, 1, false, false)); // 速度2，7秒
    player.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, 140, 0, false, false)); // 生命恢复1，7秒
    player.addPotionEffect(new PotionEffect(PotionEffectType.NIGHT_VISION, 300, 0, false, false)); // 夜视，15秒
}
```

## 技术细节

### 冷却时间管理
- **被动技能冷却：** 使用`SkillCooldown.isOnCooldown()`检查，`SkillCooldown.setCooldown()`设置
- **内置冷却：** 使用自定义时间戳管理，避免与主冷却系统冲突

### 状态清理
- 在`clearPlayerStates()`方法中添加了新状态的清理
- 确保玩家离线或切换职业时正确清理所有状态

### 效果持续时间计算
- **检查间隔：** 5秒（100 ticks）
- **推荐持续时间：** 至少7秒（140 ticks）以上
- **夜视效果：** 15秒（300 ticks）确保稳定

## 测试建议

### 春秋必成内置冷却测试
1. 使用春秋必成创建时光分身
2. 立即尝试再次使用，应显示冷却提示
3. 等待3秒后再次使用，应能正常使用

### 春秋蝉冷却测试
1. 将血量降低到13%以下多次
2. 观察只有在真正触发传送时才进入冷却
3. 如果概率检查失败，不应进入冷却

### 暗夜领主夜视测试
1. 在夜晚环境下观察夜视效果
2. 长时间观察，确保没有闪烁现象
3. 切换白天/夜晚，观察效果的平滑过渡

## 平衡性影响

### 方源
- **春秋必成：** 3秒内置冷却增加了技能使用的策略性
- **春秋蝉：** 修复后的冷却逻辑更加公平，不会因为运气不好而浪费冷却

### 暗夜领主
- **暗夜猎影：** 修复闪烁问题提升了游戏体验，没有改变平衡性

## 后续优化建议

1. **视觉反馈：** 可以考虑为内置冷却添加专门的视觉提示
2. **音效优化：** 为冷却状态添加不同的音效反馈
3. **配置化：** 将内置冷却时间设为可配置参数
4. **统计数据：** 收集技能触发率数据，用于后续平衡调整
