# 操作模式切换功能实现

## 功能概述

为房间设置GUI添加了操作模式切换功能，玩家可以通过一个专门的切换按钮选择使用"鼠标操作"还是"聊天输入"模式来调整数值设置。两种模式各有优势，玩家可以根据自己的喜好和需求自由切换。

## 操作模式切换按钮

### 位置与外观
- **位置**: GUI右下角 (slot 52)
- **图标**:
  - 鼠标操作模式: 铁镐 (IRON_PICKAXE)
  - 聊天输入模式: 可写书 (WRITABLE_BOOK)
- **名称**: "<aqua><bold>操作模式切换"
- **功能**: 点击即可在两种模式间切换

### 显示信息
- 当前操作模式名称
- 模式描述
- 切换提示
- 两种模式的简要说明

## 两种操作模式

### 🖱️ 鼠标操作模式
- **优势**: 快速调整，即时反馈，无需输入
- **适用**: 微调数值，快速设置
- **操作**: 左键减少，右键增加，Shift键大幅调整

### 💬 聊天输入模式
- **优势**: 精确设置，支持大范围调整
- **适用**: 设置特定数值，大幅度修改
- **操作**: 点击后在聊天栏输入具体数值

## 支持的设置项

### 1. 游戏时长 (slot 10)
**鼠标操作模式:**
- **左键**: 减少1分钟
- **右键**: 增加1分钟
- **Shift+左键**: 减少10分钟
- **Shift+右键**: 增加10分钟

**聊天输入模式:**
- **点击**: 打开聊天输入界面
- **范围**: 1-180分钟

### 2. 世界种子 (slot 11)
**鼠标操作模式:**
- **左键**: 打开种子设置GUI（支持按钮输入、预设种子等）
- **右键**: 打开聊天输入设置具体种子
- **Shift+左键**: 快速设置为随机种子

**聊天输入模式:**
- **点击**: 直接进入聊天输入状态

### 3. 游戏难度 (slot 12)
**两种模式相同:**
- **任意点击**: 循环切换难度 (和平 → 简单 → 普通 → 困难)

### 4. 最大玩家数 (slot 13)
- **左键**: 减少1人
- **右键**: 增加1人
- **Shift+左键**: 减少5人
- **Shift+右键**: 增加5人
- **其他点击**: 打开聊天输入
- **范围**: 2-50人

### 5. 速通者人数限制 (slot 14)
- **左键**: 减少1人
- **右键**: 增加1人
- **其他点击**: 打开聊天输入
- **范围**: 1-10人

### 6. 捕猎者人数限制 (slot 15)
- **左键**: 减少1人
- **右键**: 增加1人
- **其他点击**: 打开聊天输入
- **范围**: 1-20人

### 7. 豁免时长 (slot 25)
- **左键**: 减少5秒
- **右键**: 增加5秒
- **Shift+左键**: 减少30秒
- **Shift+右键**: 增加30秒
- **其他点击**: 打开聊天输入
- **范围**: 0-300秒

### 8. 速通者生命数 (slot 38)
- **左键**: 减少1生命
- **右键**: 增加1生命
- **其他点击**: 打开聊天输入
- **范围**: 0-10 (0表示无限)

### 9. 捕猎者生命数 (slot 39)
- **左键**: 减少1生命
- **右键**: 增加1生命
- **其他点击**: 打开聊天输入
- **范围**: 0-10 (0表示无限)

### 10. 目标分数 (slot 40, 仅积分模式)
- **左键**: 减少50分
- **右键**: 增加50分
- **Shift+左键**: 减少100分
- **Shift+右键**: 增加100分
- **其他点击**: 打开聊天输入
- **范围**: 100-1000分

## 实现特点

### 1. 模式切换按钮
- **专用按钮**: 位于GUI右下角的专门切换按钮
- **视觉反馈**: 不同模式使用不同图标
- **状态显示**: 清楚显示当前操作模式
- **即时切换**: 点击即可立即切换模式

### 2. 智能模式识别
- **鼠标操作模式**: 根据点击类型执行相应操作
- **聊天输入模式**: 任何点击都打开聊天输入
- **模式记忆**: 切换后保持选择的模式

### 3. 动态界面更新
- **描述更新**: 物品描述根据当前模式动态显示
- **操作提示**: 清楚显示当前模式下的操作方式
- **实时反馈**: 模式切换后立即更新界面

### 4. 用户友好设计
- **灵活选择**: 用户可根据需要自由切换
- **清晰指引**: 每个设置项都显示当前模式和操作方式
- **保持状态**: 操作后GUI保持打开，便于连续调整
- **错误处理**: 边界值检查和友好的错误提示

## 技术实现

### 操作模式枚举
```java
public enum OperationMode {
    MOUSE("鼠标操作", "使用鼠标左右键快速调整数值"),
    CHAT("聊天输入", "在聊天栏中输入精确数值");
}
```

### 模式切换逻辑
```java
private void handleOperationModeToggle() {
    currentOperationMode = (currentOperationMode == OperationMode.MOUSE) ?
        OperationMode.CHAT : OperationMode.MOUSE;
    sendSuccess("操作模式已切换为: " + currentOperationMode.getDisplayName());
    refresh();
}
```

### 核心方法结构
每个数值设置都有三个方法：
1. `handle[Setting](boolean isLeftClick, boolean isRightClick, boolean isShiftClick)` - 主处理方法，根据当前模式分发
2. `handle[Setting]MouseOperation(...)` - 鼠标操作处理
3. `handle[Setting]ChatInput()` - 聊天输入处理

### 模式判断逻辑
```java
private void handleGameDuration(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
    if (currentOperationMode == OperationMode.MOUSE) {
        if (isLeftClick || isRightClick) {
            handleGameDurationMouseOperation(isLeftClick, isRightClick, isShiftClick);
        }
    } else {
        handleGameDurationChatInput();
    }
}
```

### 动态描述生成
```java
String gameDurationDesc = currentOperationMode == OperationMode.MOUSE ?
    "<gray>左键: -1分钟 | 右键: +1分钟\n<gray>Shift+左键: -10分钟 | Shift+右键: +10分钟" :
    "<gray>点击后在聊天栏输入具体数值";
```

## 用户体验改进

1. **自主选择**: 专门的切换按钮让用户完全控制操作方式
2. **视觉反馈**: 不同模式使用不同图标，一目了然
3. **即时反馈**: 鼠标操作立即生效，聊天输入精确设置
4. **智能提示**: 物品描述根据当前模式动态显示操作方法
5. **模式记忆**: 切换后保持选择，无需重复设置
6. **无缝切换**: 随时可以切换模式，适应不同使用场景
7. **错误处理**: 友好的错误消息和音效反馈
8. **保持状态**: 操作后GUI保持打开，便于连续调整

## 使用建议

### 何时使用鼠标操作模式
- 需要快速微调数值时
- 进行连续的小幅调整时
- 不确定具体数值，需要试验时
- 希望即时看到效果时

### 何时使用聊天输入模式
- 知道确切的目标数值时
- 需要大幅度调整时
- 希望一次性设置到位时
- 需要输入特殊值（如种子）时
