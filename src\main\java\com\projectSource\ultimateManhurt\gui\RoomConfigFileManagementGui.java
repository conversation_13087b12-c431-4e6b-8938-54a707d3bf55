package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.room.RoomSettings;
import com.projectSource.ultimateManhurt.room.RoomSettingsManager;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

/**
 * 房间配置文件管理GUI
 * 用于保存、加载和预览房间设置配置文件
 */
public class RoomConfigFileManagementGui extends BaseGui {
    
    private final Room room;
    private final RoomSettings settings;
    private final RoomSettingsManager settingsManager;
    
    public RoomConfigFileManagementGui(UltimateManhurt plugin, Player player, Room room) {
        super(plugin, player, "<gold><bold>配置文件管理 - " + room.getName(), 54);
        this.room = room;
        this.settings = room.getSettings();
        this.settingsManager = plugin.getRoomSettingsManager();
        setupGui();
    }
    
    @Override
    protected void setupGui() {
        // 检查权限
        if (!room.isOwner(player.getUniqueId())) {
            setItem(22, createItem(Material.BARRIER, "<red>权限不足", 
                "<gray>只有房主可以管理配置文件"));
            return;
        }
        
        // 填充边框
        fillBorder(Material.GRAY_STAINED_GLASS_PANE);
        
        // 标题
        setItem(4, createItem(Material.BOOKSHELF, "<gold><bold>配置文件管理",
            "<gray>保存、加载和预览房间设置配置文件",
            "<yellow>管理你的房间配置预设"));
        
        // 功能按钮
        setupFunctionButtons();
        
        // 显示已有的配置文件
        displayConfigFiles();
        
        // 控制按钮
        setupControlButtons();
    }
    
    /**
     * 设置功能按钮
     */
    private void setupFunctionButtons() {
        // 保存当前设置按钮
        setItem(19, createItem(Material.EMERALD_BLOCK, "<green><bold>保存当前设置",
            "<gray>将当前房间设置保存为配置文件",
            "<gray>文件名将自动生成",
            "<yellow>点击保存"));
        
        // 当前设置预览
        setItem(22, createItem(Material.BOOK, "<blue><bold>当前设置预览",
            "<gray>游戏时长: <white>" + settings.getGameDurationMinutes() + "分钟",
            "<gray>最大玩家: <white>" + settings.getMaxPlayers() + "人",
            "<gray>速通者: <white>" + settings.getMaxSpeedrunners() + "人",
            "<gray>捕猎者: <white>" + settings.getMaxHunters() + "人",
            "<gray>难度: <white>" + settings.getDifficulty().name(),
            "<gray>胜利模式: <white>" + settings.getVictoryMode().name(),
            "<gray>PVP: <white>" + (settings.isPvpEnabled() ? "启用" : "禁用"),
            "<gray>装备包: <white>" + (settings.isStartKitEnabled() ? "启用" : "禁用")));
        
        // 帮助信息
        setItem(25, createItem(Material.KNOWLEDGE_BOOK, "<aqua><bold>使用说明",
            "<gray>• 点击 <green>保存当前设置 <gray>来保存配置",
            "<gray>• 点击配置文件来加载或预览",
            "<gray>• 右键点击配置文件来删除",
            "<gray>• 配置文件名自动生成",
            "<gray>• 格式: 房间名_日期时间"));
    }
    
    /**
     * 显示配置文件列表
     */
    private void displayConfigFiles() {
        List<String> availableFiles = settingsManager.getAvailableSettings();
        
        if (availableFiles.isEmpty()) {
            setItem(31, createItem(Material.BARRIER, "<red>没有找到配置文件",
                "<gray>还没有保存过任何房间设置",
                "<yellow>点击上方的保存按钮来创建第一个配置文件"));
            return;
        }
        
        // 显示配置文件列表（最多显示18个文件）
        int[] slots = {
            28, 29, 30, 31, 32, 33, 34,
            37, 38, 39, 40, 41, 42, 43
        };
        
        for (int i = 0; i < Math.min(availableFiles.size(), slots.length); i++) {
            String fileName = availableFiles.get(i);
            RoomSettingsManager.SettingsFileInfo info = settingsManager.getSettingsInfo(fileName);
            
            if (info != null) {
                setItem(slots[i], createItem(Material.WRITTEN_BOOK, "<aqua><bold>" + fileName,
                    "<gray>创建者: <white>" + getPlayerName(info.getCreator()),
                    "<gray>创建时间: <white>" + formatDateTime(info.getCreatedAt()),
                    "<gray>版本: <white>" + info.getVersion(),
                    "",
                    "<yellow>左键点击加载此配置",
                    "<blue>中键点击预览此配置",
                    "<red>右键点击删除此配置"));
            } else {
                setItem(slots[i], createItem(Material.WRITTEN_BOOK, "<aqua><bold>" + fileName,
                    "<gray>文件信息获取失败",
                    "",
                    "<yellow>左键点击加载此配置",
                    "<red>右键点击删除此配置"));
            }
        }
        
        // 如果文件太多，显示提示
        if (availableFiles.size() > slots.length) {
            setItem(44, createItem(Material.ARROW, "<yellow>还有更多文件...",
                "<gray>共有 " + availableFiles.size() + " 个配置文件",
                "<gray>只显示前 " + slots.length + " 个"));
        }
    }
    
    /**
     * 设置控制按钮
     */
    private void setupControlButtons() {
        // 返回按钮
        setItem(49, createBackButton());
        
        // 刷新按钮
        setItem(45, createItem(Material.LIME_DYE, "<green>刷新列表",
            "<gray>重新加载配置文件列表"));
        
        // 清理按钮
        setItem(53, createItem(Material.LAVA_BUCKET, "<red>清理所有文件",
            "<gray>删除所有配置文件",
            "<red>此操作不可撤销！"));
    }
    
    @Override
    public void handleClick(InventoryClickEvent event) {
        event.setCancelled(true);
        
        if (event.getClickedInventory() != inventory) {
            return;
        }
        
        int slot = event.getSlot();
        ItemStack item = event.getCurrentItem();
        
        if (item == null || item.getType() == Material.AIR) {
            return;
        }
        
        playClickSound();
        
        // 检查权限
        if (!room.isOwner(player.getUniqueId())) {
            sendError("只有房主可以管理配置文件！");
            return;
        }
        
        boolean isLeftClick = event.isLeftClick();
        boolean isRightClick = event.isRightClick();
        boolean isMiddleClick = event.getClick().isShiftClick() && event.isLeftClick(); // Shift+左键作为中键
        
        switch (slot) {
            case 19: // 保存当前设置
                handleSaveCurrentSettings();
                break;
            case 45: // 刷新
                refresh();
                sendInfo("已刷新配置文件列表");
                break;
            case 49: // 返回
                close();
                plugin.getGuiManager().openRoomSettingsGui(player, room);
                break;
            case 53: // 清理所有文件
                handleClearAllFiles();
                break;
            default:
                // 检查是否点击了配置文件
                handleFileClick(slot, isLeftClick, isRightClick, isMiddleClick);
                break;
        }
    }
    
    /**
     * 处理保存当前设置
     */
    private void handleSaveCurrentSettings() {
        // 自动生成文件名
        String fileName = generateFileName();
        
        boolean success = settingsManager.saveSettings(settings, fileName, player.getUniqueId());
        if (success) {
            sendSuccess("成功保存配置文件: " + fileName);
            playSuccessSound();
            refresh(); // 刷新列表显示新文件
        } else {
            sendError("保存配置文件失败！");
            playErrorSound();
        }
    }
    
    /**
     * 自动生成文件名
     */
    private String generateFileName() {
        String roomName = room.getName().replaceAll("[^a-zA-Z0-9_\\-\\u4e00-\\u9fa5]", "_");
        String dateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return roomName + "_" + dateTime;
    }
    
    /**
     * 处理文件点击
     */
    private void handleFileClick(int slot, boolean isLeftClick, boolean isRightClick, boolean isMiddleClick) {
        // 获取文件列表中对应的文件
        List<String> availableFiles = settingsManager.getAvailableSettings();
        int[] slots = {
            28, 29, 30, 31, 32, 33, 34,
            37, 38, 39, 40, 41, 42, 43
        };
        
        int fileIndex = -1;
        for (int i = 0; i < slots.length; i++) {
            if (slots[i] == slot) {
                fileIndex = i;
                break;
            }
        }
        
        if (fileIndex == -1 || fileIndex >= availableFiles.size()) {
            return;
        }
        
        String fileName = availableFiles.get(fileIndex);
        
        if (isLeftClick) {
            // 左键：加载配置
            handleLoadFile(fileName);
        } else if (isRightClick) {
            // 右键：删除配置
            handleDeleteFile(fileName);
        } else if (isMiddleClick) {
            // 中键（Shift+左键）：预览配置
            handlePreviewFile(fileName);
        }
    }
    
    /**
     * 处理加载文件
     */
    private void handleLoadFile(String fileName) {
        RoomSettings loadedSettings = settingsManager.loadSettings(fileName);
        if (loadedSettings != null) {
            // 将加载的设置复制到当前房间设置
            copySettingsTo(loadedSettings, settings);
            sendSuccess("成功加载配置文件: " + fileName);
            playSuccessSound();
            
            // 返回房间设置界面
            close();
            plugin.getGuiManager().openRoomSettingsGui(player, room);
        } else {
            sendError("加载配置文件失败！");
            playErrorSound();
        }
    }
    
    /**
     * 处理删除文件
     */
    private void handleDeleteFile(String fileName) {
        boolean success = settingsManager.deleteSettings(fileName);
        if (success) {
            sendSuccess("成功删除配置文件: " + fileName);
            playSuccessSound();
            refresh(); // 刷新列表
        } else {
            sendError("删除配置文件失败！");
            playErrorSound();
        }
    }
    
    /**
     * 处理预览文件
     */
    private void handlePreviewFile(String fileName) {
        close();
        plugin.getGuiManager().openRoomConfigFilePreviewGui(player, room, fileName);
    }
    
    /**
     * 处理清理所有文件
     */
    private void handleClearAllFiles() {
        List<String> availableFiles = settingsManager.getAvailableSettings();
        if (availableFiles.isEmpty()) {
            sendInfo("没有配置文件需要清理");
            return;
        }

        int deletedCount = 0;
        for (String fileName : availableFiles) {
            if (settingsManager.deleteSettings(fileName)) {
                deletedCount++;
            }
        }

        if (deletedCount > 0) {
            sendSuccess("成功删除 " + deletedCount + " 个配置文件");
            playSuccessSound();
            refresh();
        } else {
            sendError("清理配置文件失败！");
            playErrorSound();
        }
    }

    /**
     * 将源设置复制到目标设置
     */
    private void copySettingsTo(RoomSettings source, RoomSettings target) {
        // 基础设置
        target.setGameDurationMinutes(source.getGameDurationMinutes());
        target.setWorldSeed(source.getWorldSeed());
        target.setDifficulty(source.getDifficulty());
        target.setSpectatorGameMode(source.getSpectatorGameMode());

        // 玩家设置
        target.setMaxPlayers(source.getMaxPlayers());
        target.setMaxSpeedrunners(source.getMaxSpeedrunners());
        target.setMaxHunters(source.getMaxHunters());
        target.setAllowSpectators(source.isAllowSpectators());

        // 游戏规则
        target.setPvpEnabled(source.isPvpEnabled());
        target.setFriendlyFire(source.isFriendlyFire());
        target.setKeepInventory(source.isKeepInventory());
        target.setNaturalRegeneration(source.isNaturalRegeneration());
        target.setShowDeathMessages(source.isShowDeathMessages());

        // 世界设置
        target.setGenerateStructures(source.isGenerateStructures());
        target.setGenerateBonusChest(source.isGenerateBonusChest());
        target.setEnableCommandBlocks(source.isEnableCommandBlocks());
        target.setDoDaylightCycle(source.isDoDaylightCycle());
        target.setDoWeatherCycle(source.isDoWeatherCycle());
        target.setCustomSpawnLogic(source.isCustomSpawnLogic());

        // 特殊功能
        target.setCompassTracking(source.isCompassTracking());
        target.setCompassUpdateInterval(source.getCompassUpdateInterval());
        target.setLocatorBar(source.isLocatorBar());
        target.setEnderPearlCooldown(source.isEnderPearlCooldown());
        target.setEnderPearlCooldownSeconds(source.getEnderPearlCooldownSeconds());
        target.setNetherPortalDelay(source.isNetherPortalDelay());
        target.setNetherPortalDelaySeconds(source.getNetherPortalDelaySeconds());

        // Ban Pick系统
        target.setBanPickEnabled(source.isBanPickEnabled());
        target.setBanPickPhaseTimeSeconds(source.getBanPickPhaseTimeSeconds());

        // 职业系统
        target.setProfessionSystemEnabled(source.isProfessionSystemEnabled());

        // 豁免设置
        target.setImmunityEnabled(source.isImmunityEnabled());
        target.setImmunityDurationSeconds(source.getImmunityDurationSeconds());

        // 胜利条件
        target.setDragonRespawn(source.isDragonRespawn());
        target.setTimeoutHuntersWin(source.isTimeoutHuntersWin());
        target.setSpeedrunnerLives(source.getSpeedrunnerLives());
        target.setHunterLives(source.getHunterLives());
        target.setAllowSpeedrunnerRespawn(source.isAllowSpeedrunnerRespawn());
        target.setAllowHunterRespawn(source.isAllowHunterRespawn());

        // 血量设置
        target.setSpeedrunnerMaxHealth(source.getSpeedrunnerMaxHealth());
        target.setHunterMaxHealth(source.getHunterMaxHealth());

        // 胜利模式设置
        target.setVictoryMode(source.getVictoryMode());
        target.setTargetScore(source.getTargetScore());

        // 守卫模式设置
        target.setWitherMaxHealth(source.getWitherMaxHealth());
        target.setWitherHealingReduction(source.getWitherHealingReduction());
        target.setWitherAttackInterval(source.getWitherAttackInterval());
        target.setWitherAttackDamage(source.getWitherAttackDamage());
        target.setWitherShieldDuration(source.getWitherShieldDuration());
        target.setWitherShieldReduction(source.getWitherShieldReduction());
        target.setHunterSpawnDistance(source.getHunterSpawnDistance());
        target.setWitherCanMove(source.isWitherCanMove());
        target.setWitherTargetHuntersOnly(source.isWitherTargetHuntersOnly());
        target.setWitherDestroyBlocks(source.isWitherDestroyBlocks());
        target.setWitherEffectDuration(source.getWitherEffectDuration());
        target.setWitherEffectLevel(source.getWitherEffectLevel());

        // StartKit设置
        target.setStartKitEnabled(source.isStartKitEnabled());
        if (source.getSpeedrunnerKit() != null) {
            target.setSpeedrunnerKit(new com.projectSource.ultimateManhurt.kit.StartKit(source.getSpeedrunnerKit()));
        }
        if (source.getHunterKit() != null) {
            target.setHunterKit(new com.projectSource.ultimateManhurt.kit.StartKit(source.getHunterKit()));
        }

        // 里程碑设置
        if (source.getMilestoneSettings() != null) {
            target.setMilestoneSettings(source.getMilestoneSettings());
        }

        // 自定义规则
        target.getCustomRules().clear();
        target.getCustomRules().putAll(source.getCustomRules());
    }

    /**
     * 获取玩家名称
     */
    private String getPlayerName(String uuidString) {
        try {
            UUID uuid = UUID.fromString(uuidString);
            String name = plugin.getPlayerCacheManager().getPlayerName(uuid);
            return name != null ? name : "未知玩家";
        } catch (Exception e) {
            return "未知玩家";
        }
    }

    /**
     * 格式化日期时间
     */
    private String formatDateTime(String isoDateTime) {
        try {
            LocalDateTime dateTime = LocalDateTime.parse(isoDateTime);
            return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            return isoDateTime;
        }
    }
}
