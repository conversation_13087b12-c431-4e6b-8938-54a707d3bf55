package com.projectSource.ultimateManhurt.kit;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.PlayerInventory;

import java.util.Map;
import java.util.UUID;

/**
 * StartKit管理器
 * 负责装备包的分发和管理
 */
public class StartKitManager {
    
    private final UltimateManhurt plugin;
    
    public StartKitManager(UltimateManhurt plugin) {
        this.plugin = plugin;
    }
    
    /**
     * 给玩家分发起始装备
     */
    public void giveStartKit(Player player, Room room) {
        if (room == null || !room.getSettings().isStartKitEnabled()) {
            return;
        }
        
        UUID playerUUID = player.getUniqueId();
        PlayerRole role = room.getPlayerRole(playerUUID);
        
        if (role == null || role == PlayerRole.SPECTATOR) {
            return; // 观察者不获得装备
        }
        
        StartKit kit = getKitForRole(room, role);

        // 清空玩家背包
        player.getInventory().clear();

        // 分发装备（如果装备包不为空）
        if (kit != null && !kit.isEmpty()) {
            distributeKit(player, kit);

            // 通知玩家获得装备包
            ComponentUtil.sendMessage(player, ComponentUtil.success(
                "已获得起始装备: " + kit.getName()));
            plugin.getLogger().info("为玩家 " + player.getName() + " 分发了起始装备: " + kit.getName());
        } else {
            // 装备包为空时的通知
            ComponentUtil.sendMessage(player, ComponentUtil.info("使用空装备包，仅发放基础物品"));
            plugin.getLogger().info("为玩家 " + player.getName() + " 使用了空装备包");
        }

        // 为速通者发放里程碑指南书籍（仅在积分模式和混合模式下）
        if (role == PlayerRole.SPEEDRUNNER && room.getSettings().getVictoryMode().requiresScoreSystem()) {
            giveMilestoneGuideBook(player, room);
        }

        // 为猎人发放特殊指南针（死亡时不会掉落）
        if (role == PlayerRole.HUNTER) {
            giveHunterCompass(player, room);
        }
    }
    
    /**
     * 给房间内所有玩家分发起始装备
     */
    public void giveStartKitToAll(Room room) {
        if (room == null || !room.getSettings().isStartKitEnabled()) {
            return;
        }
        
        for (UUID playerUUID : room.getPlayers()) {
            Player player = plugin.getServer().getPlayer(playerUUID);
            if (player != null && player.isOnline()) {
                giveStartKit(player, room);
            }
        }
        
        plugin.getLogger().info("为房间 " + room.getName() + " 的所有玩家分发了起始装备");
    }
    
    /**
     * 根据角色获取装备包
     */
    private StartKit getKitForRole(Room room, PlayerRole role) {
        switch (role) {
            case SPEEDRUNNER:
                return room.getSettings().getSpeedrunnerKit();
            case HUNTER:
                return room.getSettings().getHunterKit();
            default:
                return null;
        }
    }
    
    /**
     * 分发装备包给玩家
     */
    private void distributeKit(Player player, StartKit kit) {
        PlayerInventory inventory = player.getInventory();
        
        // 分发主要物品到指定槽位
        Map<Integer, ItemStack> items = kit.getItems();
        for (Map.Entry<Integer, ItemStack> entry : items.entrySet()) {
            int slot = entry.getKey();
            ItemStack item = entry.getValue();
            
            if (slot >= 0 && slot < 36) { // 确保槽位在有效范围内
                inventory.setItem(slot, item.clone());
            }
        }
        
        // 分发额外物品到背包空槽位
        for (ItemStack extraItem : kit.getExtraItems()) {
            // 尝试添加到背包，如果背包满了会掉落
            inventory.addItem(extraItem.clone());
        }
        
        // 更新玩家界面
        player.updateInventory();
    }
    
    /**
     * 预览装备包内容
     */
    public void previewKit(Player player, StartKit kit) {
        if (kit == null) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("装备包不存在"));
            return;
        }
        
        ComponentUtil.sendMessage(player, ComponentUtil.info("装备包预览: " + kit.getName()));
        ComponentUtil.sendMessage(player, ComponentUtil.parse("<gray>" + kit.getDescription()));
        
        if (kit.isEmpty()) {
            ComponentUtil.sendMessage(player, ComponentUtil.parse("<gray>此装备包为空"));
            return;
        }
        
        // 显示主要物品
        Map<Integer, ItemStack> items = kit.getItems();
        if (!items.isEmpty()) {
            ComponentUtil.sendMessage(player, ComponentUtil.parse("<yellow>主要装备:"));
            for (Map.Entry<Integer, ItemStack> entry : items.entrySet()) {
                ItemStack item = entry.getValue();
                String itemName = getItemDisplayName(item);
                ComponentUtil.sendMessage(player, ComponentUtil.parse(
                    "<gray>  槽位 " + entry.getKey() + ": <white>" + itemName));
            }
        }
        
        // 显示额外物品
        if (!kit.getExtraItems().isEmpty()) {
            ComponentUtil.sendMessage(player, ComponentUtil.parse("<yellow>额外物品:"));
            for (ItemStack item : kit.getExtraItems()) {
                String itemName = getItemDisplayName(item);
                ComponentUtil.sendMessage(player, ComponentUtil.parse("<gray>  <white>" + itemName));
            }
        }
        
        ComponentUtil.sendMessage(player, ComponentUtil.parse("<gray>装备包摘要: " + kit.getSummary()));
    }
    
    /**
     * 获取物品显示名称
     */
    private String getItemDisplayName(ItemStack item) {
        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            return item.getItemMeta().getDisplayName();
        }
        
        String materialName = item.getType().name().toLowerCase().replace('_', ' ');
        if (item.getAmount() > 1) {
            return materialName + " x" + item.getAmount();
        }
        return materialName;
    }
    
    /**
     * 验证装备包
     */
    public boolean validateKit(StartKit kit) {
        if (kit == null) {
            return false;
        }
        
        // 检查槽位是否有效
        for (Integer slot : kit.getItems().keySet()) {
            if (slot < 0 || slot >= 36) {
                return false;
            }
        }
        
        // 检查物品是否有效
        for (ItemStack item : kit.getItems().values()) {
            if (item == null || item.getType().isAir()) {
                return false;
            }
        }
        
        for (ItemStack item : kit.getExtraItems()) {
            if (item == null || item.getType().isAir()) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 重置玩家装备（移除所有物品）
     */
    public void clearPlayerInventory(Player player) {
        player.getInventory().clear();
        player.updateInventory();
        
        ComponentUtil.sendMessage(player, ComponentUtil.info("背包已清空"));
    }
    
    /**
     * 检查玩家是否已有起始装备
     */
    public boolean hasStartKit(Player player, Room room) {
        if (room == null || !room.getSettings().isStartKitEnabled()) {
            return false;
        }

        PlayerRole role = room.getPlayerRole(player.getUniqueId());
        if (role == null || role == PlayerRole.SPECTATOR) {
            return false;
        }

        // 简单检查：如果背包不为空，认为已有装备
        // 注意：即使是空装备包，也可能有book和指南针，所以不能直接返回false
        return !player.getInventory().isEmpty();
    }
    
    /**
     * 获取装备包统计信息
     */
    public String getKitStats(StartKit kit) {
        if (kit == null) {
            return "装备包不存在";
        }
        
        if (kit.isEmpty()) {
            return "空装备包";
        }
        
        int mainItems = kit.getItems().size();
        int extraItems = kit.getExtraItems().size();
        int totalItems = kit.getItemCount();
        
        return String.format("主要装备: %d件, 额外物品: %d件, 总计: %d件",
            mainItems, extraItems, totalItems);
    }

    /**
     * 为速通者发放里程碑指南书籍（仅在积分模式和混合模式下）
     */
    private void giveMilestoneGuideBook(Player player, Room room) {
        try {
            // 创建里程碑指南书籍
            org.bukkit.inventory.ItemStack guideBook =
                com.projectSource.ultimateManhurt.game.scoring.MilestoneGuideBook.createGuideBook(room);

            // 尝试添加到背包，如果背包满了会掉落
            player.getInventory().addItem(guideBook);

            // 通知玩家
            ComponentUtil.sendMessage(player, ComponentUtil.info(
                "📖 已获得里程碑指南书籍！右键阅读了解所有里程碑信息。"));

            plugin.getLogger().info("为速通者 " + player.getName() + " 发放了里程碑指南书籍（胜利模式: " +
                room.getSettings().getVictoryMode().getDisplayName() + "）");

        } catch (Exception e) {
            plugin.getLogger().warning("为玩家 " + player.getName() + " 发放里程碑指南书籍时出错: " + e.getMessage());
        }
    }

    /**
     * 为猎人发放特殊指南针（死亡时不会掉落）
     */
    private void giveHunterCompass(Player player, Room room) {
        try {
            // 创建特殊的猎人指南针
            org.bukkit.inventory.ItemStack hunterCompass =
                com.projectSource.ultimateManhurt.util.CompassUtil.createHunterCompass();

            // 尝试添加到背包，如果背包满了会掉落
            player.getInventory().addItem(hunterCompass);

            // 通知玩家
            ComponentUtil.sendMessage(player, ComponentUtil.info(
                "🧭 已获得猎人专用指南针！死亡时不会掉落。"));

            plugin.getLogger().info("为猎人 " + player.getName() + " 发放了特殊指南针");

        } catch (Exception e) {
            plugin.getLogger().warning("为玩家 " + player.getName() + " 发放猎人指南针时出错: " + e.getMessage());
        }
    }
}
