package com.projectSource.ultimateManhurt.util;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextColor;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 时间工具类
 * 提供时间格式化、计算和显示功能
 */
public class TimeUtil {
    
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    
    /**
     * 将秒数格式化为可读的时间字符串
     */
    public static String formatTime(long seconds) {
        if (seconds < 0) {
            return "00:00";
        }
        
        long hours = seconds / 3600;
        long minutes = (seconds % 3600) / 60;
        long secs = seconds % 60;
        
        if (hours > 0) {
            return String.format("%02d:%02d:%02d", hours, minutes, secs);
        } else {
            return String.format("%02d:%02d", minutes, secs);
        }
    }
    
    /**
     * 将毫秒数格式化为可读的时间字符串
     */
    public static String formatTimeMillis(long millis) {
        return formatTime(millis / 1000);
    }
    
    /**
     * 将Duration格式化为可读的时间字符串
     */
    public static String formatDuration(Duration duration) {
        return formatTime(duration.getSeconds());
    }
    
    /**
     * 创建带颜色的时间组件
     */
    public static Component createTimeComponent(long seconds) {
        String timeStr = formatTime(seconds);
        TextColor color = getTimeColor(seconds);
        return Component.text(timeStr, color);
    }
    
    /**
     * 根据剩余时间获取颜色
     */
    public static TextColor getTimeColor(long seconds) {
        if (seconds <= 30) {
            return NamedTextColor.RED; // 红色 - 紧急
        } else if (seconds <= 60) {
            return NamedTextColor.YELLOW; // 黄色 - 警告
        } else if (seconds <= 300) { // 5分钟
            return NamedTextColor.GOLD; // 金色 - 注意
        } else {
            return NamedTextColor.GREEN; // 绿色 - 正常
        }
    }
    
    /**
     * 创建倒计时组件
     */
    public static Component createCountdownComponent(long seconds) {
        if (seconds <= 0) {
            return Component.text("时间到！", NamedTextColor.RED);
        }
        
        String timeStr = formatTime(seconds);
        TextColor color = getTimeColor(seconds);
        
        return Component.text()
                .append(Component.text("剩余时间: ", NamedTextColor.GRAY))
                .append(Component.text(timeStr, color))
                .build();
    }
    
    /**
     * 创建游戏时长组件
     */
    public static Component createGameDurationComponent(long seconds) {
        String timeStr = formatTime(seconds);
        return Component.text()
                .append(Component.text("游戏时长: ", NamedTextColor.GRAY))
                .append(Component.text(timeStr, NamedTextColor.AQUA))
                .build();
    }
    
    /**
     * 解析时间字符串为秒数
     * 支持格式: "5m", "30s", "1h30m", "90", "1:30", "1:30:45"
     */
    public static long parseTimeToSeconds(String timeStr) {
        timeStr = timeStr.toLowerCase().trim();
        
        // 如果只是数字，默认为秒
        if (timeStr.matches("\\d+")) {
            return Long.parseLong(timeStr);
        }
        
        // 处理 HH:MM:SS 或 MM:SS 格式
        if (timeStr.matches("\\d{1,2}:\\d{2}(:\\d{2})?")) {
            String[] parts = timeStr.split(":");
            if (parts.length == 2) {
                // MM:SS
                return Long.parseLong(parts[0]) * 60 + Long.parseLong(parts[1]);
            } else if (parts.length == 3) {
                // HH:MM:SS
                return Long.parseLong(parts[0]) * 3600 + Long.parseLong(parts[1]) * 60 + Long.parseLong(parts[2]);
            }
        }
        
        // 处理带单位的格式 (如 1h30m45s)
        long totalSeconds = 0;
        String[] units = {"h", "m", "s"};
        int[] multipliers = {3600, 60, 1};
        
        for (int i = 0; i < units.length; i++) {
            String unit = units[i];
            int multiplier = multipliers[i];
            
            int index = timeStr.indexOf(unit);
            if (index > 0) {
                // 找到数字部分
                int start = index - 1;
                while (start >= 0 && Character.isDigit(timeStr.charAt(start))) {
                    start--;
                }
                start++;
                
                String numberStr = timeStr.substring(start, index);
                if (!numberStr.isEmpty()) {
                    totalSeconds += Long.parseLong(numberStr) * multiplier;
                }
            }
        }
        
        return totalSeconds > 0 ? totalSeconds : 0;
    }
    
    /**
     * 获取当前时间字符串
     */
    public static String getCurrentTimeString() {
        return LocalDateTime.now().format(DATE_TIME_FORMATTER);
    }
    
    /**
     * 获取当前时间（仅时分秒）
     */
    public static String getCurrentTime() {
        return LocalDateTime.now().format(TIME_FORMATTER);
    }
    
    /**
     * 计算两个时间之间的差值（秒）
     */
    public static long getTimeDifferenceSeconds(LocalDateTime start, LocalDateTime end) {
        return Duration.between(start, end).getSeconds();
    }
    
    /**
     * 检查是否超时
     */
    public static boolean isTimeout(LocalDateTime startTime, long timeoutSeconds) {
        return getTimeDifferenceSeconds(startTime, LocalDateTime.now()) >= timeoutSeconds;
    }
    
    /**
     * 获取剩余时间（秒）
     */
    public static long getRemainingSeconds(LocalDateTime startTime, long totalSeconds) {
        long elapsed = getTimeDifferenceSeconds(startTime, LocalDateTime.now());
        return Math.max(0, totalSeconds - elapsed);
    }
    
    /**
     * 创建进度条组件
     */
    public static Component createProgressBar(long current, long total, int barLength) {
        if (total <= 0) {
            return Component.text("N/A", NamedTextColor.GRAY);
        }
        
        double percentage = (double) current / total;
        int filledLength = (int) (barLength * percentage);
        
        StringBuilder bar = new StringBuilder();
        
        // 已完成部分
        for (int i = 0; i < filledLength; i++) {
            bar.append("█");
        }
        
        // 未完成部分
        for (int i = filledLength; i < barLength; i++) {
            bar.append("░");
        }
        
        TextColor color = ColorUtil.getPercentageColor(percentage);
        
        return Component.text()
                .append(Component.text("[", NamedTextColor.GRAY))
                .append(Component.text(bar.toString(), color))
                .append(Component.text("] ", NamedTextColor.GRAY))
                .append(Component.text(String.format("%.1f%%", percentage * 100), color))
                .build();
    }
}
