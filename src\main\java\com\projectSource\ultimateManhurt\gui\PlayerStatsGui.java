package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.data.PlayerData;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 玩家统计GUI
 */
public class PlayerStatsGui extends BaseGui {
    
    private final UUID targetPlayerId;
    private final PlayerData playerData;
    
    public PlayerStatsGui(UltimateManhurt plugin, Player player, UUID targetPlayerId) {
        super(plugin, player, "玩家统计", 54);
        this.targetPlayerId = targetPlayerId;
        this.playerData = plugin.getDataManager().getPlayerData(targetPlayerId);
        
        if (playerData == null) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("无法加载玩家数据！"));
            return;
        }
        
        setupGui();
    }

    @Override
    protected void setupGui() {
        // 玩家头像和基本信息（使用缓存）
        String playerName = plugin.getPlayerCacheManager().getPlayerName(targetPlayerId);

        // 获取段位信息
        int elo = com.projectSource.ultimateManhurt.ranking.EloSystem.getPlayerElo(playerData);
        com.projectSource.ultimateManhurt.ranking.EloSystem.Rank rank =
            com.projectSource.ultimateManhurt.ranking.EloSystem.getRank(elo);

        setItem(4, createPlayerHead(targetPlayerId, "<gold><bold>" + playerName + " 的统计",
            "<gray>段位: " + rank.getIcon() + " " + rank.getColoredName(),
            "<gray>ELO: <yellow>" + elo,
            "",
            "<gray>首次加入: <white>" + (playerData.getFirstJoin() != null ?
                playerData.getFirstJoin().toLocalDate().toString() : "未知"),
            "<gray>最后在线: <white>" + (playerData.getLastSeen() != null ?
                playerData.getLastSeen().toLocalDate().toString() : "未知")
        ));
        
        // 游戏统计
        setItem(20, createItem(Material.DIAMOND_SWORD, "<aqua><bold>游戏统计",
            "<gray>总游戏数: <white>" + playerData.getTotalGames(),
            "<gray>胜利: <green>" + playerData.getGamesWon(),
            "<gray>失败: <red>" + playerData.getGamesLost(),
            "<gray>胜率: <yellow>" + String.format("%.1f%%", playerData.getWinRate() * 100),
            "",
            "<gray>总击杀: <white>" + playerData.getTotalKills(),
            "<gray>总死亡: <white>" + playerData.getTotalDeaths(),
            "<gray>K/D比: <yellow>" + String.format("%.2f", playerData.getKDRatio()),
            "",
            "<gray>总游戏时长: <white>" + formatPlayTime(playerData.getTotalPlayTime())
        ));
        
        // 速通者统计
        setItem(22, createItem(Material.EMERALD, "<green><bold>速通者统计",
            "<gray>速通者游戏: <white>" + playerData.getSpeedrunnerGames(),
            "<gray>速通者胜利: <green>" + playerData.getSpeedrunnerWins(),
            "<gray>速通者胜率: <yellow>" + String.format("%.1f%%", playerData.getSpeedrunnerWinRate() * 100)
        ));
        
        // 捕猎者统计
        setItem(24, createItem(Material.REDSTONE, "<red><bold>捕猎者统计",
            "<gray>捕猎者游戏: <white>" + playerData.getHunterGames(),
            "<gray>捕猎者胜利: <green>" + playerData.getHunterWins(),
            "<gray>捕猎者胜率: <yellow>" + String.format("%.1f%%", playerData.getHunterWinRate() * 100)
        ));
        
        // 成就记录
        List<String> recordsLore = new ArrayList<>();
        recordsLore.add("<gray>最快击龙: <yellow>" + 
            (playerData.getFastestDragonKill() != Long.MAX_VALUE ? 
                formatTime(playerData.getFastestDragonKill()) : "未记录"));
        recordsLore.add("<gray>最长存活: <yellow>" + 
            (playerData.getLongestSurvival() > 0 ? 
                formatTime(playerData.getLongestSurvival() * 1000L) : "未记录"));
        recordsLore.add("<gray>最大连杀: <yellow>" + 
            (playerData.getMaxKillStreak() > 0 ? 
                playerData.getMaxKillStreak() : "未记录"));
        
        setItem(40, createItem(Material.GOLDEN_APPLE, "<gold><bold>成就记录",
            recordsLore.toArray(new String[0])));

        // 返回按钮
        setItem(49, createItem(Material.BARRIER, "<red>返回", "<gray>点击返回上一页"));

        // 装饰性物品
        for (int i = 0; i < inventory.getSize(); i++) {
            if (inventory.getItem(i) == null) {
                setItem(i, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));
            }
        }
    }
    
    @Override
    public void handleClick(InventoryClickEvent event) {
        event.setCancelled(true);

        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        if (event.getClickedInventory() != this.inventory) {
            return;
        }

        ItemStack item = event.getCurrentItem();
        if (item == null || item.getType() == Material.AIR) {
            return;
        }

        this.playClickSound();

        int slot = event.getSlot();

        switch (slot) {
            case 49: // 返回按钮
                this.player.closeInventory();
                break;
            default:
                // 其他槽位不做任何操作，只是防止物品被拿出
                break;
        }
    }
    
    /**
     * 格式化游戏时长
     */
    private String formatPlayTime(long milliseconds) {
        long seconds = milliseconds / 1000;
        long hours = seconds / 3600;
        long minutes = (seconds % 3600) / 60;
        long secs = seconds % 60;
        
        if (hours > 0) {
            return String.format("%d小时%d分钟", hours, minutes);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, secs);
        } else {
            return String.format("%d秒", secs);
        }
    }
    
    /**
     * 格式化时间
     */
    private String formatTime(long milliseconds) {
        long seconds = milliseconds / 1000;
        long minutes = seconds / 60;
        long secs = seconds % 60;
        
        if (minutes > 0) {
            return String.format("%d分%d秒", minutes, secs);
        } else {
            return String.format("%d秒", secs);
        }
    }
    
    /**
     * 创建玩家头像物品
     */
    private ItemStack createPlayerHead(UUID playerId, String name, String... lore) {
        // 使用缓存管理器的专用方法创建头颅
        return plugin.getPlayerCacheManager().createPlayerHeadWithMeta(playerId, name, lore);
    }
}
