# 自定义出生点逻辑配置

## 📋 概述

为了解决玩家有时出生在地下的问题，我们添加了一个新的房间配置选项：**自定义出生点逻辑**。这个选项允许房主选择是否启用改进的出生点查找算法。

## ⚙️ 配置选项

### 新增配置项

在 `RoomSettings` 类中新增了以下配置：

```java
private boolean customSpawnLogic = false; // 是否启用自定义出生点逻辑
```

### 访问方法

```java
// 获取设置
boolean isEnabled = settings.isCustomSpawnLogic();

// 设置选项
settings.setCustomSpawnLogic(true);  // 启用
settings.setCustomSpawnLogic(false); // 禁用（默认）
```

## 🔄 工作原理

### 当 `customSpawnLogic = false`（默认）

- **世界出生点**：使用原版Minecraft的世界出生点逻辑
- **捕猎者出生点**：使用简单的 `getHighestBlockYAt() + 1` 算法
- **行为**：与原版Minecraft完全一致，保持现有游戏体验

### 当 `customSpawnLogic = true`

- **世界出生点**：使用改进的安全出生点查找算法
- **捕猎者出生点**：使用智能地表检测算法
- **行为**：
  - 检查天空开放度，避免在洞穴中出生
  - 在海洋中自动创建安全平台
  - 优先选择真正的地表位置

## 📍 影响的出生点类型

| 出生点类型 | 默认模式影响 | 守卫模式影响 | 说明 |
|-----------|-------------|-------------|------|
| 世界出生点 | ✅ | ✅ | 所有模式都会受到影响 |
| 速通者出生点 | ❌ | ❌ | 默认模式使用世界出生点，守卫模式使用塔顶 |
| 捕猎者出生点 | ❌ | ❌ | 默认模式使用世界出生点，守卫模式始终使用原版逻辑 |
| 观察者出生点 | ❌ | ❌ | 总是在其他出生点上方50格 |

**注意**：守卫模式的捕猎者出生点始终使用原版逻辑，不受此设置影响，以保持守卫模式的平衡性。

## 🎮 使用建议

### 推荐启用的情况

1. **经常遇到地下出生问题**：如果玩家经常出生在洞穴或地下
2. **海洋种子**：在海洋较多的种子中，可以自动创建平台
3. **追求更好体验**：希望确保玩家总是在安全的地表开始游戏

### 推荐保持默认的情况

1. **兼容性优先**：希望保持与原版Minecraft完全一致的行为
2. **性能考虑**：自定义逻辑需要额外的计算，可能略微影响世界创建速度
3. **特殊种子**：某些特殊种子可能需要原版的出生点逻辑

## 🔧 实现细节

### 世界出生点逻辑

```java
if (settings.isCustomSpawnLogic()) {
    // 使用自定义出生点逻辑
    spawnLocation = findSafeSpawnLocation(gameWorld.getOverworld());
    plugin.getLogger().info("使用自定义出生点逻辑，出生点: " + spawnLocation);
} else {
    // 使用原版Minecraft出生点逻辑
    spawnLocation = gameWorld.getOverworld().getSpawnLocation();
    plugin.getLogger().info("使用原版出生点逻辑，出生点: " + spawnLocation);
}
```

### 捕猎者出生点逻辑（守卫模式）

```java
if (useCustomLogic) {
    // 使用自定义逻辑：寻找真正安全的地表位置
    Location safeLocation = findSafeSurfaceLocation(overworld, x, z);
    if (safeLocation != null) {
        return safeLocation;
    }
    // 后备方案：改进的最高点算法
    int safeY = findSafeY(overworld, x, z);
    return new Location(overworld, x + 0.5, safeY, z + 0.5);
} else {
    // 使用原版逻辑：简单的最高点 + 1
    int highestY = overworld.getHighestBlockYAt(x, z);
    int spawnY = highestY + 1;
    return new Location(overworld, x + 0.5, spawnY, z + 0.5);
}
```

## 📊 性能影响

- **启用时**：世界创建时间可能增加 100-500ms（取决于地形复杂度）
- **禁用时**：无性能影响，与原版完全一致
- **运行时**：无额外性能开销

## 🧪 测试

创建了 `CustomSpawnLogicTest.java` 来验证：

1. **默认值测试**：确保默认为 `false`
2. **设置功能测试**：验证 getter/setter 正常工作
3. **兼容性测试**：确保不影响现有功能

## 🎮 GUI配置

在房间设置GUI中，自定义出生点逻辑选项位于世界设置部分：

- **图标**：重生锚（Respawn Anchor）
- **位置**：世界设置区域的slot 26
- **描述**：详细说明了功能特点和注意事项
- **操作**：点击切换开启/关闭状态

GUI中的描述包括：
- 功能说明：避免洞穴出生、海洋平台创建等
- 特别提醒：守卫模式不受此设置影响
- 状态显示：清晰的启用/禁用状态

## 🔮 未来扩展

这个配置选项为未来的出生点优化提供了基础：

1. **更多出生点类型**：可以扩展到速通者、观察者出生点
2. **更智能的算法**：可以添加更多的地形检测逻辑
3. **更多配置选项**：可以添加更细粒度的控制选项

## 📝 总结

- **默认行为**：保持与原版Minecraft完全一致
- **可选启用**：房主可以选择启用改进的出生点逻辑
- **向后兼容**：不影响现有的房间和游戏
- **灵活配置**：可以根据需要随时开启或关闭
