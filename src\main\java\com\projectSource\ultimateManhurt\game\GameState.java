package com.projectSource.ultimateManhurt.game;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextColor;

/**
 * 游戏状态枚举
 */
public enum GameState {
    WAITING("等待中", "等待玩家加入", NamedTextColor.YELLOW, "⏳"),
    PROFESSION_SELECTION("职业选择", "正在选择职业", NamedTextColor.LIGHT_PURPLE, "🎭"),
    BAN_PICK("Ban Pick", "正在进行Ban Pick", NamedTextColor.AQUA, "🎯"),
    STARTING("准备开始", "游戏即将开始", NamedTextColor.GOLD, "🚀"),
    RUNNING("进行中", "游戏正在进行", NamedTextColor.GREEN, "▶"),
    PAUSED("已暂停", "游戏已暂停", NamedTextColor.BLUE, "⏸"),
    ENDING("结束中", "游戏即将结束", NamedTextColor.RED, "⏹"),
    FINISHED("已结束", "游戏已结束", NamedTextColor.GRAY, "✓");
    
    private final String displayName;
    private final String description;
    private final TextColor color;
    private final String emoji;
    
    GameState(String displayName, String description, TextColor color, String emoji) {
        this.displayName = displayName;
        this.description = description;
        this.color = color;
        this.emoji = emoji;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public TextColor getColor() {
        return color;
    }
    
    public String getEmoji() {
        return emoji;
    }
    
    /**
     * 获取带颜色的显示名称组件
     */
    public Component getDisplayComponent() {
        return Component.text(emoji + " " + displayName, color);
    }
    
    /**
     * 获取详细信息组件
     */
    public Component getDetailComponent() {
        return Component.text()
                .append(getDisplayComponent())
                .append(Component.text(" - ", NamedTextColor.GRAY))
                .append(Component.text(description, NamedTextColor.WHITE))
                .build();
    }
    
    /**
     * 检查游戏是否处于活跃状态
     */
    public boolean isActive() {
        return this == RUNNING || this == PAUSED;
    }
    
    /**
     * 检查游戏是否可以开始
     */
    public boolean canStart() {
        return this == WAITING;
    }

    /**
     * 检查是否可以进行Ban Pick
     */
    public boolean canBanPick() {
        return this == WAITING;
    }
    
    /**
     * 检查游戏是否可以暂停
     */
    public boolean canPause() {
        return this == RUNNING;
    }
    
    /**
     * 检查游戏是否可以恢复
     */
    public boolean canResume() {
        return this == PAUSED;
    }
    
    /**
     * 检查游戏是否可以结束
     */
    public boolean canEnd() {
        return this == RUNNING || this == PAUSED || this == STARTING;
    }
    
    /**
     * 检查玩家是否可以加入
     */
    public boolean canJoin() {
        return this == WAITING;
    }
    
    /**
     * 检查玩家是否可以离开
     */
    public boolean canLeave() {
        return this != RUNNING && this != ENDING;
    }
    
    /**
     * 获取下一个可能的状态
     */
    public GameState[] getNextStates() {
        switch (this) {
            case WAITING:
                return new GameState[]{PROFESSION_SELECTION, BAN_PICK, STARTING};
            case PROFESSION_SELECTION:
                return new GameState[]{BAN_PICK, STARTING, ENDING};
            case BAN_PICK:
                return new GameState[]{STARTING, ENDING};
            case STARTING, PAUSED:
                return new GameState[]{RUNNING, ENDING};
            case RUNNING:
                return new GameState[]{PAUSED, ENDING};
            case ENDING:
                return new GameState[]{FINISHED};
            case FINISHED:
                return new GameState[]{WAITING}; // 可以重新开始
            default:
                return new GameState[0];
        }
    }
    
    /**
     * 检查是否可以转换到指定状态
     */
    public boolean canTransitionTo(GameState newState) {
        GameState[] nextStates = getNextStates();
        for (GameState state : nextStates) {
            if (state == newState) {
                return true;
            }
        }
        return false;
    }
}
