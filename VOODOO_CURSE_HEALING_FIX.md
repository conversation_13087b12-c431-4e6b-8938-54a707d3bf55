# 巫毒诅咒回血禁用修复

## 🩸 问题描述

原实现只是移除药水效果，但没有真正禁止所有形式的生命恢复。

## ❌ 修复前的问题

```java
// 检查巫毒诅咒是否禁止回血
if (plugin.getProfessionManager().getActiveSkillHandler().isVoodooCursed(victim.getUniqueId())) {
    // 移除所有回血效果
    victim.removePotionEffect(PotionEffectType.REGENERATION);
    victim.removePotionEffect(PotionEffectType.INSTANT_HEALTH);
}
```

**问题**：
- ❌ 只移除现有的药水效果
- ❌ 无法阻止新的生命恢复
- ❌ 无法阻止自然回血、食物回血等
- ❌ 无法阻止其他来源的治疗

## ✅ 修复后的实现

```java
/**
 * 处理生命值恢复事件（巫毒诅咒禁止回血）
 */
@EventHandler(priority = EventPriority.HIGH)
public void onEntityRegainHealth(EntityRegainHealthEvent event) {
    if (!(event.getEntity() instanceof Player player)) return;
    
    // 检查是否受到巫毒诅咒影响
    if (plugin.getProfessionManager().getActiveSkillHandler().isVoodooCursed(player.getUniqueId())) {
        // 取消所有形式的生命恢复
        event.setCancelled(true);
        
        // 给予反馈（但不要太频繁）
        if (event.getRegainReason() == EntityRegainHealthEvent.RegainReason.REGEN ||
            event.getRegainReason() == EntityRegainHealthEvent.RegainReason.MAGIC ||
            event.getRegainReason() == EntityRegainHealthEvent.RegainReason.MAGIC_REGEN) {
            
            // 只在特定情况下显示消息，避免刷屏
            if (Math.random() < 0.1) { // 10%概率显示消息
                ComponentUtil.sendMessage(player, ComponentUtil.warning("巫毒诅咒阻止了生命恢复"));
            }
        }
    }
}
```

## 🎯 修复特性

### 完全禁止生命恢复
- ✅ **自然回血**：阻止满饱食度时的自然生命恢复
- ✅ **药水回血**：阻止生命恢复药水效果
- ✅ **瞬间治疗**：阻止瞬间治疗药水
- ✅ **食物回血**：阻止食物提供的生命恢复
- ✅ **魔法回血**：阻止其他魔法来源的治疗
- ✅ **信标效果**：阻止信标提供的生命恢复
- ✅ **其他来源**：阻止任何其他形式的生命恢复

### 用户体验优化
- ✅ **事件优先级**：使用HIGH优先级确保及时拦截
- ✅ **反馈控制**：10%概率显示消息，避免刷屏
- ✅ **特定反馈**：只对特定类型的回血显示消息
- ✅ **性能优化**：只检查玩家实体，提高效率

## 📊 生命恢复类型覆盖

| 恢复类型 | 修复前 | 修复后 | 说明 |
|----------|--------|--------|------|
| **自然回血** | ❌ 不阻止 | ✅ 完全阻止 | 满饱食度时的自动回血 |
| **生命恢复药水** | ⚠️ 只移除现有 | ✅ 完全阻止 | 包括新获得的效果 |
| **瞬间治疗药水** | ⚠️ 只移除现有 | ✅ 完全阻止 | 包括新使用的药水 |
| **食物回血** | ❌ 不阻止 | ✅ 完全阻止 | 金苹果等食物的回血 |
| **信标效果** | ❌ 不阻止 | ✅ 完全阻止 | 信标提供的生命恢复 |
| **其他魔法** | ❌ 不阻止 | ✅ 完全阻止 | 其他插件或技能的治疗 |

## 🔧 技术实现

### 事件监听
```java
@EventHandler(priority = EventPriority.HIGH)
public void onEntityRegainHealth(EntityRegainHealthEvent event)
```

### 检查逻辑
```java
// 检查是否受到巫毒诅咒影响
if (plugin.getProfessionManager().getActiveSkillHandler().isVoodooCursed(player.getUniqueId())) {
    event.setCancelled(true); // 取消所有生命恢复
}
```

### 反馈机制
```java
// 避免消息刷屏的智能反馈
if (event.getRegainReason() == EntityRegainHealthEvent.RegainReason.REGEN ||
    event.getRegainReason() == EntityRegainHealthEvent.RegainReason.MAGIC ||
    event.getRegainReason() == EntityRegainHealthEvent.RegainReason.MAGIC_REGEN) {
    
    if (Math.random() < 0.1) { // 10%概率
        ComponentUtil.sendMessage(player, ComponentUtil.warning("巫毒诅咒阻止了生命恢复"));
    }
}
```

## 🎮 游戏平衡

### 巫毒诅咒效果
- **持续时间**：8秒
- **伤害**：每秒5%当前生命值
- **回血禁用**：完全禁止任何形式的生命恢复
- **死亡条件**：血量低于25%最大生命值时立即死亡

### 反制方法
- **保持距离**：离开6格范围
- **快速击杀**：在诅咒生效前击败女巫
- **团队配合**：多人围攻减少单人受诅咒时间

## ✅ 修复完成

- ✅ 移除了不完整的药水效果移除逻辑
- ✅ 添加了EntityRegainHealthEvent监听
- ✅ 实现了完全的生命恢复禁用
- ✅ 添加了智能反馈机制
- ✅ 优化了性能和用户体验

现在巫毒诅咒真正做到了"禁止回血"，任何形式的生命恢复都会被阻止！🩸⚡
