package com.projectSource.ultimateManhurt.room;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.entity.Player;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 房间管理器
 * 负责游戏房间的创建、管理和销毁
 */
public class RoomManager {

    private final UltimateManhurt plugin;
    private final Map<String, Room> rooms = new ConcurrentHashMap<>();
    private final Map<UUID, String> playerToRoom = new ConcurrentHashMap<>();
    private final InviteManager inviteManager;



    public RoomManager(UltimateManhurt plugin) {
        this.plugin = plugin;
        this.inviteManager = new InviteManager(plugin);
        initialize();
    }

    /**
     * 初始化房间管理器
     */
    private void initialize() {
        plugin.getLogger().info("房间管理器已初始化");
    }

    /**
     * 创建房间
     */
    public Room createRoom(String name, UUID ownerId, RoomType type) {
        String roomId = generateRoomId();
        plugin.getLogger().info("生成房间ID: " + roomId + " 为玩家: " + ownerId);

        Room room = new Room(plugin, roomId, name, ownerId, type);
        plugin.getLogger().info("创建房间对象: " + room.getName() + ", 房主: " + room.getOwnerId());

        rooms.put(roomId, room);
        playerToRoom.put(ownerId, roomId);

        plugin.getLogger().info("房间映射添加完成 - 房间总数: " + rooms.size() + ", 玩家映射: " + playerToRoom.size());
        plugin.getLogger().info("创建房间: " + name + " (ID: " + roomId + ")");

        // 验证映射是否正确
        Room verifyRoom = getRoomByPlayer(ownerId);
        plugin.getLogger().info("验证房间映射 - 通过玩家ID获取的房间: " + (verifyRoom != null ? verifyRoom.getName() : "null"));

        return room;
    }

    /**
     * 删除房间
     */
    public boolean deleteRoom(String roomId) {
        Room room = rooms.remove(roomId);
        if (room == null) {
            return false;
        }

        // 移除所有玩家的房间映射
        for (UUID playerId : room.getPlayers()) {
            playerToRoom.remove(playerId);
        }

        plugin.getLogger().info("删除房间: " + room.getName() + " (ID: " + roomId + ")");
        return true;
    }

    /**
     * 玩家加入房间
     */
    public boolean joinRoom(UUID playerId, String roomId, PlayerRole role) {
        Room room = rooms.get(roomId);
        if (room == null) {
            return false;
        }

        if (!room.canJoin(playerId)) {
            return false;
        }

        // 如果玩家已在其他房间，先离开
        leaveRoom(playerId);

        if (room.addPlayer(playerId, role)) {
            playerToRoom.put(playerId, roomId);

            // 更新房间Tablist
            plugin.getTablistManager().updateRoomTablist(room);

            return true;
        }

        return false;
    }

    /**
     * 玩家离开房间
     */
    public boolean leaveRoom(UUID playerId) {
        String roomId = playerToRoom.get(playerId);
        if (roomId == null) {
            return false;
        }

        Room room = rooms.get(roomId);
        if (room == null) {
            return false;
        }

        // 检查是否可以离开房间
        if (!room.canLeave(playerId)) {
            return false;
        }

        // 移除玩家映射
        playerToRoom.remove(playerId);
        room.removePlayer(playerId);

        // 更新房间Tablist（在删除房间之前）
        if (!room.isEmpty()) {
            plugin.getTablistManager().updateRoomTablist(room);
        }

        // 如果房间为空或房主离开，删除房间
        if (room.isEmpty() || room.isOwner(playerId)) {
            deleteRoom(roomId);
        }

        return true;
    }

    /**
     * 强制玩家离开房间（用于断线等特殊情况）
     */
    public boolean forceLeaveRoom(UUID playerId) {
        String roomId = playerToRoom.remove(playerId);
        if (roomId == null) {
            return false;
        }

        Room room = rooms.get(roomId);
        if (room == null) {
            return false;
        }

        room.removePlayer(playerId);

        // 如果房间为空或房主离开，删除房间
        if (room.isEmpty() || room.isOwner(playerId)) {
            deleteRoom(roomId);
        }

        return true;
    }

    /**
     * 获取房间
     */
    public Room getRoom(String roomId) {
        return rooms.get(roomId);
    }

    /**
     * 根据玩家获取房间
     */
    public Room getRoomByPlayer(UUID playerId) {
        String roomId = playerToRoom.get(playerId);
        if (roomId == null) {
            return null;
        }
        return rooms.get(roomId);
    }

    /**
     * 获取所有房间
     */
    public Collection<Room> getAllRooms() {
        return new ArrayList<>(rooms.values());
    }

    /**
     * 获取公开房间
     */
    public List<Room> getPublicRooms() {
        return rooms.values().stream()
                .filter(room -> room.getType() == RoomType.PUBLIC)
                .filter(room -> room.getGameState().canJoin())
                .toList();
    }

    /**
     * 检查玩家是否在房间中
     */
    public boolean isPlayerInRoom(UUID playerId) {
        return playerToRoom.containsKey(playerId);
    }

    /**
     * 生成房间ID
     */
    private String generateRoomId() {
        return "room_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }

    /**
     * 获取房间数量
     */
    public int getRoomCount() {
        return rooms.size();
    }

    /**
     * 清理空房间
     */
    public void cleanupEmptyRooms() {
        List<String> emptyRooms = new ArrayList<>();

        for (Map.Entry<String, Room> entry : rooms.entrySet()) {
            Room room = entry.getValue();
            if (room.isEmpty()) {
                emptyRooms.add(entry.getKey());
            }
        }

        for (String roomId : emptyRooms) {
            deleteRoom(roomId);
        }

        if (!emptyRooms.isEmpty()) {
            plugin.getLogger().info("清理了 " + emptyRooms.size() + " 个空房间");
        }
    }

    /**
     * 根据房间名获取房间
     */
    public Room getRoomByName(String roomName) {
        for (Room room : rooms.values()) {
            if (room.getName().equals(roomName)) {
                return room;
            }
        }
        return null;
    }

    /**
     * 获取邀请管理器
     */
    public InviteManager getInviteManager() {
        return inviteManager;
    }

    /**
     * 发送房间邀请
     */
    public boolean sendInvite(Player inviter, String targetName, String roomName) {
        // 查找目标玩家
        Player target = plugin.getServer().getPlayer(targetName);
        if (target == null) {
            ComponentUtil.sendMessage(inviter, ComponentUtil.error("玩家 " + targetName + " 不在线"));
            return false;
        }

        // 查找房间
        Room room = getRoomByName(roomName);
        if (room == null) {
            ComponentUtil.sendMessage(inviter, ComponentUtil.error("房间 " + roomName + " 不存在"));
            return false;
        }

        return inviteManager.sendInvite(inviter, target, room);
    }

    /**
     * 接受邀请
     */
    public boolean acceptInvite(Player player) {
        return inviteManager.acceptInvite(player);
    }

    /**
     * 拒绝邀请
     */
    public boolean declineInvite(Player player) {
        return inviteManager.declineInvite(player);
    }

    /**
     * 玩家离线时的清理
     */
    public void onPlayerQuit(UUID playerUUID) {
        inviteManager.onPlayerQuit(playerUUID);
    }



}
