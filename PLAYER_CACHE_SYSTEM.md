# 玩家缓存系统

## 🎯 问题背景

### 原始问题
- **排行榜显示：** 离线玩家显示为"未知用户"
- **统计界面：** 无法获取离线玩家的名字和头颅
- **用户体验：** 影响排行榜和统计功能的可用性

### 问题原因
```java
// 原始代码问题
Player player = Bukkit.getPlayer(playerId);
String playerName = player != null ? player.getName() : "未知玩家"; // ❌ 离线玩家返回null
```

当玩家离线时，`Bukkit.getPlayer(playerId)` 返回 `null`，导致无法获取玩家信息。

## 🔧 解决方案

### 核心思路
创建一个**玩家缓存系统**，在玩家在线时缓存其名字和头颅信息，离线时从缓存中获取。

### 系统架构
```
PlayerCacheManager (核心缓存管理器)
    ├── 内存缓存 (ConcurrentHashMap)
    ├── 文件持久化 (player_cache.yml)
    ├── 自动清理 (30天过期)
    └── 事件监听 (PlayerCacheListener)
```

## 📦 核心组件

### 1. PlayerCacheManager
**功能：** 核心缓存管理器

**主要方法：**
```java
// 缓存玩家信息
public void cachePlayer(Player player)

// 获取玩家名字（优先在线，其次缓存，最后OfflinePlayer）
public String getPlayerName(UUID playerId)

// 获取玩家头颅（支持离线玩家）
public ItemStack getPlayerHead(UUID playerId)

// 获取带时间信息的显示名
public String getPlayerDisplayName(UUID playerId)
```

**缓存策略：**
```java
// 三级获取策略
1. 在线玩家 (Bukkit.getPlayer()) - 最快最准确
2. 内存缓存 (playerCache) - 快速访问
3. OfflinePlayer (Bukkit.getOfflinePlayer()) - 兜底方案
```

### 2. PlayerCacheData
**功能：** 缓存数据结构

```java
private static class PlayerCacheData {
    private String name;        // 玩家名字
    private long lastSeen;      // 最后在线时间
}
```

### 3. PlayerCacheListener
**功能：** 事件监听器，自动更新缓存

```java
@EventHandler
public void onPlayerJoin(PlayerJoinEvent event) {
    plugin.getPlayerCacheManager().cachePlayer(event.getPlayer());
}

@EventHandler
public void onPlayerQuit(PlayerQuitEvent event) {
    plugin.getPlayerCacheManager().cachePlayer(event.getPlayer());
}
```

## 💾 持久化机制

### 文件存储格式
```yaml
# player_cache.yml
550e8400-e29b-41d4-a716-************:
  name: "PlayerName"
  lastSeen: 1640995200000

另一个UUID:
  name: "AnotherPlayer"
  lastSeen: 1640995300000
```

### 自动保存
- **触发时机：** 玩家加入/离开时异步保存
- **保存方式：** 异步操作，不阻塞主线程
- **错误处理：** 完善的异常处理和日志记录

## 🧹 缓存清理

### 过期策略
```java
/**
 * 清理过期缓存（超过30天的缓存）
 */
public void cleanupExpiredCache() {
    long thirtyDaysAgo = System.currentTimeMillis() - (30L * 24 * 60 * 60 * 1000);
    
    playerCache.entrySet().removeIf(entry -> {
        PlayerCacheData data = entry.getValue();
        return data.getLastSeen() < thirtyDaysAgo;
    });
}
```

### 定时清理
```java
// 每天清理一次过期缓存
getServer().getScheduler().runTaskTimerAsynchronously(this, () -> {
    playerCacheManager.cleanupExpiredCache();
}, 1728000L, 1728000L); // 24小时 = 1728000 ticks
```

## 🎮 应用集成

### 排行榜系统集成
**修改前：**
```java
Player targetPlayer = this.plugin.getServer().getPlayer(data.getPlayerId());
String playerName = targetPlayer != null ? targetPlayer.getName() : "未知玩家";
```

**修改后：**
```java
// 使用缓存获取玩家名字
String playerName = plugin.getPlayerCacheManager().getPlayerName(data.getPlayerId());
```

### 统计界面集成
**修改前：**
```java
Player targetPlayer = this.plugin.getServer().getPlayer(targetPlayerId);
String playerName = targetPlayer != null ? targetPlayer.getName() : "未知玩家";
```

**修改后：**
```java
// 玩家头像和基本信息（使用缓存）
String playerName = plugin.getPlayerCacheManager().getPlayerName(targetPlayerId);
ItemStack head = plugin.getPlayerCacheManager().getPlayerHead(targetPlayerId);
```

## 🚀 性能优化

### 内存管理
- **ConcurrentHashMap：** 线程安全的高性能缓存
- **按需加载：** 只缓存访问过的玩家
- **定期清理：** 自动清理过期数据

### 异步操作
```java
// 异步保存缓存
Bukkit.getScheduler().runTaskAsynchronously(plugin, this::saveCache);

// 异步清理过期缓存
getServer().getScheduler().runTaskTimerAsynchronously(this, () -> {
    playerCacheManager.cleanupExpiredCache();
}, 1728000L, 1728000L);
```

### 智能获取策略
```java
public String getPlayerName(UUID playerId) {
    // 1. 优先从在线玩家获取（最快）
    Player onlinePlayer = Bukkit.getPlayer(playerId);
    if (onlinePlayer != null) {
        cachePlayer(onlinePlayer); // 顺便更新缓存
        return onlinePlayer.getName();
    }
    
    // 2. 从缓存获取（快速）
    PlayerCacheData cacheData = playerCache.get(playerId);
    if (cacheData != null && cacheData.getName() != null) {
        return cacheData.getName();
    }
    
    // 3. 从OfflinePlayer获取（兜底，可能较慢）
    try {
        OfflinePlayer offlinePlayer = Bukkit.getOfflinePlayer(playerId);
        if (offlinePlayer.hasPlayedBefore() && offlinePlayer.getName() != null) {
            // 更新缓存
            updateCache(playerId, offlinePlayer.getName());
            return offlinePlayer.getName();
        }
    } catch (Exception e) {
        // 错误处理
    }
    
    // 4. 最后返回未知玩家
    return "未知玩家";
}
```

## 📊 功能特性

### 核心特性
- ✅ **离线支持：** 支持获取离线玩家信息
- ✅ **自动缓存：** 玩家加入/离开时自动更新
- ✅ **持久化：** 数据持久保存到文件
- ✅ **性能优化：** 多级缓存策略
- ✅ **自动清理：** 定期清理过期数据

### 高级特性
- 🕐 **时间信息：** 显示玩家最后在线时间
- 🎭 **头颅支持：** 获取离线玩家头颅
- 🔄 **实时更新：** 在线玩家信息实时更新
- 🛡️ **错误处理：** 完善的异常处理机制

### 用户体验
```java
// 显示效果示例
"PlayerName"                        // 在线玩家
"PlayerName <gray>(2小时前)</gray>"  // 离线玩家（带时间信息，使用MiniMessage格式）
"未知玩家"                           // 无法获取信息的玩家
```

## 🧪 测试场景

### 基础功能测试
1. **在线玩家测试：**
   ```
   1. 玩家在线时查看排行榜
   2. 验证显示正确的玩家名字和头颅
   ```

2. **离线玩家测试：**
   ```
   1. 玩家离线后查看排行榜
   2. 验证仍能显示正确的玩家名字和头颅
   ```

3. **缓存更新测试：**
   ```
   1. 玩家改名后重新加入
   2. 验证缓存是否更新为新名字
   ```

### 性能测试
1. **大量数据测试：**
   ```
   1. 缓存大量玩家数据
   2. 测试查询性能
   3. 验证内存使用情况
   ```

2. **并发访问测试：**
   ```
   1. 多个玩家同时查看排行榜
   2. 验证线程安全性
   ```

### 持久化测试
1. **重启测试：**
   ```
   1. 服务器重启前后
   2. 验证缓存数据是否保持
   ```

2. **清理测试：**
   ```
   1. 等待30天过期时间（或手动触发）
   2. 验证过期数据是否被清理
   ```

## 🎉 系统优势

### 解决的问题
- ✅ **消除"未知用户"：** 离线玩家也能正确显示
- ✅ **提升用户体验：** 排行榜和统计功能更完善
- ✅ **性能优化：** 减少重复的玩家信息查询

### 技术优势
- 🚀 **高性能：** 多级缓存策略
- 🔒 **线程安全：** ConcurrentHashMap保证并发安全
- 💾 **持久化：** 数据不会因重启丢失
- 🧹 **自动管理：** 自动清理过期数据

### 扩展性
- 📈 **易扩展：** 可以轻松添加更多缓存信息
- 🔌 **模块化：** 独立的缓存系统，易于维护
- 🎯 **通用性：** 可以用于其他需要玩家信息的功能

## 📋 部署清单

### 新增文件
- ✅ `PlayerCacheManager.java` - 核心缓存管理器
- ✅ `PlayerCacheListener.java` - 事件监听器
- ✅ `player_cache.yml` - 缓存数据文件（自动生成）

### 修改文件
- ✅ `UltimateManhurt.java` - 集成缓存管理器
- ✅ `LeaderboardGui.java` - 使用缓存获取玩家名字
- ✅ `PlayerStatsGui.java` - 使用缓存获取玩家信息

### 配置更新
- ✅ 定时任务：每天清理过期缓存
- ✅ 事件监听：自动更新玩家缓存

现在排行榜和统计系统能够完美显示离线玩家的信息，彻底解决了"未知用户"的问题！🎮✨

**重要特点：**
- 智能的三级获取策略
- 完善的持久化机制
- 自动的缓存管理
- 优秀的性能表现
