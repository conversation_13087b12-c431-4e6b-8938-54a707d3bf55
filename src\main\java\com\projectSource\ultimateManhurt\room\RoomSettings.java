package com.projectSource.ultimateManhurt.room;

import com.projectSource.ultimateManhurt.game.VictoryMode;
import com.projectSource.ultimateManhurt.kit.StartKit;
import com.projectSource.ultimateManhurt.kit.StartKitTemplate;
import org.bukkit.Difficulty;
import org.bukkit.GameMode;
import org.bukkit.configuration.file.YamlConfiguration;

import java.util.HashMap;
import java.util.Map;

/**
 * 房间设置类
 * 包含游戏的各种配置参数
 */
public class RoomSettings {
    
    // 基础设置
    private int gameDurationMinutes = 30; // 游戏时长（分钟）
    private long worldSeed = 0; // 世界种子，0表示随机
    private Difficulty difficulty = Difficulty.NORMAL; // 游戏难度
    private GameMode spectatorGameMode = GameMode.SPECTATOR; // 观察者游戏模式
    
    // 玩家设置
    private int maxPlayers = 10; // 最大玩家数
    private int maxSpeedrunners = 1; // 最大速通者数量
    private int maxHunters = 9; // 最大捕猎者数量
    private boolean allowSpectators = true; // 是否允许观察者
    
    // 游戏规则
    private boolean pvpEnabled = true; // 是否启用PVP
    private boolean friendlyFire = false; // 是否允许队友伤害
    private boolean keepInventory = false; // 死亡是否保留物品
    private boolean naturalRegeneration = true; // 是否自然回血
    private boolean showDeathMessages = true; // 是否显示死亡消息
    
    // 世界设置
    private boolean generateStructures = true; // 是否生成结构
    private boolean generateBonusChest = false; // 是否生成奖励箱
    private boolean enableCommandBlocks = false; // 是否启用命令方块
    private boolean doDaylightCycle = true; // 是否启用昼夜循环
    private boolean doWeatherCycle = true; // 是否启用天气循环
    private boolean customSpawnLogic = false; // 是否启用自定义出生点逻辑
    private boolean randomSpawnEnabled = false; // 是否启用随机出生点
    private int randomSpawnDistance = 100; // 随机出生点最小距离（方块）

    // 特殊功能
    private boolean compassTracking = true; // 指南针追踪功能
    private int compassUpdateInterval = 5; // 指南针更新间隔（秒）
    private boolean locatorBar = true; // 定位条功能
    private boolean enderPearlCooldown = true; // 末影珍珠冷却
    private int enderPearlCooldownSeconds = 15; // 末影珍珠冷却时间
    private boolean netherPortalDelay = true; // 下界传送门延迟
    private int netherPortalDelaySeconds = 4; // 下界传送门延迟时间

    // Ban Pick系统
    private boolean banPickEnabled = false; // 是否启用Ban Pick系统
    private int banPickPhaseTimeSeconds = 30; // 每个Ban Pick阶段的时间（秒）

    // 职业系统
    private boolean professionSystemEnabled = false; // 是否启用职业系统

    // 豁免设置
    private boolean immunityEnabled = true; // 是否启用开局豁免
    private int immunityDurationSeconds = 60; // 豁免持续时间（秒）
    
    // 胜利条件
    private boolean dragonRespawn = false; // 末影龙是否重生
    private boolean timeoutHuntersWin = true; // 超时是否捕猎者获胜
    private int speedrunnerLives = 3; // 速通者生命数（0表示无限）
    private int hunterLives = 0; // 捕猎者生命数（0表示无限）
    private boolean allowSpeedrunnerRespawn = true; // 是否允许速通者重生
    private boolean allowHunterRespawn = true; // 是否允许捕猎者重生

    // 血量设置
    private double speedrunnerMaxHealth = 20.0; // 速通者最大血量（默认20血）
    private double hunterMaxHealth = 20.0; // 捕猎者最大血量（默认20血）

    // 胜利模式设置
    private VictoryMode victoryMode = VictoryMode.DRAGON_MODE; // 胜利模式
    private int targetScore = 500; // 目标分数（积分模式和混合模式使用）

    // 守卫模式设置
    private double witherMaxHealth = 300.0; // 凋零最大血量
    private double witherHealingReduction = 0.5; // 减疗效果（50%）
    private int witherAttackInterval = 5; // 凋零攻击间隔（秒）
    private double witherAttackDamage = 10.0; // 凋零攻击伤害
    private int witherShieldDuration = 300; // 减伤护盾持续时间（秒，5分钟）
    private double witherShieldReduction = 0.75; // 减伤护盾减伤比例（75%）
    private int hunterSpawnDistance = 200; // 捕猎者出生距离（方块）
    private boolean witherCanMove = false; // 凋零是否可以移动
    private boolean witherTargetHuntersOnly = true; // 凋零是否只攻击捕猎者
    private boolean witherDestroyBlocks = false; // 凋零攻击是否破坏地形
    private int witherEffectDuration = 60; // 凋零效果持续时间（ticks，60=3秒）
    private int witherEffectLevel = 0; // 凋零效果等级（0=I级，1=II级）

    // StartKit设置
    private boolean startKitEnabled = true; // 是否启用起始装备
    private StartKit speedrunnerKit; // 速通者装备包
    private StartKit hunterKit; // 捕猎者装备包

    // 里程碑设置
    private com.projectSource.ultimateManhurt.game.scoring.MilestoneSettings milestoneSettings; // 里程碑配置

    // 自定义规则
    private Map<String, Object> customRules = new HashMap<>();
    
    // 构造函数
    public RoomSettings() {
        // 使用默认设置
        initializeStartKits();
        initializeMilestoneSettings();
    }

    /**
     * 初始化默认装备包
     */
    private void initializeStartKits() {
        this.speedrunnerKit = StartKitTemplate.getDefaultSpeedrunnerKit();
        this.hunterKit = StartKitTemplate.getDefaultHunterKit();
    }

    /**
     * 初始化里程碑设置
     */
    private void initializeMilestoneSettings() {
        this.milestoneSettings = new com.projectSource.ultimateManhurt.game.scoring.MilestoneSettings();
    }
    
    public RoomSettings(RoomType roomType) {
        initializeStartKits();
        initializeMilestoneSettings();
        applyRoomTypeDefaults(roomType);
    }
    
    /**
     * 根据房间类型应用默认设置
     */
    private void applyRoomTypeDefaults(RoomType roomType) {
        this.gameDurationMinutes = roomType.getDefaultGameDuration();
        this.maxPlayers = roomType.getMaxPlayers();
        
        switch (roomType) {
            case PUBLIC:
                // 公开房间设置
                this.difficulty = Difficulty.NORMAL;
                this.keepInventory = false;
                this.compassUpdateInterval = 5; // 标准更新间隔
                this.enderPearlCooldownSeconds = 15; // 标准冷却时间
                break;
            case PRIVATE:
                // 私人房间设置
                this.difficulty = Difficulty.NORMAL;
                this.keepInventory = true; // 私人房间可以保留物品
                this.compassUpdateInterval = 3; // 更短的更新间隔，便于练习
                this.enderPearlCooldownSeconds = 10; // 更短的冷却时间
                break;
        }
    }
    
    // Getter和Setter方法
    public int getGameDurationMinutes() { return gameDurationMinutes; }
    public void setGameDurationMinutes(int gameDurationMinutes) { this.gameDurationMinutes = gameDurationMinutes; }
    
    public long getWorldSeed() { return worldSeed; }
    public void setWorldSeed(long worldSeed) { this.worldSeed = worldSeed; }
    
    public Difficulty getDifficulty() { return difficulty; }
    public void setDifficulty(Difficulty difficulty) { this.difficulty = difficulty; }
    
    public GameMode getSpectatorGameMode() { return spectatorGameMode; }
    public void setSpectatorGameMode(GameMode spectatorGameMode) { this.spectatorGameMode = spectatorGameMode; }
    
    public int getMaxPlayers() { return maxPlayers; }
    public void setMaxPlayers(int maxPlayers) { this.maxPlayers = maxPlayers; }
    
    public int getMaxSpeedrunners() { return maxSpeedrunners; }
    public void setMaxSpeedrunners(int maxSpeedrunners) { this.maxSpeedrunners = maxSpeedrunners; }
    
    public int getMaxHunters() { return maxHunters; }
    public void setMaxHunters(int maxHunters) { this.maxHunters = maxHunters; }
    
    public boolean isAllowSpectators() { return allowSpectators; }
    public void setAllowSpectators(boolean allowSpectators) { this.allowSpectators = allowSpectators; }
    
    public boolean isPvpEnabled() { return pvpEnabled; }
    public void setPvpEnabled(boolean pvpEnabled) { this.pvpEnabled = pvpEnabled; }
    
    public boolean isFriendlyFire() { return friendlyFire; }
    public void setFriendlyFire(boolean friendlyFire) { this.friendlyFire = friendlyFire; }
    
    public boolean isKeepInventory() { return keepInventory; }
    public void setKeepInventory(boolean keepInventory) { this.keepInventory = keepInventory; }
    
    public boolean isNaturalRegeneration() { return naturalRegeneration; }
    public void setNaturalRegeneration(boolean naturalRegeneration) { this.naturalRegeneration = naturalRegeneration; }
    
    public boolean isShowDeathMessages() { return showDeathMessages; }
    public void setShowDeathMessages(boolean showDeathMessages) { this.showDeathMessages = showDeathMessages; }
    
    public boolean isGenerateStructures() { return generateStructures; }
    public void setGenerateStructures(boolean generateStructures) { this.generateStructures = generateStructures; }
    
    public boolean isGenerateBonusChest() { return generateBonusChest; }
    public void setGenerateBonusChest(boolean generateBonusChest) { this.generateBonusChest = generateBonusChest; }
    
    public boolean isEnableCommandBlocks() { return enableCommandBlocks; }
    public void setEnableCommandBlocks(boolean enableCommandBlocks) { this.enableCommandBlocks = enableCommandBlocks; }
    
    public boolean isDoDaylightCycle() { return doDaylightCycle; }
    public void setDoDaylightCycle(boolean doDaylightCycle) { this.doDaylightCycle = doDaylightCycle; }
    
    public boolean isDoWeatherCycle() { return doWeatherCycle; }
    public void setDoWeatherCycle(boolean doWeatherCycle) { this.doWeatherCycle = doWeatherCycle; }

    public boolean isCustomSpawnLogic() { return customSpawnLogic; }
    public void setCustomSpawnLogic(boolean customSpawnLogic) { this.customSpawnLogic = customSpawnLogic; }

    public boolean isRandomSpawnEnabled() { return randomSpawnEnabled; }
    public void setRandomSpawnEnabled(boolean randomSpawnEnabled) { this.randomSpawnEnabled = randomSpawnEnabled; }

    public int getRandomSpawnDistance() { return randomSpawnDistance; }
    public void setRandomSpawnDistance(int randomSpawnDistance) {
        this.randomSpawnDistance = Math.max(50, Math.min(500, randomSpawnDistance));
    }

    public boolean isCompassTracking() { return compassTracking; }
    public void setCompassTracking(boolean compassTracking) { this.compassTracking = compassTracking; }
    
    public int getCompassUpdateInterval() { return compassUpdateInterval; }
    public void setCompassUpdateInterval(int compassUpdateInterval) { this.compassUpdateInterval = compassUpdateInterval; }

    public boolean isLocatorBar() { return locatorBar; }
    public void setLocatorBar(boolean locatorBar) { this.locatorBar = locatorBar; }

    public boolean isEnderPearlCooldown() { return enderPearlCooldown; }
    public void setEnderPearlCooldown(boolean enderPearlCooldown) { this.enderPearlCooldown = enderPearlCooldown; }
    
    public int getEnderPearlCooldownSeconds() { return enderPearlCooldownSeconds; }
    public void setEnderPearlCooldownSeconds(int enderPearlCooldownSeconds) { this.enderPearlCooldownSeconds = enderPearlCooldownSeconds; }
    
    public boolean isNetherPortalDelay() { return netherPortalDelay; }
    public void setNetherPortalDelay(boolean netherPortalDelay) { this.netherPortalDelay = netherPortalDelay; }
    
    public int getNetherPortalDelaySeconds() { return netherPortalDelaySeconds; }
    public void setNetherPortalDelaySeconds(int netherPortalDelaySeconds) { this.netherPortalDelaySeconds = netherPortalDelaySeconds; }

    public boolean isBanPickEnabled() { return banPickEnabled; }
    public void setBanPickEnabled(boolean banPickEnabled) { this.banPickEnabled = banPickEnabled; }

    public int getBanPickPhaseTimeSeconds() { return banPickPhaseTimeSeconds; }
    public void setBanPickPhaseTimeSeconds(int banPickPhaseTimeSeconds) { this.banPickPhaseTimeSeconds = banPickPhaseTimeSeconds; }

    public boolean isProfessionSystemEnabled() { return professionSystemEnabled; }
    public void setProfessionSystemEnabled(boolean professionSystemEnabled) { this.professionSystemEnabled = professionSystemEnabled; }

    public boolean isImmunityEnabled() { return immunityEnabled; }
    public void setImmunityEnabled(boolean immunityEnabled) { this.immunityEnabled = immunityEnabled; }

    public int getImmunityDurationSeconds() { return immunityDurationSeconds; }
    public void setImmunityDurationSeconds(int immunityDurationSeconds) { this.immunityDurationSeconds = immunityDurationSeconds; }

    public boolean isDragonRespawn() { return dragonRespawn; }
    public void setDragonRespawn(boolean dragonRespawn) { this.dragonRespawn = dragonRespawn; }

    public boolean isTimeoutHuntersWin() { return timeoutHuntersWin; }
    public void setTimeoutHuntersWin(boolean timeoutHuntersWin) { this.timeoutHuntersWin = timeoutHuntersWin; }

    public int getSpeedrunnerLives() { return speedrunnerLives; }
    public void setSpeedrunnerLives(int speedrunnerLives) { this.speedrunnerLives = speedrunnerLives; }

    public int getHunterLives() { return hunterLives; }
    public void setHunterLives(int hunterLives) { this.hunterLives = hunterLives; }

    public boolean isAllowSpeedrunnerRespawn() { return allowSpeedrunnerRespawn; }
    public void setAllowSpeedrunnerRespawn(boolean allowSpeedrunnerRespawn) { this.allowSpeedrunnerRespawn = allowSpeedrunnerRespawn; }

    public boolean isAllowHunterRespawn() { return allowHunterRespawn; }
    public void setAllowHunterRespawn(boolean allowHunterRespawn) { this.allowHunterRespawn = allowHunterRespawn; }

    public double getSpeedrunnerMaxHealth() { return speedrunnerMaxHealth; }
    public void setSpeedrunnerMaxHealth(double speedrunnerMaxHealth) { this.speedrunnerMaxHealth = Math.max(1.0, Math.min(100.0, speedrunnerMaxHealth)); }

    public double getHunterMaxHealth() { return hunterMaxHealth; }
    public void setHunterMaxHealth(double hunterMaxHealth) { this.hunterMaxHealth = Math.max(1.0, Math.min(100.0, hunterMaxHealth)); }

    public VictoryMode getVictoryMode() { return victoryMode; }
    public void setVictoryMode(VictoryMode victoryMode) { this.victoryMode = victoryMode; }

    public boolean isScoreVictoryEnabled() { return victoryMode.requiresScoreSystem(); }

    public int getTargetScore() { return targetScore; }
    public void setTargetScore(int targetScore) { this.targetScore = targetScore; }

    // 守卫模式配置的getter和setter方法
    public double getWitherMaxHealth() { return witherMaxHealth; }
    public void setWitherMaxHealth(double witherMaxHealth) { this.witherMaxHealth = Math.max(100.0, Math.min(10000.0, witherMaxHealth)); }

    public double getWitherHealingReduction() { return witherHealingReduction; }
    public void setWitherHealingReduction(double witherHealingReduction) { this.witherHealingReduction = Math.max(0.0, Math.min(1.0, witherHealingReduction)); }

    public int getWitherAttackInterval() { return witherAttackInterval; }
    public void setWitherAttackInterval(int witherAttackInterval) { this.witherAttackInterval = Math.max(1, Math.min(60, witherAttackInterval)); }

    public double getWitherAttackDamage() { return witherAttackDamage; }
    public void setWitherAttackDamage(double witherAttackDamage) { this.witherAttackDamage = Math.max(1.0, Math.min(100.0, witherAttackDamage)); }

    public int getWitherShieldDuration() { return witherShieldDuration; }
    public void setWitherShieldDuration(int witherShieldDuration) { this.witherShieldDuration = Math.max(0, Math.min(1800, witherShieldDuration)); }

    public double getWitherShieldReduction() { return witherShieldReduction; }
    public void setWitherShieldReduction(double witherShieldReduction) { this.witherShieldReduction = Math.max(0.0, Math.min(1.0, witherShieldReduction)); }

    public int getHunterSpawnDistance() { return hunterSpawnDistance; }
    public void setHunterSpawnDistance(int hunterSpawnDistance) { this.hunterSpawnDistance = Math.max(50, Math.min(1000, hunterSpawnDistance)); }

    public boolean isWitherCanMove() { return witherCanMove; }
    public void setWitherCanMove(boolean witherCanMove) { this.witherCanMove = witherCanMove; }

    public boolean isWitherTargetHuntersOnly() { return witherTargetHuntersOnly; }
    public void setWitherTargetHuntersOnly(boolean witherTargetHuntersOnly) { this.witherTargetHuntersOnly = witherTargetHuntersOnly; }

    public boolean isWitherDestroyBlocks() { return witherDestroyBlocks; }
    public void setWitherDestroyBlocks(boolean witherDestroyBlocks) { this.witherDestroyBlocks = witherDestroyBlocks; }

    public int getWitherEffectDuration() { return witherEffectDuration; }
    public void setWitherEffectDuration(int witherEffectDuration) { this.witherEffectDuration = witherEffectDuration; }

    public int getWitherEffectLevel() { return witherEffectLevel; }
    public void setWitherEffectLevel(int witherEffectLevel) { this.witherEffectLevel = witherEffectLevel; }

    public boolean isStartKitEnabled() { return startKitEnabled; }
    public void setStartKitEnabled(boolean startKitEnabled) { this.startKitEnabled = startKitEnabled; }

    public StartKit getSpeedrunnerKit() { return speedrunnerKit; }
    public void setSpeedrunnerKit(StartKit speedrunnerKit) { this.speedrunnerKit = speedrunnerKit; }

    public StartKit getHunterKit() { return hunterKit; }
    public void setHunterKit(StartKit hunterKit) { this.hunterKit = hunterKit; }

    public com.projectSource.ultimateManhurt.game.scoring.MilestoneSettings getMilestoneSettings() { return milestoneSettings; }
    public void setMilestoneSettings(com.projectSource.ultimateManhurt.game.scoring.MilestoneSettings milestoneSettings) { this.milestoneSettings = milestoneSettings; }

    public Map<String, Object> getCustomRules() { return customRules; }
    public void setCustomRules(Map<String, Object> customRules) { this.customRules = customRules; }
    
    /**
     * 添加自定义规则
     */
    public void addCustomRule(String key, Object value) {
        customRules.put(key, value);
    }
    
    /**
     * 获取自定义规则
     */
    public Object getCustomRule(String key) {
        return customRules.get(key);
    }
    
    /**
     * 获取游戏时长（秒）
     */
    public long getGameDurationSeconds() {
        return gameDurationMinutes * 60L;
    }
    
    /**
     * 验证设置是否有效
     */
    public boolean isValid() {
        return gameDurationMinutes > 0
            && maxPlayers > 0
            && maxSpeedrunners > 0
            && maxHunters >= 0
            && (maxSpeedrunners + maxHunters) <= maxPlayers
            && compassUpdateInterval > 0
            && enderPearlCooldownSeconds >= 0
            && netherPortalDelaySeconds >= 0
            && immunityDurationSeconds >= 0;
    }
    
    /**
     * 克隆设置
     */
    public RoomSettings clone() {
        RoomSettings clone = new RoomSettings();
        // 复制所有字段
        clone.gameDurationMinutes = this.gameDurationMinutes;
        clone.worldSeed = this.worldSeed;
        clone.difficulty = this.difficulty;
        clone.spectatorGameMode = this.spectatorGameMode;
        clone.maxPlayers = this.maxPlayers;
        clone.maxSpeedrunners = this.maxSpeedrunners;
        clone.maxHunters = this.maxHunters;
        clone.allowSpectators = this.allowSpectators;
        clone.pvpEnabled = this.pvpEnabled;
        clone.friendlyFire = this.friendlyFire;
        clone.keepInventory = this.keepInventory;
        clone.naturalRegeneration = this.naturalRegeneration;
        clone.showDeathMessages = this.showDeathMessages;
        clone.generateStructures = this.generateStructures;
        clone.generateBonusChest = this.generateBonusChest;
        clone.enableCommandBlocks = this.enableCommandBlocks;
        clone.doDaylightCycle = this.doDaylightCycle;
        clone.doWeatherCycle = this.doWeatherCycle;
        clone.customSpawnLogic = this.customSpawnLogic;
        clone.randomSpawnEnabled = this.randomSpawnEnabled;
        clone.randomSpawnDistance = this.randomSpawnDistance;
        clone.compassTracking = this.compassTracking;
        clone.compassUpdateInterval = this.compassUpdateInterval;
        clone.enderPearlCooldown = this.enderPearlCooldown;
        clone.enderPearlCooldownSeconds = this.enderPearlCooldownSeconds;
        clone.netherPortalDelay = this.netherPortalDelay;
        clone.netherPortalDelaySeconds = this.netherPortalDelaySeconds;
        clone.banPickEnabled = this.banPickEnabled;
        clone.banPickPhaseTimeSeconds = this.banPickPhaseTimeSeconds;
        clone.professionSystemEnabled = this.professionSystemEnabled;
        clone.immunityEnabled = this.immunityEnabled;
        clone.immunityDurationSeconds = this.immunityDurationSeconds;
        clone.dragonRespawn = this.dragonRespawn;
        clone.timeoutHuntersWin = this.timeoutHuntersWin;
        clone.speedrunnerLives = this.speedrunnerLives;
        clone.hunterLives = this.hunterLives;
        clone.allowSpeedrunnerRespawn = this.allowSpeedrunnerRespawn;
        clone.allowHunterRespawn = this.allowHunterRespawn;
        clone.speedrunnerMaxHealth = this.speedrunnerMaxHealth;
        clone.hunterMaxHealth = this.hunterMaxHealth;
        clone.victoryMode = this.victoryMode;
        clone.targetScore = this.targetScore;
        // 守卫模式配置
        clone.witherMaxHealth = this.witherMaxHealth;
        clone.witherHealingReduction = this.witherHealingReduction;
        clone.witherAttackInterval = this.witherAttackInterval;
        clone.witherAttackDamage = this.witherAttackDamage;
        clone.witherShieldDuration = this.witherShieldDuration;
        clone.witherShieldReduction = this.witherShieldReduction;
        clone.hunterSpawnDistance = this.hunterSpawnDistance;
        clone.witherCanMove = this.witherCanMove;
        clone.witherTargetHuntersOnly = this.witherTargetHuntersOnly;
        clone.witherDestroyBlocks = this.witherDestroyBlocks;
        clone.witherEffectDuration = this.witherEffectDuration;
        clone.witherEffectLevel = this.witherEffectLevel;
        clone.startKitEnabled = this.startKitEnabled;
        clone.speedrunnerKit = this.speedrunnerKit != null ? new StartKit(this.speedrunnerKit) : null;
        clone.hunterKit = this.hunterKit != null ? new StartKit(this.hunterKit) : null;
        clone.milestoneSettings = this.milestoneSettings != null ? this.milestoneSettings.clone() : null;
        clone.customRules = new HashMap<>(this.customRules);
        return clone;
    }
}
