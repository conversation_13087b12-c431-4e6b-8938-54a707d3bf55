package com.projectSource.ultimateManhurt.world;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.entity.Player;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 游戏世界实体类
 * 包含一个完整的游戏世界组（主世界、下界、末地）
 */
public class GameWorld {
    
    private final String roomId;
    private final World overworld;
    private final World nether;
    private final World end;
    private final LocalDateTime createdTime;
    
    // 特殊位置
    private Location speedrunnerSpawn;
    private Location hunterSpawn;
    private Location spectatorSpawn;
    
    // 世界状态
    private boolean isActive = false;
    private boolean isPreGenerated = false;
    
    public GameWorld(String roomId, World overworld, World nether, World end) {
        this.roomId = roomId;
        this.overworld = overworld;
        this.nether = nether;
        this.end = end;
        this.createdTime = LocalDateTime.now();
        
        // 设置默认出生点
        this.speedrunnerSpawn = overworld.getSpawnLocation();
        this.hunterSpawn = overworld.getSpawnLocation();
        this.spectatorSpawn = overworld.getSpawnLocation().add(0, 50, 0); // 观察者在高处
    }
    
    // Getter方法
    public String getRoomId() {
        return roomId;
    }
    
    public World getOverworld() {
        return overworld;
    }
    
    public World getNether() {
        return nether;
    }
    
    public World getEnd() {
        return end;
    }
    
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
    
    public Location getSpeedrunnerSpawn() {
        return speedrunnerSpawn.clone();
    }
    
    public void setSpeedrunnerSpawn(Location speedrunnerSpawn) {
        this.speedrunnerSpawn = speedrunnerSpawn.clone();
    }
    
    public Location getHunterSpawn() {
        return hunterSpawn.clone();
    }
    
    public void setHunterSpawn(Location hunterSpawn) {
        this.hunterSpawn = hunterSpawn.clone();
    }
    
    public Location getSpectatorSpawn() {
        return spectatorSpawn.clone();
    }
    
    public void setSpectatorSpawn(Location spectatorSpawn) {
        this.spectatorSpawn = spectatorSpawn.clone();
    }

    /**
     * 根据胜利模式配置出生点
     */
    public void configureSpawnPoints(com.projectSource.ultimateManhurt.game.VictoryMode victoryMode,
                                   com.projectSource.ultimateManhurt.room.RoomSettings settings) {
        switch (victoryMode) {
            case GUARD_MODE:
                configureGuardModeSpawns(settings);
                break;
            default:
                configureDefaultSpawns(settings);
                break;
        }
    }

    /**
     * 配置守卫模式的出生点
     */
    private void configureGuardModeSpawns(com.projectSource.ultimateManhurt.room.RoomSettings settings) {
        // 先生成守卫塔结构，确保塔存在
        com.projectSource.ultimateManhurt.game.guard.TowerGenerator.generateTower(overworld);

        // 速通者在塔顶出生（保护凋零）
        Location towerTopLocation = getTowerTopSpawnLocation();
        this.speedrunnerSpawn = towerTopLocation;

        // 捕猎者在指定距离外随机出生（守卫模式始终使用原版逻辑）
        int distance = settings.getHunterSpawnDistance();
        Location hunterLocation = findHunterSpawnLocation(distance, false); // 守卫模式强制使用原版逻辑
        this.hunterSpawn = hunterLocation;

        // 观察者在中心上方更高处
        this.spectatorSpawn = towerTopLocation.clone().add(0, 20, 0);

        // 记录日志
        System.out.println("守卫模式出生点已配置:");
        System.out.println("- 速通者出生点: " + speedrunnerSpawn);
        System.out.println("- 捕猎者出生点: " + hunterSpawn);
        System.out.println("- 观察者出生点: " + spectatorSpawn);
    }

    /**
     * 获取塔顶的速通者出生位置
     */
    private Location getTowerTopSpawnLocation() {
        // 使用TowerGenerator的方法确保位置一致
        return com.projectSource.ultimateManhurt.game.guard.TowerGenerator.getSpeedrunnerSpawnLocation(overworld);
    }

    /**
     * 配置默认出生点
     */
    private void configureDefaultSpawns(com.projectSource.ultimateManhurt.room.RoomSettings settings) {
        Location worldSpawn = overworld.getSpawnLocation();

        if (settings.isRandomSpawnEnabled()) {
            // 启用随机出生点
            configureRandomSpawns(settings, worldSpawn);
        } else {
            // 使用传统的相同出生点
            this.speedrunnerSpawn = worldSpawn.clone();
            this.hunterSpawn = worldSpawn.clone();
            this.spectatorSpawn = worldSpawn.clone().add(0, 50, 0);

            System.out.println("默认模式使用世界出生点: " + worldSpawn);
        }
    }

    /**
     * 配置随机出生点
     */
    private void configureRandomSpawns(com.projectSource.ultimateManhurt.room.RoomSettings settings, Location centerLocation) {
        int minDistance = settings.getRandomSpawnDistance();
        boolean useCustomLogic = settings.isCustomSpawnLogic();

        // 为速通者随机选择一个位置
        Location speedrunnerLocation = findRandomSpawnLocation(centerLocation, minDistance, useCustomLogic);
        this.speedrunnerSpawn = speedrunnerLocation;

        // 为猎人随机选择另一个位置，确保与速通者保持最小距离
        Location hunterLocation = findRandomSpawnLocation(centerLocation, minDistance, useCustomLogic, speedrunnerLocation);
        this.hunterSpawn = hunterLocation;

        // 观察者在中心上方
        this.spectatorSpawn = centerLocation.clone().add(0, 50, 0);

        System.out.println("随机出生点已配置:");
        System.out.println("- 速通者出生点: " + speedrunnerSpawn);
        System.out.println("- 猎人出生点: " + hunterSpawn);
        System.out.println("- 观察者出生点: " + spectatorSpawn);
        System.out.println("- 最小距离: " + minDistance + " 方块");
    }

    /**
     * 寻找捕猎者出生位置
     */
    private Location findHunterSpawnLocation(int distance, boolean useCustomLogic) {
        // 在距离中心指定距离的圆周上随机选择一个点
        double angle = Math.random() * 2 * Math.PI;
        int x = (int) (distance * Math.cos(angle));
        int z = (int) (distance * Math.sin(angle));

        if (useCustomLogic) {
            // 使用自定义逻辑：寻找真正安全的地表位置
            Location safeLocation = findSafeSurfaceLocation(overworld, x, z);
            if (safeLocation != null) {
                System.out.println("捕猎者使用自定义出生点逻辑，位置: " + safeLocation);
                return safeLocation;
            }

            // 如果找不到安全位置，使用改进的最高点算法
            int safeY = findSafeY(overworld, x, z);
            Location fallbackLocation = new Location(overworld, x + 0.5, safeY, z + 0.5);
            System.out.println("捕猎者使用自定义出生点逻辑（后备方案），位置: " + fallbackLocation);
            return fallbackLocation;
        } else {
            // 使用原版逻辑：简单的最高点 + 1
            int highestY = overworld.getHighestBlockYAt(x, z);
            int spawnY = highestY + 1;
            Location vanillaLocation = new Location(overworld, x + 0.5, spawnY, z + 0.5);
            System.out.println("捕猎者使用原版出生点逻辑，位置: " + vanillaLocation);
            return vanillaLocation;
        }
    }

    /**
     * 寻找真正安全的地表位置
     */
    private Location findSafeSurfaceLocation(World world, int x, int z) {
        // 获取该位置的最高点
        int highestY = world.getHighestBlockYAt(x, z);

        // 检查是否在海洋中
        if (highestY < world.getSeaLevel()) {
            // 在海洋中，寻找海底的安全位置或者在海面上创建平台
            return findOceanSafeLocation(world, x, z, highestY);
        }

        // 从最高点开始向下搜索，寻找真正的地表
        for (int y = highestY; y >= Math.max(world.getSeaLevel(), world.getMinHeight() + 5); y--) {
            Location testLocation = new Location(world, x + 0.5, y + 1, z + 0.5);

            // 检查这个位置是否是真正的地表（不是洞穴顶部）
            if (isTrueSurfaceLocation(world, x, y, z)) {
                return testLocation;
            }
        }

        return null; // 没找到合适的地表位置
    }

    /**
     * 检查是否是真正的地表位置（不是洞穴顶部）
     */
    private boolean isTrueSurfaceLocation(World world, int x, int y, int z) {
        // 检查脚下有实体方块
        Material groundMaterial = world.getBlockAt(x, y, z).getType();
        if (!groundMaterial.isSolid()) {
            return false;
        }

        // 检查身体和头部位置为空气
        if (!world.getBlockAt(x, y + 1, z).getType().isAir() ||
            !world.getBlockAt(x, y + 2, z).getType().isAir()) {
            return false;
        }

        // 检查是否有足够的天空开放度（避免在洞穴中）
        int skyAccess = 0;
        for (int checkY = y + 3; checkY <= Math.min(y + 20, world.getMaxHeight() - 1); checkY++) {
            if (world.getBlockAt(x, checkY, z).getType().isAir()) {
                skyAccess++;
            }
        }

        // 如果上方至少有10格空气，认为是地表
        return skyAccess >= 10;
    }

    /**
     * 在海洋中寻找安全位置
     */
    private Location findOceanSafeLocation(World world, int x, int z, int seaFloorY) {
        // 优先在海面上创建一个小平台
        int seaLevel = world.getSeaLevel();

        // 在海面上创建一个3x3的石头平台
        for (int dx = -1; dx <= 1; dx++) {
            for (int dz = -1; dz <= 1; dz++) {
                world.getBlockAt(x + dx, seaLevel, z + dz).setType(org.bukkit.Material.STONE);
                // 确保平台上方是空气
                world.getBlockAt(x + dx, seaLevel + 1, z + dz).setType(org.bukkit.Material.AIR);
                world.getBlockAt(x + dx, seaLevel + 2, z + dz).setType(org.bukkit.Material.AIR);
            }
        }

        System.out.println("在海洋位置 (" + x + ", " + z + ") 创建了出生平台，高度: " + (seaLevel + 1));
        return new Location(world, x + 0.5, seaLevel + 1, z + 0.5);
    }

    /**
     * 寻找安全的Y坐标
     */
    private int findSafeY(World world, int x, int z) {
        // 从高处向下寻找安全的地面
        for (int y = world.getMaxHeight() - 1; y > world.getMinHeight(); y--) {
            Location testLocation = new Location(world, x, y, z);
            if (world.getBlockAt(testLocation).getType().isSolid() &&
                world.getBlockAt(testLocation.clone().add(0, 1, 0)).getType().isAir() &&
                world.getBlockAt(testLocation.clone().add(0, 2, 0)).getType().isAir()) {
                return y + 1; // 在地面上方一格
            }
        }

        // 如果找不到合适位置，在最高点上方生成一个安全平台
        int safeY = world.getHighestBlockYAt(x, z) + 1;
        // 确保有足够的空间
        world.getBlockAt(x, safeY - 1, z).setType(org.bukkit.Material.STONE);
        world.getBlockAt(x, safeY, z).setType(org.bukkit.Material.AIR);
        world.getBlockAt(x, safeY + 1, z).setType(org.bukkit.Material.AIR);
        return safeY;
    }
    
    public boolean isActive() {
        return isActive;
    }
    
    public void setActive(boolean active) {
        isActive = active;
    }
    
    public boolean isPreGenerated() {
        return isPreGenerated;
    }
    
    public void setPreGenerated(boolean preGenerated) {
        isPreGenerated = preGenerated;
    }
    
    /**
     * 获取世界中的所有玩家
     */
    public List<Player> getAllPlayers() {
        List<Player> allPlayers = new ArrayList<>();
        allPlayers.addAll(overworld.getPlayers());
        allPlayers.addAll(nether.getPlayers());
        allPlayers.addAll(end.getPlayers());
        return allPlayers;
    }
    
    /**
     * 获取指定世界
     */
    public World getWorld(World.Environment environment) {
        switch (environment) {
            case NORMAL:
                return overworld;
            case NETHER:
                return nether;
            case THE_END:
                return end;
            default:
                return overworld;
        }
    }
    
    /**
     * 检查世界是否属于这个游戏世界组
     */
    public boolean containsWorld(World world) {
        return world.equals(overworld) || world.equals(nether) || world.equals(end);
    }
    
    /**
     * 检查世界是否属于这个游戏世界组
     */
    public boolean containsWorld(String worldName) {
        return overworld.getName().equals(worldName) 
            || nether.getName().equals(worldName) 
            || end.getName().equals(worldName);
    }
    
    /**
     * 获取世界的环境类型
     */
    public World.Environment getEnvironment(World world) {
        if (world.equals(overworld)) {
            return World.Environment.NORMAL;
        } else if (world.equals(nether)) {
            return World.Environment.NETHER;
        } else if (world.equals(end)) {
            return World.Environment.THE_END;
        }
        return null;
    }
    
    /**
     * 获取所有世界
     */
    public List<World> getAllWorlds() {
        List<World> worlds = new ArrayList<>();
        worlds.add(overworld);
        worlds.add(nether);
        worlds.add(end);
        return worlds;
    }
    
    /**
     * 获取世界名称列表
     */
    public List<String> getWorldNames() {
        List<String> names = new ArrayList<>();
        names.add(overworld.getName());
        names.add(nether.getName());
        names.add(end.getName());
        return names;
    }
    
    /**
     * 设置世界时间
     */
    public void setTime(long time) {
        overworld.setTime(time);
        // 下界和末地通常不受时间影响，但为了一致性也设置
        nether.setTime(time);
        end.setTime(time);
    }
    
    /**
     * 设置世界天气
     */
    public void setStorm(boolean hasStorm) {
        overworld.setStorm(hasStorm);
        // 只有主世界有天气
    }
    
    /**
     * 清理世界（移除掉落物、怪物等）
     */
    public void cleanup() {
        cleanupWorld(overworld);
        cleanupWorld(nether);
        cleanupWorld(end);
    }
    
    /**
     * 清理单个世界
     */
    private void cleanupWorld(World world) {
        // 清理掉落物
        world.getEntities().stream()
            .filter(entity -> entity instanceof org.bukkit.entity.Item)
            .forEach(org.bukkit.entity.Entity::remove);
        
        // 清理经验球
        world.getEntities().stream()
            .filter(entity -> entity instanceof org.bukkit.entity.ExperienceOrb)
            .forEach(org.bukkit.entity.Entity::remove);
        
        // 可以根据需要添加更多清理逻辑
    }
    
    /**
     * 重置世界到初始状态
     */
    public void reset() {
        cleanup();
        setTime(0);
        setStorm(false);
        
        // 重置出生点
        overworld.setSpawnLocation(overworld.getSpawnLocation());
    }
    
    @Override
    public String toString() {
        return "GameWorld{" +
                "roomId='" + roomId + '\'' +
                ", overworld=" + overworld.getName() +
                ", nether=" + nether.getName() +
                ", end=" + end.getName() +
                ", createdTime=" + createdTime +
                ", isActive=" + isActive +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        GameWorld gameWorld = (GameWorld) obj;
        return roomId.equals(gameWorld.roomId);
    }
    
    @Override
    public int hashCode() {
        return roomId.hashCode();
    }

    /**
     * 寻找随机出生位置（单个位置）
     */
    private Location findRandomSpawnLocation(Location center, int minDistance, boolean useCustomLogic) {
        int maxAttempts = 50; // 最大尝试次数

        for (int attempt = 0; attempt < maxAttempts; attempt++) {
            // 在指定距离范围内随机选择一个点
            double angle = Math.random() * 2 * Math.PI;
            int distance = minDistance + (int)(Math.random() * minDistance); // 距离在minDistance到2*minDistance之间
            int x = center.getBlockX() + (int) (distance * Math.cos(angle));
            int z = center.getBlockZ() + (int) (distance * Math.sin(angle));

            Location spawnLocation = findSpawnLocationAt(x, z, useCustomLogic);
            if (spawnLocation != null) {
                return spawnLocation;
            }
        }

        // 如果找不到合适位置，使用默认逻辑
        System.out.println("警告：无法找到合适的随机出生点，使用默认位置");
        return center.clone();
    }

    /**
     * 寻找随机出生位置（确保与已有位置保持距离）
     */
    private Location findRandomSpawnLocation(Location center, int minDistance, boolean useCustomLogic, Location existingLocation) {
        int maxAttempts = 100; // 增加尝试次数，因为需要避开已有位置

        for (int attempt = 0; attempt < maxAttempts; attempt++) {
            // 在指定距离范围内随机选择一个点
            double angle = Math.random() * 2 * Math.PI;
            int distance = minDistance + (int)(Math.random() * minDistance); // 距离在minDistance到2*minDistance之间
            int x = center.getBlockX() + (int) (distance * Math.cos(angle));
            int z = center.getBlockZ() + (int) (distance * Math.sin(angle));

            Location candidateLocation = findSpawnLocationAt(x, z, useCustomLogic);
            if (candidateLocation != null) {
                // 检查与已有位置的距离
                double distanceToExisting = candidateLocation.distance(existingLocation);
                if (distanceToExisting >= minDistance) {
                    return candidateLocation;
                }
            }
        }

        // 如果找不到合适位置，使用默认逻辑但确保距离
        System.out.println("警告：无法找到合适的随机出生点，使用后备方案");
        double angle = Math.random() * 2 * Math.PI;
        int x = center.getBlockX() + (int) (minDistance * Math.cos(angle));
        int z = center.getBlockZ() + (int) (minDistance * Math.sin(angle));
        return findSpawnLocationAt(x, z, useCustomLogic);
    }

    /**
     * 在指定坐标寻找出生位置
     */
    private Location findSpawnLocationAt(int x, int z, boolean useCustomLogic) {
        if (useCustomLogic) {
            // 使用自定义逻辑：寻找真正安全的地表位置
            Location safeLocation = findSafeSurfaceLocation(overworld, x, z);
            if (safeLocation != null) {
                return safeLocation;
            }

            // 如果找不到安全位置，使用改进的最高点算法
            int safeY = findSafeY(overworld, x, z);
            return new Location(overworld, x + 0.5, safeY, z + 0.5);
        } else {
            // 使用原版逻辑：简单的最高点 + 1
            int highestY = overworld.getHighestBlockYAt(x, z);
            int spawnY = highestY + 1;
            return new Location(overworld, x + 0.5, spawnY, z + 0.5);
        }
    }
}
