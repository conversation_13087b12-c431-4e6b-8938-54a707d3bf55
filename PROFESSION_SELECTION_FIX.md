# 职业选择逻辑修复

## 🐛 问题描述

**问题现象：**
- 双方只要有一个选择职业，另外一个不会选择职业，游戏直接开始了
- 职业选择阶段没有等待所有玩家完成选择就直接进入游戏

**问题原因：**
- `checkAllPlayersSelectedProfession` 方法检查的是房间中所有非观察者玩家，包括离线玩家
- 如果有离线玩家，系统会认为他们"没有选择职业"，导致永远无法满足"所有玩家都选择了职业"的条件
- 但如果所有在线玩家都选择了职业，而离线玩家被忽略，就会导致游戏提前开始

## 🔧 解决方案

### 1. 修复职业选择检查逻辑

**修复位置：** `src/main/java/com/projectSource/ultimateManhurt/game/GameSession.java`

**修复前的问题：**
```java
// 检查所有非观察者玩家是否都已选择职业
boolean allSelected = true;
for (UUID playerId : playerRoles.keySet()) {
    PlayerRole role = playerRoles.get(playerId);
    if (role != PlayerRole.SPECTATOR) {
        if (!plugin.getProfessionManager().hasPlayerProfession(playerId)) {
            allSelected = false; // 包括离线玩家
            break;
        }
    }
}
```

**修复后的逻辑：**
```java
// 统计需要选择职业的在线玩家
int totalPlayersNeedingProfession = 0;
int playersWithProfession = 0;

for (UUID playerId : playerRoles.keySet()) {
    PlayerRole role = playerRoles.get(playerId);
    if (role != PlayerRole.SPECTATOR) {
        // 检查玩家是否在线
        Player player = Bukkit.getPlayer(playerId);
        if (player != null && player.isOnline()) {
            totalPlayersNeedingProfession++;
            if (plugin.getProfessionManager().hasPlayerProfession(playerId)) {
                playersWithProfession++;
            }
        }
    }
}

// 只有当所有在线的非观察者玩家都选择了职业时才继续
if (totalPlayersNeedingProfession > 0 && playersWithProfession >= totalPlayersNeedingProfession) {
    proceedAfterProfessionSelection();
}
```

### 2. 增强职业选择阶段启动逻辑

**改进的 `startProfessionSelection` 方法：**
```java
private void startProfessionSelection() {
    broadcastMessage(ComponentUtil.info("=== 职业选择阶段开始 ==="));
    broadcastMessage(ComponentUtil.info("请选择你的职业来增强你的能力"));

    // 统计需要选择职业的在线玩家数量
    int onlinePlayersNeedingProfession = 0;
    
    // 为所有非观察者玩家打开职业选择GUI
    for (UUID playerId : playerRoles.keySet()) {
        PlayerRole role = playerRoles.get(playerId);
        if (role != PlayerRole.SPECTATOR) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                onlinePlayersNeedingProfession++;
                plugin.getGuiManager().openProfessionSelectionGui(player, this);
            }
        }
    }

    // 如果没有在线玩家需要选择职业，直接继续游戏流程
    if (onlinePlayersNeedingProfession == 0) {
        plugin.getLogger().warning("没有在线玩家需要选择职业，跳过职业选择阶段");
        proceedAfterProfessionSelection();
        return;
    }
    
    // 设置职业选择超时（60秒）
    Bukkit.getScheduler().runTaskLater(plugin, () -> {
        if (state == GameState.PROFESSION_SELECTION) {
            plugin.getLogger().warning("职业选择阶段超时，强制继续游戏");
            broadcastMessage(ComponentUtil.warning("职业选择时间已到，未选择职业的玩家将使用默认职业"));
            
            // 为未选择职业的玩家分配默认职业
            assignDefaultProfessions();
            
            // 继续游戏流程
            proceedAfterProfessionSelection();
        }
    }, 60 * 20L); // 60秒
}
```

### 3. 添加默认职业分配机制

**新增方法：**
```java
/**
 * 为未选择职业的玩家分配默认职业
 */
private void assignDefaultProfessions() {
    for (UUID playerId : playerRoles.keySet()) {
        PlayerRole role = playerRoles.get(playerId);
        if (role != PlayerRole.SPECTATOR) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                if (!plugin.getProfessionManager().hasPlayerProfession(playerId)) {
                    // 根据角色分配默认职业
                    Profession defaultProfession = getDefaultProfessionForRole(role);
                    if (defaultProfession != null) {
                        boolean success = plugin.getProfessionManager().setProfession(playerId, defaultProfession);
                        if (success) {
                            ComponentUtil.sendMessage(player, ComponentUtil.warning(
                                "由于超时，已为你分配默认职业: " + defaultProfession.getDisplayName()));
                        }
                    }
                }
            }
        }
    }
}

/**
 * 获取角色的默认职业
 */
private Profession getDefaultProfessionForRole(PlayerRole role) {
    Profession[] availableProfessions = Profession.getAvailableProfessions(role);
    // 返回第一个可用职业作为默认职业
    return availableProfessions.length > 0 ? availableProfessions[0] : null;
}
```

## 🎯 修复特点

### 精确统计
- **在线检查：** 只统计在线的非观察者玩家
- **实时计数：** 准确计算需要选择职业的玩家数量
- **调试信息：** 记录详细的选择进度日志

### 超时保护
- **60秒超时：** 防止职业选择阶段无限等待
- **默认分配：** 超时后自动为未选择的玩家分配默认职业
- **友好提示：** 告知玩家超时和默认职业分配

### 边界情况处理
- **无在线玩家：** 如果没有在线玩家需要选择职业，直接跳过
- **离线玩家：** 忽略离线玩家，不影响游戏进程
- **状态检查：** 确保只在职业选择阶段执行超时逻辑

## 🧪 测试场景

### 正常情况
1. **双方在线：** 速通者和捕猎者都在线并选择职业
2. **预期结果：** 等待所有在线玩家选择完毕后开始游戏

### 离线玩家情况
1. **部分离线：** 房间中有离线玩家，但在线玩家都选择了职业
2. **预期结果：** 忽略离线玩家，在线玩家选择完毕后开始游戏

### 超时情况
1. **选择超时：** 某些玩家在60秒内未选择职业
2. **预期结果：** 自动分配默认职业并开始游戏

### 边界情况
1. **无需选择：** 所有玩家都是观察者或离线
2. **预期结果：** 直接跳过职业选择阶段

## 📊 调试信息

**新增的日志记录：**
- 职业选择进度统计
- 在线玩家数量统计
- 超时处理记录
- 默认职业分配记录

**示例日志：**
```
[INFO] 房间 TestRoom 开始职业选择阶段，共有 2 名在线玩家需要选择职业
[INFO] 职业选择进度: 1/2 玩家已选择职业
[INFO] 职业选择进度: 2/2 玩家已选择职业
[INFO] 所有在线玩家都已选择职业，继续游戏流程
```

## 🎉 总结

成功修复了职业选择阶段的逻辑问题：

- ✅ **精确检查：** 只检查在线的非观察者玩家
- ✅ **实时统计：** 准确计算选择进度
- ✅ **超时保护：** 60秒超时机制防止无限等待
- ✅ **默认分配：** 超时后自动分配默认职业
- ✅ **边界处理：** 处理无在线玩家等特殊情况
- ✅ **调试友好：** 详细的日志记录便于问题排查

现在职业选择阶段会正确等待所有在线玩家完成选择，不会因为离线玩家或其他问题导致游戏提前开始！🎮✨
