package com.projectSource.ultimateManhurt.listener;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.game.scoring.ScoreMilestone;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.world.PortalCreateEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.inventory.CraftItemEvent;
import org.bukkit.event.inventory.FurnaceExtractEvent;
import org.bukkit.event.player.*;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.entity.EntityType;

import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 积分监听器
 * 监听玩家的各种行为并给予相应的积分
 */
public class ScoreListener implements Listener {
    
    private final UltimateManhurt plugin;
    
    // 记录玩家已获得的里程碑，避免重复给分
    private final Set<String> completedMilestones = ConcurrentHashMap.newKeySet();

    // 记录玩家死亡状态，用于无死亡奖励
    private final Set<UUID> playerDeaths = ConcurrentHashMap.newKeySet();

    // 记录玩家被捕猎者攻击的时间，用于逃脱检测
    private final Map<UUID, Long> hunterAttackTime = new ConcurrentHashMap<>();
    
    public ScoreListener(UltimateManhurt plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onBlockBreak(BlockBreakEvent event) {
        if (event.isCancelled()) return;
        
        Player player = event.getPlayer();
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
        if (gameSession == null) return;
        
        PlayerRole role = gameSession.getPlayerRole(player.getUniqueId());
        if (role != PlayerRole.SPEEDRUNNER) return;
        
        Material blockType = event.getBlock().getType();
        String milestoneKey = player.getUniqueId() + ":" + blockType.name();
        
        // 检查木头
        if (isWoodType(blockType) && !completedMilestones.contains(player.getUniqueId() + ":WOOD")) {
            gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.GET_WOOD);
            completedMilestones.add(player.getUniqueId() + ":WOOD");
        }
        
        // 检查石头
        if (blockType == Material.STONE && !completedMilestones.contains(milestoneKey)) {
            gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.MINE_STONE);
            completedMilestones.add(milestoneKey);
        }
        
        // 检查钻石矿
        if ((blockType == Material.DIAMOND_ORE || blockType == Material.DEEPSLATE_DIAMOND_ORE)
            && !completedMilestones.contains(player.getUniqueId() + ":DIAMOND")) {
            gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.FIND_DIAMONDS);
            completedMilestones.add(player.getUniqueId() + ":DIAMOND");
        }

        // 检查铁矿
        if ((blockType == Material.IRON_ORE || blockType == Material.DEEPSLATE_IRON_ORE)
            && !completedMilestones.contains(player.getUniqueId() + ":IRON_ORE")) {
            // 铁矿不直接给分，但记录挖掘状态
            completedMilestones.add(player.getUniqueId() + ":IRON_ORE");
        }
    }



    @EventHandler(priority = EventPriority.MONITOR)
    public void onCraftItem(CraftItemEvent event) {
        if (event.isCancelled()) return;
        
        Player player = (Player) event.getWhoClicked();
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
        if (gameSession == null) return;
        
        PlayerRole role = gameSession.getPlayerRole(player.getUniqueId());
        if (role != PlayerRole.SPEEDRUNNER) return;
        
        ItemStack result = event.getRecipe().getResult();
        Material itemType = result.getType();
        String milestoneKey = player.getUniqueId() + ":" + itemType.name();
        
        if (completedMilestones.contains(milestoneKey)) return;
        
        switch (itemType) {
            case CRAFTING_TABLE:
                gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.CRAFT_WORKBENCH);
                completedMilestones.add(milestoneKey);
                break;

            // 木制工具
            case WOODEN_SWORD:
            case WOODEN_PICKAXE:
            case WOODEN_AXE:
            case WOODEN_SHOVEL:
                if (!completedMilestones.contains(player.getUniqueId() + ":WOODEN_TOOLS")) {
                    gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.CRAFT_WOODEN_TOOLS);
                    completedMilestones.add(player.getUniqueId() + ":WOODEN_TOOLS");
                }
                break;

            // 石制工具
            case STONE_SWORD:
            case STONE_PICKAXE:
            case STONE_AXE:
            case STONE_SHOVEL:
                if (!completedMilestones.contains(player.getUniqueId() + ":STONE_TOOLS")) {
                    gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.CRAFT_STONE_TOOLS);
                    completedMilestones.add(player.getUniqueId() + ":STONE_TOOLS");
                }
                break;

            case FURNACE:
                gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.CRAFT_FURNACE);
                completedMilestones.add(milestoneKey);
                break;

            // 铁制工具
            case IRON_SWORD:
            case IRON_PICKAXE:
            case IRON_AXE:
            case IRON_SHOVEL:
                if (!completedMilestones.contains(player.getUniqueId() + ":IRON_TOOLS")) {
                    gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.CRAFT_IRON_TOOLS);
                    completedMilestones.add(player.getUniqueId() + ":IRON_TOOLS");
                }
                break;

            // 铁制盔甲
            case IRON_HELMET:
            case IRON_CHESTPLATE:
            case IRON_LEGGINGS:
            case IRON_BOOTS:
                if (!completedMilestones.contains(player.getUniqueId() + ":IRON_ARMOR")) {
                    gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.CRAFT_IRON_ARMOR);
                    completedMilestones.add(player.getUniqueId() + ":IRON_ARMOR");
                }
                break;

            // 钻石工具
            case DIAMOND_SWORD:
            case DIAMOND_PICKAXE:
            case DIAMOND_AXE:
            case DIAMOND_SHOVEL:
                if (!completedMilestones.contains(player.getUniqueId() + ":DIAMOND_TOOLS")) {
                    gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.CRAFT_DIAMOND_TOOLS);
                    completedMilestones.add(player.getUniqueId() + ":DIAMOND_TOOLS");
                }
                break;

            // 钻石盔甲
            case DIAMOND_HELMET:
            case DIAMOND_CHESTPLATE:
            case DIAMOND_LEGGINGS:
            case DIAMOND_BOOTS:
                if (!completedMilestones.contains(player.getUniqueId() + ":DIAMOND_ARMOR")) {
                    gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.CRAFT_DIAMOND_ARMOR);
                    completedMilestones.add(player.getUniqueId() + ":DIAMOND_ARMOR");
                }
                break;

            case BREWING_STAND:
                gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.CRAFT_BREWING_STAND);
                completedMilestones.add(milestoneKey);
                break;

            case ENDER_EYE:
                gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.CRAFT_ENDER_EYE);
                completedMilestones.add(milestoneKey);
                break;

            default:
                // 其他物品不给予积分
                break;
        }

        // 检查全套装备制作
        checkFullSetCrafting(player, gameSession);
    }

    /**
     * 检查全套装备制作
     */
    private void checkFullSetCrafting(Player player, GameSession gameSession) {
        String milestoneKey = player.getUniqueId() + ":CRAFT_FULL_SET";
        if (completedMilestones.contains(milestoneKey)) return;

        // 移除了CRAFT_FULL_SET里程碑检查，因为与其他里程碑重复
        // 全套装备的逻辑已被移除
    }

    /**
     * 检查是否有全套装备
     */
    private boolean hasFullEquipmentSet(Player player) {
        org.bukkit.inventory.PlayerInventory inventory = player.getInventory();

        // 检查是否有完整的工具套装（剑、镐、斧、铲）
        boolean hasTools = (inventory.contains(Material.IRON_SWORD) || inventory.contains(Material.DIAMOND_SWORD)) &&
                          (inventory.contains(Material.IRON_PICKAXE) || inventory.contains(Material.DIAMOND_PICKAXE)) &&
                          (inventory.contains(Material.IRON_AXE) || inventory.contains(Material.DIAMOND_AXE)) &&
                          (inventory.contains(Material.IRON_SHOVEL) || inventory.contains(Material.DIAMOND_SHOVEL));

        // 检查是否有完整的盔甲套装
        boolean hasArmor = (inventory.contains(Material.IRON_HELMET) || inventory.contains(Material.DIAMOND_HELMET)) &&
                          (inventory.contains(Material.IRON_CHESTPLATE) || inventory.contains(Material.DIAMOND_CHESTPLATE)) &&
                          (inventory.contains(Material.IRON_LEGGINGS) || inventory.contains(Material.DIAMOND_LEGGINGS)) &&
                          (inventory.contains(Material.IRON_BOOTS) || inventory.contains(Material.DIAMOND_BOOTS));

        return hasTools && hasArmor;
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onFurnaceExtract(FurnaceExtractEvent event) {
        Player player = event.getPlayer();
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
        if (gameSession == null) return;

        PlayerRole role = gameSession.getPlayerRole(player.getUniqueId());
        if (role != PlayerRole.SPEEDRUNNER) return;

        Material itemType = event.getItemType();
        String milestoneKey = player.getUniqueId() + ":" + itemType.name();

        if (completedMilestones.contains(milestoneKey)) return;

        switch (itemType) {
            case IRON_INGOT:
                gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.SMELT_IRON);
                completedMilestones.add(milestoneKey);
                break;
            default:
                break;
        }
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onPortalCreate(PortalCreateEvent event) {
        if (event.isCancelled()) return;

        // 只处理下界传送门
        if (event.getReason() != PortalCreateEvent.CreateReason.FIRE) return;

        // 寻找附近的速通者
        org.bukkit.Location portalLocation = event.getBlocks().get(0).getLocation();
        org.bukkit.World world = portalLocation.getWorld();
        if (world == null) return;

        // 在10格范围内寻找速通者
        for (Player player : world.getNearbyEntities(portalLocation, 10, 10, 10).stream()
            .filter(entity -> entity instanceof Player)
            .map(entity -> (Player) entity)
            .toList()) {

            GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
            if (gameSession == null) continue;

            PlayerRole role = gameSession.getPlayerRole(player.getUniqueId());
            if (role != PlayerRole.SPEEDRUNNER) continue;

            String milestoneKey = player.getUniqueId() + ":BUILD_NETHER_PORTAL";
            if (!completedMilestones.contains(milestoneKey)) {
                gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.BUILD_NETHER_PORTAL);
                completedMilestones.add(milestoneKey);

                plugin.getLogger().info("玩家 " + player.getName() + " 建造了下界传送门");
                break; // 只给第一个找到的速通者积分
            }
        }
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerChangedWorld(PlayerChangedWorldEvent event) {
        Player player = event.getPlayer();
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
        if (gameSession == null) return;

        PlayerRole role = gameSession.getPlayerRole(player.getUniqueId());
        if (role != PlayerRole.SPEEDRUNNER) return;

        org.bukkit.World.Environment environment = player.getWorld().getEnvironment();

        // 检查进入下界
        if (environment == org.bukkit.World.Environment.NETHER) {
            String netherKey = player.getUniqueId() + ":ENTER_NETHER";
            if (!completedMilestones.contains(netherKey)) {
                gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.ENTER_NETHER);
                completedMilestones.add(netherKey);

                // 检查速度奖励
                gameSession.getScoreSystem().checkSpeedBonus(player.getUniqueId(), ScoreMilestone.ENTER_NETHER);
            }
        }

        // 检查进入末地
        if (environment == org.bukkit.World.Environment.THE_END) {
            String endKey = player.getUniqueId() + ":ENTER_END";
            if (!completedMilestones.contains(endKey)) {
                gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.ENTER_END);
                completedMilestones.add(endKey);

                // 检查速度奖励
                gameSession.getScoreSystem().checkSpeedBonus(player.getUniqueId(), ScoreMilestone.ENTER_END);
            }
        }
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onEntityDeath(EntityDeathEvent event) {
        if (event.getEntity().getKiller() == null) return;

        Player player = event.getEntity().getKiller();
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
        if (gameSession == null) return;

        PlayerRole role = gameSession.getPlayerRole(player.getUniqueId());
        if (role != PlayerRole.SPEEDRUNNER) return;

        EntityType entityType = event.getEntity().getType();
        String milestoneKey = player.getUniqueId() + ":" + entityType.name();

        if (completedMilestones.contains(milestoneKey)) return;

        switch (entityType) {
            case BLAZE:
                gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.KILL_BLAZE);
                completedMilestones.add(milestoneKey);

                // 检查是否掉落烈焰棒
                if (event.getDrops().stream().anyMatch(item -> item.getType() == Material.BLAZE_ROD)) {
                    String blazeRodKey = player.getUniqueId() + ":BLAZE_ROD";
                    if (!completedMilestones.contains(blazeRodKey)) {
                        gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.GET_BLAZE_ROD);
                        completedMilestones.add(blazeRodKey);
                    }
                }
                break;
            case ENDER_DRAGON:
                gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.KILL_DRAGON);
                completedMilestones.add(milestoneKey);

                // 检查速度奖励
                gameSession.getScoreSystem().checkSpeedBonus(player.getUniqueId(), ScoreMilestone.KILL_DRAGON);
                break;
            case PLAYER:
                // 检查是否击杀了捕猎者
                Player killedPlayer = (Player) event.getEntity();
                GameSession killedGameSession = plugin.getGameManager().getGameSessionByPlayer(killedPlayer.getUniqueId());
                if (killedGameSession != null && killedGameSession.equals(gameSession)) {
                    PlayerRole killedRole = killedGameSession.getPlayerRole(killedPlayer.getUniqueId());
                    if (killedRole == PlayerRole.HUNTER) {
                        String killHunterKey = player.getUniqueId() + ":KILL_HUNTER";
                        if (!completedMilestones.contains(killHunterKey)) {
                            gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.KILL_HUNTER);
                            completedMilestones.add(killHunterKey);
                        }
                    }
                }
                break;
            default:
                break;
        }
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerDeath(PlayerDeathEvent event) {
        Player player = event.getEntity();
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
        if (gameSession == null) return;

        PlayerRole role = gameSession.getPlayerRole(player.getUniqueId());
        if (role != PlayerRole.SPEEDRUNNER) return;

        // 记录玩家死亡，影响无死亡奖励
        playerDeaths.add(player.getUniqueId());
        gameSession.getScoreSystem().recordPlayerDeath(player.getUniqueId());
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        if (event.isCancelled()) return;

        // 检查是否对末影龙造成伤害
        if (event.getEntity().getType() == EntityType.ENDER_DRAGON && event.getDamager() instanceof Player) {
            Player player = (Player) event.getDamager();
            GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
            if (gameSession == null) return;

            PlayerRole role = gameSession.getPlayerRole(player.getUniqueId());
            if (role != PlayerRole.SPEEDRUNNER) return;

            String milestoneKey = player.getUniqueId() + ":DAMAGE_DRAGON";
            if (!completedMilestones.contains(milestoneKey)) {
                gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.DAMAGE_DRAGON);
                completedMilestones.add(milestoneKey);
            }
        }

        // 检查捕猎者攻击速通者的情况（用于逃脱检测）
        if (event.getEntity() instanceof Player && event.getDamager() instanceof Player) {
            Player victim = (Player) event.getEntity();
            Player attacker = (Player) event.getDamager();

            GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(victim.getUniqueId());
            if (gameSession == null) return;

            PlayerRole victimRole = gameSession.getPlayerRole(victim.getUniqueId());
            PlayerRole attackerRole = gameSession.getPlayerRole(attacker.getUniqueId());

            // 如果捕猎者攻击速通者，记录攻击时间
            if (victimRole == PlayerRole.SPEEDRUNNER && attackerRole == PlayerRole.HUNTER) {
                hunterAttackTime.put(victim.getUniqueId(), System.currentTimeMillis());

                // 延迟检查是否成功逃脱
                org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    checkEscapeFromHunter(victim, gameSession);
                }, 200L); // 10秒后检查
            }
        }
    }

    /**
     * 检查是否成功逃脱捕猎者
     */
    private void checkEscapeFromHunter(Player player, GameSession gameSession) {
        String milestoneKey = player.getUniqueId() + ":ESCAPE_HUNTER";
        if (completedMilestones.contains(milestoneKey)) return;

        Long attackTime = hunterAttackTime.get(player.getUniqueId());
        if (attackTime == null) return;

        long currentTime = System.currentTimeMillis();

        // 如果玩家在被攻击后10秒内没有死亡，且距离攻击者足够远，认为成功逃脱
        if (currentTime - attackTime >= 10000 && player.isOnline() && !playerDeaths.contains(player.getUniqueId())) {
            // 检查是否远离捕猎者
            boolean escapedSuccessfully = true;
            for (UUID hunterId : gameSession.getPlayersByRole(PlayerRole.HUNTER)) {
                Player hunter = org.bukkit.Bukkit.getPlayer(hunterId);
                if (hunter != null && hunter.isOnline()) {
                    double distance = player.getLocation().distance(hunter.getLocation());
                    if (distance < 50) { // 如果距离任何捕猎者少于50格，认为没有成功逃脱
                        escapedSuccessfully = false;
                        break;
                    }
                }
            }

            if (escapedSuccessfully) {
                // 移除了ESCAPE_HUNTER里程碑，因为在积分模式下不适用
                // 逃脱捕猎者的奖励逻辑已被移除
                hunterAttackTime.remove(player.getUniqueId()); // 清除记录
            }
        }
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onEntityPickupItem(org.bukkit.event.entity.EntityPickupItemEvent event) {
        if (!(event.getEntity() instanceof Player)) return;

        Player player = (Player) event.getEntity();
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
        if (gameSession == null) return;

        PlayerRole role = gameSession.getPlayerRole(player.getUniqueId());
        if (role != PlayerRole.SPEEDRUNNER) return;

        Material itemType = event.getItem().getItemStack().getType();
        String milestoneKey = player.getUniqueId() + ":" + itemType.name();

        if (completedMilestones.contains(milestoneKey)) return;

        switch (itemType) {
            case BLAZE_ROD:
                gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.GET_BLAZE_ROD);
                completedMilestones.add(milestoneKey);
                break;
            case DIAMOND:
                if (!completedMilestones.contains(player.getUniqueId() + ":DIAMOND_ITEM")) {
                    gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.FIND_DIAMONDS);
                    completedMilestones.add(player.getUniqueId() + ":DIAMOND_ITEM");
                }
                break;
            default:
                break;
        }

        // 检查收集材料成就
        checkMaterialCollection(player, gameSession);
    }

    /**
     * 检查材料收集成就
     */
    private void checkMaterialCollection(Player player, GameSession gameSession) {
        String milestoneKey = player.getUniqueId() + ":COLLECT_ALL_MATERIALS";
        if (completedMilestones.contains(milestoneKey)) return;

        // 检查玩家背包中是否有所有基础材料
        org.bukkit.inventory.PlayerInventory inventory = player.getInventory();
        boolean hasAllMaterials = hasRequiredMaterials(inventory);

        if (hasAllMaterials) {
            // 移除了COLLECT_ALL_MATERIALS里程碑，因为过于复杂
            // 收集所有材料的奖励逻辑已被移除
        }
    }

    /**
     * 检查是否有所有必需的材料
     */
    private boolean hasRequiredMaterials(org.bukkit.inventory.PlayerInventory inventory) {
        // 定义必需的材料列表
        Material[] requiredMaterials = {
            Material.OAK_LOG, Material.STONE, Material.IRON_INGOT, Material.DIAMOND,
            Material.COAL, Material.REDSTONE, Material.GOLD_INGOT, Material.EMERALD,
            Material.BLAZE_ROD, Material.ENDER_PEARL
        };

        for (Material material : requiredMaterials) {
            if (!inventory.contains(material)) {
                return false;
            }
        }
        return true;
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (event.isCancelled()) return;

        Player player = event.getPlayer();
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
        if (gameSession == null) return;

        PlayerRole role = gameSession.getPlayerRole(player.getUniqueId());
        if (role != PlayerRole.SPEEDRUNNER) return;

        // 检查是否在末地传送门框架上放置末影之眼
        if (event.getAction() == org.bukkit.event.block.Action.RIGHT_CLICK_BLOCK &&
            event.getClickedBlock() != null &&
            event.getClickedBlock().getType() == Material.END_PORTAL_FRAME &&
            event.getItem() != null &&
            event.getItem().getType() == Material.ENDER_EYE) {

            // 延迟检查是否激活了末地传送门
            org.bukkit.Location frameLocation = event.getClickedBlock().getLocation();
            org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
                checkEndPortalActivation(player, frameLocation, gameSession);
            }, 5L); // 延迟5tick检查
        }
    }

    /**
     * 检查末地传送门激活
     */
    private void checkEndPortalActivation(Player player, org.bukkit.Location frameLocation, GameSession gameSession) {
        String milestoneKey = player.getUniqueId() + ":ACTIVATE_END_PORTAL";
        if (completedMilestones.contains(milestoneKey)) return;

        // 检查附近是否有末地传送门方块
        org.bukkit.World world = frameLocation.getWorld();
        if (world == null) return;

        for (int x = -3; x <= 3; x++) {
            for (int y = -2; y <= 2; y++) {
                for (int z = -3; z <= 3; z++) {
                    org.bukkit.Location checkLoc = frameLocation.clone().add(x, y, z);
                    if (world.getBlockAt(checkLoc).getType() == Material.END_PORTAL) {
                        gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.ACTIVATE_END_PORTAL);
                        completedMilestones.add(milestoneKey);
                        return;
                    }
                }
            }
        }
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerMove(PlayerMoveEvent event) {
        if (event.isCancelled()) return;

        Player player = event.getPlayer();
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
        if (gameSession == null) return;

        PlayerRole role = gameSession.getPlayerRole(player.getUniqueId());
        if (role != PlayerRole.SPEEDRUNNER) return;

        org.bukkit.Location location = player.getLocation();
        org.bukkit.World world = location.getWorld();
        if (world == null) return;

        // 移除了有问题的下界要塞和主世界要塞检测
        // 这些检测机制不够准确，容易误判
    }

    // 移除了checkNetherFortress和checkStronghold方法
    // 这些检测机制不够准确，容易误判，已被移除

    // 移除了hasNetherFortressBlocks和hasStrongholdBlocks方法
    // 这些检测方法不够准确，已被移除

    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerAdvancement(PlayerAdvancementDoneEvent event) {
        Player player = event.getPlayer();
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
        if (gameSession == null) return;
        
        PlayerRole role = gameSession.getPlayerRole(player.getUniqueId());
        if (role != PlayerRole.SPEEDRUNNER) return;
        
        String advancementKey = event.getAdvancement().getKey().getKey();
        String milestoneKey = player.getUniqueId() + ":" + advancementKey;
        
        if (completedMilestones.contains(milestoneKey)) return;
        
        switch (advancementKey) {
            case "story/enter_the_nether":
                gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.ENTER_NETHER);
                completedMilestones.add(milestoneKey);
                break;
            // 移除了 "story/follow_ender_eye" case，因为FIND_STRONGHOLD里程碑已被移除
            case "story/enter_the_end":
                gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.ENTER_END);
                completedMilestones.add(milestoneKey);
                break;
            case "end/kill_dragon":
                gameSession.getScoreSystem().addScore(player.getUniqueId(), ScoreMilestone.KILL_DRAGON);
                completedMilestones.add(milestoneKey);
                break;
        }
    }
    
    /**
     * 检查是否为木头类型
     */
    private boolean isWoodType(Material material) {
        return material.name().contains("LOG") || 
               material.name().contains("WOOD") ||
               material == Material.OAK_LOG ||
               material == Material.BIRCH_LOG ||
               material == Material.SPRUCE_LOG ||
               material == Material.JUNGLE_LOG ||
               material == Material.ACACIA_LOG ||
               material == Material.DARK_OAK_LOG;
    }
    
    /**
     * 清理玩家的里程碑记录（游戏结束时调用）
     */
    public void clearPlayerMilestones(UUID playerId) {
        completedMilestones.removeIf(key -> key.startsWith(playerId.toString()));
    }
    
    /**
     * 清理所有里程碑记录
     */
    public void clearAllMilestones() {
        completedMilestones.clear();
    }
}
