package com.projectSource.ultimateManhurt.data;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据管理器
 * 负责玩家数据和游戏数据的存储和加载
 */
public class DataManager {

    private final UltimateManhurt plugin;
    private final Map<UUID, PlayerData> playerDataCache = new ConcurrentHashMap<>();
    private final File dataFolder;

    public DataManager(UltimateManhurt plugin) {
        this.plugin = plugin;
        this.dataFolder = new File(plugin.getDataFolder(), "playerdata");
        initialize();
    }

    /**
     * 初始化数据管理器
     */
    private void initialize() {
        // 创建数据文件夹
        if (!dataFolder.exists()) {
            dataFolder.mkdirs();
        }

        plugin.getLogger().info("数据管理器已初始化");
    }

    /**
     * 加载玩家数据
     */
    public PlayerData loadPlayerData(UUID playerId) {
        // 先检查缓存
        PlayerData data = playerDataCache.get(playerId);
        if (data != null) {
            return data;
        }

        // 从文件加载（这里简化实现，实际项目中可以使用JSON或数据库）
        data = new PlayerData(playerId);

        // 加载基础数据
        File playerFile = new File(dataFolder, playerId.toString() + ".yml");
        if (playerFile.exists()) {
            try {
                FileConfiguration config = YamlConfiguration.loadConfiguration(playerFile);
                loadPlayerDataFromConfig(data, config);
                plugin.getLogger().info("成功加载玩家数据: " + playerId);
            } catch (Exception e) {
                plugin.getLogger().severe("加载玩家数据失败 " + playerId + ": " + e.getMessage());
                // 使用默认数据
            }
        } else {
            plugin.getLogger().info("创建新玩家数据: " + playerId);
        }

        // 缓存数据
        playerDataCache.put(playerId, data);
        return data;
    }

    /**
     * 保存玩家数据
     */
    public void savePlayerData(UUID playerId) {
        PlayerData data = playerDataCache.get(playerId);
        if (data == null) {
            return;
        }

        // 保存到文件
        File playerFile = new File(dataFolder, playerId.toString() + ".yml");
        try {
            FileConfiguration config = new YamlConfiguration();
            savePlayerDataToConfig(data, config);
            config.save(playerFile);
            plugin.getLogger().info("成功保存玩家数据: " + playerId);
        } catch (IOException e) {
            plugin.getLogger().severe("保存玩家数据失败 " + playerId + ": " + e.getMessage());
        }
    }

    /**
     * 获取玩家数据
     */
    public PlayerData getPlayerData(UUID playerId) {
        // 先检查缓存，避免递归调用
        PlayerData data = playerDataCache.get(playerId);
        if (data != null) {
            return data;
        }

        // 如果缓存中没有，则加载数据
        return loadPlayerData(playerId);
    }

    /**
     * 移除玩家数据缓存
     */
    public void removePlayerData(UUID playerId) {
        playerDataCache.remove(playerId);
    }

    /**
     * 保存所有数据
     */
    public void saveAll() {
        for (UUID playerId : playerDataCache.keySet()) {
            savePlayerData(playerId);
        }
        plugin.getLogger().info("所有数据已保存");
    }

    /**
     * 加载所有数据
     */
    public void loadAll() {
        // 这里可以预加载一些全局数据
        plugin.getLogger().info("所有数据已加载");
    }

    /**
     * 获取数据文件夹
     */
    public File getDataFolder() {
        return dataFolder;
    }

    /**
     * 获取缓存的玩家数量
     */
    public int getCachedPlayerCount() {
        return playerDataCache.size();
    }

    /**
     * 从配置文件加载玩家数据
     */
    private void loadPlayerDataFromConfig(PlayerData data, FileConfiguration config) {
        // 基础信息
        if (config.contains("first-join")) {
            data.setFirstJoin(LocalDateTime.parse(Objects.requireNonNull(config.getString("first-join")), DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        }
        if (config.contains("last-seen")) {
            data.setLastSeen(LocalDateTime.parse(Objects.requireNonNull(config.getString("last-seen")), DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        }

        // 游戏统计
        data.setTotalGames(config.getInt("stats.total-games", 0));
        data.setGamesWon(config.getInt("stats.games-won", 0));
        data.setGamesLost(config.getInt("stats.games-lost", 0));
        data.setTotalKills(config.getInt("stats.total-kills", 0));
        data.setTotalDeaths(config.getInt("stats.total-deaths", 0));
        data.setTotalPlayTime(config.getLong("stats.total-playtime", 0));

        // 角色统计
        data.setSpeedrunnerGames(config.getInt("stats.speedrunner-games", 0));
        data.setSpeedrunnerWins(config.getInt("stats.speedrunner-wins", 0));
        data.setHunterGames(config.getInt("stats.hunter-games", 0));
        data.setHunterWins(config.getInt("stats.hunter-wins", 0));

        // 记录
        data.setFastestDragonKill(config.getLong("records.fastest-dragon-kill", Long.MAX_VALUE));
        data.setLongestSurvival(config.getInt("records.longest-survival", 0));
        data.setMaxKillStreak(config.getInt("records.max-kill-streak", 0));

        // 设置
        data.setShowScoreboard(config.getBoolean("settings.show-scoreboard", true));
        data.setPlaySound(config.getBoolean("settings.play-sound", true));
        data.setPreferredLanguage(config.getString("settings.language", "zh_CN"));

        // 自定义数据
        if (config.contains("custom")) {
            for (String key : Objects.requireNonNull(config.getConfigurationSection("custom")).getKeys(false)) {
                data.setCustomData(key, config.get("custom." + key));
            }
        }
    }

    /**
     * 将玩家数据保存到配置文件
     */
    private void savePlayerDataToConfig(PlayerData data, FileConfiguration config) {
        // 基础信息
        config.set("first-join", data.getFirstJoin().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        config.set("last-seen", data.getLastSeen().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

        // 游戏统计
        config.set("stats.total-games", data.getTotalGames());
        config.set("stats.games-won", data.getGamesWon());
        config.set("stats.games-lost", data.getGamesLost());
        config.set("stats.total-kills", data.getTotalKills());
        config.set("stats.total-deaths", data.getTotalDeaths());
        config.set("stats.total-playtime", data.getTotalPlayTime());

        // 角色统计
        config.set("stats.speedrunner-games", data.getSpeedrunnerGames());
        config.set("stats.speedrunner-wins", data.getSpeedrunnerWins());
        config.set("stats.hunter-games", data.getHunterGames());
        config.set("stats.hunter-wins", data.getHunterWins());

        // 记录
        if (data.getFastestDragonKill() != Long.MAX_VALUE) {
            config.set("records.fastest-dragon-kill", data.getFastestDragonKill());
        }
        config.set("records.longest-survival", data.getLongestSurvival());
        config.set("records.max-kill-streak", data.getMaxKillStreak());

        // 设置
        config.set("settings.show-scoreboard", data.isShowScoreboard());
        config.set("settings.play-sound", data.isPlaySound());
        config.set("settings.language", data.getPreferredLanguage());

        // 自定义数据
        Map<String, Object> customData = data.getAllCustomData();
        if (!customData.isEmpty()) {
            for (Map.Entry<String, Object> entry : customData.entrySet()) {
                config.set("custom." + entry.getKey(), entry.getValue());
            }
        }

        // 添加版本信息和时间戳
        config.set("version", "1.0");
        config.set("saved-at", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
    }

    /**
     * 获取所有玩家数据
     * @return 所有玩家数据的列表
     */
    public java.util.List<PlayerData> getAllPlayerData() {
        java.util.List<PlayerData> allData = new java.util.ArrayList<>();

        // 首先添加缓存中的数据
        allData.addAll(playerDataCache.values());

        // 然后扫描数据文件夹，加载未缓存的数据
        if (dataFolder.exists() && dataFolder.isDirectory()) {
            File[] files = dataFolder.listFiles((dir, name) -> name.endsWith(".yml"));
            if (files != null) {
                for (File file : files) {
                    try {
                        // 从文件名提取UUID
                        String fileName = file.getName();
                        String uuidString = fileName.substring(0, fileName.length() - 4); // 移除.yml后缀
                        UUID playerId = UUID.fromString(uuidString);

                        // 如果缓存中没有这个数据，则加载它
                        if (!playerDataCache.containsKey(playerId)) {
                            PlayerData data = loadPlayerData(playerId);
                            if (data != null) {
                                allData.add(data);
                            }
                        }
                    } catch (IllegalArgumentException e) {
                        // 忽略无效的UUID文件名
                        plugin.getLogger().warning("跳过无效的数据文件: " + file.getName());
                    }
                }
            }
        }

        return allData;
    }

    /**
     * 获取所有已缓存的玩家数据
     * @return 缓存中的所有玩家数据
     */
    public java.util.Collection<PlayerData> getCachedPlayerData() {
        return new java.util.ArrayList<>(playerDataCache.values());
    }

    /**
     * 清理缓存中的数据
     * @param saveBeforeClear 清理前是否保存数据
     */
    public void clearCache(boolean saveBeforeClear) {
        if (saveBeforeClear) {
            for (UUID playerId : playerDataCache.keySet()) {
                savePlayerData(playerId);
            }
        }
        playerDataCache.clear();
        plugin.getLogger().info("玩家数据缓存已清理");
    }
}
