# 莱娜闪避效果调试日志说明

## 🔍 添加的调试日志

我已经在关键位置添加了详细的调试日志来帮助定位莱娜闪避效果的问题。

### 1. 光环启动日志
**位置**: `PassiveSkillHandler.startLenaForestBlessing()`
```java
plugin.getLogger().info("[森之祝福] 启动光环 - 莱娜: " + lena.getName() + ", UUID: " + lenaId);
```

### 2. 光环任务执行日志
**位置**: 光环任务循环中
```java
plugin.getLogger().info("[森之祝福] 执行光环任务 - 莱娜: " + currentLena.getName());
```

### 3. 光环效果应用日志
**位置**: `PassiveSkillHandler.applyForestBlessingAura()`
```java
plugin.getLogger().info("[森之祝福] 应用光环效果给玩家: " + nearbyPlayer.getName() + 
    ", 距离: " + String.format("%.2f", distance) + 
    ", 莱娜: " + lena.getName());
```

### 4. 效果标记设置日志
**位置**: `PassiveSkillHandler.setForestBlessingEffect()`
```java
plugin.getLogger().info("[森之祝福] 设置效果标记给玩家: " + playerName + 
    ", 当前标记数量: " + forestBlessingEffectPlayers.size());
```

### 5. 效果标记移除日志
**位置**: 效果移除任务中
```java
plugin.getLogger().info("[森之祝福] 移除效果标记: " + playerName + 
    ", 移除成功: " + removed + 
    ", 剩余标记数量: " + forestBlessingEffectPlayers.size());
```

### 6. 伤害事件日志
**位置**: `ProfessionListener.onEntityDamageByEntity()`
```java
plugin.getLogger().info("[伤害事件] 检查森之祝福闪避 - 受害者: " + victim.getName() + 
    ", 攻击者: " + (attacker != null ? attacker.getName() : "未知") + 
    ", 伤害类型: " + event.getCause() + 
    ", 伤害值: " + event.getDamage());
```

### 7. 闪避检查详细日志
**位置**: `PassiveSkillHandler.handleForestBlessingDodge()`
```java
// 开始检查
plugin.getLogger().info("[森之祝福] 开始闪避检查 - 玩家: " + victim.getName() + 
    ", UUID: " + victimId);

// 效果检查结果
plugin.getLogger().info("[森之祝福] 效果检查结果 - 玩家: " + victim.getName() + 
    ", 有效果: " + hasEffect + 
    ", 当前标记总数: " + forestBlessingEffectPlayers.size());

// 概率检查
plugin.getLogger().info("[森之祝福] 闪避概率检查 - 玩家: " + victim.getName() + 
    ", 随机值: " + String.format("%.3f", randomValue) + 
    ", 闪避成功: " + dodgeSuccess);

// 结果日志
plugin.getLogger().info("[森之祝福] 闪避成功！玩家: " + victim.getName() + " 成功闪避攻击");
// 或
plugin.getLogger().info("[森之祝福] 闪避失败 - 玩家: " + victim.getName() + " 未能闪避攻击");
```

### 8. 伤害取消日志
**位置**: `ProfessionListener.onEntityDamageByEntity()`
```java
plugin.getLogger().info("[伤害事件] 森之祝福闪避成功！取消伤害 - 受害者: " + victim.getName());
// 或
plugin.getLogger().info("[伤害事件] 森之祝福闪避检查完成，继续处理伤害 - 受害者: " + victim.getName());
```

## 📋 测试步骤

### 1. 启动测试
1. 让一个玩家选择莱娜职业
2. 观察日志中是否出现：`[森之祝福] 启动光环 - 莱娜: [玩家名]`

### 2. 光环运行测试
1. 莱娜站在原地等待
2. 每2秒应该看到：`[森之祝福] 执行光环任务 - 莱娜: [玩家名]`

### 3. 效果应用测试
1. 让其他速通者玩家靠近莱娜（10格内）
2. 观察是否出现：`[森之祝福] 应用光环效果给玩家: [玩家名], 距离: [数值], 莱娜: [莱娜名]`
3. 观察是否出现：`[森之祝福] 设置效果标记给玩家: [玩家名], 当前标记数量: [数量]`

### 4. 闪避测试
1. 让猎人攻击受到光环影响的速通者
2. 观察完整的日志序列：
   - `[伤害事件] 检查森之祝福闪避 - 受害者: [玩家名]...`
   - `[森之祝福] 开始闪避检查 - 玩家: [玩家名]...`
   - `[森之祝福] 效果检查结果 - 玩家: [玩家名], 有效果: true/false...`
   - `[森之祝福] 闪避概率检查 - 玩家: [玩家名], 随机值: [数值], 闪避成功: true/false`
   - 结果日志

## 🔍 问题排查指南

### 如果光环没有启动
- 检查是否出现启动日志
- 确认玩家是否正确选择了莱娜职业

### 如果光环任务不执行
- 检查是否每2秒出现执行日志
- 确认莱娜是否在线且职业未改变

### 如果效果没有应用
- 检查是否出现应用效果日志
- 确认其他玩家是否在10格范围内
- 确认其他玩家是否为速通者阵营

### 如果效果标记有问题
- 检查设置和移除标记的日志
- 确认标记数量是否正确
- 检查3秒后是否正确移除

### 如果闪避检查失败
- 检查效果检查结果中的"有效果"字段
- 确认当前标记总数是否大于0
- 检查玩家UUID是否匹配

### 如果概率计算有问题
- 检查随机值是否在0-1范围内
- 确认35%概率计算是否正确（随机值 < 0.35）

## 📝 日志示例

### 正常工作的日志序列
```
[森之祝福] 启动光环 - 莱娜: Alice, UUID: 12345678-1234-1234-1234-123456789abc
[森之祝福] 执行光环任务 - 莱娜: Alice
[森之祝福] 应用光环效果给玩家: Bob, 距离: 5.23, 莱娜: Alice
[森之祝福] 设置效果标记给玩家: Bob, 当前标记数量: 1
[伤害事件] 检查森之祝福闪避 - 受害者: Bob, 攻击者: Charlie, 伤害类型: ENTITY_ATTACK, 伤害值: 6.0
[森之祝福] 开始闪避检查 - 玩家: Bob, UUID: *************-4321-4321-cba987654321
[森之祝福] 效果检查结果 - 玩家: Bob, 有效果: true, 当前标记总数: 1
[森之祝福] 闪避概率检查 - 玩家: Bob, 随机值: 0.234, 闪避成功: true
[森之祝福] 闪避成功！玩家: Bob 成功闪避攻击
[伤害事件] 森之祝福闪避成功！取消伤害 - 受害者: Bob
```

## 🎯 请提供的信息

当你测试时，请提供以下信息：

1. **完整的日志输出**（从启动光环到攻击测试的全过程）
2. **测试场景描述**：
   - 莱娜的位置和状态
   - 其他玩家的位置和阵营
   - 攻击方式和时机
3. **观察到的现象**：
   - 是否有闪避音效和粒子效果
   - 是否有闪避成功的消息
   - 伤害是否被取消

这些日志将帮助我们精确定位问题所在！🔍✨
