#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff8ceb3b76c, pid=26232, tid=44372
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# Problematic frame:
# V  [jvm.dll+0x3bb76c]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://github.com/adoptium/adoptium-support/issues
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\d7c25a54a6ee316104bfe6acdb8443cd\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\d7c25a54a6ee316104bfe6acdb8443cd\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-403e1d1a70b6176218efbb72cb40245e-sock

Host: Genuine Intel(R) 0000, 32 cores, 31G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4768)
Time: Sun Jul 27 10:52:43 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4768) elapsed time: 2.704402 seconds (0d 0h 0m 2s)

---------------  T H R E A D  ---------------

Current thread (0x000001d2fbc71c90):  JavaThread "C2 CompilerThread5" daemon [_thread_in_native, id=44372, stack(0x000000133f200000,0x000000133f300000) (1024K)]


Current CompileTask:
C2:2704 5377       4       org.eclipse.osgi.internal.loader.BundleLoader::addExportedProvidersFor (167 bytes)

Stack: [0x000000133f200000,0x000000133f300000],  sp=0x000000133f2fc7c0,  free space=1009k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x3bb76c]
V  [jvm.dll+0x3b518c]
V  [jvm.dll+0x1e005e]
V  [jvm.dll+0x247c42]
V  [jvm.dll+0x2470cf]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


siginfo: EXCEPTION_ACCESS_VIOLATION (0xc0000005), ExceptionInformation=0x0000000000000003 0x0000000000000000


Registers:
RAX=0x000001d2ff262200, RBX=0x000000133f2fc8f0, RCX=0x000001d2ff262220, RDX=0x0000000000000070
RSP=0x000000133f2fc7c0, RBP=0x00107ff8cf4163b0, RSI=0x000000133f2fe390, RDI=0x000000000000001c
R8 =0x0000000000000000, R9 =0x000000133f2fca70, R10=0x000001d2ff262c38, R11=0x0000000000000000
R12=0x000001d2d4f05ee0, R13=0x0000000000000020, R14=0x00000000000000e0, R15=0x000001d2e103c250
RIP=0x00007ff8ceb3b76c, EFLAGS=0x0000000000010202

XMM[0]=0x0000000000000000 0x0000000000000000
XMM[1]=0x0000000000000000 0x3fe7026cb14c3d02
XMM[2]=0x0000000000000000 0x0000000000000000
XMM[3]=0x0000000000000000 0x0000000000000000
XMM[4]=0x0000000000000000 0x0000000000000000
XMM[5]=0x0000000000000000 0x0000000000000000
XMM[6]=0x0000000000000000 0x0000000000000000
XMM[7]=0x0000000000000000 0x0000000000000000
XMM[8]=0x0000000000000000 0x0000000000000000
XMM[9]=0x0000000000000000 0x0000000000000000
XMM[10]=0x0000000000000000 0x0000000000000000
XMM[11]=0x0000000000000000 0x0000000000000000
XMM[12]=0x0000000000000000 0x0000000000000000
XMM[13]=0x0000000000000000 0x0000000000000000
XMM[14]=0x0000000000000000 0x0000000000000000
XMM[15]=0x0000000000000000 0x0000000000000000
  MXCSR=0x00001fa0


Register to memory mapping:

RAX=0x000001d2ff262200 points into unknown readable memory: 0x000001d2ff263380 | 80 33 26 ff d2 01 00 00
RBX=0x000000133f2fc8f0 is pointing into the stack for thread: 0x000001d2fbc71c90
RCX=0x000001d2ff262220 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
RDX=0x0000000000000070 is an unknown value
RSP=0x000000133f2fc7c0 is pointing into the stack for thread: 0x000001d2fbc71c90
RBP=0x00107ff8cf4163b0 is an unknown value
RSI=0x000000133f2fe390 is pointing into the stack for thread: 0x000001d2fbc71c90
RDI=0x000000000000001c is an unknown value
R8 =0x0 is null
R9 =0x000000133f2fca70 is pointing into the stack for thread: 0x000001d2fbc71c90
R10=0x000001d2ff262c38 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
R11=0x0 is null
R12=0x000001d2d4f05ee0 points into unknown readable memory: 0x000001d2d4822fb0 | b0 2f 82 d4 d2 01 00 00
R13=0x0000000000000020 is an unknown value
R14=0x00000000000000e0 is an unknown value
R15=0x000001d2e103c250 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00

Top of Stack: (sp=0x000000133f2fc7c0)
0x000000133f2fc7c0:   0000000000000000 0000000100001d2f
0x000000133f2fc7d0:   000000230000001e 000001d2ff262c20
0x000000133f2fc7e0:   000001d2ff3b27e0 000001d2ff3b26a0
0x000000133f2fc7f0:   000001d2e17c45d0 000000000000000c
0x000000133f2fc800:   000000133f2fceb0 00007ff8ceb3518c
0x000000133f2fc810:   000000133f2fc910 0000000000000000
0x000000133f2fc820:   0000000000000020 0000000000193640
0x000000133f2fc830:   000000133f2fc860 0000000000000006
0x000000133f2fc840:   ffffffff00000023 000001d2ff3b26a0
0x000000133f2fc850:   0000000000000024 000001d2ff3b27e0
0x000000133f2fc860:   0000002300000000 0000000e00000020
0x000000133f2fc870:   00007ff800000000 0000002500000000
0x000000133f2fc880:   0000000d0000000c 00007ff900000000
0x000000133f2fc890:   0000000000000496 000001d20000079b
0x000000133f2fc8a0:   0000000000000000 0000000000000000
0x000000133f2fc8b0:   0000000300000c99 0000000d0000000d
0x000000133f2fc8c0:   000001d2ff25fe80 000001d2ff260370
0x000000133f2fc8d0:   000000133f2fc8f0 000001d2ff3b26ec
0x000000133f2fc8e0:   000001d2ff3b26f4 000000000000051b
0x000000133f2fc8f0:   0000000000000000 000001d2ff2634b0
0x000000133f2fc900:   000001d2e1bd2950 00007ff8ce8457a1
0x000000133f2fc910:   0000000000000680 00000000000000d0
0x000000133f2fc920:   000001d2d46a3c60 000000000000051b
0x000000133f2fc930:   0000000000000000 00007ff8ced57d3b
0x000000133f2fc940:   000000000000001d 000000133f2fc9d9
0x000000133f2fc950:   000001d2e1bd2950 000000133f2fc9d9
0x000000133f2fc960:   000000133f2fc9d0 00007ff8cee9ba6e
0x000000133f2fc970:   0000000000000680 000001d2d4823020
0x000000133f2fc980:   000001d200000025 000001d2d4d10ef0
0x000000133f2fc990:   000001d2fbd10000 0000000000000000
0x000000133f2fc9a0:   000001d2d6998500 000001d2d4d10e00
0x000000133f2fc9b0:   000001d2d46a3c60 00007ff8cf1b5818 

Instructions: (pc=0x00007ff8ceb3b76c)
0x00007ff8ceb3b66c:   8b 93 90 00 00 00 b8 20 00 00 00 44 8b e8 48 c1
0x00007ff8ceb3b67c:   e2 03 48 8b 04 07 48 8b 88 60 06 00 00 48 8b 81
0x00007ff8ceb3b68c:   80 00 00 00 4c 8b 88 d0 08 00 00 49 8b 49 18 49
0x00007ff8ceb3b69c:   8b 41 20 48 2b c1 48 3b c2 72 0a 48 8d 04 11 49
0x00007ff8ceb3b6ac:   89 41 18 eb 0e 45 33 c0 49 8b c9 e8 f4 9d d0 ff
0x00007ff8ceb3b6bc:   48 8b c8 ba 70 00 00 00 33 ff 48 89 4b 08 39 bb
0x00007ff8ceb3b6cc:   90 00 00 00 0f 86 d6 00 00 00 65 48 8b 04 25 58
0x00007ff8ceb3b6dc:   00 00 00 48 8d 0d ca ac 8d 00 48 89 6c 24 50 4c
0x00007ff8ceb3b6ec:   89 64 24 58 4c 89 74 24 60 4c 8d 24 f0 0f 1f 80
0x00007ff8ceb3b6fc:   00 00 00 00 49 8b 47 08 4c 8d 34 fd 00 00 00 00
0x00007ff8ceb3b70c:   49 8b 2c 06 48 3b e9 75 0a 48 8b 43 08 49 89 0c
0x00007ff8ceb3b71c:   06 eb 6b 49 8b 34 24 80 3c 16 00 75 05 e8 52 b9
0x00007ff8ceb3b72c:   58 00 4a 8b 04 2e 48 8b 88 60 06 00 00 48 8b b1
0x00007ff8ceb3b73c:   80 00 00 00 48 8b 8e d8 08 00 00 48 85 c9 75 0c
0x00007ff8ceb3b74c:   e8 2f 09 00 00 48 8b 8e d8 08 00 00 48 8b 01 0f
0x00007ff8ceb3b75c:   57 c0 48 89 86 d8 08 00 00 0f 11 01 0f 11 41 10
0x00007ff8ceb3b76c:   0f 10 45 00 0f 11 01 0f 10 4d 10 0f 11 49 10 48
0x00007ff8ceb3b77c:   8b 43 08 49 89 0c 06 48 8d 0d 26 ac 8d 00 ff c7
0x00007ff8ceb3b78c:   ba 70 00 00 00 3b bb 90 00 00 00 0f 82 63 ff ff
0x00007ff8ceb3b79c:   ff 4c 8b 74 24 60 4c 8b 64 24 58 48 8b 6c 24 50
0x00007ff8ceb3b7ac:   48 8b c3 48 83 c4 20 41 5f 41 5d 5f 5e 5b c3 cc
0x00007ff8ceb3b7bc:   cc cc cc cc 48 89 74 24 18 57 48 83 ec 20 8b 51
0x00007ff8ceb3b7cc:   0c 48 8b f9 83 fa 04 73 29 48 8b 49 18 0f 1f 80
0x00007ff8ceb3b7dc:   00 00 00 00 8b c2 44 8d 4a 01 48 83 3c c1 00 4c
0x00007ff8ceb3b7ec:   8d 14 c1 0f 85 91 00 00 00 41 8b d1 41 83 f9 04
0x00007ff8ceb3b7fc:   72 e2 48 89 5c 24 30 8b 5f 10 48 89 6c 24 38 3b
0x00007ff8ceb3b80c:   5f 14 73 5f 48 8d 2d 99 ab 8d 00 66 0f 1f 84 00
0x00007ff8ceb3b81c:   00 00 00 00 48 8b 47 20 8b cb 48 8b 14 c8 48 3b
0x00007ff8ceb3b82c:   d5 74 39 33 c0 48 89 57 18 33 c9 66 0f 1f 84 00
0x00007ff8ceb3b83c:   00 00 00 00 48 83 3c 11 00 44 8d 48 01 75 78 48
0x00007ff8ceb3b84c:   83 c1 08 41 8b c1 41 83 f9 04 72 e8 48 8b 4f 28
0x00007ff8ceb3b85c:   48 85 c9 74 07 8b d3 e8 58 01 00 00 ff c3 3b 5f 


Stack slot to memory mapping:

stack at sp + 0 slots: 0x0 is null
stack at sp + 1 slots: 0x0000000100001d2f is an unknown value
stack at sp + 2 slots: 0x000000230000001e is an unknown value
stack at sp + 3 slots: 0x000001d2ff262c20 points into unknown readable memory: 0x0000800000000000 | 00 00 00 00 00 80 00 00
stack at sp + 4 slots: 0x000001d2ff3b27e0 points into unknown readable memory: 0x00007ff8cf16a788 | 88 a7 16 cf f8 7f 00 00
stack at sp + 5 slots: 0x000001d2ff3b26a0 points into unknown readable memory: 0x000001d2e11d9b50 | 50 9b 1d e1 d2 01 00 00
stack at sp + 6 slots: 0x000001d2e17c45d0 points into unknown readable memory: 0x00007ff8cf124968 | 68 49 12 cf f8 7f 00 00
stack at sp + 7 slots: 0x000000000000000c is an unknown value


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001d2e1bcdd20, length=79, elements={
0x000001d2f2a1d860, 0x000001d2fbad1fe0, 0x000001d2fbad2c90, 0x000001d2fbad8270,
0x000001d2fbaf9130, 0x000001d2fbb04190, 0x000001d2fbb013a0, 0x000001d2fbb17a90,
0x000001d2fbb2b0b0, 0x000001d2fbbc0150, 0x000001d2fbb020c0, 0x000001d2fbc70ef0,
0x000001d2fbc73100, 0x000001d2fbc737d0, 0x000001d2fbb01a30, 0x000001d2fbc70820,
0x000001d2fbc73ea0, 0x000001d2fbc72360, 0x000001d2fbb04820, 0x000001d2fbb02750,
0x000001d2fbb03470, 0x000001d2fbb03b00, 0x000001d2d48e1500, 0x000001d2d48e28b0,
0x000001d2d48e2220, 0x000001d2d48e2f40, 0x000001d2fbc715c0, 0x000001d2d48e35d0,
0x000001d2d48e3c60, 0x000001d2d48e07e0, 0x000001d2d48e42f0, 0x000001d2fbc71c90,
0x000001d2fbc72a30, 0x000001d2d4fef790, 0x000001d2d48e7e00, 0x000001d2d48e70e0,
0x000001d2d48e6a50, 0x000001d2d48e4980, 0x000001d2d48e7770, 0x000001d2d48e5010,
0x000001d2d48e56a0, 0x000001d2d48e5d30, 0x000001d2d48e63c0, 0x000001d2d75897e0,
0x000001d2d7589e70, 0x000001d2d7589150, 0x000001d2d758a500, 0x000001d2d758ab90,
0x000001d2d7587da0, 0x000001d2d758b220, 0x000001d2d758b8b0, 0x000001d2d7588ac0,
0x000001d2d7588430, 0x000001d2d758d980, 0x000001d2d758e010, 0x000001d2d758cc60,
0x000001d2d758d2f0, 0x000001d2d758f3c0, 0x000001d2d758bf40, 0x000001d2d758e6a0,
0x000001d2d758ed30, 0x000001d2d758c5d0, 0x000001d2d78df5a0, 0x000001d2d78dc7b0,
0x000001d2d78dfc30, 0x000001d2d78def10, 0x000001d2d78dd4d0, 0x000001d2d78de1f0,
0x000001d2d78ddb60, 0x000001d2d78de880, 0x000001d2d78e02c0, 0x000001d2d78dce40,
0x000001d2d4fefe60, 0x000001d2d4fee9f0, 0x000001d2d78e1670, 0x000001d2d78e0950,
0x000001d2d78e30b0, 0x000001d2d78e3740, 0x000001d2d78e0fe0
}

Java Threads: ( => current thread )
  0x000001d2f2a1d860 JavaThread "main"                              [_thread_blocked, id=44048, stack(0x000000133bf00000,0x000000133c000000) (1024K)]
  0x000001d2fbad1fe0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=19008, stack(0x000000133c300000,0x000000133c400000) (1024K)]
  0x000001d2fbad2c90 JavaThread "Finalizer"                  daemon [_thread_blocked, id=22496, stack(0x000000133c400000,0x000000133c500000) (1024K)]
  0x000001d2fbad8270 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=20028, stack(0x000000133c500000,0x000000133c600000) (1024K)]
  0x000001d2fbaf9130 JavaThread "Attach Listener"            daemon [_thread_blocked, id=44368, stack(0x000000133c600000,0x000000133c700000) (1024K)]
  0x000001d2fbb04190 JavaThread "Service Thread"             daemon [_thread_blocked, id=29960, stack(0x000000133c700000,0x000000133c800000) (1024K)]
  0x000001d2fbb013a0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=4148, stack(0x000000133c800000,0x000000133c900000) (1024K)]
  0x000001d2fbb17a90 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=13464, stack(0x000000133c900000,0x000000133ca00000) (1024K)]
  0x000001d2fbb2b0b0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=27316, stack(0x000000133ca00000,0x000000133cb00000) (1024K)]
  0x000001d2fbbc0150 JavaThread "C1 CompilerThread1"         daemon [_thread_blocked, id=31648, stack(0x000000133cb00000,0x000000133cc00000) (1024K)]
  0x000001d2fbb020c0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=19740, stack(0x000000133cc00000,0x000000133cd00000) (1024K)]
  0x000001d2fbc70ef0 JavaThread "C1 CompilerThread2"         daemon [_thread_blocked, id=31048, stack(0x000000133cd00000,0x000000133ce00000) (1024K)]
  0x000001d2fbc73100 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=27916, stack(0x000000133ce00000,0x000000133cf00000) (1024K)]
  0x000001d2fbc737d0 JavaThread "C1 CompilerThread3"         daemon [_thread_blocked, id=43652, stack(0x000000133cf00000,0x000000133d000000) (1024K)]
  0x000001d2fbb01a30 JavaThread "Notification Thread"        daemon [_thread_blocked, id=36964, stack(0x000000133d000000,0x000000133d100000) (1024K)]
  0x000001d2fbc70820 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=28200, stack(0x000000133d100000,0x000000133d200000) (1024K)]
  0x000001d2fbc73ea0 JavaThread "C2 CompilerThread3"         daemon [_thread_in_native, id=8508, stack(0x000000133d800000,0x000000133d900000) (1024K)]
  0x000001d2fbc72360 JavaThread "C2 CompilerThread4"         daemon [_thread_in_native, id=33828, stack(0x000000133d900000,0x000000133da00000) (1024K)]
  0x000001d2fbb04820 JavaThread "Active Thread: Equinox Container: 062d70ee-3932-44cf-9e2f-64d9e22b46c3"        [_thread_blocked, id=20680, stack(0x000000133da00000,0x000000133db00000) (1024K)]
  0x000001d2fbb02750 JavaThread "Refresh Thread: Equinox Container: 062d70ee-3932-44cf-9e2f-64d9e22b46c3" daemon [_thread_blocked, id=29784, stack(0x000000133dc00000,0x000000133dd00000) (1024K)]
  0x000001d2fbb03470 JavaThread "Framework Event Dispatcher: Equinox Container: 062d70ee-3932-44cf-9e2f-64d9e22b46c3" daemon [_thread_blocked, id=19492, stack(0x000000133dd00000,0x000000133de00000) (1024K)]
  0x000001d2fbb03b00 JavaThread "Start Level: Equinox Container: 062d70ee-3932-44cf-9e2f-64d9e22b46c3" daemon [_thread_blocked, id=28808, stack(0x000000133de00000,0x000000133df00000) (1024K)]
  0x000001d2d48e1500 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=35260, stack(0x000000133e200000,0x000000133e300000) (1024K)]
  0x000001d2d48e28b0 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=37984, stack(0x000000133e300000,0x000000133e400000) (1024K)]
  0x000001d2d48e2220 JavaThread "SCR Component Registry"     daemon [_thread_blocked, id=3412, stack(0x000000133e400000,0x000000133e500000) (1024K)]
  0x000001d2d48e2f40 JavaThread "Worker-JM"                         [_thread_blocked, id=28416, stack(0x000000133e600000,0x000000133e700000) (1024K)]
  0x000001d2fbc715c0 JavaThread "C1 CompilerThread4"         daemon [_thread_blocked, id=7080, stack(0x000000133ea00000,0x000000133eb00000) (1024K)]
  0x000001d2d48e35d0 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=16460, stack(0x000000133eb00000,0x000000133ec00000) (1024K)]
  0x000001d2d48e3c60 JavaThread "Worker-0: Initialize After Load"        [_thread_blocked, id=19148, stack(0x000000133ec00000,0x000000133ed00000) (1024K)]
  0x000001d2d48e07e0 JavaThread "Worker-1"                          [_thread_blocked, id=28328, stack(0x000000133ed00000,0x000000133ee00000) (1024K)]
  0x000001d2d48e42f0 JavaThread "Worker-2"                          [_thread_blocked, id=20016, stack(0x000000133f100000,0x000000133f200000) (1024K)]
=>0x000001d2fbc71c90 JavaThread "C2 CompilerThread5"         daemon [_thread_in_native, id=44372, stack(0x000000133f200000,0x000000133f300000) (1024K)]
  0x000001d2fbc72a30 JavaThread "C2 CompilerThread6"         daemon [_thread_in_native, id=23884, stack(0x000000133f300000,0x000000133f400000) (1024K)]
  0x000001d2d4fef790 JavaThread "C2 CompilerThread7"         daemon [_thread_in_native, id=30800, stack(0x000000133f400000,0x000000133f500000) (1024K)]
  0x000001d2d48e7e00 JavaThread "Java indexing"              daemon [_thread_in_Java, id=33292, stack(0x000000133f700000,0x000000133f800000) (1024K)]
  0x000001d2d48e70e0 JavaThread "Worker-3: Repository registry initialization"        [_thread_blocked, id=31540, stack(0x000000133fe00000,0x000000133ff00000) (1024K)]
  0x000001d2d48e6a50 JavaThread "Worker-4: Java indexing... "        [_thread_blocked, id=8536, stack(0x000000133ff00000,0x0000001340000000) (1024K)]
  0x000001d2d48e4980 JavaThread "Worker-5: Updating Maven Dependencies"        [_thread_in_Java, id=45296, stack(0x0000001340000000,0x0000001340100000) (1024K)]
  0x000001d2d48e7770 JavaThread "Thread-2"                   daemon [_thread_in_native, id=25124, stack(0x0000001340100000,0x0000001340200000) (1024K)]
  0x000001d2d48e5010 JavaThread "Thread-3"                   daemon [_thread_in_native, id=30552, stack(0x0000001340200000,0x0000001340300000) (1024K)]
  0x000001d2d48e56a0 JavaThread "Thread-4"                   daemon [_thread_in_native, id=25288, stack(0x0000001340300000,0x0000001340400000) (1024K)]
  0x000001d2d48e5d30 JavaThread "Thread-5"                   daemon [_thread_in_native, id=43180, stack(0x0000001340400000,0x0000001340500000) (1024K)]
  0x000001d2d48e63c0 JavaThread "Thread-6"                   daemon [_thread_in_native, id=31372, stack(0x0000001340500000,0x0000001340600000) (1024K)]
  0x000001d2d75897e0 JavaThread "Thread-7"                   daemon [_thread_in_native, id=39196, stack(0x0000001340600000,0x0000001340700000) (1024K)]
  0x000001d2d7589e70 JavaThread "Thread-8"                   daemon [_thread_in_native, id=42468, stack(0x0000001340700000,0x0000001340800000) (1024K)]
  0x000001d2d7589150 JavaThread "Thread-9"                   daemon [_thread_in_native, id=29144, stack(0x0000001340800000,0x0000001340900000) (1024K)]
  0x000001d2d758a500 JavaThread "Thread-10"                  daemon [_thread_in_native, id=17280, stack(0x0000001340900000,0x0000001340a00000) (1024K)]
  0x000001d2d758ab90 JavaThread "Thread-11"                  daemon [_thread_in_native, id=4408, stack(0x0000001340a00000,0x0000001340b00000) (1024K)]
  0x000001d2d7587da0 JavaThread "Thread-12"                  daemon [_thread_in_native, id=45972, stack(0x0000001340b00000,0x0000001340c00000) (1024K)]
  0x000001d2d758b220 JavaThread "Thread-13"                  daemon [_thread_in_native, id=30752, stack(0x0000001340c00000,0x0000001340d00000) (1024K)]
  0x000001d2d758b8b0 JavaThread "Thread-14"                  daemon [_thread_in_native, id=14836, stack(0x0000001340d00000,0x0000001340e00000) (1024K)]
  0x000001d2d7588ac0 JavaThread "Thread-15"                  daemon [_thread_in_native, id=5768, stack(0x0000001340e00000,0x0000001340f00000) (1024K)]
  0x000001d2d7588430 JavaThread "Thread-16"                  daemon [_thread_in_native, id=13468, stack(0x0000001340f00000,0x0000001341000000) (1024K)]
  0x000001d2d758d980 JavaThread "Thread-17"                  daemon [_thread_in_native, id=46008, stack(0x0000001341000000,0x0000001341100000) (1024K)]
  0x000001d2d758e010 JavaThread "Thread-18"                  daemon [_thread_in_native, id=30984, stack(0x0000001341100000,0x0000001341200000) (1024K)]
  0x000001d2d758cc60 JavaThread "Thread-19"                  daemon [_thread_in_native, id=29528, stack(0x0000001341200000,0x0000001341300000) (1024K)]
  0x000001d2d758d2f0 JavaThread "Thread-20"                  daemon [_thread_in_native, id=27096, stack(0x0000001341300000,0x0000001341400000) (1024K)]
  0x000001d2d758f3c0 JavaThread "Thread-21"                  daemon [_thread_in_native, id=16448, stack(0x0000001341400000,0x0000001341500000) (1024K)]
  0x000001d2d758bf40 JavaThread "Thread-22"                  daemon [_thread_in_native, id=42560, stack(0x0000001341500000,0x0000001341600000) (1024K)]
  0x000001d2d758e6a0 JavaThread "Thread-23"                  daemon [_thread_in_native, id=17264, stack(0x0000001341600000,0x0000001341700000) (1024K)]
  0x000001d2d758ed30 JavaThread "Thread-24"                  daemon [_thread_in_native, id=43040, stack(0x0000001341700000,0x0000001341800000) (1024K)]
  0x000001d2d758c5d0 JavaThread "Thread-25"                  daemon [_thread_in_native, id=42928, stack(0x0000001341800000,0x0000001341900000) (1024K)]
  0x000001d2d78df5a0 JavaThread "Thread-26"                  daemon [_thread_in_native, id=34700, stack(0x0000001341900000,0x0000001341a00000) (1024K)]
  0x000001d2d78dc7b0 JavaThread "Thread-27"                  daemon [_thread_in_native, id=5572, stack(0x0000001341a00000,0x0000001341b00000) (1024K)]
  0x000001d2d78dfc30 JavaThread "Thread-28"                  daemon [_thread_in_native, id=39676, stack(0x0000001341b00000,0x0000001341c00000) (1024K)]
  0x000001d2d78def10 JavaThread "Thread-29"                  daemon [_thread_in_native, id=24500, stack(0x0000001341c00000,0x0000001341d00000) (1024K)]
  0x000001d2d78dd4d0 JavaThread "Thread-30"                  daemon [_thread_in_native, id=15880, stack(0x0000001341d00000,0x0000001341e00000) (1024K)]
  0x000001d2d78de1f0 JavaThread "Thread-31"                  daemon [_thread_in_native, id=3932, stack(0x0000001341e00000,0x0000001341f00000) (1024K)]
  0x000001d2d78ddb60 JavaThread "Thread-32"                  daemon [_thread_in_native, id=29556, stack(0x0000001341f00000,0x0000001342000000) (1024K)]
  0x000001d2d78de880 JavaThread "Thread-33"                  daemon [_thread_in_native, id=40816, stack(0x0000001342000000,0x0000001342100000) (1024K)]
  0x000001d2d78e02c0 JavaThread "Thread-34"                  daemon [_thread_in_native, id=43132, stack(0x0000001342100000,0x0000001342200000) (1024K)]
  0x000001d2d78dce40 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=42276, stack(0x0000001342200000,0x0000001342300000) (1024K)]
  0x000001d2d4fefe60 JavaThread "C2 CompilerThread8"         daemon [_thread_in_native, id=39348, stack(0x0000001342300000,0x0000001342400000) (1024K)]
  0x000001d2d4fee9f0 JavaThread "C2 CompilerThread9"         daemon [_thread_in_native, id=45960, stack(0x0000001342400000,0x0000001342500000) (1024K)]
  0x000001d2d78e1670 JavaThread "Worker-6"                          [_thread_blocked, id=42440, stack(0x0000001342500000,0x0000001342600000) (1024K)]
  0x000001d2d78e0950 JavaThread "Worker-7"                          [_thread_blocked, id=29704, stack(0x0000001342600000,0x0000001342700000) (1024K)]
  0x000001d2d78e30b0 JavaThread "Worker-8"                          [_thread_blocked, id=44572, stack(0x0000001342700000,0x0000001342800000) (1024K)]
  0x000001d2d78e3740 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=8076, stack(0x0000001342800000,0x0000001342900000) (1024K)]
  0x000001d2d78e0fe0 JavaThread "pool-1-thread-1"                   [_thread_in_vm, id=44840, stack(0x0000001342900000,0x0000001342a00000) (1024K)]
Total: 79

Other Threads:
  0x000001d2fbac51a0 VMThread "VM Thread"                           [id=22272, stack(0x000000133c200000,0x000000133c300000) (1024K)]
  0x000001d2f28aca00 WatcherThread "VM Periodic Task Thread"        [id=26984, stack(0x000000133c100000,0x000000133c200000) (1024K)]
  0x000001d2f2a3be10 WorkerThread "GC Thread#0"                     [id=29592, stack(0x000000133c000000,0x000000133c100000) (1024K)]
  0x000001d2ff6b5680 WorkerThread "GC Thread#1"                     [id=33136, stack(0x000000133d200000,0x000000133d300000) (1024K)]
  0x000001d2ff231030 WorkerThread "GC Thread#2"                     [id=44612, stack(0x000000133d300000,0x000000133d400000) (1024K)]
  0x000001d2ff3db2a0 WorkerThread "GC Thread#3"                     [id=38828, stack(0x000000133d400000,0x000000133d500000) (1024K)]
  0x000001d2ff3db850 WorkerThread "GC Thread#4"                     [id=42472, stack(0x000000133d500000,0x000000133d600000) (1024K)]
  0x000001d2ff3dbbf0 WorkerThread "GC Thread#5"                     [id=22956, stack(0x000000133d600000,0x000000133d700000) (1024K)]
  0x000001d2ff3dc3a0 WorkerThread "GC Thread#6"                     [id=9652, stack(0x000000133d700000,0x000000133d800000) (1024K)]
  0x000001d2d4318d50 WorkerThread "GC Thread#7"                     [id=5092, stack(0x000000133db00000,0x000000133dc00000) (1024K)]
  0x000001d2d45c5ee0 WorkerThread "GC Thread#8"                     [id=5796, stack(0x000000133df00000,0x000000133e000000) (1024K)]
  0x000001d2d45c69c0 WorkerThread "GC Thread#9"                     [id=43356, stack(0x000000133e000000,0x000000133e100000) (1024K)]
  0x000001d2d45c6280 WorkerThread "GC Thread#10"                    [id=35368, stack(0x000000133e100000,0x000000133e200000) (1024K)]
  0x000001d2d45c7100 WorkerThread "GC Thread#11"                    [id=19084, stack(0x000000133e500000,0x000000133e600000) (1024K)]
  0x000001d2d45c7840 WorkerThread "GC Thread#12"                    [id=704, stack(0x000000133e700000,0x000000133e800000) (1024K)]
  0x000001d2d44ec8b0 WorkerThread "GC Thread#13"                    [id=15268, stack(0x000000133e800000,0x000000133e900000) (1024K)]
  0x000001d2d44ecff0 WorkerThread "GC Thread#14"                    [id=44072, stack(0x000000133e900000,0x000000133ea00000) (1024K)]
  0x000001d2d44ede70 WorkerThread "GC Thread#15"                    [id=25048, stack(0x000000133ee00000,0x000000133ef00000) (1024K)]
  0x000001d2d44ee210 WorkerThread "GC Thread#16"                    [id=38508, stack(0x000000133ef00000,0x000000133f000000) (1024K)]
  0x000001d2d44ec510 WorkerThread "GC Thread#17"                    [id=43104, stack(0x000000133f000000,0x000000133f100000) (1024K)]
  0x000001d2d44ed730 WorkerThread "GC Thread#18"                    [id=45200, stack(0x000000133f500000,0x000000133f600000) (1024K)]
  0x000001d2d44ee950 WorkerThread "GC Thread#19"                    [id=22084, stack(0x000000133f600000,0x000000133f700000) (1024K)]
  0x000001d2d44eecf0 WorkerThread "GC Thread#20"                    [id=10256, stack(0x000000133f800000,0x000000133f900000) (1024K)]
  0x000001d2d44ef430 WorkerThread "GC Thread#21"                    [id=30596, stack(0x000000133f900000,0x000000133fa00000) (1024K)]
  0x000001d2d44edad0 WorkerThread "GC Thread#22"                    [id=29996, stack(0x000000133fa00000,0x000000133fb00000) (1024K)]
Total: 25

Threads with active compile tasks:
C2 CompilerThread0  2734 6728       4       org.eclipse.jdt.internal.compiler.classfmt.MethodInfo::getSelector (49 bytes)
C1 CompilerThread0  2734 6847       3       org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer::extractArgCount (316 bytes)
C1 CompilerThread1  2734 6850       3       org.eclipse.jdt.core.Signature::encodeQualifiedName (207 bytes)
C1 CompilerThread2  2734 6845       3       org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer::addTypeReference (92 bytes)
C2 CompilerThread1  2734 6801       4       org.eclipse.jdt.core.Signature::appendClassTypeSignature (323 bytes)
C1 CompilerThread3  2734 6849       3       org.eclipse.jdt.core.Signature::checkArrayDimension (92 bytes)
C2 CompilerThread2  2734 5659   !   4       org.eclipse.osgi.internal.loader.classpath.ClasspathManager::findClassImpl (343 bytes)
C2 CompilerThread3  2734 6763 %     4       org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer::extractReferenceFromConstantPool @ 155 (358 bytes)
C2 CompilerThread4  2734 5936       4       java.util.ArrayList$SubList::equals (47 bytes)
C1 CompilerThread4  2734 6846       2       java.security.CodeSource::getCertificates (95 bytes)
C2 CompilerThread5  2734 5377       4       org.eclipse.osgi.internal.loader.BundleLoader::addExportedProvidersFor (167 bytes)
C2 CompilerThread6  2734 6755 %     4       java.net.URI::quote @ 24 (214 bytes)
C2 CompilerThread7  2735 6758       4       org.eclipse.jdt.internal.compiler.util.CharDeduplication::intern (11 bytes)
C2 CompilerThread8  2735 6764 % !   4       org.eclipse.jdt.internal.compiler.classfmt.ClassFileReader::<init> @ 81 (2135 bytes)
C2 CompilerThread9  2735 6840       4       org.eclipse.jdt.internal.compiler.util.HashtableOfObject::putUnsafely (78 bytes)
Total: 15

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000001d28f000000-0x000001d28fba0000-0x000001d28fba0000), size 12189696, SharedBaseAddress: 0x000001d28f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001d290000000-0x000001d2d0000000, reserved size: **********
Narrow klass base: 0x000001d28f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 32 total, 32 available
 Memory: 32555M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 23

Heap:
 PSYoungGen      total 26624K, used 21060K [0x00000000d5580000, 0x00000000d7d00000, 0x0000000100000000)
  eden space 22016K, 74% used [0x00000000d5580000,0x00000000d6594508,0x00000000d6b00000)
  from space 4608K, 99% used [0x00000000d6b80000,0x00000000d6ffccd8,0x00000000d7000000)
  to   space 9216K, 0% used [0x00000000d7400000,0x00000000d7400000,0x00000000d7d00000)
 ParOldGen       total 68608K, used 39089K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 56% used [0x0000000080000000,0x000000008262c450,0x0000000084300000)
 Metaspace       used 40214K, committed 41408K, reserved 1114112K
  class space    used 4030K, committed 4544K, reserved 1048576K

Card table byte_map: [0x000001d2f23c0000,0x000001d2f27d0000] _byte_map_base: 0x000001d2f1fc0000

Marking Bits: (ParMarkBitMap*) 0x00007ff8cf4331f0
 Begin Bits: [0x000001d2f5ec0000, 0x000001d2f7ec0000)
 End Bits:   [0x000001d2f7ec0000, 0x000001d2f9ec0000)

Polling page: 0x000001d2f0660000

Metaspace:

Usage:
  Non-class:     35.34 MB used.
      Class:      3.94 MB used.
       Both:     39.27 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      36.00 MB ( 56%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       4.44 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      40.44 MB (  4%) committed. 

Chunk freelists:
   Non-Class:  11.81 MB
       Class:  11.50 MB
        Both:  23.31 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 58.88 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 12.
num_arena_births: 820.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 647.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 26.
num_chunks_taken_from_freelist: 2624.
num_chunk_merges: 17.
num_chunk_splits: 1611.
num_chunks_enlarged: 909.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=118848Kb used=3064Kb max_used=3064Kb free=115783Kb
 bounds [0x000001d287bf0000, 0x000001d287ef0000, 0x000001d28f000000]
CodeHeap 'profiled nmethods': size=118848Kb used=12972Kb max_used=12972Kb free=105875Kb
 bounds [0x000001d280000000, 0x000001d280cb0000, 0x000001d287410000]
CodeHeap 'non-nmethods': size=8064Kb used=3636Kb max_used=3730Kb free=4427Kb
 bounds [0x000001d287410000, 0x000001d2877c0000, 0x000001d287bf0000]
 total_blobs=6850 nmethods=6163 adapters=589
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 2.698 Thread 0x000001d2fbb2b0b0 nmethod 6793 0x000001d280c84290 code [0x000001d280c84440, 0x000001d280c845f0]
Event: 2.698 Thread 0x000001d2fbc737d0 nmethod 6790 0x000001d280c84710 code [0x000001d280c84ba0, 0x000001d280c87048]
Event: 2.698 Thread 0x000001d2fbbc0150 6795       3       java.util.regex.Matcher::checkGroup (41 bytes)
Event: 2.698 Thread 0x000001d2fbc70ef0 nmethod 6794 0x000001d280c87d90 code [0x000001d280c87f40, 0x000001d280c88100]
Event: 2.698 Thread 0x000001d2fbc715c0 6797       3       java.util.regex.ASCII::isDigit (18 bytes)
Event: 2.698 Thread 0x000001d2fbb2b0b0 6796       3       java.util.Locale::getDefault (63 bytes)
Event: 2.698 Thread 0x000001d2fbc73100 nmethod 6651 0x000001d287ee2310 code [0x000001d287ee24e0, 0x000001d287ee26e8]
Event: 2.698 Thread 0x000001d2fbc70ef0 6799       3       org.eclipse.jdt.internal.compiler.util.Util::scanIdentifier (71 bytes)
Event: 2.698 Thread 0x000001d2fbc715c0 nmethod 6797 0x000001d280c88210 code [0x000001d280c883a0, 0x000001d280c884f0]
Event: 2.699 Thread 0x000001d2fbb17a90 nmethod 5658 0x000001d287ee2990 code [0x000001d287ee3020, 0x000001d287ee7090]
Event: 2.699 Thread 0x000001d2fbbc0150 nmethod 6795 0x000001d280c88590 code [0x000001d280c887c0, 0x000001d280c88e48]
Event: 2.699 Thread 0x000001d2fbc715c0 6798       3       java.util.regex.Matcher::start (18 bytes)
Event: 2.699 Thread 0x000001d2fbb2b0b0 nmethod 6796 0x000001d280c89110 code [0x000001d280c89340, 0x000001d280c89a70]
Event: 2.699 Thread 0x000001d2fbc737d0 6800       1       java.lang.reflect.Field::getGenericSignature (5 bytes)
Event: 2.699 Thread 0x000001d2fbc70ef0 nmethod 6799 0x000001d280c89c90 code [0x000001d280c89ea0, 0x000001d280c8a460]
Event: 2.700 Thread 0x000001d2fbbc0150 6802       3       java.lang.StringUTF16::charAt (11 bytes)
Event: 2.700 Thread 0x000001d2fbc73100 6801       4       org.eclipse.jdt.core.Signature::appendClassTypeSignature (323 bytes)
Event: 2.700 Thread 0x000001d2fbc737d0 nmethod 6800 0x000001d287eeab10 code [0x000001d287eeaca0, 0x000001d287eead68]
Event: 2.700 Thread 0x000001d2fbc715c0 nmethod 6798 0x000001d280c8a690 code [0x000001d280c8a880, 0x000001d280c8ac30]
Event: 2.700 Thread 0x000001d2fbbc0150 nmethod 6802 0x000001d280c8ae10 code [0x000001d280c8afc0, 0x000001d280c8b228]

GC Heap History (20 events):
Event: 2.087 GC heap before
{Heap before GC invocations=15 (full 1):
 PSYoungGen      total 25600K, used 20224K [0x00000000d5580000, 0x00000000d7200000, 0x0000000100000000)
  eden space 22016K, 81% used [0x00000000d5580000,0x00000000d671ad30,0x00000000d6b00000)
  from space 3584K, 61% used [0x00000000d6c00000,0x00000000d6e25418,0x00000000d6f80000)
  to   space 2560K, 0% used [0x00000000d6f80000,0x00000000d6f80000,0x00000000d7200000)
 ParOldGen       total 68608K, used 18702K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 27% used [0x0000000080000000,0x0000000081243908,0x0000000084300000)
 Metaspace       used 34757K, committed 35904K, reserved 1114112K
  class space    used 3365K, committed 3840K, reserved 1048576K
}
Event: 2.089 GC heap after
{Heap after GC invocations=15 (full 1):
 PSYoungGen      total 24576K, used 2129K [0x00000000d5580000, 0x00000000d7200000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 2560K, 83% used [0x00000000d6f80000,0x00000000d7194410,0x00000000d7200000)
  to   space 2560K, 0% used [0x00000000d6d00000,0x00000000d6d00000,0x00000000d6f80000)
 ParOldGen       total 68608K, used 20499K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 29% used [0x0000000080000000,0x0000000081404d30,0x0000000084300000)
 Metaspace       used 34757K, committed 35904K, reserved 1114112K
  class space    used 3365K, committed 3840K, reserved 1048576K
}
Event: 2.120 GC heap before
{Heap before GC invocations=16 (full 1):
 PSYoungGen      total 24576K, used 5623K [0x00000000d5580000, 0x00000000d7200000, 0x0000000100000000)
  eden space 22016K, 15% used [0x00000000d5580000,0x00000000d58e9ad8,0x00000000d6b00000)
  from space 2560K, 83% used [0x00000000d6f80000,0x00000000d7194410,0x00000000d7200000)
  to   space 2560K, 0% used [0x00000000d6d00000,0x00000000d6d00000,0x00000000d6f80000)
 ParOldGen       total 68608K, used 20499K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 29% used [0x0000000080000000,0x0000000081404d30,0x0000000084300000)
 Metaspace       used 35102K, committed 36160K, reserved 1114112K
  class space    used 3411K, committed 3904K, reserved 1048576K
}
Event: 2.122 GC heap after
{Heap after GC invocations=16 (full 1):
 PSYoungGen      total 23552K, used 1408K [0x00000000d5580000, 0x00000000d7000000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 1536K, 91% used [0x00000000d6d00000,0x00000000d6e60000,0x00000000d6e80000)
  to   space 1536K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7000000)
 ParOldGen       total 68608K, used 22260K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 32% used [0x0000000080000000,0x00000000815bd140,0x0000000084300000)
 Metaspace       used 35102K, committed 36160K, reserved 1114112K
  class space    used 3411K, committed 3904K, reserved 1048576K
}
Event: 2.122 GC heap before
{Heap before GC invocations=17 (full 2):
 PSYoungGen      total 23552K, used 1408K [0x00000000d5580000, 0x00000000d7000000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 1536K, 91% used [0x00000000d6d00000,0x00000000d6e60000,0x00000000d6e80000)
  to   space 1536K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7000000)
 ParOldGen       total 68608K, used 22260K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 32% used [0x0000000080000000,0x00000000815bd140,0x0000000084300000)
 Metaspace       used 35102K, committed 36160K, reserved 1114112K
  class space    used 3411K, committed 3904K, reserved 1048576K
}
Event: 2.140 GC heap after
{Heap after GC invocations=17 (full 2):
 PSYoungGen      total 23552K, used 0K [0x00000000d5580000, 0x00000000d7000000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 1536K, 0% used [0x00000000d6d00000,0x00000000d6d00000,0x00000000d6e80000)
  to   space 1536K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7000000)
 ParOldGen       total 68608K, used 22052K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 32% used [0x0000000080000000,0x00000000815890c8,0x0000000084300000)
 Metaspace       used 35102K, committed 36160K, reserved 1114112K
  class space    used 3411K, committed 3904K, reserved 1048576K
}
Event: 2.192 GC heap before
{Heap before GC invocations=18 (full 2):
 PSYoungGen      total 23552K, used 22016K [0x00000000d5580000, 0x00000000d7000000, 0x0000000100000000)
  eden space 22016K, 100% used [0x00000000d5580000,0x00000000d6b00000,0x00000000d6b00000)
  from space 1536K, 0% used [0x00000000d6d00000,0x00000000d6d00000,0x00000000d6e80000)
  to   space 1536K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7000000)
 ParOldGen       total 68608K, used 22052K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 32% used [0x0000000080000000,0x00000000815890c8,0x0000000084300000)
 Metaspace       used 35565K, committed 36672K, reserved 1114112K
  class space    used 3485K, committed 3968K, reserved 1048576K
}
Event: 2.193 GC heap after
{Heap after GC invocations=18 (full 2):
 PSYoungGen      total 23552K, used 1504K [0x00000000d5580000, 0x00000000d7000000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 1536K, 97% used [0x00000000d6e80000,0x00000000d6ff8010,0x00000000d7000000)
  to   space 2560K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6d80000)
 ParOldGen       total 68608K, used 22828K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 33% used [0x0000000080000000,0x000000008164b0d8,0x0000000084300000)
 Metaspace       used 35565K, committed 36672K, reserved 1114112K
  class space    used 3485K, committed 3968K, reserved 1048576K
}
Event: 2.223 GC heap before
{Heap before GC invocations=19 (full 2):
 PSYoungGen      total 23552K, used 23520K [0x00000000d5580000, 0x00000000d7000000, 0x0000000100000000)
  eden space 22016K, 100% used [0x00000000d5580000,0x00000000d6b00000,0x00000000d6b00000)
  from space 1536K, 97% used [0x00000000d6e80000,0x00000000d6ff8010,0x00000000d7000000)
  to   space 2560K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6d80000)
 ParOldGen       total 68608K, used 22828K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 33% used [0x0000000080000000,0x000000008164b0d8,0x0000000084300000)
 Metaspace       used 35742K, committed 36800K, reserved 1114112K
  class space    used 3516K, committed 3968K, reserved 1048576K
}
Event: 2.224 GC heap after
{Heap after GC invocations=19 (full 2):
 PSYoungGen      total 24576K, used 2532K [0x00000000d5580000, 0x00000000d7400000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 2560K, 98% used [0x00000000d6b00000,0x00000000d6d79000,0x00000000d6d80000)
  to   space 4608K, 0% used [0x00000000d6f80000,0x00000000d6f80000,0x00000000d7400000)
 ParOldGen       total 68608K, used 24383K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 35% used [0x0000000080000000,0x00000000817cfdb8,0x0000000084300000)
 Metaspace       used 35742K, committed 36800K, reserved 1114112K
  class space    used 3516K, committed 3968K, reserved 1048576K
}
Event: 2.249 GC heap before
{Heap before GC invocations=20 (full 2):
 PSYoungGen      total 24576K, used 24548K [0x00000000d5580000, 0x00000000d7400000, 0x0000000100000000)
  eden space 22016K, 100% used [0x00000000d5580000,0x00000000d6b00000,0x00000000d6b00000)
  from space 2560K, 98% used [0x00000000d6b00000,0x00000000d6d79000,0x00000000d6d80000)
  to   space 4608K, 0% used [0x00000000d6f80000,0x00000000d6f80000,0x00000000d7400000)
 ParOldGen       total 68608K, used 24383K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 35% used [0x0000000080000000,0x00000000817cfdb8,0x0000000084300000)
 Metaspace       used 35891K, committed 36992K, reserved 1114112K
  class space    used 3535K, committed 4032K, reserved 1048576K
}
Event: 2.250 GC heap after
{Heap after GC invocations=20 (full 2):
 PSYoungGen      total 24576K, used 2396K [0x00000000d5580000, 0x00000000d7200000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 2560K, 93% used [0x00000000d6f80000,0x00000000d71d73f0,0x00000000d7200000)
  to   space 3072K, 0% used [0x00000000d6c00000,0x00000000d6c00000,0x00000000d6f00000)
 ParOldGen       total 68608K, used 26755K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 38% used [0x0000000080000000,0x0000000081a20d90,0x0000000084300000)
 Metaspace       used 35891K, committed 36992K, reserved 1114112K
  class space    used 3535K, committed 4032K, reserved 1048576K
}
Event: 2.284 GC heap before
{Heap before GC invocations=21 (full 2):
 PSYoungGen      total 24576K, used 24412K [0x00000000d5580000, 0x00000000d7200000, 0x0000000100000000)
  eden space 22016K, 100% used [0x00000000d5580000,0x00000000d6b00000,0x00000000d6b00000)
  from space 2560K, 93% used [0x00000000d6f80000,0x00000000d71d73f0,0x00000000d7200000)
  to   space 3072K, 0% used [0x00000000d6c00000,0x00000000d6c00000,0x00000000d6f00000)
 ParOldGen       total 68608K, used 26755K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 38% used [0x0000000080000000,0x0000000081a20d90,0x0000000084300000)
 Metaspace       used 36166K, committed 37312K, reserved 1114112K
  class space    used 3566K, committed 4032K, reserved 1048576K
}
Event: 2.287 GC heap after
{Heap after GC invocations=21 (full 2):
 PSYoungGen      total 24576K, used 2552K [0x00000000d5580000, 0x00000000d7100000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 2560K, 99% used [0x00000000d6c00000,0x00000000d6e7e210,0x00000000d6e80000)
  to   space 2560K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7100000)
 ParOldGen       total 68608K, used 28891K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 42% used [0x0000000080000000,0x0000000081c36d90,0x0000000084300000)
 Metaspace       used 36166K, committed 37312K, reserved 1114112K
  class space    used 3566K, committed 4032K, reserved 1048576K
}
Event: 2.414 GC heap before
{Heap before GC invocations=22 (full 2):
 PSYoungGen      total 24576K, used 24568K [0x00000000d5580000, 0x00000000d7100000, 0x0000000100000000)
  eden space 22016K, 100% used [0x00000000d5580000,0x00000000d6b00000,0x00000000d6b00000)
  from space 2560K, 99% used [0x00000000d6c00000,0x00000000d6e7e210,0x00000000d6e80000)
  to   space 2560K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7100000)
 ParOldGen       total 68608K, used 28891K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 42% used [0x0000000080000000,0x0000000081c36d90,0x0000000084300000)
 Metaspace       used 37446K, committed 38592K, reserved 1114112K
  class space    used 3706K, committed 4224K, reserved 1048576K
}
Event: 2.417 GC heap after
{Heap after GC invocations=22 (full 2):
 PSYoungGen      total 24064K, used 1888K [0x00000000d5580000, 0x00000000d7080000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 2048K, 92% used [0x00000000d6e80000,0x00000000d7058020,0x00000000d7080000)
  to   space 2560K, 0% used [0x00000000d6b80000,0x00000000d6b80000,0x00000000d6e00000)
 ParOldGen       total 68608K, used 31131K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 45% used [0x0000000080000000,0x0000000081e66fa0,0x0000000084300000)
 Metaspace       used 37446K, committed 38592K, reserved 1114112K
  class space    used 3706K, committed 4224K, reserved 1048576K
}
Event: 2.509 GC heap before
{Heap before GC invocations=23 (full 2):
 PSYoungGen      total 24064K, used 23904K [0x00000000d5580000, 0x00000000d7080000, 0x0000000100000000)
  eden space 22016K, 100% used [0x00000000d5580000,0x00000000d6b00000,0x00000000d6b00000)
  from space 2048K, 92% used [0x00000000d6e80000,0x00000000d7058020,0x00000000d7080000)
  to   space 2560K, 0% used [0x00000000d6b80000,0x00000000d6b80000,0x00000000d6e00000)
 ParOldGen       total 68608K, used 31131K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 45% used [0x0000000080000000,0x0000000081e66fa0,0x0000000084300000)
 Metaspace       used 38735K, committed 39872K, reserved 1114112K
  class space    used 3867K, committed 4352K, reserved 1048576K
}
Event: 2.512 GC heap after
{Heap after GC invocations=23 (full 2):
 PSYoungGen      total 24576K, used 2528K [0x00000000d5580000, 0x00000000d7500000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 2560K, 98% used [0x00000000d6b80000,0x00000000d6df8020,0x00000000d6e00000)
  to   space 5120K, 0% used [0x00000000d7000000,0x00000000d7000000,0x00000000d7500000)
 ParOldGen       total 68608K, used 33011K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 48% used [0x0000000080000000,0x000000008203cff0,0x0000000084300000)
 Metaspace       used 38735K, committed 39872K, reserved 1114112K
  class space    used 3867K, committed 4352K, reserved 1048576K
}
Event: 2.639 GC heap before
{Heap before GC invocations=24 (full 2):
 PSYoungGen      total 24576K, used 24544K [0x00000000d5580000, 0x00000000d7500000, 0x0000000100000000)
  eden space 22016K, 100% used [0x00000000d5580000,0x00000000d6b00000,0x00000000d6b00000)
  from space 2560K, 98% used [0x00000000d6b80000,0x00000000d6df8020,0x00000000d6e00000)
  to   space 5120K, 0% used [0x00000000d7000000,0x00000000d7000000,0x00000000d7500000)
 ParOldGen       total 68608K, used 33011K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 48% used [0x0000000080000000,0x000000008203cff0,0x0000000084300000)
 Metaspace       used 39532K, committed 40640K, reserved 1114112K
  class space    used 3953K, committed 4416K, reserved 1048576K
}
Event: 2.641 GC heap after
{Heap after GC invocations=24 (full 2):
 PSYoungGen      total 26624K, used 4181K [0x00000000d5580000, 0x00000000d7480000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 4608K, 90% used [0x00000000d7000000,0x00000000d74154a8,0x00000000d7480000)
  to   space 4608K, 0% used [0x00000000d6b80000,0x00000000d6b80000,0x00000000d7000000)
 ParOldGen       total 68608K, used 35442K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 51% used [0x0000000080000000,0x000000008229cb30,0x0000000084300000)
 Metaspace       used 39532K, committed 40640K, reserved 1114112K
  class space    used 3953K, committed 4416K, reserved 1048576K
}

Dll operation events (10 events):
Event: 0.005 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.020 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.050 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.052 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.053 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.055 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.063 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.093 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 0.541 Loaded shared library C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 1.052 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-92903040\jna14980692054778180018.dll

Deoptimization events (20 events):
Event: 2.675 Thread 0x000001d2d48e7e00 DEOPT PACKING pc=0x000001d2803e060d sp=0x000000133f7fe700
Event: 2.675 Thread 0x000001d2d48e7e00 DEOPT UNPACKING pc=0x000001d287464242 sp=0x000000133f7fdb98 mode 0
Event: 2.678 Thread 0x000001d2d48e7e00 DEOPT PACKING pc=0x000001d2803e060d sp=0x000000133f7fe700
Event: 2.678 Thread 0x000001d2d48e7e00 DEOPT UNPACKING pc=0x000001d287464242 sp=0x000000133f7fdb98 mode 0
Event: 2.683 Thread 0x000001d2d48e7e00 DEOPT PACKING pc=0x000001d2803e059a sp=0x000000133f7fe700
Event: 2.683 Thread 0x000001d2d48e7e00 DEOPT UNPACKING pc=0x000001d287464242 sp=0x000000133f7fdb98 mode 0
Event: 2.685 Thread 0x000001d2d48e7e00 DEOPT PACKING pc=0x000001d2803e060d sp=0x000000133f7fe700
Event: 2.685 Thread 0x000001d2d48e7e00 DEOPT UNPACKING pc=0x000001d287464242 sp=0x000000133f7fdb98 mode 0
Event: 2.688 Thread 0x000001d2d48e7e00 DEOPT PACKING pc=0x000001d2803e059a sp=0x000000133f7fe700
Event: 2.688 Thread 0x000001d2d48e7e00 DEOPT UNPACKING pc=0x000001d287464242 sp=0x000000133f7fdb98 mode 0
Event: 2.690 Thread 0x000001d2d48e7e00 DEOPT PACKING pc=0x000001d2803e059a sp=0x000000133f7fe700
Event: 2.690 Thread 0x000001d2d48e7e00 DEOPT UNPACKING pc=0x000001d287464242 sp=0x000000133f7fdb98 mode 0
Event: 2.692 Thread 0x000001d2d48e7e00 DEOPT PACKING pc=0x000001d2803e059a sp=0x000000133f7fe700
Event: 2.692 Thread 0x000001d2d48e7e00 DEOPT UNPACKING pc=0x000001d287464242 sp=0x000000133f7fdb98 mode 0
Event: 2.693 Thread 0x000001d2d48e7e00 DEOPT PACKING pc=0x000001d2803e060d sp=0x000000133f7fe700
Event: 2.693 Thread 0x000001d2d48e7e00 DEOPT UNPACKING pc=0x000001d287464242 sp=0x000000133f7fdb98 mode 0
Event: 2.699 Thread 0x000001d2d78e1670 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001d287d6dd3c relative=0x000000000000061c
Event: 2.699 Thread 0x000001d2d78e1670 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001d287d6dd3c method=java.lang.AbstractStringBuilder.appendChars(Ljava/lang/String;II)V @ 11 c2
Event: 2.700 Thread 0x000001d2d78e1670 DEOPT PACKING pc=0x000001d287d6dd3c sp=0x00000013425fd640
Event: 2.700 Thread 0x000001d2d78e1670 DEOPT UNPACKING pc=0x000001d287463aa2 sp=0x00000013425fd580 mode 2

Classes loaded (20 events):
Event: 2.487 Loading class java/text/CalendarBuilder
Event: 2.488 Loading class java/text/CalendarBuilder done
Event: 2.493 Loading class java/io/PrintWriter$1
Event: 2.493 Loading class jdk/internal/access/JavaIOPrintWriterAccess
Event: 2.493 Loading class jdk/internal/access/JavaIOPrintWriterAccess done
Event: 2.494 Loading class java/io/PrintWriter$1 done
Event: 2.497 Loading class java/time/format/DateTimeParseContext
Event: 2.498 Loading class java/time/format/DateTimeParseContext done
Event: 2.498 Loading class java/time/format/Parsed
Event: 2.498 Loading class java/time/format/Parsed done
Event: 2.546 Loading class java/lang/annotation/ElementType
Event: 2.546 Loading class java/lang/annotation/ElementType done
Event: 2.570 Loading class sun/reflect/generics/repository/FieldRepository
Event: 2.571 Loading class sun/reflect/generics/repository/FieldRepository done
Event: 2.689 Loading class java/lang/Throwable$WrappedPrintWriter
Event: 2.689 Loading class java/lang/Throwable$PrintStreamOrWriter
Event: 2.689 Loading class java/lang/Throwable$PrintStreamOrWriter done
Event: 2.689 Loading class java/lang/Throwable$WrappedPrintWriter done
Event: 2.700 Loading class sun/nio/ch/WindowsAsynchronousFileChannelImpl$WriteTask
Event: 2.700 Loading class sun/nio/ch/WindowsAsynchronousFileChannelImpl$WriteTask done

Classes unloaded (7 events):
Event: 1.286 Thread 0x000001d2fbac51a0 Unloading class 0x000001d2901a4c00 'java/lang/invoke/LambdaForm$MH+0x000001d2901a4c00'
Event: 1.286 Thread 0x000001d2fbac51a0 Unloading class 0x000001d2901a4800 'java/lang/invoke/LambdaForm$MH+0x000001d2901a4800'
Event: 1.286 Thread 0x000001d2fbac51a0 Unloading class 0x000001d2901a4400 'java/lang/invoke/LambdaForm$MH+0x000001d2901a4400'
Event: 1.286 Thread 0x000001d2fbac51a0 Unloading class 0x000001d2901a4000 'java/lang/invoke/LambdaForm$MH+0x000001d2901a4000'
Event: 1.286 Thread 0x000001d2fbac51a0 Unloading class 0x000001d2901a3c00 'java/lang/invoke/LambdaForm$BMH+0x000001d2901a3c00'
Event: 1.286 Thread 0x000001d2fbac51a0 Unloading class 0x000001d2901a3800 'java/lang/invoke/LambdaForm$DMH+0x000001d2901a3800'
Event: 1.286 Thread 0x000001d2fbac51a0 Unloading class 0x000001d2901a2800 'java/lang/invoke/LambdaForm$DMH+0x000001d2901a2800'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 2.084 Thread 0x000001d2d48e4980 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d663f250}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d663f250) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.111 Thread 0x000001d2d48e07e0 Exception <a 'java/io/FileNotFoundException'{0x00000000d56c3ef0}> (0x00000000d56c3ef0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 2.111 Thread 0x000001d2d48e07e0 Exception <a 'java/io/FileNotFoundException'{0x00000000d56c5720}> (0x00000000d56c5720) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 2.115 Thread 0x000001d2d48e07e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d56dcb90}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d56dcb90) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.283 Thread 0x000001d2d48e70e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6911f18}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000d6911f18) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.283 Thread 0x000001d2d48e70e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6915718}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000d6915718) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.288 Thread 0x000001d2d48e70e0 Implicit null exception at 0x000001d287e702f6 to 0x000001d287e7062c
Event: 2.290 Thread 0x000001d2d48e70e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d56c8578}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d56c8578) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.333 Thread 0x000001d2d48e07e0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d6226be8}> (0x00000000d6226be8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 2.334 Thread 0x000001d2d48e07e0 Exception <a 'java/io/FileNotFoundException'{0x00000000d6283aa0}> (0x00000000d6283aa0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 2.423 Thread 0x000001d2d48e07e0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d559c0d8}> (0x00000000d559c0d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 2.423 Thread 0x000001d2d48e07e0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d559ca78}> (0x00000000d559ca78) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 2.432 Thread 0x000001d2f2a1d860 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d59e3de0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d59e3de0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.434 Thread 0x000001d2f2a1d860 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d5bde6b8}: Found class java.lang.Object, but interface was expected> (0x00000000d5bde6b8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 2.435 Thread 0x000001d2f2a1d860 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d5be05b8}: Found class java.lang.Object, but interface was expected> (0x00000000d5be05b8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 2.445 Thread 0x000001d2d48e07e0 Exception <a 'java/io/FileNotFoundException'{0x00000000d5d5ab80}> (0x00000000d5d5ab80) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 2.445 Thread 0x000001d2d48e07e0 Exception <a 'java/io/FileNotFoundException'{0x00000000d5d5bed8}> (0x00000000d5d5bed8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 2.445 Thread 0x000001d2d48e07e0 Exception <a 'java/io/FileNotFoundException'{0x00000000d5d5ce28}> (0x00000000d5d5ce28) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 2.623 Thread 0x000001d2d48e7e00 Implicit null exception at 0x000001d287c8f25a to 0x000001d287c8f358
Event: 2.688 Thread 0x000001d2d78e1670 Exception <a 'java/lang/NullPointerException'{0x00000000d62f2e20}> (0x00000000d62f2e20) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1456]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 2.089 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold)
Event: 2.092 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold) done
Event: 2.120 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold)
Event: 2.140 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold) done
Event: 2.189 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 2.193 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 2.222 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 2.224 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 2.248 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 2.250 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 2.284 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 2.287 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 2.413 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 2.417 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 2.509 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 2.512 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 2.567 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 2.570 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 2.637 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 2.641 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 2.132 Thread 0x000001d2fbac51a0 flushing  nmethod 0x000001d28073f790
Event: 2.132 Thread 0x000001d2fbac51a0 flushing  nmethod 0x000001d28076ff90
Event: 2.132 Thread 0x000001d2fbac51a0 flushing  nmethod 0x000001d280771310
Event: 2.132 Thread 0x000001d2fbac51a0 flushing  nmethod 0x000001d280771c10
Event: 2.132 Thread 0x000001d2fbac51a0 flushing  nmethod 0x000001d280772e10
Event: 2.132 Thread 0x000001d2fbac51a0 flushing  nmethod 0x000001d280773a10
Event: 2.132 Thread 0x000001d2fbac51a0 flushing  nmethod 0x000001d280774190
Event: 2.132 Thread 0x000001d2fbac51a0 flushing  nmethod 0x000001d280774b10
Event: 2.132 Thread 0x000001d2fbac51a0 flushing  nmethod 0x000001d280775010
Event: 2.132 Thread 0x000001d2fbac51a0 flushing  nmethod 0x000001d280776510
Event: 2.132 Thread 0x000001d2fbac51a0 flushing  nmethod 0x000001d280776d90
Event: 2.132 Thread 0x000001d2fbac51a0 flushing  nmethod 0x000001d280778690
Event: 2.132 Thread 0x000001d2fbac51a0 flushing  nmethod 0x000001d280778990
Event: 2.132 Thread 0x000001d2fbac51a0 flushing  nmethod 0x000001d28077b110
Event: 2.132 Thread 0x000001d2fbac51a0 flushing  nmethod 0x000001d28077ba10
Event: 2.132 Thread 0x000001d2fbac51a0 flushing  nmethod 0x000001d2807b7810
Event: 2.132 Thread 0x000001d2fbac51a0 flushing  nmethod 0x000001d2807b8310
Event: 2.132 Thread 0x000001d2fbac51a0 flushing  nmethod 0x000001d2807d8210
Event: 2.132 Thread 0x000001d2fbac51a0 flushing  nmethod 0x000001d2807e1410
Event: 2.132 Thread 0x000001d2fbac51a0 flushing  nmethod 0x000001d280872410

Events (20 events):
Event: 1.977 Thread 0x000001d2f2a1d860 Thread added: 0x000001d2d758e6a0
Event: 1.977 Thread 0x000001d2f2a1d860 Thread added: 0x000001d2d758ed30
Event: 1.977 Thread 0x000001d2f2a1d860 Thread added: 0x000001d2d758c5d0
Event: 1.977 Thread 0x000001d2f2a1d860 Thread added: 0x000001d2d78df5a0
Event: 1.978 Thread 0x000001d2f2a1d860 Thread added: 0x000001d2d78dc7b0
Event: 1.978 Thread 0x000001d2f2a1d860 Thread added: 0x000001d2d78dfc30
Event: 1.978 Thread 0x000001d2f2a1d860 Thread added: 0x000001d2d78def10
Event: 1.978 Thread 0x000001d2f2a1d860 Thread added: 0x000001d2d78dd4d0
Event: 1.979 Thread 0x000001d2f2a1d860 Thread added: 0x000001d2d78de1f0
Event: 1.979 Thread 0x000001d2f2a1d860 Thread added: 0x000001d2d78ddb60
Event: 1.979 Thread 0x000001d2f2a1d860 Thread added: 0x000001d2d78de880
Event: 1.979 Thread 0x000001d2f2a1d860 Thread added: 0x000001d2d78e02c0
Event: 2.017 Thread 0x000001d2f2a1d860 Thread added: 0x000001d2d78dce40
Event: 2.046 Thread 0x000001d2fbc737d0 Thread added: 0x000001d2d4fefe60
Event: 2.051 Thread 0x000001d2fbc73100 Thread added: 0x000001d2d4fee9f0
Event: 2.333 Thread 0x000001d2d48e6a50 Thread added: 0x000001d2d78e1670
Event: 2.334 Thread 0x000001d2d78e1670 Thread added: 0x000001d2d78e0950
Event: 2.382 Thread 0x000001d2d78e0950 Thread added: 0x000001d2d78e30b0
Event: 2.482 Thread 0x000001d2f2a1d860 Thread added: 0x000001d2d78e3740
Event: 2.482 Thread 0x000001d2f2a1d860 Thread added: 0x000001d2d78e0fe0


Dynamic libraries:
0x00007ff7f45a0000 - 0x00007ff7f45ae000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff9c8720000 - 0x00007ff9c8987000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff9c70c0000 - 0x00007ff9c7189000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff9c5f60000 - 0x00007ff9c6350000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff9c59d0000 - 0x00007ff9c5b1b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff9aeb90000 - 0x00007ff9aeba8000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff9ae810000 - 0x00007ff9ae82e000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff9c7190000 - 0x00007ff9c7355000 	C:\WINDOWS\System32\USER32.dll
0x00007ff9b9f70000 - 0x00007ff9ba20a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4768_none_3e0c112ce331287c\COMCTL32.dll
0x00007ff9c5b20000 - 0x00007ff9c5b47000 	C:\WINDOWS\System32\win32u.dll
0x00007ff9c66b0000 - 0x00007ff9c6759000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff9c7090000 - 0x00007ff9c70bb000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff9c5e20000 - 0x00007ff9c5f58000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff9c6360000 - 0x00007ff9c6403000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff9c65c0000 - 0x00007ff9c65ef000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff9b8fb0000 - 0x00007ff9b8fbc000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff943fa0000 - 0x00007ff94402d000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007ff8ce780000 - 0x00007ff8cf510000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff9c6760000 - 0x00007ff9c6814000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff9c6600000 - 0x00007ff9c66a6000 	C:\WINDOWS\System32\sechost.dll
0x00007ff9c6b60000 - 0x00007ff9c6c78000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff9c69e0000 - 0x00007ff9c6a54000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff9c56b0000 - 0x00007ff9c570e000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff9bdd00000 - 0x00007ff9bdd35000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff9bdd50000 - 0x00007ff9bdd5b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff9c5690000 - 0x00007ff9c56a4000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff9c4590000 - 0x00007ff9c45ab000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff9b8ef0000 - 0x00007ff9b8efa000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff9b4c30000 - 0x00007ff9b4e71000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff9c7b10000 - 0x00007ff9c7e95000 	C:\WINDOWS\System32\combase.dll
0x00007ff9c7ea0000 - 0x00007ff9c7f80000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff9b5250000 - 0x00007ff9b5293000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff9c6410000 - 0x00007ff9c64a9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff9b8eb0000 - 0x00007ff9b8ebf000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff9ae0d0000 - 0x00007ff9ae0ef000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ff9c73c0000 - 0x00007ff9c7b0d000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff9c5ca0000 - 0x00007ff9c5e13000 	C:\WINDOWS\System32\wintypes.dll
0x00007ff9c3410000 - 0x00007ff9c3c6f000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff9c64b0000 - 0x00007ff9c65a5000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff9c7020000 - 0x00007ff9c708a000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff9c5770000 - 0x00007ff9c5799000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff9a6cc0000 - 0x00007ff9a6cd8000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ff9b8d40000 - 0x00007ff9b8d50000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ff9c0500000 - 0x00007ff9c061e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff9c4b00000 - 0x00007ff9c4b6b000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff99d730000 - 0x00007ff99d746000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ff9b8d20000 - 0x00007ff9b8d30000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ff989c10000 - 0x00007ff989c55000 	C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ff9c6830000 - 0x00007ff9c69d0000 	C:\WINDOWS\System32\ole32.dll
0x00007ff9c4ee0000 - 0x00007ff9c4efb000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff9c44f0000 - 0x00007ff9c452b000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff9c4ba0000 - 0x00007ff9c4bcb000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff9c5740000 - 0x00007ff9c5766000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ff9c4d40000 - 0x00007ff9c4d4c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff9c3f80000 - 0x00007ff9c3fb3000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff9c6820000 - 0x00007ff9c682a000 	C:\WINDOWS\System32\NSI.dll
0x00007ff9b5f00000 - 0x00007ff9b5f49000 	C:\Users\<USER>\AppData\Local\Temp\jna-92903040\jna14980692054778180018.dll
0x00007ff9c86d0000 - 0x00007ff9c86d8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff9c04d0000 - 0x00007ff9c04ef000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ff9c04a0000 - 0x00007ff9c04c5000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4768_none_3e0c112ce331287c;c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-92903040

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\d7c25a54a6ee316104bfe6acdb8443cd\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\d7c25a54a6ee316104bfe6acdb8443cd\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-403e1d1a70b6176218efbb72cb40245e-sock
java_class_path (initial): c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 15                                        {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\d7c25a54a6ee316104bfe6acdb8443cd\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 8192380                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 121732930                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 121732930                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Zulu\zulu-24\
PATH=C:\Program Files\Zulu\zulu-24\bin\;C:\Users\<USER>\.jdks\openjdk-23.0.1\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files\Git\cmd;C:\Program Files\Go\bin;C:\TDM-GCC-64\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;;C:\Program Files\JetBrains\IntelliJ IDEA 2024.3.1.1\bin;;C:\Program Files\JetBrains\GoLand 2024.3.2.1\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin
USERNAME=alice
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4768)
OS uptime: 3 days 23:59 hours
Hyper-V role detected

CPU: total 32 (initial active 32) (16 cores per cpu, 2 threads per core) family 6 model 183 stepping 1 microcode 0x12b, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv, serialize, rdtscp, rdpid, fsrm, f16c, cet_ibt, cet_ss
Processor Information for processor 0
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 1
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 2
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 3
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 4
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 5
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 6
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 7
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 8
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 9
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 10
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 11
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 12
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 13
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 14
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 15
  Max Mhz: 1700, Current Mhz: 1619, Mhz Limit: 1615
Processor Information for processor 16
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 17
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 18
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 19
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 20
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 21
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 22
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 23
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 24
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 25
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 26
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 27
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 28
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 29
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 30
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 31
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700

Memory: 4k page, system-wide physical 32555M (6402M free)
TotalPageFile size 45596M (AvailPageFile size 1799M)
current process WorkingSet (physical memory assigned to process): 289M, peak: 291M
current process commit charge ("private bytes"): 442M, peak: 446M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
