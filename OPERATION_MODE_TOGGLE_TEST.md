# 操作模式切换功能测试指南

## 测试环境准备

1. 启动Minecraft服务器
2. 加载UltimateManhurt插件
3. 创建一个房间
4. 打开房间设置GUI

## 功能测试清单

### 1. 切换按钮测试

#### 1.1 按钮显示测试
- [ ] 确认切换按钮位于GUI右下角 (slot 52)
- [ ] 默认模式为鼠标操作，按钮显示铁镐图标
- [ ] 按钮名称显示为"操作模式切换"
- [ ] 按钮描述正确显示当前模式信息

#### 1.2 模式切换测试
- [ ] 点击切换按钮，模式从"鼠标操作"切换到"聊天输入"
- [ ] 按钮图标变为可写书
- [ ] 收到切换成功的消息提示
- [ ] GUI自动刷新，显示新模式信息

#### 1.3 反复切换测试
- [ ] 再次点击按钮，模式切换回"鼠标操作"
- [ ] 按钮图标变回铁镐
- [ ] 可以反复切换，每次都正常工作

### 2. 鼠标操作模式测试

#### 2.1 游戏时长设置测试
- [ ] 切换到鼠标操作模式
- [ ] 左键点击游戏时长：数值减少1分钟
- [ ] 右键点击游戏时长：数值增加1分钟
- [ ] Shift+左键：数值减少10分钟
- [ ] Shift+右键：数值增加10分钟
- [ ] 达到边界值时显示错误提示

#### 2.2 最大玩家数设置测试
- [ ] 左键点击：减少1人
- [ ] 右键点击：增加1人
- [ ] Shift+左键：减少5人
- [ ] Shift+右键：增加5人
- [ ] 边界值检查正常工作

#### 2.3 豁免时长设置测试
- [ ] 左键点击：减少5秒
- [ ] 右键点击：增加5秒
- [ ] Shift+左键：减少30秒
- [ ] Shift+右键：增加30秒

#### 2.4 生命数设置测试
- [ ] 左键点击：减少1生命
- [ ] 右键点击：增加1生命
- [ ] 正确处理0值（无限生命）

### 3. 聊天输入模式测试

#### 3.1 基本聊天输入测试
- [ ] 切换到聊天输入模式
- [ ] 点击游戏时长设置：打开聊天输入界面
- [ ] 输入有效数值：设置成功
- [ ] 输入无效数值：显示错误提示
- [ ] 输入"cancel"：取消操作

#### 3.2 各设置项聊天输入测试
- [ ] 游戏时长：输入范围1-180的数值
- [ ] 世界种子：输入种子值或0（随机）
- [ ] 最大玩家数：输入范围2-50的数值
- [ ] 豁免时长：输入范围0-300的数值
- [ ] 生命数：输入范围0-10的数值

### 4. 界面显示测试

#### 4.1 描述动态更新测试
- [ ] 鼠标操作模式下，设置项显示鼠标操作说明
- [ ] 聊天输入模式下，设置项显示聊天输入说明
- [ ] 切换模式后，所有设置项描述立即更新
- [ ] 当前模式信息正确显示在每个设置项中

#### 4.2 特殊设置项测试
- [ ] 世界种子在鼠标模式下：左键随机，右键聊天输入
- [ ] 游戏难度：两种模式下都是点击切换
- [ ] 目标分数：正确显示Shift操作说明

### 5. 错误处理测试

#### 5.1 边界值测试
- [ ] 游戏时长：最小值1分钟，最大值180分钟
- [ ] 最大玩家数：最小值2人，最大值50人
- [ ] 豁免时长：最小值0秒，最大值300秒
- [ ] 生命数：最小值0（无限），最大值10

#### 5.2 错误反馈测试
- [ ] 达到边界值时播放错误音效
- [ ] 显示友好的错误提示消息
- [ ] 无效输入时给出明确的错误说明

### 6. 用户体验测试

#### 6.1 操作流畅性测试
- [ ] 模式切换响应迅速
- [ ] 鼠标操作即时生效
- [ ] GUI刷新无延迟
- [ ] 连续操作无卡顿

#### 6.2 信息清晰度测试
- [ ] 当前模式一目了然
- [ ] 操作说明清楚易懂
- [ ] 成功/错误消息明确
- [ ] 按钮图标直观易识别

## 测试结果记录

### 通过的测试项
- [ ] 切换按钮功能
- [ ] 鼠标操作模式
- [ ] 聊天输入模式
- [ ] 界面动态更新
- [ ] 错误处理机制
- [ ] 用户体验

### 发现的问题
记录测试过程中发现的任何问题：

1. 
2. 
3. 

### 改进建议
记录可能的改进建议：

1. 
2. 
3. 

## 测试完成确认

- [ ] 所有核心功能正常工作
- [ ] 用户界面友好易用
- [ ] 错误处理完善
- [ ] 性能表现良好
- [ ] 功能符合设计要求

测试人员：___________
测试日期：___________
测试版本：___________
