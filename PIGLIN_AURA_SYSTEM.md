# 猪灵光环系统重构

## 🔄 系统重构

### 修改前（攻击触发）
- **触发方式：** 只有在攻击时才触发狂意效果
- **持续性：** 非持续，只在攻击瞬间生效
- **光环特性：** 不符合光环的持续性特点

### 修改后（真正光环）
- **触发方式：** 持续的光环效果，无需攻击触发
- **持续性：** 每2秒自动刷新光环效果
- **光环特性：** 真正的光环系统，符合设计理念

## 🐷 新光环系统详解

### 技术实现
```java
/**
 * 启动猪灵光环任务
 * 每2秒检查一次所有猪灵玩家，应用光环效果
 */
private void startPiglinAuraTask() {
    piglinAuraTask = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
        // 遍历所有在线玩家，找到猪灵职业的玩家
        for (Player player : Bukkit.getOnlinePlayers()) {
            Profession profession = plugin.getProfessionManager().getPlayerProfession(player.getUniqueId());
            if (profession == Profession.PIGLIN) {
                // 应用猪灵光环效果
                applyPiglinAura(player);
            }
        }
    }, 0L, 40L); // 每2秒执行一次 (40 tick = 2秒)
}
```

### 光环效果应用
```java
/**
 * 应用猪灵光环效果
 */
private void applyPiglinAura(Player piglin) {
    Location piglinLoc = piglin.getLocation();
    
    // 给范围内的友军（猎人）速度加成
    for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
        if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) == PlayerRole.HUNTER) {
            double distance = onlinePlayer.getLocation().distance(piglinLoc);
            if (distance <= 30.0) { // 30格范围
                onlinePlayer.addPotionEffect(new PotionEffect(
                    PotionEffectType.SPEED, 60, 0)); // 3秒速度1（比任务间隔稍长）
            }
        }
    }
    
    // 给范围内的速通者高亮和伤害加深标记
    for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
        if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) == PlayerRole.SPEEDRUNNER) {
            double distance = onlinePlayer.getLocation().distance(piglinLoc);
            if (distance <= 30.0) { // 30格范围
                // 高亮效果
                onlinePlayer.addPotionEffect(new PotionEffect(
                    PotionEffectType.GLOWING, 60, 0)); // 3秒发光
                // 伤害加深标记
                onlinePlayer.addPotionEffect(new PotionEffect(
                    PotionEffectType.WEAKNESS, 60, 0)); // 3秒虚弱作为标记
            }
        }
    }
}
```

## ⚙️ 系统特点

### 1. 持续性光环
- **自动刷新：** 每2秒自动检查和应用效果
- **无需触发：** 不需要攻击或其他行为触发
- **真正光环：** 符合光环技能的设计理念

### 2. 智能效果管理
- **效果时长：** 3秒效果时长，比2秒刷新间隔稍长
- **无缝覆盖：** 确保效果不会中断
- **性能优化：** 合理的刷新频率

### 3. 范围检测
- **30格范围：** 大范围的光环影响
- **实时计算：** 每次刷新时重新计算距离
- **动态效果：** 进入范围获得效果，离开范围失去效果

## 🎮 游戏体验改进

### 猪灵玩家体验
**修改前：**
- 需要主动攻击才能为队友提供增益
- 光环效果不连续，容易中断
- 不符合支援职业的定位

**修改后：**
- ✅ **被动支援：** 只需存在就能为队友提供增益
- ✅ **持续效果：** 光环效果连续不断
- ✅ **支援定位：** 真正的团队支援核心

### 队友体验
**修改前：**
- 只有在猪灵攻击时才能获得增益
- 效果不稳定，难以依赖

**修改后：**
- ✅ **稳定增益：** 在猪灵附近持续获得速度加成
- ✅ **可预期：** 可以依赖光环效果制定战术
- ✅ **团队协作：** 鼓励队友靠近猪灵

### 敌方体验
**修改前：**
- 只有在猪灵攻击时才会被标记
- 威胁不明显

**修改后：**
- ✅ **持续威胁：** 在猪灵附近持续被高亮和标记
- ✅ **明确反馈：** 清楚知道何时受到光环影响
- ✅ **战术考量：** 需要考虑与猪灵的距离

## 📊 性能考虑

### 任务频率
- **刷新间隔：** 2秒（40 tick）
- **效果时长：** 3秒（60 tick）
- **重叠时间：** 1秒重叠，确保无缝连接

### 性能优化
- **条件检查：** 只对猪灵玩家执行光环逻辑
- **距离计算：** 高效的距离检测算法
- **效果管理：** 合理的效果时长避免频繁刷新

### 资源管理
```java
/**
 * 停止光环任务（在插件卸载时调用）
 */
public void shutdown() {
    if (piglinAuraTask != null) {
        piglinAuraTask.cancel();
        piglinAuraTask = null;
    }
}
```

## 🔧 系统集成

### ProfessionManager集成
```java
// 在ProfessionManager中管理PassiveSkillHandler
private final PassiveSkillHandler passiveSkillHandler;

// 构造函数中初始化
this.passiveSkillHandler = new PassiveSkillHandler(plugin, pseudoRandom);

// 关闭时清理资源
public void shutdown() {
    if (passiveSkillHandler != null) {
        passiveSkillHandler.shutdown();
    }
}
```

### 生命周期管理
1. **启动：** 在PassiveSkillHandler构造时启动光环任务
2. **运行：** 持续检查和应用光环效果
3. **关闭：** 在插件关闭时停止任务，释放资源

## 🎯 效果对比

### 友军增益效果
| 方面 | 修改前 | 修改后 |
|------|--------|--------|
| **触发条件** | 猪灵攻击时 | 持续自动 |
| **效果持续** | 5秒一次性 | 3秒持续刷新 |
| **覆盖率** | 低（需攻击） | 高（持续存在） |
| **战术价值** | 有限 | 显著提升 |

### 敌军标记效果
| 方面 | 修改前 | 修改后 |
|------|--------|--------|
| **标记时机** | 猪灵攻击时 | 持续自动 |
| **威胁感知** | 不明显 | 持续威胁 |
| **战术影响** | 有限 | 需要考虑位置 |

## 🧪 测试建议

### 基础功能测试
1. **光环范围测试：**
   ```
   1. 猪灵站在固定位置
   2. 队友在不同距离测试速度效果
   3. 验证30格范围的准确性
   ```

2. **持续性测试：**
   ```
   1. 队友在猪灵附近停留
   2. 观察速度效果是否持续
   3. 验证3秒效果时长和2秒刷新间隔
   ```

### 性能测试
1. **多猪灵测试：**
   ```
   1. 多个猪灵同时在线
   2. 观察服务器性能影响
   3. 验证任务执行效率
   ```

2. **大范围测试：**
   ```
   1. 大量玩家在猪灵附近
   2. 测试距离计算性能
   3. 验证效果应用效率
   ```

## 🎉 重构总结

成功将猪灵狂意从"攻击触发"重构为"真正光环"：

- ✅ **持续光环：** 每2秒自动刷新，无需攻击触发
- ✅ **稳定效果：** 3秒效果时长确保无缝连接
- ✅ **真正支援：** 符合团队支援职业的定位
- ✅ **性能优化：** 合理的刷新频率和资源管理
- ✅ **系统集成：** 完善的生命周期管理

### 关键改进点
1. **光环特性：** 从触发式改为持续式光环
2. **用户体验：** 猪灵成为真正的团队支援核心
3. **战术价值：** 队友可以依赖光环效果制定战术
4. **技术实现：** 完善的任务管理和资源清理

现在猪灵真正成为了团队的支援核心，只需要存在就能为队友提供持续的增益效果！🐷✨

**重要特点：**
- 真正的被动光环效果
- 30格范围的持续支援
- 无需任何操作的自动增益
- 完善的性能优化和资源管理
