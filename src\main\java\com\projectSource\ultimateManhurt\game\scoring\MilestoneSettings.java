package com.projectSource.ultimateManhurt.game.scoring;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 里程碑设置类
 * 管理每个房间的里程碑配置，包括开关状态和自定义分数
 */
public class MilestoneSettings implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    // 里程碑开关状态 (里程碑 -> 是否启用)
    private Map<ScoreMilestone, Boolean> milestoneEnabled = new HashMap<>();
    
    // 里程碑自定义分数 (里程碑 -> 自定义分数)
    private Map<ScoreMilestone, Integer> customScores = new HashMap<>();
    
    /**
     * 构造函数，使用默认设置
     */
    public MilestoneSettings() {
        initializeDefaults();
    }
    
    /**
     * 初始化默认设置
     */
    private void initializeDefaults() {
        // 默认所有里程碑都启用，使用原始分数
        for (ScoreMilestone milestone : ScoreMilestone.values()) {
            milestoneEnabled.put(milestone, true);
            customScores.put(milestone, milestone.getDefaultPoints());
        }
    }
    
    /**
     * 检查里程碑是否启用
     */
    public boolean isMilestoneEnabled(ScoreMilestone milestone) {
        return milestoneEnabled.getOrDefault(milestone, true);
    }
    
    /**
     * 设置里程碑启用状态
     */
    public void setMilestoneEnabled(ScoreMilestone milestone, boolean enabled) {
        milestoneEnabled.put(milestone, enabled);
    }
    
    /**
     * 获取里程碑的自定义分数
     */
    public int getMilestoneScore(ScoreMilestone milestone) {
        return customScores.getOrDefault(milestone, milestone.getDefaultPoints());
    }
    
    /**
     * 设置里程碑的自定义分数
     */
    public void setMilestoneScore(ScoreMilestone milestone, int score) {
        customScores.put(milestone, Math.max(0, score)); // 确保分数不为负数
    }
    
    /**
     * 重置里程碑为默认分数
     */
    public void resetMilestoneScore(ScoreMilestone milestone) {
        customScores.put(milestone, milestone.getDefaultPoints());
    }
    
    /**
     * 重置所有里程碑为默认设置
     */
    public void resetToDefaults() {
        milestoneEnabled.clear();
        customScores.clear();
        initializeDefaults();
    }
    
    /**
     * 获取所有启用的里程碑
     */
    public Map<ScoreMilestone, Boolean> getAllMilestoneStates() {
        return new HashMap<>(milestoneEnabled);
    }
    
    /**
     * 获取所有自定义分数
     */
    public Map<ScoreMilestone, Integer> getAllCustomScores() {
        return new HashMap<>(customScores);
    }
    
    /**
     * 批量设置里程碑状态
     */
    public void setMilestoneStates(Map<ScoreMilestone, Boolean> states) {
        milestoneEnabled.putAll(states);
    }
    
    /**
     * 批量设置自定义分数
     */
    public void setCustomScores(Map<ScoreMilestone, Integer> scores) {
        for (Map.Entry<ScoreMilestone, Integer> entry : scores.entrySet()) {
            setMilestoneScore(entry.getKey(), entry.getValue());
        }
    }
    
    /**
     * 获取启用的里程碑总数
     */
    public int getEnabledMilestoneCount() {
        return (int) milestoneEnabled.values().stream().mapToInt(enabled -> enabled ? 1 : 0).sum();
    }
    
    /**
     * 获取所有启用里程碑的总分数
     */
    public int getTotalPossibleScore() {
        int total = 0;
        for (ScoreMilestone milestone : ScoreMilestone.values()) {
            if (isMilestoneEnabled(milestone)) {
                total += getMilestoneScore(milestone);
            }
        }
        return total;
    }
    
    /**
     * 克隆设置
     */
    public MilestoneSettings clone() {
        MilestoneSettings clone = new MilestoneSettings();
        clone.milestoneEnabled = new HashMap<>(this.milestoneEnabled);
        clone.customScores = new HashMap<>(this.customScores);
        return clone;
    }
    
    /**
     * 检查是否有自定义设置
     */
    public boolean hasCustomSettings() {
        for (ScoreMilestone milestone : ScoreMilestone.values()) {
            if (!isMilestoneEnabled(milestone) || 
                getMilestoneScore(milestone) != milestone.getDefaultPoints()) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 获取设置摘要
     */
    public String getSettingsSummary() {
        int enabledCount = getEnabledMilestoneCount();
        int totalCount = ScoreMilestone.values().length;
        int totalScore = getTotalPossibleScore();
        
        return String.format("启用 %d/%d 个里程碑，总分 %d", enabledCount, totalCount, totalScore);
    }
}
