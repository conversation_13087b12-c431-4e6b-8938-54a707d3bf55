package com.projectSource.ultimateManhurt.banpick;

/**
 * Ban Pick阶段 - 现代化流程设计
 *
 * 新流程设计理念：
 * 1. 更短的流程 - 总共8个阶段，约4分钟
 * 2. 交替进行 - 增加策略博弈
 * 3. 灵活时间 - 重要阶段时间更长
 * 4. 现代化体验 - 参考主流电竞游戏
 */
public enum BanPickPhase {

    // === 准备阶段 ===
    WAITING("准备阶段", "等待所有玩家准备", 10, PhaseType.PREPARATION),

    // === 第一轮：开场Ban ===
    HUNTER_BAN_1("捕猎者首Ban", "捕猎者禁用速通者的关键物品", 25, PhaseType.BAN),
    SPEEDRUNNER_BAN_1("速通者首Ban", "速通者禁用捕猎者的威胁物品", 25, PhaseType.BAN),

    // === 第二轮：核心Pick ===
    SPEEDRUNNER_PICK_1("速通者首选", "速通者锁定核心物品", 30, PhaseType.PICK),
    HUNTER_PICK_1("捕猎者首选", "捕猎者锁定核心物品", 30, PhaseType.PICK),

    // === 第三轮：战术Ban ===
    HUNTER_BAN_2("捕猎者次Ban", "捕猎者针对性禁用", 20, PhaseType.BAN),
    SPEEDRUNNER_BAN_2("速通者次Ban", "速通者针对性禁用", 20, PhaseType.BAN),

    // === 第四轮：补充Pick ===
    SPEEDRUNNER_PICK_2("速通者次选", "速通者补充选择", 25, PhaseType.PICK),
    HUNTER_PICK_2("捕猎者次选", "捕猎者补充选择", 25, PhaseType.PICK),

    // === 完成阶段 ===
    COMPLETED("完成", "Ban Pick阶段完成，准备开始游戏", 0, PhaseType.COMPLETION);

    /**
     * 阶段类型枚举
     */
    public enum PhaseType {
        PREPARATION("准备"),
        BAN("禁用"),
        PICK("选择"),
        COMPLETION("完成");

        private final String displayName;

        PhaseType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    private final String displayName;
    private final String description;
    private final int duration; // 阶段持续时间（秒）
    private final PhaseType type; // 阶段类型

    BanPickPhase(String displayName, String description, int duration, PhaseType type) {
        this.displayName = displayName;
        this.description = description;
        this.duration = duration;
        this.type = type;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    public int getDuration() {
        return duration;
    }

    public PhaseType getType() {
        return type;
    }
    
    /**
     * 获取下一个阶段
     */
    public BanPickPhase getNext() {
        BanPickPhase[] phases = values();
        int currentIndex = this.ordinal();
        if (currentIndex < phases.length - 1) {
            return phases[currentIndex + 1];
        }
        return this; // 如果已经是最后一个阶段，返回自己
    }
    
    /**
     * 判断是否是Ban阶段
     */
    public boolean isBanPhase() {
        return type == PhaseType.BAN;
    }

    /**
     * 判断是否是Pick阶段
     */
    public boolean isPickPhase() {
        return type == PhaseType.PICK;
    }

    /**
     * 判断是否是捕猎者的回合
     */
    public boolean isHunterTurn() {
        return this == HUNTER_BAN_1 || this == HUNTER_BAN_2 ||
               this == HUNTER_PICK_1 || this == HUNTER_PICK_2;
    }

    /**
     * 判断是否是速通者的回合
     */
    public boolean isSpeedrunnerTurn() {
        return this == SPEEDRUNNER_PICK_1 || this == SPEEDRUNNER_PICK_2 ||
               this == SPEEDRUNNER_BAN_1 || this == SPEEDRUNNER_BAN_2;
    }

    /**
     * 获取阶段的颜色代码（用于GUI显示）
     */
    public String getColorCode() {
        switch (type) {
            case PREPARATION:
                return "<gray>";
            case BAN:
                return "<red>";
            case PICK:
                return "<green>";
            case COMPLETION:
                return "<gold>";
            default:
                return "<white>";
        }
    }

    /**
     * 获取阶段的图标
     */
    public String getIcon() {
        switch (type) {
            case PREPARATION:
                return "⏳";
            case BAN:
                return "❌";
            case PICK:
                return "✅";
            case COMPLETION:
                return "🎉";
            default:
                return "❓";
        }
    }

    /**
     * 获取当前轮次（第几轮）
     */
    public int getRound() {
        switch (this) {
            case HUNTER_BAN_1:
            case SPEEDRUNNER_BAN_1:
            case SPEEDRUNNER_PICK_1:
            case HUNTER_PICK_1:
                return 1;
            case HUNTER_BAN_2:
            case SPEEDRUNNER_BAN_2:
            case SPEEDRUNNER_PICK_2:
            case HUNTER_PICK_2:
                return 2;
            default:
                return 0;
        }
    }
}
