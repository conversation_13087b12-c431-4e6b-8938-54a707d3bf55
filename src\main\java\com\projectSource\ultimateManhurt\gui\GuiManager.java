package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.room.Room;
import org.bukkit.entity.Player;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * GUI管理器
 * 负责所有GUI界面的管理
 */
public class GuiManager {

    private final UltimateManhurt plugin;
    private final Map<UUID, BaseGui> openGuis = new ConcurrentHashMap<>();
    private final ChatInputManager chatInputManager;

    public GuiManager(UltimateManhurt plugin) {
        this.plugin = plugin;
        this.chatInputManager = new ChatInputManager(plugin);
        initialize();
    }

    /**
     * 初始化GUI管理器
     */
    private void initialize() {
        // 注册聊天输入监听器
        plugin.getServer().getPluginManager().registerEvents(chatInputManager, plugin);
        plugin.getLogger().info("GUI管理器已初始化");
    }

    /**
     * 打开房间列表GUI
     */
    public void openRoomListGui(Player player) {
        RoomListGui gui = new RoomListGui(plugin, player);
        openGui(player, gui);
    }

    /**
     * 打开房间设置GUI
     */
    public void openRoomSettingsGui(Player player, Room room) {
        if (room == null) {
            // 房间为null，尝试从RoomManager获取玩家当前的房间
            room = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());

            if (room == null) {
                // 仍然为null，显示错误并打开房间列表
                plugin.getLogger().warning("无法找到玩家 " + player.getName() + " 的房间");
                com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(
                    player,
                    com.projectSource.ultimateManhurt.util.ComponentUtil.error("房间不存在或已被删除")
                );
                openRoomListGui(player);
                return;
            }
        }

        RoomSettingsGui gui = new RoomSettingsGui(plugin, player, room);
        openGui(player, gui);
    }

    /**
     * 打开种子设置GUI
     */
    public void openSeedSettingsGui(Player player, Room room) {
        if (room == null) {
            // 房间为null，尝试从RoomManager获取玩家当前的房间
            room = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());

            if (room == null) {
                // 仍然为null，显示错误并打开房间列表
                plugin.getLogger().warning("无法找到玩家 " + player.getName() + " 的房间");
                com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(
                    player,
                    com.projectSource.ultimateManhurt.util.ComponentUtil.error("房间不存在或已被删除")
                );
                openRoomListGui(player);
                return;
            }
        }

        SeedSettingsGui gui = new SeedSettingsGui(plugin, player, room);
        openGui(player, gui);
    }

    /**
     * 打开玩家管理GUI
     */
    public void openPlayerManagementGui(Player player, Room room) {
        if (room == null) {
            // 房间为null，尝试从RoomManager获取玩家当前的房间
            room = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());

            if (room == null) {
                // 仍然为null，显示错误并打开房间列表
                plugin.getLogger().warning("无法找到玩家 " + player.getName() + " 的房间");
                com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(
                    player,
                    com.projectSource.ultimateManhurt.util.ComponentUtil.error("房间不存在或已被删除")
                );
                openRoomListGui(player);
                return;
            }
        }

        PlayerManagementGui gui = new PlayerManagementGui(plugin, player, room);
        openGui(player, gui);
    }

    /**
     * 打开胜利模式选择GUI
     */
    public void openVictoryModeSelectionGui(Player player, Room room) {
        VictoryModeSelectionGui gui = new VictoryModeSelectionGui(plugin, player, room);
        openGui(player, gui);
    }

    /**
     * 打开守卫模式设置GUI
     */
    public void openGuardModeSettingsGui(Player player, Room room) {
        if (room == null) {
            // 房间为null，尝试从RoomManager获取玩家当前的房间
            room = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());

            if (room == null) {
                // 仍然为null，显示错误并打开房间列表
                plugin.getLogger().warning("无法找到玩家 " + player.getName() + " 的房间");
                com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(
                    player,
                    com.projectSource.ultimateManhurt.util.ComponentUtil.error("房间不存在或已被删除")
                );
                openRoomListGui(player);
                return;
            }
        }

        GuardModeSettingsGui gui = new GuardModeSettingsGui(plugin, player, room);
        openGui(player, gui);
    }

    /**
     * 打开角色选择GUI（天梯模式）
     */
    public void openRoleSelectionGui(Player player, Room room) {
        RoleSelectionGui gui = new RoleSelectionGui(plugin, player, room);
        openGui(player, gui);
    }

    /**
     * 打开房间类型选择GUI
     */
    public void openRoomTypeSelectionGui(Player player, String roomName) {
        RoomTypeSelectionGui gui = new RoomTypeSelectionGui(plugin, player, roomName);
        openGui(player, gui);
    }

    /**
     * 打开GUI
     */
    private void openGui(Player player, BaseGui gui) {
        UUID playerId = player.getUniqueId();

        // 关闭现有GUI
        closeGui(player);

        // 载具检查和处理将在 BaseGui.open() 中进行

        // 打开新GUI
        openGuis.put(playerId, gui);
        gui.open();
    }

    /**
     * 强制打开房间列表GUI（忽略载具状态）
     */
    public void forceOpenRoomListGui(Player player) {
        RoomListGui gui = new RoomListGui(plugin, player);
        UUID playerId = player.getUniqueId();

        // 关闭现有GUI
        closeGui(player);

        // 强制打开新GUI
        openGuis.put(playerId, gui);
        gui.forceOpen();
    }

    /**
     * 关闭玩家的GUI
     */
    public void closeGui(Player player) {
        UUID playerId = player.getUniqueId();
        BaseGui gui = openGuis.remove(playerId);

        if (gui != null) {
            gui.close();
        }
    }

    /**
     * 获取玩家当前打开的GUI
     */
    public BaseGui getOpenGui(Player player) {
        return openGuis.get(player.getUniqueId());
    }

    /**
     * 检查玩家是否有打开的GUI
     */
    public boolean hasOpenGui(Player player) {
        return openGuis.containsKey(player.getUniqueId());
    }

    /**
     * 获取聊天输入管理器
     */
    public ChatInputManager getChatInputManager() {
        return chatInputManager;
    }

    /**
     * 关闭所有GUI
     */
    public void closeAllGuis() {
        for (BaseGui gui : openGuis.values()) {
            gui.close();
        }
        openGuis.clear();

        // 清理聊天输入会话
        chatInputManager.cleanup();
    }

    /**
     * 打开玩家统计GUI
     */
    public void openPlayerStatsGui(Player player, UUID targetPlayerId) {
        PlayerStatsGui gui = new PlayerStatsGui(plugin, player, targetPlayerId);
        openGui(player, gui);
    }

    /**
     * 打开排行榜GUI
     */
    public void openLeaderboardGui(Player player) {
        LeaderboardGui gui = new LeaderboardGui(plugin, player);
        openGui(player, gui);
    }

    /**
     * 打开邀请管理GUI
     */
    public void openInviteGui(Player player, com.projectSource.ultimateManhurt.room.Room room) {
        InviteGui gui = new InviteGui(plugin, player, room);
        openGui(player, gui);
    }

    /**
     * 打开StartKit管理GUI
     */
    public void openStartKitGui(Player player, com.projectSource.ultimateManhurt.room.Room room) {
        StartKitGui gui = new StartKitGui(plugin, player, room);
        openGui(player, gui);
    }

    /**
     * 打开StartKit编辑器GUI
     */
    public void openStartKitEditorGui(Player player, com.projectSource.ultimateManhurt.room.Room room, com.projectSource.ultimateManhurt.game.PlayerRole role) {
        StartKitEditorGui gui = new StartKitEditorGui(plugin, player, room, role);
        openGui(player, gui);
    }

    /**
     * 打开StartKit模板选择GUI
     */
    public void openStartKitTemplateGui(Player player, com.projectSource.ultimateManhurt.room.Room room, com.projectSource.ultimateManhurt.game.PlayerRole role) {
        StartKitTemplateGui gui = new StartKitTemplateGui(plugin, player, room, role);
        openGui(player, gui);
    }

    /**
     * 打开自定义模板管理GUI
     */
    public void openCustomTemplateGui(Player player, com.projectSource.ultimateManhurt.room.Room room, com.projectSource.ultimateManhurt.game.PlayerRole role) {
        CustomTemplateGui gui = new CustomTemplateGui(plugin, player, room, role);
        openGui(player, gui);
    }

    /**
     * 打开里程碑设置GUI
     */
    public void openMilestoneSettingsGui(Player player, com.projectSource.ultimateManhurt.room.Room room) {
        if (room == null) {
            // 房间为null，尝试从RoomManager获取玩家当前的房间
            room = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());

            if (room == null) {
                // 仍然为null，显示错误并打开房间列表
                plugin.getLogger().warning("无法找到玩家 " + player.getName() + " 的房间");
                com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(
                    player,
                    com.projectSource.ultimateManhurt.util.ComponentUtil.error("房间不存在或已被删除")
                );
                openRoomListGui(player);
                return;
            }
        }

        MilestoneSettingsGui gui = new MilestoneSettingsGui(plugin, player, room);
        openGui(player, gui);
    }

    /**
     * 打开装备包模板应用选择GUI
     */
    public void openStartKitTemplateApplyGui(Player player, com.projectSource.ultimateManhurt.room.Room room, String templateId) {
        StartKitTemplateApplyGui gui = new StartKitTemplateApplyGui(plugin, player, room, templateId);
        openGui(player, gui);
    }

    /**
     * 打开专业模板GUI（暂时重定向到普通模板GUI）
     */
    public void openStartKitProTemplateGui(Player player, com.projectSource.ultimateManhurt.room.Room room) {
        // 专业模板功能可以后续扩展，目前使用普通模板GUI
        openStartKitTemplateGui(player, room, null);
    }

    /**
     * 打开装备包复制GUI
     */
    public void openStartKitCopyGui(Player player, com.projectSource.ultimateManhurt.room.Room room) {
        StartKitCopyGui gui = new StartKitCopyGui(plugin, player, room);
        openGui(player, gui);
    }

    /**
     * 打开装备包测试GUI
     */
    public void openStartKitTestGui(Player player, com.projectSource.ultimateManhurt.room.Room room) {
        StartKitTestGui gui = new StartKitTestGui(plugin, player, room);
        openGui(player, gui);
    }

    /**
     * 打开职业选择GUI
     */
    public void openProfessionSelectionGui(Player player, com.projectSource.ultimateManhurt.game.GameSession gameSession) {
        com.projectSource.ultimateManhurt.profession.gui.ProfessionSelectionGui gui =
            new com.projectSource.ultimateManhurt.profession.gui.ProfessionSelectionGui(plugin, player, gameSession);
        openGui(player, gui);
    }



    /**
     * 打开房间配置文件管理GUI
     */
    public void openRoomConfigFileManagementGui(Player player, com.projectSource.ultimateManhurt.room.Room room) {
        if (room == null) {
            // 房间为null，尝试从RoomManager获取玩家当前的房间
            room = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());

            if (room == null) {
                // 仍然为null，显示错误并打开房间列表
                plugin.getLogger().warning("无法找到玩家 " + player.getName() + " 的房间");
                com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(
                    player,
                    com.projectSource.ultimateManhurt.util.ComponentUtil.error("房间不存在或已被删除")
                );
                openRoomListGui(player);
                return;
            }
        }

        RoomConfigFileManagementGui gui = new RoomConfigFileManagementGui(plugin, player, room);
        openGui(player, gui);
    }

    /**
     * 打开房间配置文件预览GUI
     */
    public void openRoomConfigFilePreviewGui(Player player, com.projectSource.ultimateManhurt.room.Room room, String fileName) {
        if (room == null) {
            // 房间为null，尝试从RoomManager获取玩家当前的房间
            room = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());

            if (room == null) {
                // 仍然为null，显示错误并打开房间列表
                plugin.getLogger().warning("无法找到玩家 " + player.getName() + " 的房间");
                com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(
                    player,
                    com.projectSource.ultimateManhurt.util.ComponentUtil.error("房间不存在或已被删除")
                );
                openRoomListGui(player);
                return;
            }
        }

        RoomConfigFilePreviewGui gui = new RoomConfigFilePreviewGui(plugin, player, room, fileName);
        openGui(player, gui);
    }
}
