# 房间设置保存和加载功能

## 功能概述

现在RoomSetting可以保存成文件然后下一次直接加载。RoomSetting GUI提供了一个专门的配置文件管理入口，支持保存、加载和预览配置文件。

## 新增功能

### 1. 配置文件管理系统
- **自动文件命名**: 配置文件名自动生成，格式为"房间名_日期时间"
- **保存当前设置**: 将当前房间设置保存为配置文件
- **加载配置文件**: 从配置文件加载设置到当前房间
- **预览配置文件**: 详细查看配置文件内容
- **文件管理**: 查看、删除已保存的配置文件

### 2. 新增的类和组件

#### RoomSettingsManager
- 位置: `src/main/java/com/projectSource/ultimateManhurt/room/RoomSettingsManager.java`
- 功能: 负责房间设置的文件保存和加载
- 主要方法:
  - `saveSettings(RoomSettings, String, UUID)`: 保存设置到文件
  - `loadSettings(String)`: 从文件加载设置
  - `getAvailableSettings()`: 获取所有可用的配置文件
  - `deleteSettings(String)`: 删除配置文件
  - `getSettingsInfo(String)`: 获取配置文件信息

#### RoomConfigFileManagementGui
- 位置: `src/main/java/com/projectSource/ultimateManhurt/gui/RoomConfigFileManagementGui.java`
- 功能: 配置文件管理主界面
- 特性:
  - 保存当前设置（自动命名）
  - 显示所有配置文件列表
  - 支持加载、预览、删除操作
  - 批量清理功能

#### RoomConfigFilePreviewGui
- 位置: `src/main/java/com/projectSource/ultimateManhurt/gui/RoomConfigFilePreviewGui.java`
- 功能: 配置文件详细预览界面
- 特性:
  - 详细显示所有设置项
  - 支持直接加载配置
  - 支持删除配置文件

### 3. 修改的现有组件

#### RoomSettingsGui
- 新增按钮:
  - **配置文件管理** (槽位52): 打开配置文件管理界面
- 恢复操作模式切换按钮到原位 (槽位53)

#### UltimateManhurt (主类)
- 新增 `RoomSettingsManager` 实例
- 新增对应的getter方法

#### GuiManager
- 新增 `openRoomConfigFileManagementGui()` 方法
- 新增 `openRoomConfigFilePreviewGui()` 方法

## 使用方法

### 保存房间设置
1. 进入房间设置界面
2. 配置好所需的设置
3. 点击 **"配置文件管理"** 按钮
4. 在管理界面点击 **"保存当前设置"**
5. 系统自动生成文件名并保存
6. 设置保存完成

### 加载房间设置
1. 进入房间设置界面
2. 点击 **"配置文件管理"** 按钮
3. 从列表中左键点击要加载的配置文件
4. 确认加载（将覆盖当前所有设置）
5. 设置加载完成

### 预览配置文件
1. 进入配置文件管理界面
2. Shift+左键点击配置文件进行预览
3. 查看详细的配置信息
4. 可在预览界面直接加载或删除配置

## 文件存储

### 存储位置
配置文件保存在: `plugins/UltimateManhurt/room-settings/`

### 文件命名规则
- 自动生成格式: `房间名_日期时间.yml`
- 示例: `我的房间_20250130_143052.yml`
- 特殊字符会被替换为下划线

### 文件格式
- 格式: YAML (.yml)
- 编码: UTF-8
- 可以手动编辑或在服务器间分享

### 文件结构
```yaml
metadata:
  name: "配置文件名"
  creator: "创建者UUID"
  created-at: "创建时间"
  version: "1.0"

basic:
  game-duration-minutes: 30
  world-seed: 0
  difficulty: "NORMAL"
  spectator-game-mode: "SPECTATOR"

players:
  max-players: 10
  max-speedrunners: 1
  max-hunters: 9
  allow-spectators: true

game-rules:
  pvp-enabled: true
  friendly-fire: false
  keep-inventory: false
  natural-regeneration: true
  show-death-messages: true

# ... 更多设置分类
```

## 权限要求

- 只有房主可以保存和加载房间设置文件
- 所有房间成员可以查看当前设置

## 技术细节

### 保存的设置内容
- 基础设置（游戏时长、世界种子、难度等）
- 玩家设置（最大玩家数、角色限制等）
- 游戏规则（PVP、友军伤害、死亡保留物品等）
- 世界设置（结构生成、昼夜循环等）
- 特殊功能（指南针追踪、末影珍珠冷却等）
- Ban Pick系统设置
- 职业系统设置
- 豁免设置
- 胜利条件设置
- 血量设置
- 胜利模式设置
- 守卫模式设置
- StartKit设置（装备包）
- 里程碑设置
- 自定义规则

### 兼容性
- 支持所有现有的房间设置选项
- 向后兼容，不会影响现有功能
- 文件版本控制，支持未来扩展

## 操作说明

### 配置文件管理界面操作
- **左键点击配置文件**: 加载配置到当前房间
- **右键点击配置文件**: 删除配置文件
- **Shift+左键点击配置文件**: 预览配置文件详情
- **保存当前设置**: 自动生成文件名并保存当前房间设置
- **清理所有文件**: 删除所有配置文件（谨慎使用）

### 配置文件预览界面操作
- **加载此配置**: 将预览的配置应用到当前房间
- **删除此配置**: 删除当前预览的配置文件
- **查看自定义规则**: 显示配置文件中的自定义规则详情

## 注意事项

1. **自动命名**: 文件名自动生成，无需手动输入
2. **覆盖警告**: 加载设置会覆盖当前所有设置，请谨慎操作
3. **权限检查**: 只有房主可以进行文件操作
4. **文件大小**: 配置文件通常很小（几KB），不会占用太多存储空间
5. **备份建议**: 重要配置建议手动备份到其他位置
6. **预览功能**: 加载前可先预览配置内容，避免误操作

## 错误处理

- 文件不存在时会显示相应错误信息
- 文件格式错误时会使用默认值
- 权限不足时会阻止操作并提示
- 网络或IO错误时会显示错误信息

## 未来扩展

- 支持配置文件导入/导出
- 支持配置文件分享功能
- 支持配置文件模板系统
- 支持批量操作
