package com.projectSource.ultimateManhurt.game.scoring;

import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import net.kyori.adventure.text.Component;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.BookMeta;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 里程碑指南书籍
 * 为速通者生成包含里程碑内容和分数的指南书籍
 */
public class MilestoneGuideBook {
    
    /**
     * 创建里程碑指南书籍
     */
    public static ItemStack createGuideBook(Room room) {
        ItemStack book = new ItemStack(Material.WRITTEN_BOOK);
        BookMeta meta = (BookMeta) book.getItemMeta();
        
        if (meta == null) {
            return book;
        }
        
        // 设置书籍基本信息
        meta.setTitle("里程碑指南");
        meta.setAuthor("UltimateManhurt");

        // 添加特殊标识，用于死亡保护识别
        meta.getPersistentDataContainer().set(
            new org.bukkit.NamespacedKey("ultimatemanhurt", "protected_item"),
            org.bukkit.persistence.PersistentDataType.STRING,
            "speedrunner_guide_book"
        );
        
        // 获取里程碑设置
        MilestoneSettings milestoneSettings = room != null ? room.getSettings().getMilestoneSettings() : null;
        
        // 生成书籍页面
        List<Component> pages = generatePages(milestoneSettings);
        meta.pages(pages);
        
        book.setItemMeta(meta);
        
        // 设置书籍显示名称和描述
        return enhanceBookAppearance(book, milestoneSettings);
    }
    
    /**
     * 增强书籍外观
     */
    private static ItemStack enhanceBookAppearance(ItemStack book, MilestoneSettings milestoneSettings) {
        BookMeta meta = (BookMeta) book.getItemMeta();
        if (meta == null) return book;
        
        // 设置显示名称
        meta.displayName(ComponentUtil.parse("<gold><bold>📖 里程碑指南"));
        
        // 设置描述
        List<Component> lore = new ArrayList<>();
        lore.add(ComponentUtil.parse("<gray>包含所有游戏里程碑的详细信息"));
        
        if (milestoneSettings != null) {
            lore.add(ComponentUtil.parse("<gray>启用里程碑: <aqua>" +
                milestoneSettings.getEnabledMilestoneCount() + "/" + ScoreMilestone.values().length));
            lore.add(ComponentUtil.parse("<gray>总可能分数: <gold>" + milestoneSettings.getTotalPossibleScore()));

            if (milestoneSettings.hasCustomSettings()) {
                lore.add(ComponentUtil.parse("<yellow>⚠ 包含自定义设置"));
            }
        }
        
        lore.add(ComponentUtil.parse(""));
        lore.add(ComponentUtil.parse("<green>右键阅读指南"));
        
        meta.lore(lore);
        book.setItemMeta(meta);
        
        return book;
    }
    
    /**
     * 生成书籍页面
     */
    private static List<Component> generatePages(MilestoneSettings milestoneSettings) {
        List<Component> pages = new ArrayList<>();
        
        // 第一页：封面和介绍
        pages.add(generateCoverPage(milestoneSettings));
        
        // 按类别分组里程碑
        pages.addAll(generateMilestonePages(milestoneSettings));
        
        // 最后一页：总结
        pages.add(generateSummaryPage(milestoneSettings));
        
        return pages;
    }
    
    /**
     * 生成封面页
     */
    private static Component generateCoverPage(MilestoneSettings milestoneSettings) {
        StringBuilder content = new StringBuilder();

        content.append("<gold><bold>📖 里程碑指南\n\n");
        content.append("<gray>欢迎来到UltimateManhurt！\n\n");
        content.append("<gray>本指南包含了游戏中所有可获得分数的里程碑信息。\n\n");

        if (milestoneSettings != null) {
            content.append("<dark_gray>当前房间设置：\n");
            content.append("<dark_gray>• 启用里程碑：<aqua>").append(milestoneSettings.getEnabledMilestoneCount())
                   .append("<dark_gray>/<aqua>").append(ScoreMilestone.values().length).append("\n");
            content.append("<dark_gray>• 总可能分数：<gold>").append(milestoneSettings.getTotalPossibleScore()).append("\n\n");

            if (milestoneSettings.hasCustomSettings()) {
                content.append("<yellow>⚠ 此房间使用了自定义里程碑设置\n\n");
            }
        }

        content.append("<green>祝你好运！");

        return ComponentUtil.parse(content.toString());
    }
    
    /**
     * 生成里程碑页面
     */
    private static List<Component> generateMilestonePages(MilestoneSettings milestoneSettings) {
        List<Component> pages = new ArrayList<>();
        
        // 按类别分组
        List<ScoreMilestone> basicMilestones = Arrays.asList(
            ScoreMilestone.GET_WOOD, ScoreMilestone.CRAFT_WORKBENCH, ScoreMilestone.CRAFT_WOODEN_TOOLS,
            ScoreMilestone.MINE_STONE, ScoreMilestone.CRAFT_STONE_TOOLS, ScoreMilestone.CRAFT_FURNACE,
            ScoreMilestone.SMELT_IRON, ScoreMilestone.CRAFT_IRON_TOOLS, ScoreMilestone.CRAFT_IRON_ARMOR
        );
        
        List<ScoreMilestone> importantMilestones = Arrays.asList(
            ScoreMilestone.FIND_DIAMONDS, ScoreMilestone.CRAFT_DIAMOND_TOOLS, ScoreMilestone.CRAFT_DIAMOND_ARMOR,
            ScoreMilestone.BUILD_NETHER_PORTAL, ScoreMilestone.ENTER_NETHER
        );
        
        List<ScoreMilestone> netherMilestones = Arrays.asList(
            ScoreMilestone.KILL_BLAZE, ScoreMilestone.GET_BLAZE_ROD,
            ScoreMilestone.CRAFT_BREWING_STAND, ScoreMilestone.CRAFT_ENDER_EYE
        );

        List<ScoreMilestone> endMilestones = Arrays.asList(
            ScoreMilestone.ACTIVATE_END_PORTAL, ScoreMilestone.ENTER_END,
            ScoreMilestone.DAMAGE_DRAGON, ScoreMilestone.KILL_DRAGON
        );
        
        List<ScoreMilestone> bonusMilestones = Arrays.asList(
            ScoreMilestone.SURVIVAL_BONUS, ScoreMilestone.NO_DEATH_BONUS, ScoreMilestone.SPEED_BONUS_NETHER,
            ScoreMilestone.SPEED_BONUS_END, ScoreMilestone.SPEED_BONUS_DRAGON, ScoreMilestone.EFFICIENCY_BONUS,
            ScoreMilestone.KILL_HUNTER
        );
        
        // 生成各类别页面
        pages.add(generateCategoryPage("<gold><bold>基础进度", basicMilestones, milestoneSettings));
        pages.add(generateCategoryPage("<red><bold>重要里程碑", importantMilestones, milestoneSettings));
        pages.add(generateCategoryPage("<dark_red><bold>下界进度", netherMilestones, milestoneSettings));
        pages.add(generateCategoryPage("<dark_purple><bold>末地挑战", endMilestones, milestoneSettings));
        pages.add(generateCategoryPage("<green><bold>奖励里程碑", bonusMilestones, milestoneSettings));
        
        return pages;
    }
    
    /**
     * 生成类别页面
     */
    private static Component generateCategoryPage(String categoryTitle, List<ScoreMilestone> milestones, 
                                                 MilestoneSettings milestoneSettings) {
        StringBuilder content = new StringBuilder();
        content.append(categoryTitle).append("\n\n");
        
        for (ScoreMilestone milestone : milestones) {
            boolean enabled = milestoneSettings == null || milestoneSettings.isMilestoneEnabled(milestone);
            int score = milestoneSettings != null ?
                milestoneSettings.getMilestoneScore(milestone) : milestone.getDefaultPoints();

            // 里程碑状态图标
            String statusIcon = enabled ? "<green>✓" : "<red>✗";

            content.append(statusIcon).append(" <aqua>").append(milestone.getDisplayName()).append("\n");
            content.append("<dark_gray>").append(milestone.getDescription()).append("\n");
            content.append("<dark_gray>分数：<yellow>").append(score);

            // 如果分数被自定义了，显示原始分数
            if (milestoneSettings != null && score != milestone.getDefaultPoints()) {
                content.append(" <dark_gray>(原：<gold>").append(milestone.getDefaultPoints()).append("<dark_gray>)");
            }

            content.append("\n");
            content.append("<dark_gray>难度：<light_purple>").append(milestone.getDifficulty().getDisplayName()).append("\n\n");
        }
        
        return ComponentUtil.parse(content.toString());
    }
    
    /**
     * 生成总结页
     */
    private static Component generateSummaryPage(MilestoneSettings milestoneSettings) {
        StringBuilder content = new StringBuilder();

        content.append("<gold><bold>📊 总结\n\n");

        if (milestoneSettings != null) {
            content.append("<dark_gray>里程碑统计：\n");
            content.append("<dark_gray>• 总里程碑数：<aqua>").append(ScoreMilestone.values().length).append("\n");
            content.append("<dark_gray>• 启用数量：<green>").append(milestoneSettings.getEnabledMilestoneCount()).append("\n");
            content.append("<dark_gray>• 禁用数量：<red>").append(ScoreMilestone.values().length - milestoneSettings.getEnabledMilestoneCount()).append("\n\n");

            content.append("<dark_gray>分数统计：\n");
            content.append("<dark_gray>• 总可能分数：<gold>").append(milestoneSettings.getTotalPossibleScore()).append("\n");
            
            // 按难度统计
            int easyCount = 0, mediumCount = 0, hardCount = 0, extremeCount = 0;
            int easyScore = 0, mediumScore = 0, hardScore = 0, extremeScore = 0;
            
            for (ScoreMilestone milestone : ScoreMilestone.values()) {
                if (!milestoneSettings.isMilestoneEnabled(milestone)) continue;
                
                int score = milestoneSettings.getMilestoneScore(milestone);
                switch (milestone.getDifficulty()) {
                    case EASY:
                        easyCount++;
                        easyScore += score;
                        break;
                    case MEDIUM:
                        mediumCount++;
                        mediumScore += score;
                        break;
                    case HARD:
                        hardCount++;
                        hardScore += score;
                        break;
                    case EXTREME:
                        extremeCount++;
                        extremeScore += score;
                        break;
                }
            }
            
            content.append("\n<dark_gray>难度分布：\n");
            content.append("<green>简单：<aqua>").append(easyCount).append("个 (<gold>").append(easyScore).append("分)\n");
            content.append("<yellow>中等：<aqua>").append(mediumCount).append("个 (<gold>").append(mediumScore).append("分)\n");
            content.append("<gold>困难：<aqua>").append(hardCount).append("个 (<gold>").append(hardScore).append("分)\n");
            content.append("<red>极难：<aqua>").append(extremeCount).append("个 (<gold>").append(extremeScore).append("分)\n\n");
        }

        content.append("<green><bold>祝你在游戏中取得好成绩！");
        
        return ComponentUtil.parse(content.toString());
    }
}
