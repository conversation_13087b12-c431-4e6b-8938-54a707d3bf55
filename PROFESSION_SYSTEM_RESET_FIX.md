# 职业系统重置问题修复

## 🐛 严重问题描述

**问题现象：**
- 房间进行第二轮游戏时，职业系统出现严重问题
- 只有一边可以选择职业，另一边沿用上一把的职业
- 触发"默认职业分配"机制，导致游戏状态混乱
- **最严重：出现"影子地图"问题**

**问题根源：**
- 游戏结束后，职业系统没有正确清理玩家的职业状态
- 第二轮游戏开始时，系统检测到玩家已有职业，跳过职业选择阶段
- 导致游戏状态不一致，引发各种异常

## 🔧 修复方案

### 1. 游戏结束时清理职业状态

**修复位置：** `GameSession.cleanup()` 方法

**修复前的问题：**
```java
// 清理玩家位置数据
for (UUID playerId : playerRoles.keySet()) {
    plugin.getPlayerLocationManager().removePlayerLocation(playerId);
}

// 通知游戏管理器清理
plugin.getGameManager().removeGameSession(sessionId);
```

**修复后的逻辑：**
```java
// 清理玩家位置数据
for (UUID playerId : playerRoles.keySet()) {
    plugin.getPlayerLocationManager().removePlayerLocation(playerId);
}

// 清理职业系统状态
if (room.getSettings().isProfessionSystemEnabled()) {
    for (UUID playerId : playerRoles.keySet()) {
        plugin.getProfessionManager().removePlayerProfession(playerId);
        plugin.getLogger().info("已清理玩家 " + playerId + " 的职业状态");
    }
}

// 通知游戏管理器清理
plugin.getGameManager().removeGameSession(sessionId);
```

### 2. 职业选择阶段开始时强制清理

**修复位置：** `GameSession.startProfessionSelection()` 方法

**新增的预清理逻辑：**
```java
private void startProfessionSelection() {
    broadcastMessage(ComponentUtil.info("=== 职业选择阶段开始 ==="));
    broadcastMessage(ComponentUtil.info("请选择你的职业来增强你的能力"));

    // 清理所有玩家的职业状态，确保新游戏的干净状态
    for (UUID playerId : playerRoles.keySet()) {
        plugin.getProfessionManager().removePlayerProfession(playerId);
    }
    plugin.getLogger().info("已清理所有玩家的职业状态，开始新的职业选择阶段");

    // 统计需要选择职业的在线玩家数量
    int onlinePlayersNeedingProfession = 0;
    // ... 后续逻辑
}
```

### 3. 增强调试信息

**新增详细的职业状态记录：**
```java
// 详细记录每个玩家的职业选择状态
for (UUID playerId : playerRoles.keySet()) {
    PlayerRole role = playerRoles.get(playerId);
    if (role != PlayerRole.SPECTATOR) {
        Player player = Bukkit.getPlayer(playerId);
        if (player != null && player.isOnline()) {
            boolean hasProfession = plugin.getProfessionManager().hasPlayerProfession(playerId);
            Profession profession = plugin.getProfessionManager().getPlayerProfession(playerId);
            plugin.getLogger().info("玩家 " + player.getName() + " (角色: " + role + ") - 已选择职业: " + hasProfession + 
                (profession != null ? " (职业: " + profession.getDisplayName() + ")" : ""));
        }
    }
}
```

## 🎯 修复特点

### 双重保障机制
- **游戏结束清理：** 在游戏结束时立即清理所有职业状态
- **开始前清理：** 在职业选择阶段开始前再次强制清理
- **防止遗漏：** 确保无论何种情况都能获得干净的状态

### 详细调试信息
- **状态追踪：** 记录每个玩家的职业选择状态
- **清理日志：** 记录职业状态清理过程
- **问题排查：** 便于发现和解决潜在问题

### 条件检查
- **功能开关：** 只在启用职业系统时进行清理
- **角色过滤：** 只处理非观察者玩家
- **在线检查：** 确保玩家在线状态

## 🚫 解决的问题

### 1. 职业状态残留
**问题：** 上一轮游戏的职业状态没有清理
**解决：** 游戏结束时强制清理所有玩家职业状态

### 2. 职业选择跳过
**问题：** 系统检测到玩家已有职业，跳过选择阶段
**解决：** 职业选择开始前强制清理状态

### 3. 默认职业误触发
**问题：** 部分玩家被分配默认职业，导致游戏不平衡
**解决：** 确保所有玩家都能正常选择职业

### 4. 影子地图问题
**问题：** 游戏状态混乱导致世界创建异常
**解决：** 通过正确的状态管理避免异常情况

## 🧪 测试场景

### 正常流程测试
1. **第一轮游戏：**
   ```
   1. 启动房间，启用职业系统
   2. 所有玩家正常选择职业
   3. 游戏正常进行并结束
   4. 检查日志确认职业状态已清理
   ```

2. **第二轮游戏：**
   ```
   1. 在同一房间启动第二轮游戏
   2. 确认所有玩家都能重新选择职业
   3. 验证没有玩家沿用上一轮职业
   4. 游戏正常进行
   ```

### 异常情况测试
1. **玩家离线重连：**
   ```
   1. 第一轮游戏中玩家离线
   2. 游戏结束后玩家重连
   3. 第二轮游戏确认玩家能正常选择职业
   ```

2. **快速重启：**
   ```
   1. 第一轮游戏结束后立即启动第二轮
   2. 确认职业状态正确清理
   3. 所有玩家能正常选择职业
   ```

## 📊 日志示例

**游戏结束时的清理日志：**
```
[INFO] 已清理玩家 uuid-1 的职业状态
[INFO] 已清理玩家 uuid-2 的职业状态
[INFO] 游戏会话 session-id 已清理
```

**职业选择开始时的日志：**
```
[INFO] 已清理所有玩家的职业状态，开始新的职业选择阶段
[INFO] 房间 TestRoom 开始职业选择阶段，共有 2 名在线玩家需要选择职业
[INFO] 玩家 Player1 (角色: HUNTER) - 已选择职业: false
[INFO] 玩家 Player2 (角色: SPEEDRUNNER) - 已选择职业: false
```

**职业选择完成时的日志：**
```
[INFO] 玩家 Player1 (角色: HUNTER) - 已选择职业: true (职业: 末影人)
[INFO] 玩家 Player2 (角色: SPEEDRUNNER) - 已选择职业: true (职业: 探险家)
[INFO] 职业选择进度: 2/2 玩家已选择职业
[INFO] 所有在线玩家都已选择职业，继续游戏流程
```

## 🎉 总结

成功修复了职业系统的严重重置问题：

- ✅ **状态清理：** 游戏结束时正确清理所有职业状态
- ✅ **双重保障：** 职业选择开始前再次强制清理
- ✅ **调试增强：** 详细的状态追踪和日志记录
- ✅ **问题解决：** 彻底解决影子地图和状态混乱问题
- ✅ **稳定性提升：** 确保多轮游戏的稳定运行

现在房间可以正常进行多轮游戏，每轮游戏的职业选择都是独立和干净的！🎮✨

**重要提醒：** 这个修复解决了可能导致服务器不稳定的严重问题，建议立即测试并部署。
