package com.projectSource.ultimateManhurt.banpick;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Ban Pick系统管理器
 */
public class BanPickManager {
    
    private final UltimateManhurt plugin;
    private final GameSession gameSession;
    
    // 当前阶段
    private BanPickPhase currentPhase;
    
    // 投票系统
    private final Map<UUID, BanPickItem> currentVotes; // 当前阶段的投票
    private final Set<UUID> votedPlayers; // 已投票的玩家
    
    // 结果存储
    private final Set<BanPickItem> hunterBannedItems; // 捕猎者Ban的物品（速通者不能合成）
    private final Set<BanPickItem> speedrunnerBannedItems; // 速通者Ban的物品（捕猎者不能合成）
    private final Set<BanPickItem> hunterPickedItems; // 捕猎者锁定的物品
    private final Set<BanPickItem> speedrunnerPickedItems; // 速通者锁定的物品
    
    // 阶段计时器
    private BukkitTask phaseTimer;
    private int remainingTime;
    // 移除固定时间，改为使用阶段自带的时间
    
    // GUI管理
    private BanPickGUI banPickGUI;
    
    public BanPickManager(UltimateManhurt plugin, GameSession gameSession) {
        this.plugin = plugin;
        this.gameSession = gameSession;
        this.currentPhase = BanPickPhase.WAITING;
        this.currentVotes = new ConcurrentHashMap<>();
        this.votedPlayers = new HashSet<>();
        this.hunterBannedItems = new HashSet<>();
        this.speedrunnerBannedItems = new HashSet<>();
        this.hunterPickedItems = new HashSet<>();
        this.speedrunnerPickedItems = new HashSet<>();
        this.banPickGUI = new BanPickGUI(plugin, this);
    }
    
    /**
     * 开始Ban Pick阶段
     */
    public void startBanPick() {
        currentPhase = BanPickPhase.WAITING;

        // 通知所有玩家新的流程
        broadcastMessage(ComponentUtil.info("=== 🎮 现代化Ban Pick开始 ==="));
        broadcastMessage(ComponentUtil.info("📋 新流程: 捕猎者首Ban → 速通者首Ban → 速通者首选 → 捕猎者首选"));
        broadcastMessage(ComponentUtil.info("⏱️ 总时长约4分钟，每个阶段时间不同"));
        broadcastMessage(ComponentUtil.info("🎯 策略提示: 开场Ban针对对手核心，首选锁定自己必需"));

        // 为所有玩家打开GUI
        openGUIForAllPlayers();

        // 开始准备阶段
        startPhase();

        plugin.getLogger().info("房间 " + gameSession.getRoom().getName() + " 开始现代化Ban Pick阶段");
    }
    
    /**
     * 开始当前阶段
     */
    private void startPhase() {
        // 清理上一阶段的数据
        currentVotes.clear();
        votedPlayers.clear();
        remainingTime = currentPhase.getDuration();

        // 通知当前阶段信息
        broadcastPhaseInfo();

        // 更新所有玩家的GUI
        updateAllGUIs();

        // 启动计时器（如果阶段有时间限制）
        if (remainingTime > 0) {
            startPhaseTimer();
        }
    }
    
    /**
     * 广播阶段信息 - 现代化版本
     */
    private void broadcastPhaseInfo() {
        // 使用现代化的阶段显示
        String phaseIcon = currentPhase.getIcon();
        String phaseColor = currentPhase.getColorCode();
        String phaseName = currentPhase.getDisplayName();

        // 构建阶段标题
        String phaseTitle = String.format("%s=== %s %s ===",
            phaseColor, phaseIcon, phaseName);
        broadcastMessage(ComponentUtil.parse(phaseTitle));

        // 显示阶段描述
        String description = String.format("<yellow>%s", currentPhase.getDescription());
        broadcastMessage(ComponentUtil.parse(description));

        // 显示轮次信息
        int round = currentPhase.getRound();
        if (round > 0) {
            String roundInfo = String.format("<aqua>🔄 第%d轮 %s", round, currentPhase.getType().getDisplayName());
            broadcastMessage(ComponentUtil.parse(roundInfo));
        }

        // 显示剩余时间（如果有时间限制）
        if (remainingTime > 0) {
            String timeInfo = String.format("<gray>⏰ 剩余时间: <white>%d<gray>秒", remainingTime);
            broadcastMessage(ComponentUtil.parse(timeInfo));
        }

        // 显示当前统计
        showCurrentStats();
    }

    /**
     * 显示当前Ban Pick统计
     */
    private void showCurrentStats() {
        int hunterBanned = hunterBannedItems.size();
        int speedrunnerBanned = speedrunnerBannedItems.size();
        int hunterPicked = hunterPickedItems.size();
        int speedrunnerPicked = speedrunnerPickedItems.size();

        String stats = String.format("<gray>📊 当前状态: <blue>捕猎者 <red>%d禁<green>%d选 <gray>| <green>速通者 <red>%d禁<green>%d选",
            hunterBanned, hunterPicked, speedrunnerBanned, speedrunnerPicked);
        broadcastMessage(ComponentUtil.parse(stats));
    }
    
    /**
     * 启动阶段计时器
     */
    private void startPhaseTimer() {
        if (phaseTimer != null) {
            phaseTimer.cancel();
        }
        
        phaseTimer = new BukkitRunnable() {
            @Override
            public void run() {
                remainingTime--;
                
                // 更新GUI中的时间显示（只更新时间，避免频繁重建）
                updateTimerDisplay();
                
                // 智能时间警告系统
                BanPickManager.this.sendTimeWarning();
                
                // 时间到
                if (remainingTime <= 0) {
                    cancel();
                    onPhaseTimeout();
                }
            }
        }.runTaskTimer(plugin, 20L, 20L); // 每秒执行一次
    }
    
    /**
     * 阶段超时处理
     */
    private void onPhaseTimeout() {
        broadcastMessage(ComponentUtil.warning("时间到！进入下一阶段"));
        
        // 处理当前阶段的投票结果
        processPhaseResult();
        
        // 进入下一阶段
        nextPhase();
    }
    
    /**
     * 处理阶段投票结果
     */
    private void processPhaseResult() {
        if (currentVotes.isEmpty()) {
            broadcastMessage(ComponentUtil.info("本阶段没有任何投票"));
            return;
        }
        
        // 统计投票
        Map<BanPickItem, Integer> voteCount = new HashMap<>();
        for (BanPickItem item : currentVotes.values()) {
            voteCount.put(item, voteCount.getOrDefault(item, 0) + 1);
        }
        
        // 找到得票最多的物品
        BanPickItem winner = voteCount.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(null);
        
        if (winner != null) {
            applyPhaseResult(winner);
        }
    }
    
    /**
     * 应用阶段结果
     */
    private void applyPhaseResult(BanPickItem item) {
        if (currentPhase.isBanPhase()) {
            if (currentPhase.isHunterTurn()) {
                // 捕猎者Ban的物品，速通者不能合成
                hunterBannedItems.add(item);
                broadcastMessage(ComponentUtil.error(
                    String.format("捕猎者禁用了 %s！速通者无法合成此物品", item.getDisplayName())));
            } else {
                // 速通者Ban的物品，捕猎者不能合成
                speedrunnerBannedItems.add(item);
                broadcastMessage(ComponentUtil.error(
                    String.format("速通者禁用了 %s！捕猎者无法合成此物品", item.getDisplayName())));
            }
        } else if (currentPhase.isPickPhase()) {
            if (currentPhase.isHunterTurn()) {
                hunterPickedItems.add(item);
                broadcastMessage(ComponentUtil.success(
                    String.format("捕猎者锁定了 %s", item.getDisplayName())));
            } else {
                speedrunnerPickedItems.add(item);
                broadcastMessage(ComponentUtil.success(
                    String.format("速通者锁定了 %s", item.getDisplayName())));
            }
        }
    }
    
    /**
     * 进入下一阶段
     */
    private void nextPhase() {
        if (currentPhase == BanPickPhase.COMPLETED) {
            completeBanPick();
            return;
        }
        
        currentPhase = currentPhase.getNext();
        
        if (currentPhase == BanPickPhase.COMPLETED) {
            completeBanPick();
        } else {
            startPhase();
        }
    }
    
    /**
     * 完成Ban Pick
     */
    private void completeBanPick() {
        // 取消计时器
        if (phaseTimer != null) {
            phaseTimer.cancel();
            phaseTimer = null;
        }
        
        // 关闭所有GUI
        closeAllGUIs();
        
        // 显示最终结果
        showFinalResults();
        
        // 通知游戏会话Ban Pick完成
        gameSession.onBanPickCompleted();
        
        plugin.getLogger().info("房间 " + gameSession.getRoom().getName() + " 完成Ban Pick阶段");
    }
    
    /**
     * 显示最终结果
     */
    private void showFinalResults() {
        broadcastMessage(ComponentUtil.parse("<gold>=== Ban Pick结果 ==="));
        
        // 显示捕猎者禁用的物品（速通者不能合成）
        if (!hunterBannedItems.isEmpty()) {
            broadcastMessage("<red>捕猎者禁用的物品（速通者不能合成）:");
            for (BanPickItem item : hunterBannedItems) {
                broadcastMessage(String.format("<gray>- <red>%s", item.getDisplayName()));
            }
        }

        // 显示速通者禁用的物品（捕猎者不能合成）
        if (!speedrunnerBannedItems.isEmpty()) {
            broadcastMessage("<red>速通者禁用的物品（捕猎者不能合成）:");
            for (BanPickItem item : speedrunnerBannedItems) {
                broadcastMessage(String.format("<gray>- <red>%s", item.getDisplayName()));
            }
        }

        // 显示捕猎者锁定的物品
        if (!hunterPickedItems.isEmpty()) {
            broadcastMessage("<blue>捕猎者锁定的物品:");
            for (BanPickItem item : hunterPickedItems) {
                broadcastMessage(String.format("<gray>- <blue>%s", item.getDisplayName()));
            }
        }
        
        // 显示速通者锁定的物品
        if (!speedrunnerPickedItems.isEmpty()) {
            broadcastMessage("<green>速通者锁定的物品:");
            for (BanPickItem item : speedrunnerPickedItems) {
                broadcastMessage(String.format("<gray>- <green>%s", item.getDisplayName()));
            }
        }

        broadcastMessage("<green>Ban Pick完成！游戏即将开始...");
    }
    
    /**
     * 玩家投票
     */
    public boolean vote(Player player, BanPickItem item) {
        UUID playerId = player.getUniqueId();
        PlayerRole role = gameSession.getPlayerRole(playerId);

        // 检查是否是该角色的回合
        if (!canPlayerVote(role)) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("现在不是你的回合！"));
            return false;
        }

        // 检查物品是否可选择
        if (!isItemAvailable(item)) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("该物品已被禁用或选择！"));
            return false;
        }

        // 记录投票
        currentVotes.put(playerId, item);
        votedPlayers.add(playerId);

        // 通知投票成功
        String action = currentPhase.isBanPhase() ? "禁用" : "锁定";
        ComponentUtil.sendMessage(player, ComponentUtil.success(
            String.format("你投票%s: %s", action, item.getDisplayName())));

        // 检查是否所有该角色的玩家都已投票
        checkPhaseCompletion();

        return true;
    }

    /**
     * 检查玩家是否可以投票
     */
    private boolean canPlayerVote(PlayerRole role) {
        if (currentPhase.isHunterTurn() && role == PlayerRole.HUNTER) {
            return true;
        }
        if (currentPhase.isSpeedrunnerTurn() && role == PlayerRole.SPEEDRUNNER) {
            return true;
        }
        return false;
    }

    /**
     * 检查物品是否可用
     */
    private boolean isItemAvailable(BanPickItem item) {
        // 已被任一方禁用的物品不能选择
        if (hunterBannedItems.contains(item) || speedrunnerBannedItems.contains(item)) {
            return false;
        }

        // 已被任一方锁定的物品不能再选择
        if (hunterPickedItems.contains(item) || speedrunnerPickedItems.contains(item)) {
            return false;
        }

        return true;
    }

    /**
     * 检查阶段是否完成
     */
    private void checkPhaseCompletion() {
        PlayerRole currentRole = currentPhase.isHunterTurn() ? PlayerRole.HUNTER : PlayerRole.SPEEDRUNNER;

        // 统计该角色的玩家数量
        long rolePlayerCount = gameSession.getRoom().getPlayers().stream()
                .filter(playerId -> gameSession.getPlayerRole(playerId) == currentRole)
                .count();

        // 统计已投票的该角色玩家数量
        long votedRolePlayerCount = votedPlayers.stream()
                .filter(playerId -> gameSession.getPlayerRole(playerId) == currentRole)
                .count();

        // 如果所有该角色的玩家都已投票，立即进入下一阶段
        if (votedRolePlayerCount >= rolePlayerCount) {
            if (phaseTimer != null) {
                phaseTimer.cancel();
            }

            broadcastMessage("<green>所有玩家已投票完成！");
            processPhaseResult();
            nextPhase();
        }
    }

    /**
     * 获取玩家当前投票
     */
    public BanPickItem getPlayerVote(UUID playerId) {
        return currentVotes.get(playerId);
    }

    /**
     * 玩家是否已投票
     */
    public boolean hasPlayerVoted(UUID playerId) {
        return votedPlayers.contains(playerId);
    }

    // Getter方法
    public BanPickPhase getCurrentPhase() { return currentPhase; }
    public int getRemainingTime() { return remainingTime; }
    public Set<BanPickItem> getHunterBannedItems() { return new HashSet<>(hunterBannedItems); }
    public Set<BanPickItem> getSpeedrunnerBannedItems() { return new HashSet<>(speedrunnerBannedItems); }
    public Set<BanPickItem> getHunterPickedItems() { return new HashSet<>(hunterPickedItems); }
    public Set<BanPickItem> getSpeedrunnerPickedItems() { return new HashSet<>(speedrunnerPickedItems); }
    public GameSession getGameSession() { return gameSession; }
    
    // 辅助方法
    private void broadcastMessage(String message) {
        for (UUID playerId : gameSession.getRoom().getPlayers()) {
            Player player = plugin.getServer().getPlayer(playerId);
            if (player != null && player.isOnline()) {
                player.sendMessage(ComponentUtil.parse(message));
            }
        }
    }

    private void broadcastMessage(net.kyori.adventure.text.Component message) {
        for (UUID playerId : gameSession.getRoom().getPlayers()) {
            Player player = plugin.getServer().getPlayer(playerId);
            if (player != null && player.isOnline()) {
                player.sendMessage(message);
            }
        }
    }
    
    private void openGUIForAllPlayers() {
        for (UUID playerId : gameSession.getRoom().getPlayers()) {
            Player player = plugin.getServer().getPlayer(playerId);
            if (player != null && player.isOnline()) {
                banPickGUI.openGUI(player);
            }
        }
    }

    private void updateAllGUIs() {
        for (UUID playerId : gameSession.getRoom().getPlayers()) {
            Player player = plugin.getServer().getPlayer(playerId);
            if (player != null && player.isOnline()) {
                banPickGUI.updateGUI(player);
            }
        }
    }

    private void updateTimerDisplay() {
        for (UUID playerId : gameSession.getRoom().getPlayers()) {
            Player player = plugin.getServer().getPlayer(playerId);
            if (player != null && player.isOnline()) {
                banPickGUI.updateTimerOnly(player);
            }
        }
    }

    private void closeAllGUIs() {
        banPickGUI.cleanup();
        for (UUID playerId : gameSession.getRoom().getPlayers()) {
            Player player = plugin.getServer().getPlayer(playerId);
            if (player != null && player.isOnline()) {
                player.closeInventory();
            }
        }
    }

    /**
     * 智能时间警告系统
     */
    private void sendTimeWarning() {
        int totalTime = currentPhase.getDuration();

        // 根据阶段总时间动态调整警告时机
        if (totalTime >= 30) {
            // 长阶段：30秒、15秒、10秒、5秒警告
            if (remainingTime == 15) {
                broadcastMessage(ComponentUtil.parse("<yellow>⚠️ 还有15秒结束当前阶段！"));
            } else if (remainingTime == 10) {
                broadcastMessage(ComponentUtil.parse("<orange>⚠️ 还有10秒结束当前阶段！"));
            } else if (remainingTime == 5) {
                broadcastMessage(ComponentUtil.parse("<red>⚠️ 还有5秒结束当前阶段！"));
            }
        } else if (totalTime >= 20) {
            // 中等阶段：10秒、5秒警告
            if (remainingTime == 10) {
                broadcastMessage(ComponentUtil.parse("<orange>⚠️ 还有10秒结束当前阶段！"));
            } else if (remainingTime == 5) {
                broadcastMessage(ComponentUtil.parse("<red>⚠️ 还有5秒结束当前阶段！"));
            }
        } else {
            // 短阶段：只有5秒警告
            if (remainingTime == 5) {
                broadcastMessage(ComponentUtil.parse("<red>⚠️ 还有5秒结束当前阶段！"));
            }
        }

        // 最后3秒倒计时
        if (remainingTime <= 3 && remainingTime > 0) {
            String countdownColor = remainingTime == 1 ? "<red>" : "<yellow>";
            broadcastMessage(ComponentUtil.parse(String.format("%s%d...", countdownColor, remainingTime)));
        }
    }
}
