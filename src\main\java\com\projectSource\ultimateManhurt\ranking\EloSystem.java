package com.projectSource.ultimateManhurt.ranking;

import com.projectSource.ultimateManhurt.data.PlayerData;
import com.projectSource.ultimateManhurt.game.PlayerRole;

/**
 * ELO评分系统
 */
public class EloSystem {
    
    // ELO系统常量
    private static final int DEFAULT_ELO = 1200; // 默认ELO分数
    private static final int BASE_STAKE = 25; // 每个玩家的基础赌注分数

    /**
     * 计算新的ELO分数变化（基于赌注池系统）
     *
     * @param speedrunnerCount 速通者数量
     * @param hunterCount 猎人数量
     * @param won 是否获胜
     * @param playerRole 玩家角色
     * @param winnerRole 获胜方角色
     * @return ELO分数变化
     */
    public static int calculateEloChange(int speedrunnerCount, int hunterCount, boolean won,
                                       PlayerRole playerRole, PlayerRole winnerRole) {
        // 计算总赌注池
        int totalStake = (speedrunnerCount + hunterCount) * BASE_STAKE;

        if (won) {
            // 获胜方：获得所有分数并平分
            int teamSize = (playerRole == PlayerRole.SPEEDRUNNER) ? speedrunnerCount : hunterCount;
            return totalStake / teamSize;
        } else {
            // 失败方：根据角色不同扣分规则
            if (playerRole == PlayerRole.SPEEDRUNNER) {
                // 速通者失败：只扣除自己的25分
                return -BASE_STAKE;
            } else {
                // 猎人失败：扣除对方阵营平分后应得的分数
                int opponentTeamSize = (winnerRole == PlayerRole.SPEEDRUNNER) ? speedrunnerCount : hunterCount;
                int opponentGainPerPlayer = totalStake / opponentTeamSize;
                return -opponentGainPerPlayer;
            }
        }
    }

    /**
     * 旧版本的ELO计算方法（保留用于兼容性，已弃用）
     * @deprecated 使用新的基于赌注池的计算方法
     */
    @Deprecated
    public static int calculateEloChangeOld(int playerElo, int opponentAvgElo, boolean won, PlayerRole playerRole) {
        // 计算期望胜率
        double expectedScore = calculateExpectedScore(playerElo, opponentAvgElo);

        // 实际得分（胜利=1，失败=0）
        double actualScore = won ? 1.0 : 0.0;

        // 角色难度系数
        double roleMultiplier = getRoleMultiplier(playerRole);

        // 计算ELO变化
        double eloChange = 32 * roleMultiplier * (actualScore - expectedScore);

        return (int) Math.round(eloChange);
    }

    /**
     * 计算期望胜率（保留用于其他功能）
     */
    private static double calculateExpectedScore(int playerElo, int opponentElo) {
        return 1.0 / (1.0 + Math.pow(10.0, (opponentElo - playerElo) / 400.0));
    }

    /**
     * 获取角色难度系数（保留用于其他功能）
     */
    private static double getRoleMultiplier(PlayerRole role) {
        switch (role) {
            case SPEEDRUNNER:
                return 1.2; // 速通者更难，获胜奖励更多
            case HUNTER:
                return 1.0;
            default:
                return 1.0;
        }
    }
    
    /**
     * 获取玩家ELO分数
     */
    public static int getPlayerElo(PlayerData playerData) {
        Object eloObj = playerData.getCustomData("elo");
        if (eloObj instanceof Integer) {
            return (Integer) eloObj;
        }
        return DEFAULT_ELO;
    }
    
    /**
     * 设置玩家ELO分数
     */
    public static void setPlayerElo(PlayerData playerData, int elo) {
        playerData.setCustomData("elo", elo);
    }
    
    /**
     * 更新玩家ELO分数
     */
    public static void updatePlayerElo(PlayerData playerData, int eloChange) {
        int currentElo = getPlayerElo(playerData);
        int newElo = Math.max(0, currentElo + eloChange); // ELO不能低于0
        setPlayerElo(playerData, newElo);
    }
    
    /**
     * 根据ELO获取段位
     */
    public static Rank getRank(int elo) {
        if (elo >= 2400) return Rank.GRANDMASTER;
        if (elo >= 2200) return Rank.MASTER;
        if (elo >= 2000) return Rank.DIAMOND;
        if (elo >= 1800) return Rank.PLATINUM;
        if (elo >= 1600) return Rank.GOLD;
        if (elo >= 1400) return Rank.SILVER;
        if (elo >= 1200) return Rank.BRONZE;
        return Rank.IRON;
    }
    
    /**
     * 段位枚举
     */
    public enum Rank {
        IRON("黑铁", "<color:#555555>", 0),
        BRONZE("青铜", "<color:#CD7F32>", 1200),
        SILVER("白银", "<color:#C0C0C0>", 1400),
        GOLD("黄金", "<color:#FFD700>", 1600),
        PLATINUM("铂金", "<color:#00FFFF>", 1800),
        DIAMOND("钻石", "<color:#00BFFF>", 2000),
        MASTER("大师", "<color:#DDA0DD>", 2200),
        GRANDMASTER("宗师", "<color:#FF0000>", 2400);
        
        private final String displayName;
        private final String color;
        private final int minElo;
        
        Rank(String displayName, String color, int minElo) {
            this.displayName = displayName;
            this.color = color;
            this.minElo = minElo;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public String getColor() {
            return color;
        }
        
        public int getMinElo() {
            return minElo;
        }
        
        public String getColoredName() {
            return color + displayName;
        }
        
        /**
         * 获取段位图标
         */
        public String getIcon() {
            switch (this) {
                case IRON: return "⚫";
                case BRONZE: return "🟤";
                case SILVER: return "⚪";
                case GOLD: return "🟡";
                case PLATINUM: return "🔵";
                case DIAMOND: return "💎";
                case MASTER: return "🟣";
                case GRANDMASTER: return "🔴";
                default: return "❓";
            }
        }
        
        /**
         * 获取下一个段位
         */
        public Rank getNext() {
            Rank[] ranks = values();
            int currentIndex = this.ordinal();
            if (currentIndex < ranks.length - 1) {
                return ranks[currentIndex + 1];
            }
            return this; // 已经是最高段位
        }
        
        /**
         * 获取升级所需ELO
         */
        public int getEloToNext() {
            Rank next = getNext();
            if (next == this) {
                return 0; // 已经是最高段位
            }
            return next.getMinElo();
        }
    }
    
    /**
     * 计算多人游戏的平均ELO
     */
    public static int calculateAverageElo(java.util.List<PlayerData> players) {
        if (players.isEmpty()) {
            return DEFAULT_ELO;
        }
        
        int totalElo = 0;
        for (PlayerData player : players) {
            totalElo += getPlayerElo(player);
        }
        
        return totalElo / players.size();
    }
    
    /**
     * 判断是否为排位赛（基于玩家ELO差距）
     */
    public static boolean isRankedMatch(java.util.List<PlayerData> speedrunners, java.util.List<PlayerData> hunters) {
        if (speedrunners.isEmpty() || hunters.isEmpty()) {
            return false;
        }

        int speedrunnerAvgElo = calculateAverageElo(speedrunners);
        int hunterAvgElo = calculateAverageElo(hunters);

        // ELO差距不超过200分才算排位赛
        return Math.abs(speedrunnerAvgElo - hunterAvgElo) <= 200;
    }

    /**
     * 获取基础赌注分数
     */
    public static int getBaseStake() {
        return BASE_STAKE;
    }

    /**
     * 计算赌注池总分数
     */
    public static int calculateTotalStake(int speedrunnerCount, int hunterCount) {
        return (speedrunnerCount + hunterCount) * BASE_STAKE;
    }

    /**
     * 预览ELO变化（用于显示预期收益）
     */
    public static EloChangePreview previewEloChange(int speedrunnerCount, int hunterCount,
                                                   PlayerRole playerRole) {
        int totalStake = calculateTotalStake(speedrunnerCount, hunterCount);
        int teamSize = (playerRole == PlayerRole.SPEEDRUNNER) ? speedrunnerCount : hunterCount;
        int opponentTeamSize = (playerRole == PlayerRole.SPEEDRUNNER) ? hunterCount : speedrunnerCount;

        // 获胜时的收益
        int winGain = totalStake / teamSize;

        // 失败时的损失
        int loseLoss;
        if (playerRole == PlayerRole.SPEEDRUNNER) {
            loseLoss = -BASE_STAKE; // 速通者只扣自己的25分
        } else {
            loseLoss = -(totalStake / opponentTeamSize); // 猎人扣对方平分后的分数
        }

        return new EloChangePreview(winGain, loseLoss, totalStake, teamSize, opponentTeamSize);
    }

    /**
     * ELO变化预览类
     */
    public static class EloChangePreview {
        private final int winGain;
        private final int loseLoss;
        private final int totalStake;
        private final int teamSize;
        private final int opponentTeamSize;

        public EloChangePreview(int winGain, int loseLoss, int totalStake, int teamSize, int opponentTeamSize) {
            this.winGain = winGain;
            this.loseLoss = loseLoss;
            this.totalStake = totalStake;
            this.teamSize = teamSize;
            this.opponentTeamSize = opponentTeamSize;
        }

        public int getWinGain() { return winGain; }
        public int getLoseLoss() { return loseLoss; }
        public int getTotalStake() { return totalStake; }
        public int getTeamSize() { return teamSize; }
        public int getOpponentTeamSize() { return opponentTeamSize; }

        public String getDescription(PlayerRole role) {
            String roleStr = role == PlayerRole.SPEEDRUNNER ? "速通者" : "猎人";
            return String.format("%s - 获胜: +%d分, 失败: %d分 (赌注池: %d分)",
                roleStr, winGain, loseLoss, totalStake);
        }
    }
}
