package com.projectSource.ultimateManhurt.game;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Bukkit;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitTask;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.UUID;

/**
 * 豁免状态管理器
 * 负责管理speedrunner的开局豁免状态
 */
public class ImmunityManager {
    
    private final UltimateManhurt plugin;
    private final GameSession gameSession;
    private BukkitTask immunityTask;
    private boolean immunityActive = false;
    
    public ImmunityManager(UltimateManhurt plugin, GameSession gameSession) {
        this.plugin = plugin;
        this.gameSession = gameSession;
    }
    
    /**
     * 开始豁免期
     */
    public void startImmunity() {
        if (!gameSession.getRoom().getSettings().isImmunityEnabled()) {
            return;
        }
        
        if (gameSession.getState() != GameState.RUNNING) {
            return;
        }
        
        immunityActive = true;
        int immunityDuration = gameSession.getRoom().getSettings().getImmunityDurationSeconds();
        
        // 给所有speedrunner添加视觉效果
        applyImmunityEffects();
        
        // 广播豁免开始消息
        broadcastImmunityStart(immunityDuration);
        
        // 启动豁免倒计时任务
        startImmunityCountdown(immunityDuration);
        
        plugin.getLogger().info("游戏 " + gameSession.getSessionId() + " 豁免期开始，持续 " + immunityDuration + " 秒");
    }
    
    /**
     * 结束豁免期
     */
    public void endImmunity() {
        if (!immunityActive) {
            return;
        }
        
        immunityActive = false;
        
        // 移除所有speedrunner的视觉效果
        removeImmunityEffects();
        
        // 广播豁免结束消息
        broadcastImmunityEnd();
        
        // 取消倒计时任务
        if (immunityTask != null) {
            immunityTask.cancel();
            immunityTask = null;
        }
        
        plugin.getLogger().info("游戏 " + gameSession.getSessionId() + " 豁免期结束");
    }
    
    /**
     * 检查是否在豁免期内
     */
    public boolean isImmunityActive() {
        if (!gameSession.getRoom().getSettings().isImmunityEnabled()) {
            return false;
        }
        
        if (gameSession.getState() != GameState.RUNNING) {
            return false;
        }
        
        LocalDateTime startTime = gameSession.getStartTime();
        if (startTime == null) {
            return false;
        }
        
        long secondsSinceStart = ChronoUnit.SECONDS.between(startTime, LocalDateTime.now());
        return secondsSinceStart < gameSession.getRoom().getSettings().getImmunityDurationSeconds();
    }
    
    /**
     * 获取剩余豁免时间（秒）
     */
    public long getRemainingImmunityTime() {
        if (!isImmunityActive()) {
            return 0;
        }
        
        LocalDateTime startTime = gameSession.getStartTime();
        if (startTime == null) {
            return 0;
        }
        
        long secondsSinceStart = ChronoUnit.SECONDS.between(startTime, LocalDateTime.now());
        long immunityDuration = gameSession.getRoom().getSettings().getImmunityDurationSeconds();
        
        return Math.max(0, immunityDuration - secondsSinceStart);
    }
    
    /**
     * 应用豁免效果
     */
    private void applyImmunityEffects() {
        for (UUID speedrunnerId : gameSession.getPlayersByRole(PlayerRole.SPEEDRUNNER)) {
            Player speedrunner = Bukkit.getPlayer(speedrunnerId);
            if (speedrunner != null && speedrunner.isOnline()) {
                // 添加发光效果（让speedrunner知道自己在豁免期）
                speedrunner.addPotionEffect(new PotionEffect(PotionEffectType.GLOWING, Integer.MAX_VALUE, 0, false, false));
                
                // 播放豁免开始音效
                speedrunner.playSound(speedrunner.getLocation(), Sound.BLOCK_BEACON_ACTIVATE, 1.0f, 1.5f);
            }
        }
    }
    
    /**
     * 移除豁免效果
     */
    private void removeImmunityEffects() {
        for (UUID speedrunnerId : gameSession.getPlayersByRole(PlayerRole.SPEEDRUNNER)) {
            Player speedrunner = Bukkit.getPlayer(speedrunnerId);
            if (speedrunner != null && speedrunner.isOnline()) {
                // 移除发光效果
                speedrunner.removePotionEffect(PotionEffectType.GLOWING);
                
                // 播放豁免结束音效
                speedrunner.playSound(speedrunner.getLocation(), Sound.BLOCK_BEACON_DEACTIVATE, 1.0f, 1.0f);
            }
        }
    }
    
    /**
     * 广播豁免开始消息
     */
    private void broadcastImmunityStart(int duration) {
        var message = ComponentUtil.info("速通者获得 " + duration + " 秒豁免保护！");
        gameSession.broadcastMessage(message);
        
        // 给speedrunner发送特殊消息
        for (UUID speedrunnerId : gameSession.getPlayersByRole(PlayerRole.SPEEDRUNNER)) {
            Player speedrunner = Bukkit.getPlayer(speedrunnerId);
            if (speedrunner != null && speedrunner.isOnline()) {
                ComponentUtil.sendTitle(speedrunner,
                    ComponentUtil.parse("<gold><bold>豁免保护"),
                    ComponentUtil.parse("<yellow>" + duration + " 秒内免受伤害"),
                    10, 40, 10
                );
            }
        }
    }
    
    /**
     * 广播豁免结束消息
     */
    private void broadcastImmunityEnd() {
        var message = ComponentUtil.warning("豁免期结束！PVP现在开启！");
        gameSession.broadcastMessage(message);
        
        // 给所有玩家发送标题提醒
        for (UUID playerId : gameSession.getPlayerRoles().keySet()) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                PlayerRole role = gameSession.getPlayerRole(playerId);
                if (role == PlayerRole.SPEEDRUNNER) {
                    ComponentUtil.sendTitle(player,
                        ComponentUtil.parse("<red><bold>豁免结束"),
                        ComponentUtil.parse("<yellow>小心捕猎者！"),
                        10, 30, 10
                    );
                } else if (role == PlayerRole.HUNTER) {
                    ComponentUtil.sendTitle(player,
                        ComponentUtil.parse("<green><bold>开始狩猎"),
                        ComponentUtil.parse("<yellow>现在可以攻击速通者！"),
                        10, 30, 10
                    );
                }
            }
        }
    }
    
    /**
     * 启动豁免倒计时任务
     */
    private void startImmunityCountdown(int duration) {
        immunityTask = Bukkit.getScheduler().runTaskTimer(plugin, new Runnable() {
            private int remaining = duration;
            
            @Override
            public void run() {
                if (remaining <= 0 || gameSession.getState() != GameState.RUNNING) {
                    endImmunity();
                    return;
                }
                
                // 在最后10秒显示倒计时
                if (remaining <= 10) {
                    for (UUID speedrunnerId : gameSession.getPlayersByRole(PlayerRole.SPEEDRUNNER)) {
                        Player speedrunner = Bukkit.getPlayer(speedrunnerId);
                        if (speedrunner != null && speedrunner.isOnline()) {
                            ComponentUtil.sendActionBar(speedrunner, 
                                ComponentUtil.parse("<gold>豁免剩余: <yellow>" + remaining + "s"));
                            
                            // 播放倒计时音效
                            if (remaining <= 5) {
                                speedrunner.playSound(speedrunner.getLocation(), Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 2.0f);
                            }
                            
                            // 显示粒子效果
                            speedrunner.getWorld().spawnParticle(Particle.HAPPY_VILLAGER,
                                speedrunner.getLocation().add(0, 2, 0), 5, 0.5, 0.5, 0.5, 0);
                        }
                    }
                }
                
                remaining--;
            }
        }, 0L, 20L); // 每秒执行一次
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        endImmunity();
    }
}
