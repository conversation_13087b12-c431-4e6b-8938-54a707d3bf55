# 房间自动关闭功能调试

## 问题描述
用户反馈：所有玩家都不在线的情况下，过了一分钟之后还是没有强制终止游戏。

## 可能的原因分析

### 1. 计时器没有启动
- 玩家离线时 `checkRoomAutoClose` 没有被调用
- 房间状态检查逻辑有问题
- 玩家在线状态判断错误

### 2. 计时器启动了但没有执行
- BukkitTask 调度失败
- 插件被禁用或重载
- 服务器性能问题导致任务延迟

### 3. 计时器执行了但没有强制关闭
- 游戏会话查找失败
- 游戏状态判断错误
- 强制结束游戏的方法有问题

## 添加的调试功能

### 1. 详细的状态检查日志
```java
plugin.getLogger().info("checkRoomAutoClose: 房间 " + room.getName() + " 玩家状态 - 总数: " + totalPlayers + ", 在线: " + onlinePlayers);
```

### 2. 计时器启动日志
```java
plugin.getLogger().info("房间 " + room.getName() + " 所有玩家都离线，启动自动关闭计时器（10秒）" + 
                       (hadExistingTimer ? " [替换现有计时器]" : " [新计时器]"));
```

### 3. 计时器执行日志
```java
plugin.getLogger().info("自动关闭计时器执行 - 房间ID: " + roomId);
plugin.getLogger().info("计时器检查结果 - 房间: " + currentRoom.getName() + 
                       ", 在线玩家: " + onlineCount + ", 全部离线: " + stillAllOffline);
```

### 4. 强制关闭详细日志
```java
plugin.getLogger().info("检查游戏会话 - 房间: " + room.getName() + 
                       ", 游戏会话: " + (gameSession != null ? "存在" : "不存在") +
                       (gameSession != null ? ", 状态: " + gameSession.getState() : ""));
```

### 5. 玩家离线事件日志
```java
plugin.getLogger().info("玩家 " + player.getName() + " 离线，检查房间 " + room.getName() + " 是否需要自动关闭");
```

## 临时修改

### 1. 缩短测试时间
将自动关闭延迟从60秒改为10秒，便于快速测试：
```java
private static final long AUTO_CLOSE_DELAY = 10 * 20L; // 10秒 = 200 ticks (临时测试用)
```

### 2. 异常处理
在强制结束游戏时添加了try-catch块：
```java
try {
    gameSession.forceEndGame(false);
    plugin.getLogger().info("游戏强制结束成功");
} catch (Exception e) {
    plugin.getLogger().severe("强制结束游戏时发生错误: " + e.getMessage());
    e.printStackTrace();
}
```

## 测试步骤

### 1. 创建房间并开始游戏
1. 创建一个房间
2. 添加玩家并开始游戏
3. 确保游戏正在进行中

### 2. 模拟所有玩家离线
1. 所有玩家断开连接
2. 观察控制台日志

### 3. 预期的日志输出
```
[INFO]: 玩家 PlayerName 离线，检查房间 RoomName 是否需要自动关闭
[INFO]: checkRoomAutoClose: 房间 RoomName 玩家状态 - 总数: 2, 在线: 0
[INFO]: checkRoomAutoClose: 房间 RoomName 无在线玩家，启动自动关闭计时器
[INFO]: 房间 RoomName 所有玩家都离线，启动自动关闭计时器（10秒） [新计时器]
[INFO]: 自动关闭计时器执行 - 房间ID: room_xxx
[INFO]: 计时器检查结果 - 房间: RoomName, 在线玩家: 0, 全部离线: true
[INFO]: 执行强制关闭房间: RoomName
[INFO]: 房间 RoomName 所有玩家离线超过10秒，强制关闭
[INFO]: 检查游戏会话 - 房间: RoomName, 游戏会话: 存在, 状态: RUNNING
[INFO]: 强制结束房间 RoomName 的游戏（不计分）
[INFO]: 游戏强制结束成功
[INFO]: 删除房间: RoomName
[INFO]: 房间删除完成: RoomName
```

## 可能的问题点

### 1. 玩家状态检查
- `Bukkit.getPlayer(playerId)` 可能返回null
- `player.isOnline()` 可能不准确
- 玩家UUID映射问题

### 2. 游戏会话查找
- `getGameSessionByRoom(roomId)` 可能找不到会话
- 房间ID和游戏会话的映射可能有问题

### 3. 游戏状态判断
- `gameSession.getState().isActive()` 可能判断错误
- 游戏状态可能不是预期的值

### 4. 任务调度问题
- BukkitScheduler 可能有问题
- 插件重载可能导致任务丢失

## 调试建议

### 1. 检查日志输出
运行测试后检查控制台日志，看看哪一步没有执行或执行异常。

### 2. 手动触发
可以添加一个调试命令来手动触发房间自动关闭检查：
```java
/manhunt debug autoclose <roomId>
```

### 3. 状态验证
在关键点添加断点或更多日志来验证：
- 玩家真的离线了吗？
- 房间状态是否正确？
- 游戏会话是否存在？

### 4. 恢复设置
测试完成后记得将延迟时间改回60秒：
```java
private static final long AUTO_CLOSE_DELAY = 60 * 20L; // 60秒 = 1200 ticks
```

## 注意事项

1. **日志级别**: 确保服务器日志级别设置为INFO或更低
2. **性能影响**: 大量调试日志可能影响性能，测试完成后应该移除
3. **时区问题**: 确保服务器时间设置正确
4. **并发问题**: 多个玩家同时离线可能导致竞态条件

## 下一步

如果问题仍然存在，可以考虑：
1. 添加更详细的堆栈跟踪
2. 检查Bukkit版本兼容性
3. 验证插件加载顺序
4. 检查是否有其他插件干扰
