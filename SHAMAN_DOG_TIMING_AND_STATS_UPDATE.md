# 萨满狗伙伴时机和属性调整

## 🐕 调整概览

### 主要变更
1. **生成时机：** 立即生成 → 游戏开始3分钟后生成
2. **生命值：** 80点 → 60点
3. **攻击力：** 6点 → 5点

### 调整原因
- **平衡性考虑：** 避免萨满在游戏初期过于强势
- **战术深度：** 增加游戏前期的策略性
- **数值平衡：** 调整狗的战斗力到合理水平

## ⏰ 生成时机调整

### 修改前
```java
// 延迟5秒后尝试生成狗（给游戏世界创建时间）
Bukkit.getScheduler().runTaskLater(plugin, () -> {
    // 生成狗的逻辑
}, 100L); // 5秒延迟 (100 tick = 5秒)
```

### 修改后
```java
// 延迟3分钟后生成狗
Bukkit.getScheduler().runTaskLater(plugin, () -> {
    // 生成狗的逻辑
}, 3600L); // 3分钟延迟 (3600 tick = 180秒 = 3分钟)
```

### 用户体验更新
**职业选择时的提示：**
```java
ComponentUtil.sendMessage(shaman, ComponentUtil.info("动物伙伴激活！狗伙伴将在游戏开始3分钟后召唤"));
```

**生成时的确认：**
```java
ComponentUtil.sendMessage(shaman, ComponentUtil.info("狗伙伴已召唤！获得了 " + dogs.size() + " 只狗伙伴"));
plugin.getLogger().info("萨满 " + shaman.getName() + " 在游戏开始3分钟后获得了 " + dogs.size() + " 只狗伙伴");
```

## 📊 属性调整

### 生命值调整
**修改前：**
```java
// 设置80点生命值
dog.getAttribute(Attribute.MAX_HEALTH).setBaseValue(80.0);
dog.setHealth(80.0);
```

**修改后：**
```java
// 设置60点生命值
dog.getAttribute(Attribute.MAX_HEALTH).setBaseValue(60.0);
dog.setHealth(60.0);
```

### 攻击力调整
**修改前：**
```java
// 增强狗的攻击力
dog.getAttribute(Attribute.ATTACK_DAMAGE).setBaseValue(6.0);
```

**修改后：**
```java
// 设置5点攻击力
dog.getAttribute(Attribute.ATTACK_DAMAGE).setBaseValue(5.0);
```

### 职业描述更新
**修改前：**
```java
"出生获得两只狗，狗拥有80点生命值。狗死亡后会扣除萨满20%的最大生命值，每次击杀猎人额外多获得一只狗。每只狗死亡后过90s会复活"
```

**修改后：**
```java
"游戏开始3分钟后获得两只狗，狗拥有60点生命值和5点攻击力。狗死亡后会扣除萨满20%的最大生命值，每次击杀猎人额外多获得一只狗。每只狗死亡后过90s会复活"
```

## 🎮 游戏体验影响

### 萨满玩家体验

**游戏前期（0-3分钟）：**
- ❌ **无狗支援：** 萨满需要独自面对敌人
- ✅ **技能预期：** 明确知道3分钟后会获得狗伙伴
- ⚖️ **平衡考验：** 考验萨满的个人生存能力

**游戏中期（3分钟后）：**
- ✅ **狗伙伴到达：** 获得2只狗的战斗支援
- ⚖️ **适中属性：** 60血+5攻击的平衡战斗力
- 🎯 **战术转换：** 从个人战斗转为团队配合

### 敌方玩家体验

**游戏前期：**
- ✅ **压制机会：** 可以在萨满无狗支援时主动攻击
- 🎯 **时间窗口：** 3分钟的优势期需要充分利用
- ⚖️ **战术规划：** 需要考虑3分钟后的威胁变化

**游戏中期：**
- ⚖️ **威胁适中：** 狗的战斗力降低，更容易应对
- 🎯 **策略选择：** 可以选择优先击杀狗来削弱萨满

## 📈 平衡性分析

### 时机调整的平衡效果

**萨满劣势期（0-3分钟）：**
- 无狗支援，个人战斗力有限
- 容易被敌方针对和压制
- 需要更谨慎的游戏策略

**萨满优势期（3分钟后）：**
- 获得狗伙伴支援
- 战斗力显著提升
- 可以更主动地参与战斗

### 属性调整的平衡效果

**狗的战斗力对比：**
| 属性 | 修改前 | 修改后 | 变化 |
|------|--------|--------|------|
| **生命值** | 80点 | 60点 | -25% |
| **攻击力** | 6点 | 5点 | -16.7% |
| **总体战斗力** | 高 | 中等 | 降低 |

**平衡效果：**
- ✅ **更易击杀：** 60血的狗更容易被消灭
- ✅ **威胁适中：** 5点攻击力不会过于致命
- ⚖️ **仍有价值：** 保持作为战斗伙伴的意义

## 🎯 战术影响

### 萨满战术变化

**前期战术（0-3分钟）：**
- 🛡️ **防守为主：** 避免过度激进的攻击
- 🏃 **生存优先：** 确保能活到狗伙伴生成
- 🤝 **团队依赖：** 更需要队友的保护和支援

**后期战术（3分钟后）：**
- ⚔️ **攻守兼备：** 有狗支援后可以更主动
- 🎯 **目标选择：** 利用狗群优势选择合适目标
- 📈 **价值提升：** 成为团队的重要战力

### 敌方战术变化

**针对萨满的策略：**
- ⏰ **时间窗口：** 充分利用前3分钟的优势期
- 🎯 **优先目标：** 考虑在狗生成前击杀萨满
- ⚔️ **狗群应对：** 3分钟后需要应对狗群威胁

**资源分配：**
- 🕐 **前期压制：** 前3分钟重点针对萨满
- 🐕 **狗群处理：** 后期优先击杀狗来削弱萨满
- ⚖️ **威胁评估：** 重新评估萨满的威胁等级

## 🧪 测试建议

### 时机测试
1. **3分钟计时测试：**
   ```
   1. 萨满选择职业
   2. 开始计时
   3. 验证3分钟后狗是否准时生成
   4. 确认生成消息正确显示
   ```

2. **萨满下线测试：**
   ```
   1. 萨满选择职业后下线
   2. 3分钟后重新上线
   3. 验证狗生成逻辑是否正常
   ```

### 属性测试
1. **狗属性验证：**
   ```
   1. 生成狗后检查生命值是否为60
   2. 测试狗的攻击伤害是否为5点
   3. 验证属性在复活后是否保持一致
   ```

2. **战斗力测试：**
   ```
   1. 狗与其他实体的战斗测试
   2. 验证60血的生存能力
   3. 测试5点攻击的威胁程度
   ```

### 平衡性测试
1. **前期平衡测试：**
   ```
   1. 萨满在前3分钟的生存能力
   2. 敌方对萨满的压制效果
   3. 团队保护萨满的必要性
   ```

2. **后期平衡测试：**
   ```
   1. 狗生成后萨满的战斗力提升
   2. 敌方应对狗群的难度
   3. 整体游戏平衡的影响
   ```

## 📊 数值对比总结

### 调整前后对比
| 方面 | 调整前 | 调整后 | 影响 |
|------|--------|--------|------|
| **生成时机** | 立即（5秒） | 3分钟后 | 增加前期平衡 |
| **狗生命值** | 80点 | 60点 | 降低25%生存能力 |
| **狗攻击力** | 6点 | 5点 | 降低16.7%威胁 |
| **前期萨满** | 强势 | 弱势 | 平衡性提升 |
| **后期萨满** | 很强 | 适中 | 威胁合理化 |

### 平衡性评估
- ✅ **前期平衡：** 萨满不再在游戏开始就拥有强力支援
- ✅ **时间策略：** 增加了游戏的时间维度策略性
- ✅ **威胁适中：** 狗的战斗力调整到合理水平
- ✅ **反制机会：** 给敌方更多的应对和反制机会

## 🎉 调整总结

成功完成萨满狗伙伴的时机和属性调整：

### 关键改进
- ⏰ **时机延迟：** 3分钟生成增加游戏策略深度
- 📉 **属性平衡：** 60血+5攻击的合理战斗力
- 🎮 **体验优化：** 明确的时间提示和反馈
- ⚖️ **平衡提升：** 更公平的游戏环境

### 战术价值
1. **增加前期变数：** 萨满前3分钟的脆弱期
2. **时间窗口策略：** 敌方的压制机会
3. **后期价值保持：** 狗伙伴仍然有意义
4. **整体平衡：** 更合理的职业强度曲线

现在萨满的狗伙伴系统更加平衡：前期需要谨慎生存，3分钟后获得适中战斗力的狗伙伴支援！🐕⏰✨

**重要特点：**
- 3分钟延迟生成机制
- 60血+5攻击的平衡属性
- 增强的游戏策略深度
- 更公平的职业平衡
