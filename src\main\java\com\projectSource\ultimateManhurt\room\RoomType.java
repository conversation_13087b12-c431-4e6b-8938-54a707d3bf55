package com.projectSource.ultimateManhurt.room;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextColor;

/**
 * 房间类型枚举
 */
public enum RoomType {
    PUBLIC("公开房间", "任何人都可以加入，计算战绩", NamedTextColor.GREEN, "🌍", 20),
    PRIVATE("私人房间", "仅限邀请加入，不计算战绩", NamedTextColor.BLUE, "🔒", 20);
    
    private final String displayName;
    private final String description;
    private final TextColor color;
    private final String emoji;
    private final int maxPlayers;
    
    RoomType(String displayName, String description, TextColor color, String emoji, int maxPlayers) {
        this.displayName = displayName;
        this.description = description;
        this.color = color;
        this.emoji = emoji;
        this.maxPlayers = maxPlayers;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public TextColor getColor() {
        return color;
    }
    
    public String getEmoji() {
        return emoji;
    }
    
    public int getMaxPlayers() {
        return maxPlayers;
    }
    
    /**
     * 获取带颜色的显示名称组件
     */
    public Component getDisplayComponent() {
        return Component.text(emoji + " " + displayName, color);
    }
    
    /**
     * 获取详细信息组件
     */
    public Component getDetailComponent() {
        return Component.text()
                .append(getDisplayComponent())
                .append(Component.text(" - ", NamedTextColor.GRAY))
                .append(Component.text(description, NamedTextColor.WHITE))
                .append(Component.text(" (最多" + maxPlayers + "人)", NamedTextColor.GRAY))
                .build();
    }
    
    /**
     * 检查是否需要邀请才能加入
     */
    public boolean requiresInvitation() {
        return this == PRIVATE;
    }
    
    /**
     * 检查是否计算战绩（只有公开房间计算战绩）
     */
    public boolean countsStats() {
        return this == PUBLIC;
    }

    /**
     * 检查是否显示在计分板（公开和私人都显示）
     */
    public boolean showsOnScoreboard() {
        return true; // 所有房间类型都显示在计分板
    }
    
    /**
     * 获取默认游戏时长（分钟）
     */
    public int getDefaultGameDuration() {
        switch (this) {
            case PUBLIC:
                return 30; // 公开房间默认30分钟
            case PRIVATE:
                return 60; // 私人房间默认1小时
            default:
                return 30;
        }
    }
    
    /**
     * 获取最小玩家数量
     */
    public int getMinPlayers() {
        switch (this) {
            case PUBLIC:
                return 2; // 公开房间至少2人
            case PRIVATE:
                return 1; // 私人房间可以单人测试
            default:
                return 2;
        }
    }
    
    /**
     * 检查玩家数量是否有效
     */
    public boolean isValidPlayerCount(int playerCount) {
        return playerCount >= getMinPlayers() && playerCount <= getMaxPlayers();
    }
}
