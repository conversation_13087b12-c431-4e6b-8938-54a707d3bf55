# 新职业测试指南

## 测试环境准备

### 1. 启用职业系统
1. 创建或进入一个房间
2. 在房间设置中启用"职业系统"选项
3. 确保房间中有至少2名玩家（1个速通者，1个猎人）

### 2. 职业选择
1. 开始游戏后，在职业选择阶段选择新职业：
   - 速通者选择"方源 ⏰"
   - 猎人选择"暗夜领主 🌙"

## 方源（FANGYUAN）测试

### 主动技能测试 - 春秋必成

**测试步骤：**
1. 将任意剑放在副手（按F键）
2. 右键点击空气使用技能（第一次）
3. 观察是否显示"时光分身已创建！所有玩家状态已记录"
4. 移动位置、消耗物品、受到伤害等改变状态
5. 再次右键使用技能（第二次）
6. 观察是否回到记录状态

**预期结果：**
- 第一次使用：记录所有玩家状态，不进入冷却
- 第二次使用：所有玩家回到记录状态，进入300秒冷却
- 所有玩家都会收到相应提示消息

### 被动技能测试 - 春秋蝉

**测试步骤：**
1. 先使用主动技能创建时光分身
2. 移动到其他位置
3. 使用命令或受到伤害将血量降低到13%以下
4. 观察是否触发传送回时光分身位置

**预期结果：**
- 血量低于13%时有80%概率触发
- 触发时传送回时光分身位置
- 显示"春秋蝉触发！回到了时光分身的位置"
- 360秒冷却时间

**测试命令（管理员）：**
```
/effect give <玩家名> minecraft:instant_damage 1 10
```

## 暗夜领主（NIGHT_LORD）测试

### 主动技能测试 - 暗月升起

**测试步骤：**
1. 将任意剑放在副手
2. 右键点击空气使用技能
3. 观察世界是否变为夜晚
4. 检查是否获得飞行能力
5. 等待30秒观察效果是否结束

**预期结果：**
- 世界时间变为夜晚（18000）
- 暗夜领主获得飞行能力
- 持续30秒后自动结束
- 世界时间恢复为白天（1000）
- 120秒冷却时间

### 被动技能测试 - 暗夜猎影

**测试步骤：**
1. 等待或使用命令将世界时间设置为夜晚
2. 观察暗夜领主是否获得增益效果
3. 将世界时间改为白天，观察效果是否消失

**预期结果：**
- 夜晚时获得：速度2、生命恢复1、夜视
- 白天时效果消失
- 每5秒自动检查并应用效果

**测试命令（管理员）：**
```
/time set night    # 设置为夜晚
/time set day      # 设置为白天
```

## 综合测试场景

### 场景1：团队配合测试
1. 方源创建时光分身
2. 团队进行一段时间的游戏
3. 在关键时刻使用春秋必成回溯
4. 观察所有玩家是否正确回到记录状态

### 场景2：战斗测试
1. 暗夜领主使用暗月升起
2. 在夜晚环境下与速通者战斗
3. 测试飞行能力和夜晚增益的战斗优势
4. 观察方源在危险时是否触发春秋蝉

### 场景3：冷却时间测试
1. 使用技能后观察冷却时间显示
2. 在冷却期间尝试再次使用技能
3. 验证冷却时间是否准确

## 常见问题排查

### 方源问题
1. **春秋蝉不触发**
   - 检查是否已创建时光分身
   - 确认血量是否真的低于13%
   - 考虑80%概率可能未触发

2. **春秋必成状态不正确**
   - 确认是否在游戏会话中使用
   - 检查是否有其他玩家在线

### 暗夜领主问题
1. **暗月升起无效果**
   - 确认是否在游戏会话中
   - 检查世界时间是否真的改变
   - 验证飞行权限是否正确设置

2. **被动技能不生效**
   - 确认当前是否为夜晚时间
   - 检查职业是否正确设置

## 性能监控

### 监控指标
1. 服务器TPS是否稳定
2. 内存使用是否正常
3. 时光分身数据是否正确清理
4. 夜晚检测任务是否正常运行

### 日志检查
查看服务器日志中的相关信息：
- 职业设置成功消息
- 技能使用记录
- 错误或异常信息

## 平衡性评估

### 数据收集
1. 记录新职业的胜率
2. 统计技能使用频率
3. 收集玩家反馈

### 调整建议
根据测试结果，可能需要调整：
- 技能冷却时间
- 伤害数值
- 持续时间
- 触发概率

## 测试报告模板

```
测试日期：
测试人员：
测试环境：

方源测试结果：
- 春秋必成：□ 正常 □ 异常（描述：）
- 春秋蝉：□ 正常 □ 异常（描述：）

暗夜领主测试结果：
- 暗月升起：□ 正常 □ 异常（描述：）
- 暗夜猎影：□ 正常 □ 异常（描述：）

发现的问题：
1. 
2. 
3. 

建议改进：
1. 
2. 
3. 
```
