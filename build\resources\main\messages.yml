# Ultimate Manhunt 消息配置文件

# 通用消息
general:
  plugin-enabled: "<green>Ultimate Manhunt 插件已启用！"
  plugin-disabled: "<red>Ultimate Manhunt 插件已禁用！"
  no-permission: "<red>你没有权限执行此操作！"
  player-only: "<red>此命令只能由玩家执行！"
  invalid-command: "<red>无效的命令！使用 /manhunt help 查看帮助。"
  loading: "<yellow>正在加载..."
  success: "<green>操作成功！"
  error: "<red>操作失败！"

# 房间相关消息
room:
  created: "<green>房间 <aqua>{name} <green>创建成功！"
  create-failed: "<red>创建房间失败：{reason}"
  joined: "<green>你已加入房间 <aqua>{name}"
  join-failed: "<red>加入房间失败：{reason}"
  left: "<yellow>你已离开房间 <aqua>{name}"
  leave-failed: "<red>离开房间失败：{reason}"
  not-in-room: "<red>你当前不在任何房间中！"
  already-in-room: "<red>你已经在房间 <aqua>{name} <red>中了！"
  room-full: "<red>房间已满！"
  room-not-found: "<red>房间不存在！"
  room-private: "<red>这是一个私人房间，需要邀请才能加入！"
  room-password-required: "<red>此房间需要密码！"
  room-password-incorrect: "<red>房间密码错误！"
  room-disbanded: "<yellow>房间 <aqua>{name} <yellow>已解散。"
  owner-left: "<yellow>房主已离开，房间将在 <red>30秒 <yellow>后解散。"
  new-owner: "<green><aqua>{player} <green>成为了新的房主。"

# 游戏相关消息
game:
  starting: "<green>游戏即将开始！倒计时：<yellow>{seconds}秒"
  started: "<green>游戏开始！祝你好运！"
  ended: "<yellow>游戏结束！"
  paused: "<yellow>游戏已暂停。"
  resumed: "<green>游戏已恢复。"
  time-remaining: "<aqua>剩余时间：<yellow>{time}"
  speedrunner-win: "<green>速通者获胜！末影龙已被击败！"
  hunter-win: "<red>捕猎者获胜！时间已到！"
  role-assigned: "<green>你的角色：{role}"
  cannot-start: "<red>无法开始游戏：{reason}"
  not-enough-players: "<red>玩家数量不足！至少需要 {min} 人。"
  too-many-players: "<red>玩家数量过多！最多允许 {max} 人。"

# 角色相关消息
role:
  speedrunner: "<green>🏃 速通者"
  hunter: "<red>🗡 捕猎者"
  spectator: "<gray>👁 观察者"
  speedrunner-desc: "需要在时间限制内击败末影龙"
  hunter-desc: "阻止速通者完成目标"
  spectator-desc: "观看游戏进行"

# GUI相关消息
gui:
  room-settings: "房间设置"
  room-list: "房间列表"
  player-list: "玩家列表"
  game-settings: "游戏设置"
  back: "返回"
  next-page: "下一页"
  previous-page: "上一页"
  confirm: "确认"
  cancel: "取消"
  close: "关闭"

# 设置相关消息
settings:
  game-duration: "游戏时长"
  world-seed: "世界种子"
  difficulty: "游戏难度"
  max-players: "最大玩家数"
  pvp-enabled: "PVP开启"
  keep-inventory: "死亡保留物品"
  compass-tracking: "指南针追踪"
  setting-changed: "<green>设置 <aqua>{setting} <green>已更改为 <yellow>{value}"
  invalid-value: "<red>无效的设置值！"

# 邀请相关消息
invitation:
  sent: "<green>邀请已发送给 <aqua>{player}"
  received: "<yellow>你收到了来自 <aqua>{player} <yellow>的房间邀请！"
  accepted: "<green>你接受了 <aqua>{player} <green>的邀请。"
  declined: "<red>你拒绝了 <aqua>{player} <red>的邀请。"
  expired: "<yellow>来自 <aqua>{player} <yellow>的邀请已过期。"
  already-invited: "<red>你已经邀请过这个玩家了！"
  cannot-invite-self: "<red>你不能邀请自己！"
  player-offline: "<red>玩家 <aqua>{player} <red>不在线！"
  player-busy: "<red>玩家 <aqua>{player} <red>正在游戏中！"

# 世界相关消息
world:
  generating: "<yellow>正在生成游戏世界..."
  generated: "<green>游戏世界生成完成！"
  teleporting: "<yellow>正在传送到游戏世界..."
  teleported: "<green>传送完成！"
  cleanup: "<yellow>正在清理游戏世界..."

# 计分板消息
scoreboard:
  title: "<gold><bold>Ultimate Manhunt"
  room: "房间: <aqua>{name}"
  players: "玩家: <yellow>{current}/{max}"
  state: "状态: {state}"
  time: "时间: <yellow>{time}"
  role: "角色: {role}"
  speedrunners: "速通者: <green>{count}"
  hunters: "捕猎者: <red>{count}"
  spectators: "观察者: <gray>{count}"

# 错误消息
error:
  unknown: "<red>发生未知错误！"
  database: "<red>数据库错误！"
  world-generation: "<red>世界生成失败！"
  file-not-found: "<red>文件未找到！"
  permission-denied: "<red>权限不足！"
  player-not-found: "<red>玩家未找到！"
  room-limit-reached: "<red>房间数量已达上限！"
  game-limit-reached: "<red>游戏数量已达上限！"

# 帮助消息
help:
  header: "<gold>========== Ultimate Manhunt 帮助 =========="
  create: "<aqua>/manhunt create [名称] <gray>- 创建房间"
  join: "<aqua>/manhunt join <房间名> <gray>- 加入房间"
  leave: "<aqua>/manhunt leave <gray>- 离开房间"
  list: "<aqua>/manhunt list <gray>- 查看房间列表"
  invite: "<aqua>/manhunt invite <玩家> <gray>- 邀请玩家"
  settings: "<aqua>/manhunt settings <gray>- 打开设置界面"
  start: "<aqua>/manhunt start <gray>- 开始游戏"
  stop: "<aqua>/manhunt stop <gray>- 结束游戏"
  admin: "<aqua>/manhunt admin <gray>- 管理员命令"
  footer: "<gold>========================================"
