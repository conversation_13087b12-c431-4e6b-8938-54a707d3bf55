# 萨满狼伙伴系统完整实现

## 🐺 系统概览

### 功能特点
- **初始狼群：** 选择萨满职业时获得2只狼伙伴
- **狼的属性：** 每只狼80点生命值，8点攻击力
- **死亡惩罚：** 狼死亡时萨满失去20%最大生命值
- **击杀奖励：** 击杀猎人时额外获得1只狼
- **复活机制：** 狼死亡90秒后自动复活

## 🔧 技术实现

### 狼管理系统
```java
// 萨满狼伙伴管理 <萨满UUID, 狼列表>
private final Map<UUID, List<Wolf>> shamanWolves = new HashMap<>();

// 狼复活任务 <狼UUID, 复活任务>
private final Map<UUID, BukkitTask> wolfRespawnTasks = new HashMap<>();
```

### 初始狼生成
```java
/**
 * 处理萨满动物伙伴被动技能
 * 出生获得两只狼，狼拥有80点生命值
 */
public void handleShamanAnimalCompanion(Player shaman) {
    UUID shamanId = shaman.getUniqueId();
    
    // 清理可能存在的旧狼
    clearShamanWolves(shamanId);
    
    // 生成两只初始狼
    List<Wolf> wolves = new ArrayList<>();
    for (int i = 0; i < 2; i++) {
        Wolf wolf = spawnWolfForShaman(shaman);
        if (wolf != null) {
            wolves.add(wolf);
        }
    }
    
    shamanWolves.put(shamanId, wolves);
    ComponentUtil.sendMessage(shaman, ComponentUtil.info("动物伙伴激活！获得了2只狼伙伴"));
}
```

### 狼属性设置
```java
/**
 * 为萨满生成一只狼
 */
private Wolf spawnWolfForShaman(Player shaman) {
    Wolf wolf = (Wolf) spawnLoc.getWorld().spawnEntity(spawnLoc, EntityType.WOLF);
    
    // 设置狼的属性
    wolf.setOwner(shaman);
    wolf.setTamed(true);
    wolf.setAdult();
    wolf.setCustomName("§6" + shaman.getName() + "的狼伙伴");
    wolf.setCustomNameVisible(true);
    
    // 设置80点生命值
    wolf.getAttribute(Attribute.GENERIC_MAX_HEALTH).setBaseValue(80.0);
    wolf.setHealth(80.0);
    
    // 增强狼的攻击力
    wolf.getAttribute(Attribute.GENERIC_ATTACK_DAMAGE).setBaseValue(8.0);
    
    return wolf;
}
```

## 🎯 核心机制

### 1. 击杀奖励系统
```java
/**
 * 处理萨满击杀猎人，奖励额外的狼
 */
public void handleShamanKillReward(Player shaman) {
    // 生成奖励狼
    Wolf newWolf = spawnWolfForShaman(shaman);
    if (newWolf != null) {
        wolves.add(newWolf);
        ComponentUtil.sendMessage(shaman, ComponentUtil.info("击杀奖励！获得了一只新的狼伙伴"));
    }
}
```

**触发条件：**
- 萨满击杀任何猎人阵营的玩家
- 通过 `EntityDeathEvent` 监听玩家死亡
- 检查击杀者是否为萨满职业

### 2. 狼死亡惩罚
```java
/**
 * 处理狼死亡
 */
public void handleWolfDeath(Wolf wolf, Player shaman) {
    // 扣除萨满20%最大生命值
    double maxHealth = shaman.getAttribute(Attribute.GENERIC_MAX_HEALTH).getValue();
    double currentHealth = shaman.getHealth();
    double damage = maxHealth * 0.20;
    double newHealth = Math.max(1.0, currentHealth - damage);
    shaman.setHealth(newHealth);
    
    ComponentUtil.sendMessage(shaman, ComponentUtil.warning("狼伙伴死亡！你失去了20%最大生命值"));
    
    // 安排90秒后复活
    scheduleWolfRespawn(wolf, shaman);
}
```

**惩罚机制：**
- 扣除萨满当前最大生命值的20%
- 确保萨满生命值不会降到1以下
- 立即生效，无延迟

### 3. 狼复活系统
```java
/**
 * 安排狼复活
 */
private void scheduleWolfRespawn(Wolf deadWolf, Player shaman) {
    BukkitTask respawnTask = Bukkit.getScheduler().runTaskLater(plugin, () -> {
        // 检查萨满是否还在线且还是萨满职业
        if (shaman.isOnline() && 
            plugin.getProfessionManager().getPlayerProfession(shaman.getUniqueId()) == Profession.SHAMAN) {
            
            Wolf newWolf = spawnWolfForShaman(shaman);
            if (newWolf != null) {
                List<Wolf> wolves = shamanWolves.get(shaman.getUniqueId());
                if (wolves != null) {
                    wolves.add(newWolf);
                }
                ComponentUtil.sendMessage(shaman, ComponentUtil.info("狼伙伴复活了！"));
            }
        }
        
        wolfRespawnTasks.remove(wolfId);
    }, 1800L); // 90秒 = 1800 tick
    
    wolfRespawnTasks.put(wolfId, respawnTask);
}
```

**复活特点：**
- 90秒延迟复活
- 检查萨满是否仍在线和仍是萨满职业
- 在萨满附近安全位置生成新狼
- 自动清理复活任务

## 🎮 游戏体验

### 萨满玩家体验
**初始阶段：**
- 选择萨满职业后立即获得2只狼伙伴
- 狼会自动跟随和保护萨满
- 狼具有80血和8攻击力，战斗力强

**战斗阶段：**
- 狼会主动攻击萨满的敌人
- 击杀猎人可以获得额外狼伙伴
- 狼死亡会造成萨满生命值损失

**策略考虑：**
- 需要保护狼伙伴避免不必要的死亡
- 积极击杀猎人扩大狼群规模
- 管理狼群的位置和战术

### 敌方玩家体验
**面对萨满时：**
- 需要同时对付萨满和狼群
- 击杀狼可以削弱萨满的生命值
- 狼群数量会随萨满击杀而增加

**战术选择：**
- 优先击杀狼来削弱萨满
- 避免被萨满击杀（防止狼群扩大）
- 利用范围攻击对付狼群

## 📊 数值平衡

### 狼的属性
| 属性 | 数值 | 说明 |
|------|------|------|
| **生命值** | 80点 | 比普通狼(8点)高10倍 |
| **攻击力** | 8点 | 比普通狼(4点)高2倍 |
| **速度** | 默认 | 与普通狼相同 |
| **AI** | 默认 | 自动攻击萨满的敌人 |

### 惩罚与奖励
| 事件 | 效果 | 数值 |
|------|------|------|
| **狼死亡** | 萨满失去生命值 | 20%最大生命值 |
| **击杀猎人** | 获得新狼 | +1只狼 |
| **狼复活** | 延迟时间 | 90秒 |
| **初始狼数** | 职业选择时 | 2只狼 |

### 平衡考虑
**萨满优势：**
- 强力的狼群战斗支援
- 击杀奖励机制鼓励主动战斗
- 狼复活确保长期战斗力

**萨满劣势：**
- 狼死亡有显著生命值惩罚
- 需要保护狼群，增加战术负担
- 狼复活需要90秒等待时间

## 🔄 事件处理流程

### 职业选择流程
```
1. 玩家选择萨满职业
2. ProfessionManager.setProfession() 调用
3. 检测到萨满职业，调用 handleShamanAnimalCompanion()
4. 生成2只初始狼伙伴
5. 萨满获得狼群支援
```

### 击杀奖励流程
```
1. 萨满击杀猎人玩家
2. EntityDeathEvent 触发
3. 检查击杀者是萨满，被击杀者是猎人
4. 调用 handleShamanKillReward()
5. 生成1只奖励狼
```

### 狼死亡流程
```
1. 萨满的狼被击杀
2. EntityDeathEvent 触发
3. 检查死亡的是萨满的狼
4. 调用 handleWolfDeath()
5. 萨满失去20%最大生命值
6. 安排90秒后复活任务
```

## 🧪 测试建议

### 基础功能测试
1. **初始狼生成：**
   ```
   1. 选择萨满职业
   2. 验证是否生成2只狼
   3. 检查狼的属性（80血，8攻击）
   4. 确认狼会跟随萨满
   ```

2. **击杀奖励：**
   ```
   1. 萨满击杀猎人玩家
   2. 验证是否获得新狼
   3. 检查狼群数量增加
   4. 确认新狼属性正确
   ```

3. **狼死亡惩罚：**
   ```
   1. 让萨满的狼被击杀
   2. 验证萨满是否失去20%生命值
   3. 检查狼是否从列表中移除
   4. 确认复活任务已安排
   ```

4. **狼复活：**
   ```
   1. 等待90秒复活时间
   2. 验证狼是否在萨满附近复活
   3. 检查新狼属性是否正确
   4. 确认狼重新加入狼群
   ```

### 边界情况测试
1. **萨满下线：**
   ```
   1. 萨满下线时狼的处理
   2. 复活任务的清理
   3. 重新上线时的状态恢复
   ```

2. **职业切换：**
   ```
   1. 萨满切换到其他职业
   2. 验证狼是否被清理
   3. 复活任务是否被取消
   ```

## 🎉 实现总结

成功完整实现了萨满的狼伙伴系统：

- ✅ **初始狼群：** 选择职业时自动获得2只狼伙伴
- ✅ **狼属性：** 80血+8攻击力的强力战斗伙伴
- ✅ **击杀奖励：** 击杀猎人获得额外狼伙伴
- ✅ **死亡惩罚：** 狼死亡扣除萨满20%最大生命值
- ✅ **复活机制：** 90秒后自动复活狼伙伴
- ✅ **智能生成：** 安全位置生成，避免卡墙等问题
- ✅ **资源管理：** 完善的清理和任务管理

### 关键特点
1. **完整生命周期：** 从生成到死亡到复活的完整管理
2. **平衡设计：** 强力支援但有合理代价
3. **智能系统：** 自动处理各种边界情况
4. **性能优化：** 高效的狼群管理和任务调度

现在萨满真正拥有了强力的狼伙伴支援，成为了独特的召唤师职业！🐺✨

**重要提醒：** 建议在游戏中测试狼的生成位置、战斗AI和各种边界情况，确保系统稳定运行。
