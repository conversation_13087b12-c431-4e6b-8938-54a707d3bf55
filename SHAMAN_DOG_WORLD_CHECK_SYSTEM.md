# 萨满狗伙伴世界检查系统

## 🐕 问题背景

### 原始问题
萨满的狗会在世界创建之前生成，导致狗出现在错误的世界中，无法跟随萨满到游戏世界。

### 问题场景
1. **职业选择阶段：** 萨满选择职业时立即生成狗
2. **世界创建延迟：** 游戏世界可能还未创建或萨满还未传送
3. **世界不匹配：** 狗在大厅世界，萨满在游戏世界
4. **无法跟随：** 狗无法跨世界跟随萨满

## 🔧 解决方案

### 1. 延迟生成机制
```java
/**
 * 处理萨满动物伙伴被动技能
 * 延迟生成狗，确保在正确世界中召唤
 */
public void handleShamanAnimalCompanion(Player shaman) {
    // 初始化狗列表（先不生成狗）
    List<Wolf> dogs = new ArrayList<>();
    shamanDogs.put(shamanId, dogs);
    
    ComponentUtil.sendMessage(shaman, ComponentUtil.info("动物伙伴激活！狗伙伴将在游戏开始后召唤"));
    
    // 延迟5秒后尝试生成狗
    Bukkit.getScheduler().runTaskLater(plugin, () -> {
        if (shaman.isOnline() && 
            plugin.getProfessionManager().getPlayerProfession(shamanId) == Profession.SHAMAN) {
            
            // 在萨满当前世界生成狗
            for (int i = 0; i < 2; i++) {
                Wolf dog = spawnDogForShaman(shaman);
                if (dog != null) {
                    dogs.add(dog);
                }
            }
        }
    }, 100L); // 5秒延迟
}
```

### 2. 定时世界检查系统
```java
/**
 * 启动狗世界检查任务
 * 每10秒检查一次所有萨满的狗是否在正确的世界中
 */
private void startDogWorldCheckTask() {
    dogWorldCheckTask = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
        // 遍历所有萨满的狗
        for (Map.Entry<UUID, List<Wolf>> entry : shamanDogs.entrySet()) {
            UUID shamanId = entry.getKey();
            List<Wolf> dogs = entry.getValue();
            
            Player shaman = Bukkit.getPlayer(shamanId);
            if (shaman == null || !shaman.isOnline()) {
                continue; // 萨满不在线，跳过
            }
            
            // 检查每只狗是否在正确的世界
            checkAndFixDogWorlds(shaman, dogs);
        }
    }, 200L, 200L); // 10秒后开始，每10秒执行一次
}
```

### 3. 智能狗修复机制
```java
// 检查狗是否在萨满的世界中
if (!dog.getWorld().equals(shaman.getWorld())) {
    plugin.getLogger().info("检测到萨满 " + shaman.getName() + " 的狗在错误世界，准备重新召唤");
    dogsToRemove.add(dog);
    dogsToRespawn++;
    
    // 移除错误世界的狗
    dog.remove();
}

// 重新召唤在错误世界的狗
for (int i = 0; i < dogsToRespawn; i++) {
    Wolf newDog = spawnDogForShaman(shaman);
    if (newDog != null) {
        dogs.add(newDog);
        plugin.getLogger().info("为萨满 " + shaman.getName() + " 重新召唤了狗伙伴");
    }
}
```

## 🎯 系统特点

### 1. 延迟生成
- **初始延迟：** 5秒延迟确保世界创建完成
- **状态保持：** 萨满技能状态立即激活
- **用户反馈：** 明确告知狗将在游戏开始后召唤

### 2. 定时检查
- **检查频率：** 每10秒检查一次
- **全面覆盖：** 检查所有在线萨满的所有狗
- **智能过滤：** 只处理在线且仍为萨满职业的玩家

### 3. 自动修复
- **世界检测：** 检查狗是否在萨满的世界中
- **自动移除：** 移除错误世界的狗
- **重新召唤：** 在正确世界重新生成狗
- **数量保证：** 确保萨满至少有2只狗

### 4. 状态管理
- **无效狗清理：** 自动移除死亡或无效的狗
- **数量补充：** 自动补充丢失的狗到至少2只
- **日志记录：** 详细记录所有操作

## 📊 检查逻辑流程

### 世界检查流程
```
1. 遍历所有萨满
   ↓
2. 检查萨满是否在线且仍为萨满职业
   ↓
3. 遍历萨满的所有狗
   ↓
4. 检查狗是否有效（未死亡、有效实体）
   ↓
5. 检查狗是否在萨满的世界中
   ↓
6. 如果不在正确世界：
   - 移除错误世界的狗
   - 在正确世界重新召唤
   ↓
7. 检查狗数量是否足够（至少2只）
   ↓
8. 如果不足，补充到2只
```

### 狗生成安全检查
```java
private Wolf spawnDogForShaman(Player shaman) {
    // 确保在萨满当前世界生成
    Location spawnLoc = findSafeDogSpawnLocation(shaman.getLocation());
    if (spawnLoc == null) {
        return null; // 无法找到安全位置
    }
    
    // 在萨满的世界中生成狗
    Wolf dog = (Wolf) spawnLoc.getWorld().spawnEntity(spawnLoc, EntityType.WOLF);
    
    // 设置狗属性...
    return dog;
}
```

## 🛡️ 边界情况处理

### 1. 萨满下线
- **检查跳过：** 萨满不在线时跳过检查
- **状态保持：** 狗的状态保持不变
- **重新上线：** 萨满重新上线时继续检查

### 2. 职业切换
- **检查跳过：** 不再是萨满职业时跳过检查
- **狗清理：** 职业切换时会清理所有狗

### 3. 世界传送
- **自动检测：** 萨满传送到新世界时自动检测
- **狗重新召唤：** 在新世界重新召唤狗
- **无缝体验：** 玩家感受不到中断

### 4. 狗死亡
- **自动清理：** 死亡的狗会被自动移除
- **复活机制：** 90秒后通过复活机制重新生成
- **数量保证：** 检查系统确保最少2只狗

## ⚡ 性能优化

### 1. 检查频率
- **10秒间隔：** 平衡及时性和性能
- **按需检查：** 只检查在线的萨满
- **智能过滤：** 跳过不需要检查的情况

### 2. 资源管理
- **任务清理：** 插件关闭时自动清理任务
- **内存优化：** 及时移除无效的狗引用
- **日志控制：** 适度的日志记录

### 3. 批量操作
- **批量检查：** 一次任务检查所有萨满
- **批量修复：** 一次性处理所有需要修复的狗
- **减少调度：** 最小化任务调度开销

## 🎮 用户体验

### 萨满玩家体验
**职业选择时：**
- ✅ **即时反馈：** 立即显示"动物伙伴激活"
- ✅ **明确说明：** 告知狗将在游戏开始后召唤
- ✅ **无感修复：** 狗的世界问题自动修复

**游戏过程中：**
- ✅ **狗始终跟随：** 狗始终在萨满的世界中
- ✅ **数量保证：** 始终至少有2只狗
- ✅ **无缝体验：** 世界传送时狗自动跟随

### 服务器管理员
**监控能力：**
- ✅ **详细日志：** 记录所有狗的召唤和修复操作
- ✅ **问题诊断：** 清楚显示何时发生世界不匹配
- ✅ **性能友好：** 合理的检查频率

## 🧪 测试建议

### 基础功能测试
1. **延迟生成测试：**
   ```
   1. 选择萨满职业
   2. 验证是否显示"将在游戏开始后召唤"
   3. 等待5秒，检查狗是否在正确世界生成
   ```

2. **世界传送测试：**
   ```
   1. 萨满在游戏世界中
   2. 传送萨满到其他世界
   3. 等待10秒，检查狗是否重新召唤
   ```

3. **世界不匹配修复测试：**
   ```
   1. 手动将狗传送到错误世界
   2. 等待10秒检查周期
   3. 验证狗是否被重新召唤到正确世界
   ```

### 边界情况测试
1. **萨满下线重新上线：**
   ```
   1. 萨满下线
   2. 重新上线
   3. 检查狗状态是否正常
   ```

2. **职业切换：**
   ```
   1. 萨满切换到其他职业
   2. 检查狗是否被清理
   3. 检查任务是否停止处理该玩家
   ```

## 🎉 系统总结

成功实现了萨满狗伙伴的世界检查和修复系统：

- ✅ **延迟生成：** 5秒延迟确保在正确世界生成
- ✅ **定时检查：** 每10秒检查狗的世界位置
- ✅ **自动修复：** 自动移除错误世界的狗并重新召唤
- ✅ **数量保证：** 确保萨满始终至少有2只狗
- ✅ **性能优化：** 合理的检查频率和资源管理
- ✅ **用户友好：** 无感的自动修复体验

### 关键优势
1. **问题预防：** 延迟生成避免世界不匹配
2. **自动修复：** 定时检查确保狗始终在正确世界
3. **稳定性：** 处理各种边界情况
4. **性能友好：** 优化的检查频率和逻辑

现在萨满的狗伙伴系统能够完美处理世界创建时序问题，确保狗始终在萨满身边！🐕🌍✨
