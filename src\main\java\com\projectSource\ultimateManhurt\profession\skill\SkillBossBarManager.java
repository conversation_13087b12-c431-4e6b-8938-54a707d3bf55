package com.projectSource.ultimateManhurt.profession.skill;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.profession.Profession;
import org.bukkit.Bukkit;
import org.bukkit.boss.BarColor;
import org.bukkit.boss.BarStyle;
import org.bukkit.boss.BossBar;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.Map;
import java.util.Set;
import java.util.HashSet;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 技能冷却Boss Bar管理器
 * 使用Boss Bar显示玩家的技能冷却状态
 */
public class SkillBossBarManager {
    
    private final UltimateManhurt plugin;
    private final Map<UUID, BossBar> activeSkillBossBars = new ConcurrentHashMap<>();
    private final Map<UUID, BossBar> passiveSkillBossBars = new ConcurrentHashMap<>();
    private BukkitTask updateTask;
    
    public SkillBossBarManager(UltimateManhurt plugin) {
        this.plugin = plugin;
        startUpdateTask();
    }
    
    /**
     * 启动更新任务
     */
    private void startUpdateTask() {
        updateTask = new BukkitRunnable() {
            @Override
            public void run() {
                updateAllBossBars();
            }
        }.runTaskTimer(plugin, 0L, 20L); // 每秒更新一次
    }
    
    /**
     * 为玩家创建技能Boss Bar
     */
    public void createSkillBossBar(Player player) {
        UUID playerId = player.getUniqueId();

        // 移除现有的Boss Bar
        removeSkillBossBar(player);

        // 检查玩家是否有职业
        Profession profession = plugin.getProfessionManager().getPlayerProfession(playerId);
        if (profession == null) {
            return; // 没有职业就不显示Boss Bar
        }

        // 立即更新一次（会根据需要创建Boss Bar）
        updatePlayerBossBars(player);
    }
    
    /**
     * 移除玩家的技能Boss Bar
     */
    public void removeSkillBossBar(Player player) {
        UUID playerId = player.getUniqueId();

        // 移除主动技能Boss Bar
        BossBar activeBossBar = activeSkillBossBars.remove(playerId);
        if (activeBossBar != null) {
            activeBossBar.removeAll();
        }

        // 移除被动技能Boss Bar
        BossBar passiveBossBar = passiveSkillBossBars.remove(playerId);
        if (passiveBossBar != null) {
            passiveBossBar.removeAll();
        }
    }
    
    /**
     * 更新所有Boss Bar
     */
    private void updateAllBossBars() {
        // 获取所有有Boss Bar的玩家
        Set<UUID> allPlayerIds = new HashSet<>();
        allPlayerIds.addAll(activeSkillBossBars.keySet());
        allPlayerIds.addAll(passiveSkillBossBars.keySet());

        for (UUID playerId : allPlayerIds) {
            Player player = Bukkit.getPlayer(playerId);
            if (player == null || !player.isOnline()) {
                // 玩家离线，移除Boss Bar
                removeSkillBossBar(player != null ? player : Bukkit.getOfflinePlayer(playerId).getPlayer());
                continue;
            }

            updatePlayerBossBars(player);
        }
    }
    
    /**
     * 更新单个玩家的Boss Bar
     */
    private void updatePlayerBossBars(Player player) {
        UUID playerId = player.getUniqueId();
        Profession profession = plugin.getProfessionManager().getPlayerProfession(playerId);

        if (profession == null) {
            // 没有职业，移除所有Boss Bar
            removeSkillBossBar(player);
            return;
        }

        SkillCooldown skillCooldown = plugin.getProfessionManager().getSkillCooldown();

        // 处理主动技能Boss Bar
        updateActiveSkillBossBar(player, profession, skillCooldown);

        // 处理被动技能Boss Bar
        updatePassiveSkillBossBar(player, profession, skillCooldown);
    }

    /**
     * 更新主动技能Boss Bar
     */
    private void updateActiveSkillBossBar(Player player, Profession profession, SkillCooldown skillCooldown) {
        UUID playerId = player.getUniqueId();
        String activeSkillName = profession.getActiveSkillName();

        if (activeSkillName == null || activeSkillName.isEmpty()) {
            // 没有主动技能，移除Boss Bar
            BossBar activeBossBar = activeSkillBossBars.remove(playerId);
            if (activeBossBar != null) {
                activeBossBar.removeAll();
            }
            return;
        }

        int activeCooldown = skillCooldown.getRemainingCooldown(playerId, activeSkillName);

        if (activeCooldown <= 0) {
            // 冷却结束，移除Boss Bar
            BossBar activeBossBar = activeSkillBossBars.remove(playerId);
            if (activeBossBar != null) {
                activeBossBar.removeAll();
            }
            return;
        }

        // 需要显示冷却，创建或更新Boss Bar
        BossBar activeBossBar = activeSkillBossBars.get(playerId);
        if (activeBossBar == null) {
            activeBossBar = Bukkit.createBossBar(
                "主动技能冷却",
                BarColor.BLUE,
                BarStyle.SEGMENTED_10
            );
            activeBossBar.addPlayer(player);
            activeSkillBossBars.put(playerId, activeBossBar);
        }

        // 更新Boss Bar内容
        String title = "§b主动: §c" + activeSkillName + " (" + activeCooldown + "s)";
        activeBossBar.setTitle(title);

        // 使用职业的实际主动技能冷却时间来计算进度
        int maxActiveCooldown = profession.getActiveSkillCooldown();
        double progress = Math.max(0.0, 1.0 - ((double) activeCooldown / maxActiveCooldown));
        activeBossBar.setProgress(Math.max(0.01, Math.min(1.0, progress))); // 至少显示一点进度
        activeBossBar.setColor(BarColor.RED);
        activeBossBar.setVisible(true);
    }

    /**
     * 更新被动技能Boss Bar
     */
    private void updatePassiveSkillBossBar(Player player, Profession profession, SkillCooldown skillCooldown) {
        UUID playerId = player.getUniqueId();
        String passiveSkillName = profession.getPassiveSkillName();

        if (passiveSkillName == null || passiveSkillName.isEmpty()) {
            // 没有被动技能，移除Boss Bar
            BossBar passiveBossBar = passiveSkillBossBars.remove(playerId);
            if (passiveBossBar != null) {
                passiveBossBar.removeAll();
            }
            return;
        }

        int passiveCooldown = skillCooldown.getRemainingCooldown(playerId, passiveSkillName);

        if (passiveCooldown <= 0) {
            // 冷却结束，移除Boss Bar
            BossBar passiveBossBar = passiveSkillBossBars.remove(playerId);
            if (passiveBossBar != null) {
                passiveBossBar.removeAll();
            }
            return;
        }

        // 获取职业的被动技能最大冷却时间
        int maxPassiveCooldown = profession.getPassiveSkillCooldown();

        // 如果职业配置的被动技能没有冷却时间，但当前有剩余冷却（可能是动态设置的），
        // 我们需要使用一个合理的默认值来计算进度
        if (maxPassiveCooldown <= 0) {
            // 对于没有配置冷却时间的被动技能，如果当前有冷却，使用当前冷却时间作为最大值
            // 这样可以正确显示进度条
            maxPassiveCooldown = passiveCooldown;
        }

        // 需要显示冷却，创建或更新Boss Bar
        BossBar passiveBossBar = passiveSkillBossBars.get(playerId);
        if (passiveBossBar == null) {
            passiveBossBar = Bukkit.createBossBar(
                "被动技能冷却",
                BarColor.YELLOW,
                BarStyle.SEGMENTED_10
            );
            passiveBossBar.addPlayer(player);
            passiveSkillBossBars.put(playerId, passiveBossBar);
        }

        // 更新Boss Bar内容
        String title = "§e被动: §c" + passiveSkillName + " (" + passiveCooldown + "s)";
        passiveBossBar.setTitle(title);

        // 使用实际的冷却时间来计算进度
        double progress = Math.max(0.0, 1.0 - ((double) passiveCooldown / maxPassiveCooldown));
        passiveBossBar.setProgress(Math.max(0.01, Math.min(1.0, progress))); // 至少显示一点进度
        passiveBossBar.setColor(BarColor.YELLOW);
        passiveBossBar.setVisible(true);
    }
    
    /**
     * 检查玩家是否应该显示技能Boss Bar
     */
    public boolean shouldShowSkillBossBar(Player player) {
        UUID playerId = player.getUniqueId();
        Profession profession = plugin.getProfessionManager().getPlayerProfession(playerId);

        if (profession == null) {
            return false;
        }

        SkillCooldown skillCooldown = plugin.getProfessionManager().getSkillCooldown();

        // 检查主动技能是否在冷却中
        String activeSkillName = profession.getActiveSkillName();
        boolean hasActiveCooldown = activeSkillName != null && !activeSkillName.isEmpty() &&
                                   skillCooldown.getRemainingCooldown(playerId, activeSkillName) > 0;

        // 检查被动技能是否在冷却中（不依赖Profession配置）
        String passiveSkillName = profession.getPassiveSkillName();
        boolean hasPassiveCooldown = passiveSkillName != null && !passiveSkillName.isEmpty() &&
                                    skillCooldown.getRemainingCooldown(playerId, passiveSkillName) > 0;

        return hasActiveCooldown || hasPassiveCooldown;
    }
    
    /**
     * 关闭管理器
     */
    public void shutdown() {
        if (updateTask != null) {
            updateTask.cancel();
            updateTask = null;
        }
        
        // 清理所有Boss Bar
        for (BossBar bossBar : activeSkillBossBars.values()) {
            bossBar.removeAll();
        }
        activeSkillBossBars.clear();

        for (BossBar bossBar : passiveSkillBossBars.values()) {
            bossBar.removeAll();
        }
        passiveSkillBossBars.clear();
    }
    
    /**
     * 玩家离开游戏时清理
     */
    public void onPlayerQuit(Player player) {
        removeSkillBossBar(player);
    }
    
    /**
     * 玩家加入游戏时创建
     */
    public void onPlayerJoin(Player player) {
        if (shouldShowSkillBossBar(player)) {
            createSkillBossBar(player);
        }
    }
    
    /**
     * 玩家职业改变时更新
     */
    public void onProfessionChange(Player player) {
        if (shouldShowSkillBossBar(player)) {
            createSkillBossBar(player);
        } else {
            removeSkillBossBar(player);
        }
    }

    /**
     * 强制为玩家创建Boss Bar（用于测试）
     */
    public void forceCreateBossBar(Player player) {
        UUID playerId = player.getUniqueId();

        // 创建测试主动技能Boss Bar
        BossBar testActiveBossBar = Bukkit.createBossBar(
            "§b测试主动技能 (30s)",
            BarColor.BLUE,
            BarStyle.SEGMENTED_10
        );
        testActiveBossBar.addPlayer(player);
        testActiveBossBar.setProgress(0.5);
        testActiveBossBar.setVisible(true);
        activeSkillBossBars.put(playerId, testActiveBossBar);

        // 创建测试被动技能Boss Bar
        BossBar testPassiveBossBar = Bukkit.createBossBar(
            "§e测试被动技能 (15s)",
            BarColor.YELLOW,
            BarStyle.SEGMENTED_10
        );
        testPassiveBossBar.addPlayer(player);
        testPassiveBossBar.setProgress(0.3);
        testPassiveBossBar.setVisible(true);
        passiveSkillBossBars.put(playerId, testPassiveBossBar);
    }
}
