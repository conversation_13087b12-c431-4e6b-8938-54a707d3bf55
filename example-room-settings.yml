# 示例房间设置配置文件
# 文件名: 专业竞技房间_20250130_120000.yml
# 这是一个完整的房间设置配置文件示例，展示自动生成的文件名格式

metadata:
  name: "专业竞技房间_20250130_120000"
  creator: "550e8400-e29b-41d4-a716-446655440000"
  created-at: "2025-01-30T12:00:00"
  version: "1.0"

# 基础设置
basic:
  game-duration-minutes: 45
  world-seed: 12345
  difficulty: "HARD"
  spectator-game-mode: "SPECTATOR"

# 玩家设置
players:
  max-players: 16
  max-speedrunners: 2
  max-hunters: 14
  allow-spectators: true

# 游戏规则
game-rules:
  pvp-enabled: true
  friendly-fire: false
  keep-inventory: false
  natural-regeneration: true
  show-death-messages: true

# 世界设置
world:
  generate-structures: true
  generate-bonus-chest: false
  enable-command-blocks: false
  do-daylight-cycle: true
  do-weather-cycle: true
  custom-spawn-logic: false

# 特殊功能
special:
  compass-tracking: true
  compass-update-interval: 3
  locator-bar: true
  ender-pearl-cooldown: true
  ender-pearl-cooldown-seconds: 10
  nether-portal-delay: true
  nether-portal-delay-seconds: 2

# Ban Pick系统
ban-pick:
  enabled: true
  phase-time-seconds: 45

# 职业系统
profession:
  enabled: true

# 豁免设置
immunity:
  enabled: true
  duration-seconds: 30

# 胜利条件
victory:
  dragon-respawn: false
  timeout-hunters-win: true
  speedrunner-lives: 1
  hunter-lives: 0
  allow-speedrunner-respawn: false
  allow-hunter-respawn: true

# 血量设置
health:
  speedrunner-max-health: 16.0
  hunter-max-health: 20.0

# 胜利模式设置
victory-mode:
  mode: "DRAGON_MODE"
  target-score: 1000

# 守卫模式设置
guard-mode:
  wither-max-health: 400.0
  wither-healing-reduction: 0.3
  wither-attack-interval: 3
  wither-attack-damage: 15.0
  wither-shield-duration: 180
  wither-shield-reduction: 0.8
  hunter-spawn-distance: 150
  wither-can-move: false
  wither-target-hunters-only: true
  wither-destroy-blocks: false
  wither-effect-duration: 40
  wither-effect-level: 1

# StartKit设置
start-kit:
  enabled: true
  speedrunner:
    name: "专业速通者装备"
    description: "适合专业比赛的速通者装备"
    template-id: "pro_speedrunner"
    items:
      "0":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: DIAMOND_SWORD
        amount: 1
      "1":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: DIAMOND_PICKAXE
        amount: 1
      "2":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: DIAMOND_AXE
        amount: 1
      "3":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: GOLDEN_APPLE
        amount: 8
      "4":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: COOKED_BEEF
        amount: 32
      "5":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: COBBLESTONE
        amount: 64
      "6":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: OAK_PLANKS
        amount: 64
      "7":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: ENDER_PEARL
        amount: 4
    extra-items:
      - ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: IRON_INGOT
        amount: 32
      - ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: DIAMOND
        amount: 16
      - ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: BLAZE_POWDER
        amount: 12
  hunter:
    name: "专业捕猎者装备"
    description: "适合专业比赛的捕猎者装备"
    template-id: "pro_hunter"
    items:
      "0":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: DIAMOND_SWORD
        amount: 1
      "1":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: BOW
        amount: 1
      "2":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: ARROW
        amount: 1
      "3":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: DIAMOND_PICKAXE
        amount: 1
      "4":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: DIAMOND_AXE
        amount: 1
      "5":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: GOLDEN_APPLE
        amount: 12
      "6":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: COOKED_BEEF
        amount: 64
      "7":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: COMPASS
        amount: 1
      "8":
        ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: CLOCK
        amount: 1
    extra-items:
      - ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: DIAMOND
        amount: 32
      - ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: IRON_INGOT
        amount: 64
      - ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: ENDER_PEARL
        amount: 8
      - ==: org.bukkit.inventory.ItemStack
        v: 3465
        type: BLAZE_ROD
        amount: 6

# 里程碑设置
milestone:
  enabled: true
  settings-summary: "专业竞技里程碑配置"

# 自定义规则
custom-rules:
  tournament-mode: true
  strict-rules: true
  anti-cheat-level: "high"
