package com.projectSource.ultimateManhurt.room;

import java.util.UUID;

/**
 * 房间邀请数据类
 */
public class RoomInvite {
    
    private final UUID inviterUUID;      // 邀请者UUID
    private final UUID targetUUID;       // 被邀请者UUID
    private final String roomId;         // 房间ID
    private final String roomName;       // 房间名称
    private final long createTime;       // 创建时间
    
    private static final long EXPIRE_TIME = 60000; // 60秒过期
    
    public RoomInvite(UUID inviterUUID, UUID targetUUID, String roomId, String roomName, long createTime) {
        this.inviterUUID = inviterUUID;
        this.targetUUID = targetUUID;
        this.roomId = roomId;
        this.roomName = roomName;
        this.createTime = createTime;
    }
    
    /**
     * 获取邀请者UUID
     */
    public UUID getInviterUUID() {
        return inviterUUID;
    }
    
    /**
     * 获取被邀请者UUID
     */
    public UUID getTargetUUID() {
        return targetUUID;
    }
    
    /**
     * 获取房间ID
     */
    public String getRoomId() {
        return roomId;
    }
    
    /**
     * 获取房间名称
     */
    public String getRoomName() {
        return roomName;
    }
    
    /**
     * 获取创建时间
     */
    public long getCreateTime() {
        return createTime;
    }
    
    /**
     * 检查邀请是否过期
     */
    public boolean isExpired() {
        return System.currentTimeMillis() - createTime > EXPIRE_TIME;
    }
    
    /**
     * 获取剩余时间（毫秒）
     */
    public long getRemainingTime() {
        long elapsed = System.currentTimeMillis() - createTime;
        return Math.max(0, EXPIRE_TIME - elapsed);
    }
    
    /**
     * 获取剩余时间（秒）
     */
    public int getRemainingSeconds() {
        return (int) (getRemainingTime() / 1000);
    }
    
    @Override
    public String toString() {
        return "RoomInvite{" +
                "inviterUUID=" + inviterUUID +
                ", targetUUID=" + targetUUID +
                ", roomId=" + roomId +
                ", roomName='" + roomName + '\'' +
                ", createTime=" + createTime +
                ", expired=" + isExpired() +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        RoomInvite that = (RoomInvite) obj;
        return inviterUUID.equals(that.inviterUUID) &&
               targetUUID.equals(that.targetUUID) &&
               roomId.equals(that.roomId);
    }
    
    @Override
    public int hashCode() {
        return inviterUUID.hashCode() + targetUUID.hashCode() + roomId.hashCode();
    }
}
