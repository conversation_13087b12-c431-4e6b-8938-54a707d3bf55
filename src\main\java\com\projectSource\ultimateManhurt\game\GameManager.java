package com.projectSource.ultimateManhurt.game;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.world.GameWorld;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 游戏管理器
 * 负责游戏逻辑的控制和管理
 */
public class GameManager {

    private final UltimateManhurt plugin;
    private final Map<String, GameSession> activeSessions = new ConcurrentHashMap<>();
    private final Map<UUID, String> playerToSession = new ConcurrentHashMap<>();

    public GameManager(UltimateManhurt plugin) {
        this.plugin = plugin;
        initialize();
    }

    /**
     * 初始化游戏管理器
     */
    private void initialize() {
        plugin.getLogger().info("游戏管理器已初始化");
    }

    /**
     * 创建游戏会话
     */
    public CompletableFuture<GameSession> createGameSession(Room room) {
        String sessionId = generateSessionId();

        // 先创建GameSession（不需要世界）
        GameSession session = new GameSession(plugin, sessionId, room);

        activeSessions.put(sessionId, session);

        // 记录玩家到会话的映射
        for (UUID playerId : room.getPlayers()) {
            playerToSession.put(playerId, sessionId);
        }

        plugin.getLogger().info("创建游戏会话: " + sessionId);

        // 返回已完成的Future
        return CompletableFuture.completedFuture(session);
    }

    /**
     * 为GameSession创建游戏世界（Ban Pick完成后调用）
     */
    public CompletableFuture<Void> createGameWorldForSession(GameSession session) {
        return plugin.getWorldManager().createGameWorld(session.getRoom())
                .thenAccept(gameWorld -> {
                    // 设置游戏世界
                    session.setGameWorld(gameWorld);
                    plugin.getLogger().info("为游戏会话 " + session.getSessionId() + " 创建游戏世界完成");
                });
    }

    /**
     * 开始游戏
     */
    public boolean startGame(String sessionId) {
        GameSession session = activeSessions.get(sessionId);
        if (session == null) {
            return false;
        }

        return session.startGame();
    }

    /**
     * 暂停游戏
     */
    public boolean pauseGame(String sessionId) {
        GameSession session = activeSessions.get(sessionId);
        if (session == null) {
            return false;
        }

        return session.pauseGame();
    }

    /**
     * 恢复游戏
     */
    public boolean resumeGame(String sessionId) {
        GameSession session = activeSessions.get(sessionId);
        if (session == null) {
            return false;
        }

        return session.resumeGame();
    }

    /**
     * 结束游戏
     */
    public void endGame(String sessionId, com.projectSource.ultimateManhurt.game.rules.WinCondition winCondition) {
        GameSession session = activeSessions.get(sessionId);
        if (session == null) {
            return;
        }

        session.endGame(winCondition);
    }

    /**
     * 移除游戏会话
     */
    public void removeGameSession(String sessionId) {
        GameSession session = activeSessions.remove(sessionId);
        if (session == null) {
            return;
        }

        // 清理GG指令的投降记录
        com.projectSource.ultimateManhurt.command.GGCommand ggCommand =
            (com.projectSource.ultimateManhurt.command.GGCommand) plugin.getCommand("gg").getExecutor();
        if (ggCommand != null) {
            ggCommand.cleanupGameSession(sessionId);
        }

        // 清理玩家映射
        for (UUID playerId : session.getPlayerRoles().keySet()) {
            playerToSession.remove(playerId);
        }

        // 清理游戏世界
        plugin.getWorldManager().deleteGameWorld(session.getRoom().getId());

        plugin.getLogger().info("移除游戏会话: " + sessionId);
    }

    /**
     * 获取游戏会话
     */
    public GameSession getGameSession(String sessionId) {
        return activeSessions.get(sessionId);
    }

    /**
     * 根据玩家获取游戏会话
     */
    public GameSession getGameSessionByPlayer(UUID playerId) {
        String sessionId = playerToSession.get(playerId);
        if (sessionId == null) {
            return null;
        }
        return activeSessions.get(sessionId);
    }

    /**
     * 根据房间ID获取游戏会话
     */
    public GameSession getGameSessionByRoom(String roomId) {
        for (GameSession session : activeSessions.values()) {
            if (session.getRoom().getId().equals(roomId)) {
                return session;
            }
        }
        return null;
    }

    /**
     * 检查玩家是否在游戏中
     */
    public boolean isPlayerInGame(UUID playerId) {
        return playerToSession.containsKey(playerId);
    }

    /**
     * 获取活跃会话数量
     */
    public int getActiveSessionCount() {
        return activeSessions.size();
    }

    /**
     * 生成会话ID
     */
    private String generateSessionId() {
        return "game_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }

    /**
     * 关闭游戏管理器
     */
    public void shutdown() {
        // 结束所有游戏
        for (GameSession session : activeSessions.values()) {
            session.endGame(com.projectSource.ultimateManhurt.game.rules.WinCondition.ADMIN_END);
        }

        activeSessions.clear();
        playerToSession.clear();

        plugin.getLogger().info("游戏管理器已关闭");
    }
}
