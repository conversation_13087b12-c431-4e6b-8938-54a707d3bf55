package com.projectSource.ultimateManhurt.game.guard;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.game.rules.WinCondition;
import com.projectSource.ultimateManhurt.room.RoomSettings;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.*;
import org.bukkit.attribute.Attribute;
import org.bukkit.entity.*;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * 守卫模式管理器
 * 管理守卫模式的核心逻辑：凋零生成、属性设置、攻击逻辑、减伤护盾等
 */
public class GuardModeManager {
    
    private final UltimateManhurt plugin;
    private final GameSession gameSession;
    private final RoomSettings settings;
    
    // 凋零实体和相关状态
    private Wither guardWither;
    private Location towerLocation;
    private boolean shieldActive = false;
    private long shieldStartTime;
    private long shieldDurationSeconds;
    private BukkitTask attackTask;
    private BukkitTask shieldTask;
    
    // 减疗效果追踪
    private final Map<UUID, Long> healingReductionPlayers = new ConcurrentHashMap<>();

    // 自定义Boss血条
    private org.bukkit.boss.BossBar customBossBar;
    
    public GuardModeManager(UltimateManhurt plugin, GameSession gameSession) {
        this.plugin = plugin;
        this.gameSession = gameSession;
        this.settings = gameSession.getRoom().getSettings();
    }
    
    /**
     * 初始化守卫模式
     */
    public void initialize() {
        // 获取塔的位置（塔已在世界配置时生成）
        World world = gameSession.getGameWorld().getOverworld();
        this.towerLocation = TowerGenerator.findTowerLocation(world);

        // 生成凋零
        spawnGuardWither();

        // 启动减伤护盾
        if (settings.getWitherShieldDuration() > 0) {
            startShield();
        }

        // 启动攻击任务
        startAttackTask();

        plugin.getLogger().info("守卫模式已初始化，凋零生成在: " + towerLocation);
    }
    

    
    /**
     * 生成守卫凋零
     */
    private void spawnGuardWither() {
        World world = towerLocation.getWorld();
        Location spawnLocation = towerLocation.clone().add(0, 2, 0); // 在塔上方生成

        // 生成凋零
        this.guardWither = (Wither) world.spawnEntity(spawnLocation, EntityType.WITHER);

        // 立即设置基本属性
        guardWither.customName(ComponentUtil.parse("<dark_red><bold>守卫凋零</bold>"));
        guardWither.setCustomNameVisible(true);
        guardWither.setRemoveWhenFarAway(false);
        guardWither.setPersistent(true);

        // 隐藏凋零的原生Boss血条
        try {
            // 尝试隐藏凋零的原生Boss血条
            org.bukkit.boss.BossBar witherBossBar = guardWither.getBossBar();
            if (witherBossBar != null) {
                witherBossBar.removeAll(); // 移除所有玩家
                witherBossBar.setVisible(false); // 设置为不可见
            }
        } catch (Exception e) {
            plugin.getLogger().info("无法隐藏凋零原生Boss血条: " + e.getMessage());
        }

        // 延迟设置属性，等待凋零完全初始化（凋零有3秒无敌期）
        new BukkitRunnable() {
            @Override
            public void run() {
                if (guardWither != null && !guardWither.isDead()) {
                    setupWitherAttributes();
                    plugin.getLogger().info("守卫凋零属性设置完成");
                }
            }
        }.runTaskLater(plugin, 80L); // 4秒后设置（80 ticks = 4秒）

        // 定期检查AI状态和位置（AI关闭后不应该有仇恨问题）
        new BukkitRunnable() {
            @Override
            public void run() {
                if (guardWither == null || guardWither.isDead()) {
                    this.cancel();
                    return;
                }

                // 强制禁用AI（防止AI被重新启用）
                if (guardWither.hasAI()) {
                    guardWither.setAI(false);
                    plugin.getLogger().info("强制禁用了守卫凋零的AI");
                }

                // 如果设置为不可移动，固定位置
                if (!settings.isWitherCanMove()) {
                    Location currentLoc = guardWither.getLocation();
                    Location targetLoc = towerLocation.clone().add(0, 2, 0);

                    if (currentLoc.distance(targetLoc) > 2.0) {
                        guardWither.teleport(targetLoc);
                    }
                }

                // AI关闭后理论上不应该有目标，但保险起见清除任何目标
                if (guardWither.getTarget() != null) {
                    guardWither.setTarget(null);
                }
            }
        }.runTaskTimer(plugin, 20L, 20L); // 每秒检查一次
        
        plugin.getLogger().info("守卫凋零已生成，血量: " + guardWither.getHealth() + "/" + guardWither.getAttribute(Attribute.MAX_HEALTH).getValue());
    }
    
    /**
     * 设置凋零属性（延迟执行，等待凋零完全初始化）
     */
    private void setupWitherAttributes() {
        try {
            // 完全禁用AI（AI关闭后不应该有自主攻击行为）
            guardWither.setAI(false);

            // 清除任何现有目标（AI关闭后理论上不需要，但保险起见）
            guardWither.setTarget(null);

            // 设置最大血量（限制在Minecraft允许的范围内）
            double maxHealth = Math.min(settings.getWitherMaxHealth(), 1024.0);
            if (settings.getWitherMaxHealth() > 1024.0) {
                plugin.getLogger().warning("凋零血量设置 " + settings.getWitherMaxHealth() + " 超过Minecraft限制，已调整为1024");
            }

            guardWither.getAttribute(Attribute.MAX_HEALTH).setBaseValue(maxHealth);
            guardWither.setHealth(maxHealth);

            // 设置攻击力
            guardWither.getAttribute(Attribute.ATTACK_DAMAGE).setBaseValue(settings.getWitherAttackDamage());

            // 再次确认AI禁用
            guardWither.setAI(false);

            // 创建自定义Boss血条
            createCustomBossBar();

            // 验证设置是否生效（简化日志）
            plugin.getLogger().info("守卫凋零属性已设置: 血量=" + (int)guardWither.getHealth() +
                "/" + (int)guardWither.getAttribute(Attribute.MAX_HEALTH).getValue() +
                ", AI=" + (guardWither.hasAI() ? "启用" : "禁用") +
                ", 攻击力=" + (int)guardWither.getAttribute(Attribute.ATTACK_DAMAGE).getValue());

        } catch (Exception e) {
            plugin.getLogger().severe("设置凋零属性时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    
    /**
     * 启动减伤护盾
     */
    private void startShield() {
        this.shieldActive = true;
        this.shieldStartTime = System.currentTimeMillis();
        this.shieldDurationSeconds = settings.getWitherShieldDuration(); // 记录护盾持续时间
        
        // 广播护盾启动消息
        gameSession.broadcastMessage(ComponentUtil.parse(
            "<gold><bold>守卫凋零获得了减伤护盾！</bold> <yellow>(" + 
            (int)(settings.getWitherShieldReduction() * 100) + "% 减伤，持续 " + 
            settings.getWitherShieldDuration() + " 秒)"
        ));
        
        // 设置护盾结束任务
        this.shieldTask = new BukkitRunnable() {
            @Override
            public void run() {
                endShield();
            }
        }.runTaskLater(plugin, settings.getWitherShieldDuration() * 20L);
        
        // 护盾视觉效果
        startShieldEffects();
    }
    
    /**
     * 结束减伤护盾
     */
    private void endShield() {
        this.shieldActive = false;
        
        gameSession.broadcastMessage(ComponentUtil.parse(
            "<red><bold>守卫凋零的减伤护盾已消失！</bold>"
        ));
        
        if (shieldTask != null) {
            shieldTask.cancel();
            shieldTask = null;
        }
    }
    
    /**
     * 启动护盾视觉效果
     */
    private void startShieldEffects() {
        new BukkitRunnable() {
            @Override
            public void run() {
                if (!shieldActive || guardWither == null || guardWither.isDead()) {
                    this.cancel();
                    return;
                }
                
                // 在凋零周围生成粒子效果
                Location loc = guardWither.getLocation();
                World world = loc.getWorld();
                
                // 生成护盾粒子
                world.spawnParticle(Particle.ENCHANT, loc, 20, 2.0, 2.0, 2.0, 0.1);
                world.spawnParticle(Particle.WITCH, loc, 10, 1.5, 1.5, 1.5, 0.05);
            }
        }.runTaskTimer(plugin, 0L, 10L); // 每0.5秒一次
    }
    
    /**
     * 启动攻击任务
     */
    private void startAttackTask() {
        this.attackTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (guardWither == null || guardWither.isDead()) {
                    this.cancel();
                    return;
                }
                
                performWitherAttack();
            }
        }.runTaskTimer(plugin, settings.getWitherAttackInterval() * 20L, settings.getWitherAttackInterval() * 20L);
    }
    
    /**
     * 执行凋零攻击
     */
    private void performWitherAttack() {
        if (guardWither == null || guardWither.isDead()) {
            return;
        }
        
        Location witherLoc = guardWither.getLocation();
        World world = witherLoc.getWorld();
        
        // 寻找附近的捕猎者（只攻击捕猎者，不攻击速通者）
        List<Player> nearbyHunters = world.getNearbyEntities(witherLoc, 20, 20, 20).stream()
            .filter(entity -> entity instanceof Player)
            .map(entity -> (Player) entity)
            .filter(player -> {
                UUID playerId = player.getUniqueId();
                // 只攻击捕猎者，不攻击速通者
                return gameSession.getPlayerRole(playerId) == com.projectSource.ultimateManhurt.game.PlayerRole.HUNTER &&
                       gameSession.isPlayerAlive(playerId);
            })
            .toList();
        
        if (nearbyHunters.isEmpty()) {
            return;
        }
        
        // 攻击所有附近的捕猎者
        for (Player hunter : nearbyHunters) {
            attackHunter(hunter);
        }
        
        // 播放攻击音效
        world.playSound(witherLoc, Sound.ENTITY_WITHER_SHOOT, 1.0f, 0.8f);
        
        // 攻击粒子效果
        world.spawnParticle(Particle.LARGE_SMOKE, witherLoc, 30, 3.0, 3.0, 3.0, 0.1);
    }
    
    /**
     * 攻击捕猎者（发射凋零骷髅头）
     */
    private void attackHunter(Player hunter) {
        // 再次确认目标是捕猎者（双重保险）
        UUID hunterId = hunter.getUniqueId();
        com.projectSource.ultimateManhurt.game.PlayerRole hunterRole = gameSession.getPlayerRole(hunterId);
        if (hunterRole != com.projectSource.ultimateManhurt.game.PlayerRole.HUNTER) {
            plugin.getLogger().warning("守卫凋零尝试攻击非捕猎者玩家: " + hunter.getName() + " (角色: " + hunterRole + ")");
            return;
        }

        // 发射凋零骷髅头（有弹道，可以躲避）
        launchWitherSkullAt(hunter);

        // 发送消息
        ComponentUtil.sendMessage(hunter, ComponentUtil.warning("守卫凋零向你发射了骷髅头！"));

        plugin.getLogger().info("守卫凋零向捕猎者 " + hunter.getName() + " 发射了骷髅头");
    }

    /**
     * 向目标发射凋零骷髅头
     */
    private void launchWitherSkullAt(Player target) {
        if (guardWither == null || guardWither.isDead()) {
            return;
        }

        Location witherLoc = guardWither.getEyeLocation();
        Location targetLoc = target.getLocation().add(0, 1, 0); // 瞄准玩家胸部

        // 计算方向向量
        org.bukkit.util.Vector direction = targetLoc.toVector().subtract(witherLoc.toVector()).normalize();

        // 生成凋零骷髅头
        org.bukkit.entity.WitherSkull skull = guardWither.getWorld().spawn(witherLoc, org.bukkit.entity.WitherSkull.class);

        // 设置骷髅头属性
        skull.setShooter(guardWither);
        skull.setDirection(direction);
        skull.setVelocity(direction.multiply(1.5)); // 调整速度，让玩家有机会躲避
        skull.setIsIncendiary(false); // 不点燃方块
        skull.setYield(0.0f); // 不破坏地形（除非设置允许）

        // 如果设置允许破坏地形，则设置爆炸威力
        if (settings.isWitherDestroyBlocks()) {
            skull.setYield(1.0f); // 小范围爆炸
        }

        // 播放发射音效
        witherLoc.getWorld().playSound(witherLoc, Sound.ENTITY_WITHER_SHOOT, 1.0f, 1.0f);
    }
    
    /**
     * 处理对凋零的伤害
     */
    public void handleWitherDamage(EntityDamageByEntityEvent event) {
        if (event.getEntity() != guardWither) {
            return;
        }

        // 检查是否是弓箭伤害
        if (event.getDamager() instanceof org.bukkit.entity.Arrow) {
            // 凋零免疫弓箭伤害
            event.setCancelled(true);

            // 获取射箭者并发送消息
            org.bukkit.entity.Arrow arrow = (org.bukkit.entity.Arrow) event.getDamager();
            if (arrow.getShooter() instanceof org.bukkit.entity.Player shooter) {
                ComponentUtil.sendMessage(shooter, ComponentUtil.warning("守卫凋零免疫弓箭伤害！"));
            }

            plugin.getLogger().info("守卫凋零免疫了弓箭伤害");
            return;
        }

        // 检查是否有减伤护盾
        if (shieldActive) {
            double originalDamage = event.getDamage();
            double reducedDamage = originalDamage * (1.0 - settings.getWitherShieldReduction());
            event.setDamage(reducedDamage);

            // 护盾被攻击的视觉效果
            Location loc = guardWither.getLocation();
            loc.getWorld().spawnParticle(Particle.ENCHANTED_HIT, loc, 10, 1.0, 1.0, 1.0, 0.1);
        }

        // 如果攻击者是玩家，施加减疗效果
        if (event.getDamager() instanceof Player attacker) {
            applyHealingReduction(attacker);
        }
    }
    
    /**
     * 施加减疗效果
     */
    private void applyHealingReduction(Player player) {
        UUID playerId = player.getUniqueId();
        healingReductionPlayers.put(playerId, System.currentTimeMillis() + 10000); // 10秒减疗
        
        // 发送消息
        ComponentUtil.sendMessage(player, ComponentUtil.warning(
            "攻击守卫凋零使你获得了减疗效果！(" + (int)(settings.getWitherHealingReduction() * 100) + "% 减疗，持续10秒)"
        ));
        
        // 粒子效果
        player.getWorld().spawnParticle(Particle.INSTANT_EFFECT, player.getLocation(), 10, 0.5, 1.0, 0.5, 0.1);
    }
    
    /**
     * 检查玩家是否有减疗效果
     */
    public boolean hasHealingReduction(UUID playerId) {
        Long endTime = healingReductionPlayers.get(playerId);
        if (endTime == null) {
            return false;
        }
        
        if (System.currentTimeMillis() > endTime) {
            healingReductionPlayers.remove(playerId);
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取减疗效果强度
     */
    public double getHealingReduction() {
        return settings.getWitherHealingReduction();
    }
    
    /**
     * 处理凋零死亡
     */
    public void handleWitherDeath() {
        if (guardWither != null) {
            plugin.getLogger().info("GuardModeManager: 处理守卫凋零死亡");

            // 广播凋零死亡消息
            gameSession.broadcastMessage(ComponentUtil.parse(
                "<red><bold>守卫凋零被击败了！捕猎者获胜！</bold>"
            ));

            // 结束游戏
            gameSession.endGame(WinCondition.HUNTER_KILL_WITHER);

            plugin.getLogger().info("GuardModeManager: 游戏已结束，捕猎者获胜");
        } else {
            plugin.getLogger().warning("GuardModeManager: 尝试处理凋零死亡，但guardWither为null");
        }
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        if (attackTask != null) {
            attackTask.cancel();
            attackTask = null;
        }
        
        if (shieldTask != null) {
            shieldTask.cancel();
            shieldTask = null;
        }
        
        healingReductionPlayers.clear();

        // 清理Boss血条
        cleanupBossBar();

        if (guardWither != null && !guardWither.isDead()) {
            guardWither.remove();
        }

        plugin.getLogger().info("守卫模式管理器已清理");
    }
    
    // Getter方法
    public Wither getGuardWither() {
        return guardWither;
    }
    
    public Location getTowerLocation() {
        return towerLocation != null ? towerLocation.clone() : null;
    }
    
    public boolean isShieldActive() {
        return shieldActive;
    }

    /**
     * 获取护盾剩余时间（秒）
     */
    public long getShieldRemainingTime() {
        if (!shieldActive || shieldStartTime == 0) {
            return 0;
        }

        // 计算已经过去的时间
        long currentTime = System.currentTimeMillis();
        long elapsedSeconds = (currentTime - shieldStartTime) / 1000;

        // 计算剩余时间
        long remainingSeconds = shieldDurationSeconds - elapsedSeconds;

        return Math.max(0, remainingSeconds);
    }

    /**
     * 创建自定义Boss血条
     */
    private void createCustomBossBar() {
        if (guardWither == null) {
            return;
        }

        // 创建Boss血条
        customBossBar = org.bukkit.Bukkit.createBossBar(
            "守卫凋零",
            org.bukkit.boss.BarColor.RED,
            org.bukkit.boss.BarStyle.SOLID
        );

        // 为所有玩家显示Boss血条
        for (UUID playerId : gameSession.getPlayerRoles().keySet()) {
            org.bukkit.entity.Player player = org.bukkit.Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                customBossBar.addPlayer(player);
            }
        }

        // 启动血条更新任务
        startBossBarUpdateTask();

        plugin.getLogger().info("守卫凋零Boss血条已创建");
    }

    /**
     * 启动Boss血条更新任务
     */
    private void startBossBarUpdateTask() {
        new org.bukkit.scheduler.BukkitRunnable() {
            @Override
            public void run() {
                if (guardWither == null || guardWither.isDead() || customBossBar == null) {
                    this.cancel();
                    return;
                }

                // 更新血条进度
                double currentHealth = guardWither.getHealth();
                double maxHealth = guardWither.getAttribute(Attribute.MAX_HEALTH).getValue();
                double progress = Math.max(0.0, Math.min(1.0, currentHealth / maxHealth));

                customBossBar.setProgress(progress);

                // 更新血条标题
                String title = String.format("守卫凋零 - %d/%d", (int)currentHealth, (int)maxHealth);
                if (shieldActive) {
                    title += " [护盾激活]";
                }
                customBossBar.setTitle(title);

                // 根据血量改变颜色
                if (progress > 0.6) {
                    customBossBar.setColor(org.bukkit.boss.BarColor.GREEN);
                } else if (progress > 0.3) {
                    customBossBar.setColor(org.bukkit.boss.BarColor.YELLOW);
                } else {
                    customBossBar.setColor(org.bukkit.boss.BarColor.RED);
                }
            }
        }.runTaskTimer(plugin, 0L, 10L); // 每0.5秒更新一次
    }

    /**
     * 清理Boss血条
     */
    private void cleanupBossBar() {
        if (customBossBar != null) {
            customBossBar.removeAll();
            customBossBar = null;
        }
    }
}
