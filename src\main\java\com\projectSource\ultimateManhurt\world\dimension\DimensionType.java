package com.projectSource.ultimateManhurt.world.dimension;

import org.bukkit.World;

/**
 * 维度类型枚举
 */
public enum DimensionType {
    OVERWORLD("主世界", World.Environment.NORMAL),
    NETHER("下界", World.Environment.NETHER),
    THE_END("末地", World.Environment.THE_END);
    
    private final String displayName;
    private final World.Environment environment;
    
    DimensionType(String displayName, World.Environment environment) {
        this.displayName = displayName;
        this.environment = environment;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public World.Environment getEnvironment() {
        return environment;
    }
    
    /**
     * 根据环境获取维度类型
     */
    public static DimensionType fromEnvironment(World.Environment environment) {
        for (DimensionType type : values()) {
            if (type.environment == environment) {
                return type;
            }
        }
        return OVERWORLD;
    }
    
    /**
     * 根据世界获取维度类型
     */
    public static DimensionType fromWorld(World world) {
        return fromEnvironment(world.getEnvironment());
    }
}
