package com.projectSource.ultimateManhurt.profession.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.gui.BaseGui;
import com.projectSource.ultimateManhurt.profession.Profession;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import java.util.ArrayList;
import java.util.List;

/**
 * 职业选择GUI
 */
public class ProfessionSelectionGui extends BaseGui {

    // 静态追踪正在进行职业选择的玩家
    private static final Map<UUID, ProfessionSelectionGui> activeProfessionGuis = new HashMap<>();

    private final GameSession gameSession;
    private final PlayerRole playerRole;
    
    public ProfessionSelectionGui(UltimateManhurt plugin, Player player, GameSession gameSession) {
        super(plugin, player, "选择职业", 54);
        this.gameSession = gameSession;
        this.playerRole = gameSession.getPlayerRole(player.getUniqueId());

        // 将此GUI添加到活跃追踪中
        activeProfessionGuis.put(player.getUniqueId(), this);

        setupGui();
    }
    
    @Override
    protected void setupGui() {
        // 清空界面
        inventory.clear();
        
        if (playerRole == null) {
            // 玩家没有角色，显示错误信息
            setItem(22, createErrorItem());
            return;
        }
        
        // 获取可用职业
        Profession[] availableProfessions = Profession.getAvailableProfessions(playerRole);
        
        if (availableProfessions.length == 0) {
            // 没有可用职业
            setItem(22, createNoAvailableItem());
            return;
        }
        
        // 显示角色信息
        setItem(4, createRoleInfoItem());
        
        // 显示职业选项 - 可扩展布局系统，支持未来新职业
        int[] slots = getOptimalSlots(availableProfessions.length);

        for (int i = 0; i < availableProfessions.length && i < slots.length; i++) {
            setItem(slots[i], createProfessionItem(availableProfessions[i]));
        }
        
        // 显示说明
        setItem(49, createInfoItem());
        
        // 添加边框装饰
        addBorder();
    }

    /**
     * 获取最优的槽位布局，支持未来职业扩展
     * 设计理念：为每个阵营预留最多15个职业位置
     */
    private int[] getOptimalSlots(int professionCount) {
        return switch (professionCount) {
            case 1 -> new int[]{22}; // 单个职业：正中央
            case 2 -> new int[]{21, 23}; // 2个职业：中央两侧
            case 3 -> new int[]{20, 22, 24}; // 3个职业：中央一排
            case 4 -> new int[]{19, 21, 23, 25}; // 4个职业：中央一排扩展
            case 5 -> new int[]{11, 13, 15, 29, 31}; // 5个职业：两排布局
            case 6 -> new int[]{10, 12, 14, 28, 30, 32}; // 6个职业：两排对称
            case 7 -> new int[]{9, 11, 13, 15, 17, 29, 31}; // 7个职业：5+2布局
            case 8 -> new int[]{9, 11, 13, 15, 17, 27, 29, 31}; // 8个职业：5+3布局
            case 9 -> new int[]{2, 4, 6, 20, 22, 24, 38, 40, 42}; // 9个职业：三排3+3+3
            case 10 -> new int[]{1, 3, 5, 7, 19, 21, 23, 25, 37, 39}; // 10个职业：三排4+4+2
            case 11 -> new int[]{1, 3, 5, 7, 19, 21, 23, 25, 37, 39, 41}; // 11个职业：三排4+4+3
            case 12 -> new int[]{1, 3, 5, 7, 19, 21, 23, 25, 37, 39, 41, 43}; // 12个职业：三排4+4+4
            default -> new int[]{1, 3, 5, 7, 9, 19, 21, 23, 25, 27, 37, 39, 41, 43, 45}; // 13+个职业：最大布局
        };
    }

    /**
     * 创建角色信息物品
     */
    private ItemStack createRoleInfoItem() {
        ItemStack item = new ItemStack(Material.NAME_TAG);
        ItemMeta meta = item.getItemMeta();
        
        meta.displayName(Component.text("你的角色: " + playerRole.getDisplayName(), playerRole.getColor()));
        
        List<Component> lore = new ArrayList<>();
        lore.add(Component.text(playerRole.getDescription(), NamedTextColor.GRAY));
        lore.add(Component.empty());
        lore.add(Component.text("请选择一个职业来增强你的能力", NamedTextColor.YELLOW));
        meta.lore(lore);
        
        item.setItemMeta(meta);
        return item;
    }
    
    /**
     * 创建职业选择物品
     */
    private ItemStack createProfessionItem(Profession profession) {
        ItemStack item = new ItemStack(profession.getIcon());
        ItemMeta meta = item.getItemMeta();
        
        meta.displayName(profession.getDisplayComponent());
        
        List<Component> lore = new ArrayList<>();
        lore.add(Component.text(profession.getDescription(), NamedTextColor.GRAY));
        lore.add(Component.empty());
        
        // 被动技能信息
        lore.add(Component.text("被动技能: " + profession.getPassiveSkillName(), NamedTextColor.GREEN));
        lore.add(Component.text(profession.getPassiveSkillDescription(), NamedTextColor.DARK_GREEN));
        if (profession.getPassiveSkillCooldown() > 0) {
            lore.add(Component.text("冷却时间: " + profession.getPassiveSkillCooldown() + "秒", NamedTextColor.BLUE));
        }
        lore.add(Component.empty());
        
        // 主动技能信息
        lore.add(Component.text("主动技能: " + profession.getActiveSkillName(), NamedTextColor.GOLD));
        lore.add(Component.text(profession.getActiveSkillDescription(), NamedTextColor.YELLOW));
        if (profession.getActiveSkillCooldown() > 0) {
            lore.add(Component.text("冷却时间: " + profession.getActiveSkillCooldown() + "秒", NamedTextColor.BLUE));
        }
        lore.add(Component.empty());
        
        lore.add(Component.text("点击选择此职业", NamedTextColor.WHITE));
        
        meta.lore(lore);
        item.setItemMeta(meta);
        return item;
    }
    
    /**
     * 创建说明物品
     */
    private ItemStack createInfoItem() {
        ItemStack item = new ItemStack(Material.BOOK);
        ItemMeta meta = item.getItemMeta();
        
        meta.displayName(Component.text("职业系统说明", NamedTextColor.AQUA));
        
        List<Component> lore = new ArrayList<>();
        lore.add(Component.text("• 每个角色都有专属的职业可选择", NamedTextColor.GRAY));
        lore.add(Component.text("• 每个职业都有一个被动技能和一个主动技能", NamedTextColor.GRAY));
        lore.add(Component.text("• 主动技能需要将剑放在副手并右键使用", NamedTextColor.GRAY));
        lore.add(Component.text("• 选择职业后无法更改，请慎重选择", NamedTextColor.RED));
        
        meta.lore(lore);
        item.setItemMeta(meta);
        return item;
    }
    
    /**
     * 创建错误物品
     */
    private ItemStack createErrorItem() {
        ItemStack item = new ItemStack(Material.BARRIER);
        ItemMeta meta = item.getItemMeta();
        
        meta.displayName(Component.text("错误", NamedTextColor.RED));
        
        List<Component> lore = new ArrayList<>();
        lore.add(Component.text("无法获取你的角色信息", NamedTextColor.GRAY));
        lore.add(Component.text("请联系管理员", NamedTextColor.GRAY));
        
        meta.lore(lore);
        item.setItemMeta(meta);
        return item;
    }
    
    /**
     * 创建无可用职业物品
     */
    private ItemStack createNoAvailableItem() {
        ItemStack item = new ItemStack(Material.BARRIER);
        ItemMeta meta = item.getItemMeta();
        
        meta.displayName(Component.text("暂无可用职业", NamedTextColor.YELLOW));
        
        List<Component> lore = new ArrayList<>();
        lore.add(Component.text("你的角色暂时没有可选择的职业", NamedTextColor.GRAY));
        
        meta.lore(lore);
        item.setItemMeta(meta);
        return item;
    }
    
    /**
     * 添加边框装饰
     */
    private void addBorder() {
        ItemStack borderItem = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta meta = borderItem.getItemMeta();
        meta.displayName(Component.empty());
        borderItem.setItemMeta(meta);
        
        // 顶部和底部边框
        for (int i = 0; i < 9; i++) {
            if (getItem(i) == null) setItem(i, borderItem);
            if (getItem(45 + i) == null) setItem(45 + i, borderItem);
        }
        
        // 左右边框
        for (int i = 1; i < 5; i++) {
            if (getItem(i * 9) == null) setItem(i * 9, borderItem);
            if (getItem(i * 9 + 8) == null) setItem(i * 9 + 8, borderItem);
        }
    }
    
    @Override
    public void handleClick(InventoryClickEvent event) {
        event.setCancelled(true);
        
        if (!(event.getWhoClicked() instanceof Player clicker)) {
            return;
        }
        
        if (!clicker.equals(player)) {
            return;
        }
        
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) {
            return;
        }
        
        // 检查是否点击了职业物品
        Profession[] availableProfessions = Profession.getAvailableProfessions(playerRole);

        // 获取对应的槽位数组
        int[] slots = getOptimalSlots(availableProfessions.length);

        for (int i = 0; i < availableProfessions.length && i < slots.length; i++) {
            if (event.getSlot() == slots[i]) {
                handleProfessionSelection(availableProfessions[i]);
                return;
            }
        }
    }
    
    /**
     * 处理职业选择
     */
    private void handleProfessionSelection(Profession profession) {
        boolean success = plugin.getProfessionManager().setProfession(player.getUniqueId(), profession);

        if (success) {
            ComponentUtil.sendMessage(player, ComponentUtil.success(
                "成功选择职业: " + profession.getDisplayName()));
            playSuccessSound();

            // 关闭GUI
            player.closeInventory();

            // 延迟移除追踪和通知，确保GUI关闭事件处理完成
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                // 从活跃追踪中移除（职业选择完成）
                activeProfessionGuis.remove(player.getUniqueId());

                // 通知游戏会话职业选择完成
                gameSession.onPlayerProfessionSelected(player.getUniqueId(), profession);

                plugin.getLogger().info("玩家 " + player.getName() + " 职业选择完成，已从追踪中移除");
            }, 1L); // 延迟1 tick，确保GUI关闭事件先处理
        } else {
            ComponentUtil.sendMessage(player, ComponentUtil.error("选择职业失败，请稍后再试"));
            playErrorSound();
        }
    }

    /**
     * 获取游戏会话
     */
    public GameSession getGameSession() {
        return gameSession;
    }

    /**
     * 检查玩家是否有活跃的职业选择GUI
     */
    public static boolean hasActiveProfessionGui(UUID playerId) {
        return activeProfessionGuis.containsKey(playerId);
    }

    /**
     * 获取玩家的活跃职业选择GUI
     */
    public static ProfessionSelectionGui getActiveProfessionGui(UUID playerId) {
        return activeProfessionGuis.get(playerId);
    }

    /**
     * 移除玩家的活跃职业选择GUI
     */
    public static void removeActiveProfessionGui(UUID playerId) {
        activeProfessionGuis.remove(playerId);
    }

    /**
     * 清理所有活跃的职业选择GUI
     */
    public static void clearAllActiveProfessionGuis() {
        activeProfessionGuis.clear();
    }
}
