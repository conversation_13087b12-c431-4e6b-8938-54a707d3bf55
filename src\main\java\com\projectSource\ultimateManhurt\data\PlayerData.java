package com.projectSource.ultimateManhurt.data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 玩家数据类
 * 存储玩家的游戏统计和设置
 */
public class PlayerData {
    
    private final UUID playerId;
    private LocalDateTime firstJoin;
    private LocalDateTime lastSeen;
    
    // 游戏统计
    private int totalGames = 0;
    private int gamesWon = 0;
    private int gamesLost = 0;
    private int totalKills = 0;
    private int totalDeaths = 0;
    private long totalPlayTime = 0; // 毫秒
    
    // 角色统计
    private int speedrunnerGames = 0;
    private int speedrunnerWins = 0;
    private int hunterGames = 0;
    private int hunterWins = 0;
    
    // 成就和记录
    private long fastestDragonKill = Long.MAX_VALUE; // 最快击败末影龙时间（毫秒）
    private int longestSurvival = 0; // 最长存活时间（秒）
    private int maxKillStreak = 0; // 最大连杀
    
    // 玩家设置
    private boolean showScoreboard = true;
    private boolean playSound = true;
    private String preferredLanguage = "zh_CN";
    
    // 自定义数据
    private final Map<String, Object> customData = new HashMap<>();
    
    public PlayerData(UUID playerId) {
        this.playerId = playerId;
        this.firstJoin = LocalDateTime.now();
        this.lastSeen = LocalDateTime.now();
    }
    
    // 基础信息
    public UUID getPlayerId() { return playerId; }
    public LocalDateTime getFirstJoin() { return firstJoin; }
    public void setFirstJoin(LocalDateTime firstJoin) { this.firstJoin = firstJoin; }
    public LocalDateTime getLastSeen() { return lastSeen; }
    public void setLastSeen(LocalDateTime lastSeen) { this.lastSeen = lastSeen; }
    
    // 游戏统计
    public int getTotalGames() { return totalGames; }
    public void setTotalGames(int totalGames) { this.totalGames = totalGames; }
    public void incrementTotalGames() { this.totalGames++; }
    
    public int getGamesWon() { return gamesWon; }
    public void setGamesWon(int gamesWon) { this.gamesWon = gamesWon; }
    public void incrementGamesWon() { this.gamesWon++; }
    
    public int getGamesLost() { return gamesLost; }
    public void setGamesLost(int gamesLost) { this.gamesLost = gamesLost; }
    public void incrementGamesLost() { this.gamesLost++; }
    
    public int getTotalKills() { return totalKills; }
    public void setTotalKills(int totalKills) { this.totalKills = totalKills; }
    public void addKills(int kills) { this.totalKills += kills; }
    
    public int getTotalDeaths() { return totalDeaths; }
    public void setTotalDeaths(int totalDeaths) { this.totalDeaths = totalDeaths; }
    public void addDeaths(int deaths) { this.totalDeaths += deaths; }
    
    public long getTotalPlayTime() { return totalPlayTime; }
    public void setTotalPlayTime(long totalPlayTime) { this.totalPlayTime = totalPlayTime; }
    public void addPlayTime(long playTime) { this.totalPlayTime += playTime; }
    
    // 角色统计
    public int getSpeedrunnerGames() { return speedrunnerGames; }
    public void setSpeedrunnerGames(int speedrunnerGames) { this.speedrunnerGames = speedrunnerGames; }
    public void incrementSpeedrunnerGames() { this.speedrunnerGames++; }
    
    public int getSpeedrunnerWins() { return speedrunnerWins; }
    public void setSpeedrunnerWins(int speedrunnerWins) { this.speedrunnerWins = speedrunnerWins; }
    public void incrementSpeedrunnerWins() { this.speedrunnerWins++; }
    
    public int getHunterGames() { return hunterGames; }
    public void setHunterGames(int hunterGames) { this.hunterGames = hunterGames; }
    public void incrementHunterGames() { this.hunterGames++; }
    
    public int getHunterWins() { return hunterWins; }
    public void setHunterWins(int hunterWins) { this.hunterWins = hunterWins; }
    public void incrementHunterWins() { this.hunterWins++; }
    
    // 记录
    public long getFastestDragonKill() { return fastestDragonKill; }
    public void setFastestDragonKill(long fastestDragonKill) { 
        if (fastestDragonKill < this.fastestDragonKill) {
            this.fastestDragonKill = fastestDragonKill; 
        }
    }
    
    public int getLongestSurvival() { return longestSurvival; }
    public void setLongestSurvival(int longestSurvival) { 
        if (longestSurvival > this.longestSurvival) {
            this.longestSurvival = longestSurvival; 
        }
    }
    
    public int getMaxKillStreak() { return maxKillStreak; }
    public void setMaxKillStreak(int maxKillStreak) { 
        if (maxKillStreak > this.maxKillStreak) {
            this.maxKillStreak = maxKillStreak; 
        }
    }
    
    // 设置
    public boolean isShowScoreboard() { return showScoreboard; }
    public void setShowScoreboard(boolean showScoreboard) { this.showScoreboard = showScoreboard; }
    
    public boolean isPlaySound() { return playSound; }
    public void setPlaySound(boolean playSound) { this.playSound = playSound; }
    
    public String getPreferredLanguage() { return preferredLanguage; }
    public void setPreferredLanguage(String preferredLanguage) { this.preferredLanguage = preferredLanguage; }
    
    // 自定义数据
    public Object getCustomData(String key) { return customData.get(key); }
    public void setCustomData(String key, Object value) { customData.put(key, value); }
    public void removeCustomData(String key) { customData.remove(key); }
    public Map<String, Object> getAllCustomData() { return new HashMap<>(customData); }
    
    // 计算方法
    public double getWinRate() {
        if (totalGames == 0) return 0.0;
        return (double) gamesWon / totalGames;
    }
    
    public double getKDRatio() {
        if (totalDeaths == 0) return totalKills;
        return (double) totalKills / totalDeaths;
    }
    
    public double getSpeedrunnerWinRate() {
        if (speedrunnerGames == 0) return 0.0;
        return (double) speedrunnerWins / speedrunnerGames;
    }
    
    public double getHunterWinRate() {
        if (hunterGames == 0) return 0.0;
        return (double) hunterWins / hunterGames;
    }
    
    /**
     * 更新最后在线时间
     */
    public void updateLastSeen() {
        this.lastSeen = LocalDateTime.now();
    }
    
    /**
     * 记录游戏结果
     */
    public void recordGameResult(boolean won, com.projectSource.ultimateManhurt.game.PlayerRole role, 
                                int kills, int deaths, long gameTime) {
        incrementTotalGames();
        if (won) {
            incrementGamesWon();
        } else {
            incrementGamesLost();
        }
        
        addKills(kills);
        addDeaths(deaths);
        addPlayTime(gameTime);
        
        // 记录角色统计
        switch (role) {
            case SPEEDRUNNER:
                incrementSpeedrunnerGames();
                if (won) incrementSpeedrunnerWins();
                break;
            case HUNTER:
                incrementHunterGames();
                if (won) incrementHunterWins();
                break;
            default:
                break;
        }
        
        updateLastSeen();
    }
    
    @Override
    public String toString() {
        return "PlayerData{" +
                "playerId=" + playerId +
                ", totalGames=" + totalGames +
                ", gamesWon=" + gamesWon +
                ", totalKills=" + totalKills +
                ", totalDeaths=" + totalDeaths +
                ", winRate=" + String.format("%.2f", getWinRate()) +
                '}';
    }
}
