package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.kit.StartKit;
import com.projectSource.ultimateManhurt.kit.StartKitTemplate;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;

/**
 * 装备包模板应用选择GUI
 * 让用户选择将模板应用到哪个角色
 */
public class StartKitTemplateApplyGui extends BaseGui {
    
    private final Room room;
    private final String templateId;
    private final StartKit template;
    
    public StartKitTemplateApplyGui(UltimateManhurt plugin, Player player, Room room, String templateId) {
        super(plugin, player, "<gold><bold>应用模板 - " + getTemplateName(templateId), 27);
        this.room = room;
        this.templateId = templateId;
        this.template = StartKitTemplate.getAllTemplates().get(templateId);
        setupGui();
    }
    
    private static String getTemplateName(String templateId) {
        StartKit template = StartKitTemplate.getAllTemplates().get(templateId);
        return template != null ? template.getName() : "未知模板";
    }
    
    @Override
    protected void setupGui() {
        // 检查权限
        if (!room.isOwner(player.getUniqueId())) {
            setItem(13, createItem(Material.BARRIER, "<red>权限不足", 
                "<gray>只有房主可以管理装备包"));
            return;
        }
        
        // 检查模板是否存在
        if (template == null) {
            setItem(13, createItem(Material.BARRIER, "<red>模板不存在", 
                "<gray>指定的模板不存在或已被删除"));
            return;
        }
        
        // 标题信息
        setItem(4, createItem(Material.BOOKSHELF, "<gold><bold>选择应用目标",
            "<gray>选择要应用模板的角色",
            "<gray>模板: <white>" + template.getName(),
            "<gray>描述: <white>" + template.getDescription()));
        
        // 速通者选项
        setItem(11, createItem(Material.DIAMOND_SWORD, "<green><bold>应用到速通者",
            "<gray>将模板应用到速通者装备包",
            "<gray>当前速通者装备: <white>" + getCurrentKitName(PlayerRole.SPEEDRUNNER),
            "",
            "<yellow>点击应用到速通者",
            "<red>注意: 这将覆盖当前的速通者装备包"));
        
        // 捕猎者选项
        setItem(15, createItem(Material.BOW, "<red><bold>应用到捕猎者",
            "<gray>将模板应用到捕猎者装备包",
            "<gray>当前捕猎者装备: <white>" + getCurrentKitName(PlayerRole.HUNTER),
            "",
            "<yellow>点击应用到捕猎者",
            "<red>注意: 这将覆盖当前的捕猎者装备包"));
        
        // 预览模板
        setItem(13, createItem(Material.ENDER_EYE, "<blue><bold>预览模板",
            "<gray>查看模板的详细内容",
            "<gray>模板: <white>" + template.getName(),
            "<gray>物品数量: <white>" + template.getItems().size(),
            "",
            "<yellow>点击预览模板内容"));
        
        // 返回按钮
        setItem(22, createItem(Material.ARROW, "<red><bold>返回",
            "<gray>返回模板选择界面",
            "<yellow>点击返回"));
        
        // 填充边框
        fillBorder(Material.GRAY_STAINED_GLASS_PANE);
    }
    
    /**
     * 获取当前装备包名称
     */
    private String getCurrentKitName(PlayerRole role) {
        StartKit currentKit = role == PlayerRole.SPEEDRUNNER ? 
            room.getSettings().getSpeedrunnerKit() : 
            room.getSettings().getHunterKit();
        
        return currentKit != null ? currentKit.getName() : "默认装备包";
    }
    
    @Override
    public void handleClick(InventoryClickEvent event) {
        event.setCancelled(true);
        
        if (room == null || template == null) {
            return;
        }
        
        // 检查权限
        if (!room.isOwner(player.getUniqueId())) {
            sendError("只有房主可以管理装备包！");
            playErrorSound();
            return;
        }
        
        int slot = event.getSlot();
        playClickSound();
        
        switch (slot) {
            case 11: // 应用到速通者
                handleApplyToRole(PlayerRole.SPEEDRUNNER);
                break;
            case 15: // 应用到捕猎者
                handleApplyToRole(PlayerRole.HUNTER);
                break;
            case 13: // 预览模板
                handlePreviewTemplate();
                break;
            case 22: // 返回
                handleBack();
                break;
        }
    }
    
    /**
     * 应用模板到指定角色
     */
    private void handleApplyToRole(PlayerRole role) {
        try {
            // 创建模板副本
            StartKit newKit = new StartKit(template);
            
            // 应用到指定角色
            if (role == PlayerRole.SPEEDRUNNER) {
                room.getSettings().setSpeedrunnerKit(newKit);
            } else {
                room.getSettings().setHunterKit(newKit);
            }
            
            String roleText = role == PlayerRole.SPEEDRUNNER ? "速通者" : "捕猎者";
            sendSuccess("已将模板 \"" + template.getName() + "\" 应用到" + roleText + "装备包");
            playSuccessSound();
            
            // 返回装备包管理
            close();
            plugin.getGuiManager().openStartKitGui(player, room);
            
        } catch (Exception e) {
            sendError("应用模板时发生错误: " + e.getMessage());
            playErrorSound();
            plugin.getLogger().warning("应用装备包模板时出错: " + e.getMessage());
        }
    }
    
    /**
     * 预览模板
     */
    private void handlePreviewTemplate() {
        try {
            plugin.getStartKitManager().previewKit(player, template);
            sendSuccess("已打开模板预览");
        } catch (Exception e) {
            sendError("预览模板时发生错误");
            playErrorSound();
        }
    }
    
    /**
     * 返回模板选择
     */
    private void handleBack() {
        close();
        plugin.getGuiManager().openStartKitTemplateGui(player, room, null);
    }
    
    // 工具方法
    protected void sendSuccess(String message) {
        ComponentUtil.sendMessage(player, ComponentUtil.success(message));
    }

    protected void sendError(String message) {
        ComponentUtil.sendMessage(player, ComponentUtil.error(message));
    }

    protected void playClickSound() {
        player.playSound(player.getLocation(), org.bukkit.Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
    }

    protected void playErrorSound() {
        player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
    }

    protected void playSuccessSound() {
        player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.0f);
    }
}
