package com.projectSource.ultimateManhurt.banpick;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.*;

/**
 * Ban Pick GUI界面
 */
public class BanPickGUI implements Listener {
    
    private final UltimateManhurt plugin;
    private final BanPickManager banPickManager;
    private final Map<UUID, Inventory> playerInventories;
    private final Set<UUID> updatingPlayers; // 正在更新GUI的玩家，防止冲突
    
    // GUI布局常量
    private static final int GUI_SIZE = 54; // 6行
    private static final int ITEMS_START_SLOT = 9; // 第二行开始
    private static final int ITEMS_END_SLOT = 44; // 第五行结束
    private static final int INFO_SLOT = 4; // 顶部中间信息槽
    private static final int TIMER_SLOT = 49; // 底部计时器槽
    
    public BanPickGUI(UltimateManhurt plugin, BanPickManager banPickManager) {
        this.plugin = plugin;
        this.banPickManager = banPickManager;
        this.playerInventories = new HashMap<>();
        this.updatingPlayers = new HashSet<>();
        
        // 注册事件监听器
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }
    
    /**
     * 为玩家打开Ban Pick GUI
     */
    public void openGUI(Player player) {
        Inventory inventory = createGUI(player);
        playerInventories.put(player.getUniqueId(), inventory);
        player.openInventory(inventory);
    }
    
    /**
     * 更新玩家的GUI
     */
    public void updateGUI(Player player) {
        UUID playerId = player.getUniqueId();

        // 防止重复更新
        if (updatingPlayers.contains(playerId)) {
            return;
        }

        Inventory inventory = playerInventories.get(playerId);
        if (inventory != null) {
            updatingPlayers.add(playerId);
            try {
                updateGUIContent(player, inventory);
            } finally {
                updatingPlayers.remove(playerId);
            }
        }
    }

    /**
     * 只更新计时器显示，避免频繁重建整个GUI
     */
    public void updateTimerOnly(Player player) {
        Inventory inventory = playerInventories.get(player.getUniqueId());
        if (inventory != null) {
            // 只更新计时器槽位
            addTimerDisplay(inventory);
        }
    }
    
    /**
     * 创建GUI界面
     */
    private Inventory createGUI(Player player) {
        String title = getGUITitle();
        Inventory inventory = Bukkit.createInventory(null, GUI_SIZE, ComponentUtil.parse(title));
        
        updateGUIContent(player, inventory);
        return inventory;
    }
    
    /**
     * 更新GUI内容
     */
    private void updateGUIContent(Player player, Inventory inventory) {
        // 清空界面
        inventory.clear();
        
        // 添加装饰边框
        addBorder(inventory);
        
        // 添加信息显示
        addInfoDisplay(inventory, player);
        
        // 添加计时器显示
        addTimerDisplay(inventory);
        
        // 添加物品选择区域
        addItemSelection(inventory, player);
        
        // 添加结果显示区域
        addResultDisplay(inventory);
    }
    
    /**
     * 添加边框装饰
     */
    private void addBorder(Inventory inventory) {
        ItemStack border = createItem(Material.GRAY_STAINED_GLASS_PANE, " ", Collections.emptyList());
        
        // 顶部和底部边框
        for (int i = 0; i < 9; i++) {
            if (i != INFO_SLOT) inventory.setItem(i, border);
            if (i + 45 != TIMER_SLOT) inventory.setItem(i + 45, border);
        }
        
        // 左右边框
        for (int row = 1; row < 5; row++) {
            inventory.setItem(row * 9, border);
            inventory.setItem(row * 9 + 8, border);
        }
    }
    
    /**
     * 添加信息显示
     */
    private void addInfoDisplay(Inventory inventory, Player player) {
        BanPickPhase phase = banPickManager.getCurrentPhase();
        PlayerRole playerRole = banPickManager.getGameSession().getPlayerRole(player.getUniqueId());
        
        Material iconMaterial;
        String title;
        List<String> lore = new ArrayList<>();
        
        if (phase == BanPickPhase.WAITING) {
            iconMaterial = Material.CLOCK;
            title = "<yellow>等待开始";
            lore.add("<gray>等待所有玩家准备");
        } else if (phase.isHunterTurn() && playerRole == PlayerRole.HUNTER) {
            iconMaterial = Material.DIAMOND_SWORD;
            title = "<blue>你的回合";
            lore.add("<blue>捕猎者 " + (phase.isBanPhase() ? "禁用" : "选择") + " 阶段");
            lore.add("<gray>点击物品进行投票");
        } else if (phase.isSpeedrunnerTurn() && playerRole == PlayerRole.SPEEDRUNNER) {
            iconMaterial = Material.ENDER_PEARL;
            title = "<green>你的回合";
            lore.add("<green>速通者 " + (phase.isBanPhase() ? "禁用" : "选择") + " 阶段");
            lore.add("<gray>点击物品进行投票");
        } else {
            iconMaterial = Material.BARRIER;
            title = "<red>等待对方";
            lore.add("<gray>" + phase.getDisplayName());
            lore.add("<gray>请等待对方完成选择");
        }
        
        // 添加当前投票信息
        BanPickItem currentVote = banPickManager.getPlayerVote(player.getUniqueId());
        if (currentVote != null) {
            lore.add("");
            lore.add("<yellow>当前投票: <white>" + currentVote.getDisplayName());
        }
        
        ItemStack infoItem = createItem(iconMaterial, title, lore);
        inventory.setItem(INFO_SLOT, infoItem);
    }
    
    /**
     * 添加计时器显示
     */
    private void addTimerDisplay(Inventory inventory) {
        int remainingTime = banPickManager.getRemainingTime();
        
        Material timerMaterial;
        String timerColor;
        if (remainingTime > 15) {
            timerMaterial = Material.GREEN_CONCRETE;
            timerColor = "<green>";
        } else if (remainingTime > 5) {
            timerMaterial = Material.YELLOW_CONCRETE;
            timerColor = "<yellow>";
        } else {
            timerMaterial = Material.RED_CONCRETE;
            timerColor = "<red>";
        }
        
        String title = timerColor + "剩余时间: " + remainingTime + "秒";
        List<String> lore = Arrays.asList(
            "<gray>时间到将自动进入下一阶段",
            "<gray>所有人投票完成可提前结束"
        );
        
        ItemStack timerItem = createItem(timerMaterial, title, lore);
        inventory.setItem(TIMER_SLOT, timerItem);
    }
    
    /**
     * 添加物品选择区域
     */
    private void addItemSelection(Inventory inventory, Player player) {
        PlayerRole playerRole = banPickManager.getGameSession().getPlayerRole(player.getUniqueId());
        BanPickPhase phase = banPickManager.getCurrentPhase();
        boolean canVote = (phase.isHunterTurn() && playerRole == PlayerRole.HUNTER) ||
                         (phase.isSpeedrunnerTurn() && playerRole == PlayerRole.SPEEDRUNNER);
        
        // 按分类排列物品
        int slot = ITEMS_START_SLOT;
        for (BanPickCategory category : BanPickCategory.values()) {
            BanPickItem[] categoryItems = BanPickItem.getByCategory(category);
            if (categoryItems.length == 0) continue;
            
            for (BanPickItem item : categoryItems) {
                if (slot > ITEMS_END_SLOT) break;
                
                ItemStack itemStack = createBanPickItemStack(item, canVote, player);
                inventory.setItem(slot, itemStack);
                slot++;
            }
        }
    }
    
    /**
     * 创建Ban Pick物品展示
     */
    private ItemStack createBanPickItemStack(BanPickItem item, boolean canVote, Player player) {
        Material material = item.getMaterial();
        String title;
        List<String> lore = new ArrayList<>();
        
        // 检查物品状态
        boolean isHunterBanned = banPickManager.getHunterBannedItems().contains(item);
        boolean isSpeedrunnerBanned = banPickManager.getSpeedrunnerBannedItems().contains(item);
        boolean isHunterPicked = banPickManager.getHunterPickedItems().contains(item);
        boolean isSpeedrunnerPicked = banPickManager.getSpeedrunnerPickedItems().contains(item);
        boolean isPlayerVoted = item.equals(banPickManager.getPlayerVote(player.getUniqueId()));
        
        // 设置标题和状态
        if (isHunterBanned) {
            title = "<red>❌ " + item.getDisplayName();
            lore.add("<red>捕猎者已禁用（速通者不能合成）");
        } else if (isSpeedrunnerBanned) {
            title = "<red>❌ " + item.getDisplayName();
            lore.add("<red>速通者已禁用（捕猎者不能合成）");
        } else if (isHunterPicked) {
            title = "<blue>✓ " + item.getDisplayName();
            lore.add("<blue>捕猎者已锁定");
        } else if (isSpeedrunnerPicked) {
            title = "<green>✓ " + item.getDisplayName();
            lore.add("<green>速通者已锁定");
        } else if (isPlayerVoted) {
            title = "<yellow>👆 " + item.getDisplayName();
            lore.add("<yellow>你已投票此物品");
        } else {
            title = "<white>" + item.getDisplayName();
        }
        
        // 添加描述
        lore.add("<gray>" + item.getDescription());
        lore.add("");
        lore.add("<gray>分类: <white>" + item.getCategory().getDisplayName());
        
        // 添加操作提示
        boolean isAnyBanned = isHunterBanned || isSpeedrunnerBanned;
        if (canVote && !isAnyBanned && !isHunterPicked && !isSpeedrunnerPicked) {
            BanPickPhase phase = banPickManager.getCurrentPhase();
            String action = phase.isBanPhase() ? "禁用" : "锁定";
            lore.add("");
            lore.add("<green>点击投票" + action + "此物品");
        } else if (!canVote) {
            lore.add("");
            lore.add("<gray>现在不是你的回合");
        } else {
            lore.add("");
            lore.add("<gray>此物品不可选择");
        }
        
        return createItem(material, title, lore);
    }
    
    /**
     * 添加结果显示区域
     */
    private void addResultDisplay(Inventory inventory) {
        // 在右侧显示当前Ban Pick结果
        // 这里可以添加一个总结面板，显示已经Ban和Pick的物品
    }
    
    /**
     * 创建物品
     */
    private ItemStack createItem(Material material, String name, List<String> lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            meta.displayName(ComponentUtil.parse(name));
            
            if (!lore.isEmpty()) {
                List<net.kyori.adventure.text.Component> componentLore = new ArrayList<>();
                for (String line : lore) {
                    componentLore.add(ComponentUtil.parse(line));
                }
                meta.lore(componentLore);
            }
            
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * 获取GUI标题
     */
    private String getGUITitle() {
        BanPickPhase phase = banPickManager.getCurrentPhase();
        return "<gold>Ban Pick - " + phase.getDisplayName();
    }
    
    /**
     * 处理GUI点击事件
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;

        Player player = (Player) event.getWhoClicked();
        Inventory clickedInventory = event.getClickedInventory();

        // 检查是否是Ban Pick GUI - 通过玩家记录判断
        if (clickedInventory == null) return;

        // 检查玩家是否有Ban Pick GUI记录
        if (!playerInventories.containsKey(player.getUniqueId())) return;

        // 检查点击的是否是玩家的Ban Pick GUI
        Inventory playerGUI = playerInventories.get(player.getUniqueId());
        if (clickedInventory != playerGUI) return;

        // 阻止所有点击操作
        event.setCancelled(true);

        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) return;

        // 查找对应的Ban Pick物品
        BanPickItem banPickItem = findBanPickItemByMaterial(clickedItem.getType());
        if (banPickItem != null) {
            // 处理投票
            boolean success = banPickManager.vote(player, banPickItem);
            if (success) {
                // 延迟更新GUI，避免与其他更新冲突
                Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    if (player.isOnline()) {
                        updateGUI(player);
                    }
                }, 1L);
            }
        }
    }
    
    /**
     * 处理GUI关闭事件
     */
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) return;

        Player player = (Player) event.getPlayer();
        UUID playerId = player.getUniqueId();

        // 检查是否是Ban Pick GUI
        if (!playerInventories.containsKey(playerId)) return;

        // 检查Ban Pick是否还在进行中
        BanPickPhase phase = banPickManager.getCurrentPhase();
        if (phase == BanPickPhase.COMPLETED || phase == BanPickPhase.WAITING) {
            // Ban Pick已完成，移除GUI记录
            playerInventories.remove(playerId);
            return;
        }

        // Ban Pick还在进行中，延迟重新打开GUI
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            if (player.isOnline() && banPickManager.getCurrentPhase() != BanPickPhase.COMPLETED) {
                // 重新打开GUI，但不移除记录
                Inventory newInventory = createGUI(player);
                playerInventories.put(playerId, newInventory);
                player.openInventory(newInventory);
            } else {
                // 玩家离线或Ban Pick已完成，移除记录
                playerInventories.remove(playerId);
            }
        }, 2L); // 延迟2 tick，避免冲突
    }
    
    /**
     * 根据材料查找Ban Pick物品
     */
    private BanPickItem findBanPickItemByMaterial(Material material) {
        return BanPickItem.fromMaterial(material);
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        playerInventories.clear();
    }
}
