package com.projectSource.ultimateManhurt.game.rules;

import com.projectSource.ultimateManhurt.game.PlayerRole;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;

/**
 * 胜利条件枚举
 */
public enum WinCondition {
    SPEEDRUNNER_KILL_DRAGON("速通者击败末影龙", "速通者在时间限制内击败了末影龙", PlayerRole.SPEEDRUNNER),
    SPEEDRUNNER_SCORE_VICTORY("速通者积分胜利", "速通者通过完成里程碑达到目标分数", PlayerRole.SPEEDRUNNER),
    HUNTER_TIMEOUT("时间到", "时间耗尽，捕猎者获胜", PlayerRole.HUNTER),
    HUNTER_KILL_SPEEDRUNNER("捕猎者击杀速通者", "所有速通者被击败", PlayerRole.HUNTER),
    SPEEDRUNNER_SURVIVE("速通者存活", "速通者成功存活到时间结束", PlayerRole.SPEEDRUNNER),
    HUNTER_KILL_WITHER("捕猎者击杀凋零", "捕猎者成功击败了守卫凋零", PlayerRole.HUNTER),
    SPEEDRUNNER_GUARD_SUCCESS("速通者守卫成功", "速通者成功守卫凋零到时间结束", PlayerRole.SPEEDRUNNER),
    SPEEDRUNNER_WIN_SURRENDER("速通者投降胜利", "捕猎者阵营投降，速通者获胜", PlayerRole.SPEEDRUNNER),
    HUNTER_WIN_SURRENDER("捕猎者投降胜利", "速通者阵营投降，捕猎者获胜", PlayerRole.HUNTER),
    ADMIN_END("管理员结束", "游戏被管理员强制结束", null),
    ROOM_DISBANDED("房间解散", "房间被解散，游戏结束", null);
    
    private final String displayName;
    private final String description;
    private final PlayerRole winner;
    
    WinCondition(String displayName, String description, PlayerRole winner) {
        this.displayName = displayName;
        this.description = description;
        this.winner = winner;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public PlayerRole getWinner() {
        return winner;
    }
    
    /**
     * 检查是否有获胜者
     */
    public boolean hasWinner() {
        return winner != null;
    }
    
    /**
     * 获取胜利消息组件
     */
    public Component getWinMessage() {
        if (winner == null) {
            return Component.text(description, NamedTextColor.GRAY);
        }
        
        return Component.text()
                .append(winner.getDisplayComponent())
                .append(Component.text(" 获胜！", NamedTextColor.GOLD))
                .append(Component.newline())
                .append(Component.text(description, NamedTextColor.WHITE))
                .build();
    }
    
    /**
     * 获取失败消息组件
     */
    public Component getLoseMessage() {
        if (winner == null) {
            return Component.text("游戏结束", NamedTextColor.GRAY);
        }
        
        PlayerRole loser = winner.getOpposite();
        return Component.text()
                .append(loser.getDisplayComponent())
                .append(Component.text(" 失败！", NamedTextColor.RED))
                .append(Component.newline())
                .append(Component.text(description, NamedTextColor.WHITE))
                .build();
    }
    
    /**
     * 检查是否为正常游戏结束
     */
    public boolean isNormalEnd() {
        return this != ADMIN_END && this != ROOM_DISBANDED;
    }
    
    /**
     * 检查是否为速通者胜利
     */
    public boolean isSpeedrunnerWin() {
        return winner == PlayerRole.SPEEDRUNNER;
    }
    
    /**
     * 检查是否为捕猎者胜利
     */
    public boolean isHunterWin() {
        return winner == PlayerRole.HUNTER;
    }
}
