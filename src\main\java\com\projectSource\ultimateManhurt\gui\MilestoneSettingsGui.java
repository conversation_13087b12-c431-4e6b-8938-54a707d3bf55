package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.scoring.MilestoneSettings;
import com.projectSource.ultimateManhurt.game.scoring.ScoreMilestone;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 里程碑设置GUI
 * 允许房主配置每个里程碑的开关状态和分数
 */
public class MilestoneSettingsGui extends BaseGui {
    
    private final Room room;
    private final MilestoneSettings milestoneSettings;
    private int currentPage = 0;
    private static final int MILESTONES_PER_PAGE = 28; // 每页显示的里程碑数量
    
    public MilestoneSettingsGui(UltimateManhurt plugin, Player player, Room room) {
        super(plugin, player, "<gold><bold>里程碑设置 - " + (room != null ? room.getName() : "未知房间"), 54);
        this.room = room;
        this.milestoneSettings = room != null ? room.getSettings().getMilestoneSettings() : null;
        setupGui();
    }
    
    @Override
    protected void setupGui() {
        // 检查房间是否存在
        if (room == null || milestoneSettings == null) {
            setItem(22, createItem(Material.BARRIER, "<red>房间不存在", "<gray>房间可能已被删除"));
            return;
        }

        // 检查权限
        if (!room.isOwner(player.getUniqueId())) {
            setItem(22, createItem(Material.BARRIER, "<red>权限不足", "<gray>只有房主可以修改里程碑设置"));
            return;
        }

        // 填充边框
        fillBorder(Material.GRAY_STAINED_GLASS_PANE);

        // 设置标题信息
        setupHeader();

        // 显示里程碑列表
        setupMilestones();

        // 设置控制按钮
        setupControlButtons();
    }
    
    /**
     * 设置标题信息
     */
    private void setupHeader() {
        setItem(4, createItem(Material.BOOK, "<gold><bold>里程碑设置",
            "<gray>配置游戏中的里程碑开关和分数",
            "<gray>启用里程碑: <white>" + milestoneSettings.getEnabledMilestoneCount() + "/" + ScoreMilestone.values().length,
            "<gray>总可能分数: <white>" + milestoneSettings.getTotalPossibleScore()));
    }
    
    /**
     * 设置里程碑列表
     */
    private void setupMilestones() {
        ScoreMilestone[] milestones = ScoreMilestone.values();
        int startIndex = currentPage * MILESTONES_PER_PAGE;
        int endIndex = Math.min(startIndex + MILESTONES_PER_PAGE, milestones.length);
        
        // 显示当前页的里程碑
        for (int i = startIndex; i < endIndex; i++) {
            ScoreMilestone milestone = milestones[i];
            int slot = getMilestoneSlot(i - startIndex);
            if (slot != -1) {
                setItem(slot, createMilestoneItem(milestone));
            }
        }
    }
    
    /**
     * 获取里程碑在GUI中的槽位
     */
    private int getMilestoneSlot(int index) {
        // 使用中间区域显示里程碑 (排除边框)
        int[] availableSlots = {
            10, 11, 12, 13, 14, 15, 16,
            19, 20, 21, 22, 23, 24, 25,
            28, 29, 30, 31, 32, 33, 34,
            37, 38, 39, 40, 41, 42, 43
        };
        
        return index < availableSlots.length ? availableSlots[index] : -1;
    }
    
    /**
     * 创建里程碑物品
     */
    private ItemStack createMilestoneItem(ScoreMilestone milestone) {
        boolean enabled = milestoneSettings.isMilestoneEnabled(milestone);
        int currentScore = milestoneSettings.getMilestoneScore(milestone);
        int defaultScore = milestone.getDefaultPoints();
        
        List<String> lore = new ArrayList<>();
        lore.add("<gray>描述: <white>" + milestone.getDescription());
        lore.add("<gray>难度: <white>" + milestone.getDifficulty().getDisplayName());
        lore.add("");
        lore.add("<gray>状态: " + (enabled ? "<green>启用" : "<red>禁用"));
        lore.add("<gray>当前分数: <white>" + currentScore);
        lore.add("<gray>默认分数: <white>" + defaultScore);
        
        if (currentScore != defaultScore) {
            lore.add("<yellow>⚠ 已自定义分数");
        }
        
        lore.add("");
        lore.add("<yellow>左键: 切换启用状态");
        lore.add("<yellow>右键: 修改分数");
        lore.add("<yellow>Shift+右键: 重置为默认分数");
        
        // 根据状态选择材料
        Material material = enabled ? Material.EMERALD : Material.REDSTONE;
        
        return createItem(material, 
            (enabled ? "<green>" : "<red>") + milestone.getDisplayName(),
            lore.toArray(new String[0]));
    }
    
    /**
     * 设置控制按钮
     */
    private void setupControlButtons() {
        // 全部启用
        setItem(45, createItem(Material.LIME_DYE, "<green><bold>全部启用",
            "<gray>启用所有里程碑",
            "<yellow>点击启用所有里程碑"));
        
        // 全部禁用
        setItem(46, createItem(Material.GRAY_DYE, "<red><bold>全部禁用",
            "<gray>禁用所有里程碑",
            "<yellow>点击禁用所有里程碑"));
        
        // 重置为默认
        setItem(47, createItem(Material.CLOCK, "<blue><bold>重置默认",
            "<gray>重置所有里程碑为默认设置",
            "<yellow>点击重置为默认设置"));
        
        // 分页控制
        ScoreMilestone[] milestones = ScoreMilestone.values();
        int totalPages = (milestones.length + MILESTONES_PER_PAGE - 1) / MILESTONES_PER_PAGE;
        
        if (totalPages > 1) {
            // 上一页
            if (currentPage > 0) {
                setItem(48, createItem(Material.ARROW, "<yellow><bold>上一页",
                    "<gray>第 " + currentPage + "/" + totalPages + " 页",
                    "<yellow>点击查看上一页"));
            }
            
            // 下一页
            if (currentPage < totalPages - 1) {
                setItem(50, createItem(Material.ARROW, "<yellow><bold>下一页",
                    "<gray>第 " + (currentPage + 2) + "/" + totalPages + " 页",
                    "<yellow>点击查看下一页"));
            }
        }
        
        // 返回按钮
        setItem(53, createItem(Material.BARRIER, "<red><bold>返回",
            "<gray>返回房间设置",
            "<yellow>点击返回"));
    }
    
    @Override
    public void handleClick(InventoryClickEvent event) {
        event.setCancelled(true);
        
        if (room == null || milestoneSettings == null) {
            return;
        }
        
        // 检查权限
        if (!room.isOwner(player.getUniqueId())) {
            sendError("只有房主可以修改里程碑设置！");
            playErrorSound();
            return;
        }
        
        int slot = event.getSlot();
        boolean isLeftClick = event.isLeftClick();
        boolean isRightClick = event.isRightClick();
        boolean isShiftClick = event.isShiftClick();
        
        playClickSound();
        
        // 处理里程碑点击
        ScoreMilestone clickedMilestone = getMilestoneFromSlot(slot);
        if (clickedMilestone != null) {
            handleMilestoneClick(clickedMilestone, isLeftClick, isRightClick, isShiftClick);
            return;
        }
        
        // 处理控制按钮
        switch (slot) {
            case 45: // 全部启用
                handleEnableAll();
                break;
            case 46: // 全部禁用
                handleDisableAll();
                break;
            case 47: // 重置默认
                handleResetDefaults();
                break;
            case 48: // 上一页
                handlePreviousPage();
                break;
            case 50: // 下一页
                handleNextPage();
                break;
            case 53: // 返回
                handleBack();
                break;
        }
    }
    
    /**
     * 从槽位获取里程碑
     */
    private ScoreMilestone getMilestoneFromSlot(int slot) {
        int[] availableSlots = {
            10, 11, 12, 13, 14, 15, 16,
            19, 20, 21, 22, 23, 24, 25,
            28, 29, 30, 31, 32, 33, 34,
            37, 38, 39, 40, 41, 42, 43
        };
        
        for (int i = 0; i < availableSlots.length; i++) {
            if (availableSlots[i] == slot) {
                int milestoneIndex = currentPage * MILESTONES_PER_PAGE + i;
                ScoreMilestone[] milestones = ScoreMilestone.values();
                if (milestoneIndex < milestones.length) {
                    return milestones[milestoneIndex];
                }
                break;
            }
        }
        return null;
    }
    
    /**
     * 处理里程碑点击
     */
    private void handleMilestoneClick(ScoreMilestone milestone, boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        if (isLeftClick) {
            // 切换启用状态
            boolean newState = !milestoneSettings.isMilestoneEnabled(milestone);
            milestoneSettings.setMilestoneEnabled(milestone, newState);
            sendSuccess((newState ? "启用" : "禁用") + "了里程碑: " + milestone.getDisplayName());
            refresh();
        } else if (isRightClick && isShiftClick) {
            // 重置为默认分数
            milestoneSettings.resetMilestoneScore(milestone);
            sendSuccess("已重置 " + milestone.getDisplayName() + " 为默认分数: " + milestone.getDefaultPoints());
            refresh();
        } else if (isRightClick) {
            // 修改分数 - 这里可以实现聊天输入或者简单的增减
            handleScoreModification(milestone);
        }
    }
    
    /**
     * 处理分数修改
     */
    private void handleScoreModification(ScoreMilestone milestone) {
        // 简单的分数修改：每次点击增加10分，Shift点击减少10分
        int currentScore = milestoneSettings.getMilestoneScore(milestone);
        int newScore = currentScore + 10;
        
        milestoneSettings.setMilestoneScore(milestone, newScore);
        sendSuccess("已修改 " + milestone.getDisplayName() + " 分数为: " + newScore);
        refresh();
    }
    
    /**
     * 启用所有里程碑
     */
    private void handleEnableAll() {
        for (ScoreMilestone milestone : ScoreMilestone.values()) {
            milestoneSettings.setMilestoneEnabled(milestone, true);
        }
        sendSuccess("已启用所有里程碑");
        refresh();
    }
    
    /**
     * 禁用所有里程碑
     */
    private void handleDisableAll() {
        for (ScoreMilestone milestone : ScoreMilestone.values()) {
            milestoneSettings.setMilestoneEnabled(milestone, false);
        }
        sendSuccess("已禁用所有里程碑");
        refresh();
    }
    
    /**
     * 重置为默认设置
     */
    private void handleResetDefaults() {
        milestoneSettings.resetToDefaults();
        sendSuccess("已重置所有里程碑为默认设置");
        refresh();
    }
    
    /**
     * 上一页
     */
    private void handlePreviousPage() {
        if (currentPage > 0) {
            currentPage--;
            refresh();
        }
    }
    
    /**
     * 下一页
     */
    private void handleNextPage() {
        ScoreMilestone[] milestones = ScoreMilestone.values();
        int totalPages = (milestones.length + MILESTONES_PER_PAGE - 1) / MILESTONES_PER_PAGE;
        
        if (currentPage < totalPages - 1) {
            currentPage++;
            refresh();
        }
    }
    
    /**
     * 返回房间设置
     */
    private void handleBack() {
        close();
        plugin.getGuiManager().openRoomSettingsGui(player, room);
    }
    
    // 工具方法
    public void sendSuccess(String message) {
        ComponentUtil.sendMessage(player, ComponentUtil.success(message));
    }
    
    public void sendError(String message) {
        ComponentUtil.sendMessage(player, ComponentUtil.error(message));
    }
    
    public void playClickSound() {
        player.playSound(player.getLocation(), org.bukkit.Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
    }
    
    public void playErrorSound() {
        player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
    }
    
    public void playSuccessSound() {
        player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.0f);
    }
}
