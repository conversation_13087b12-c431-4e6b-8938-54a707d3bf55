package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.room.RoomSettings;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

import java.util.Random;

/**
 * 种子设置专用GUI
 */
public class SeedSettingsGui extends BaseGui {
    
    private final Room room;
    private final RoomSettings settings;
    private StringBuilder currentSeed;
    private boolean isNegative;
    
    // 预设的经典种子
    private static final long[] PRESET_SEEDS = {
        404, // 经典种子
        666, // 地狱种子
        888, // 幸运种子
        1234567890L, // 简单数字
        -1234567890L, // 负数种子
        System.currentTimeMillis() / 1000000, // 基于时间的种子
    };
    
    private static final String[] PRESET_NAMES = {
        "经典404",
        "地狱666", 
        "幸运888",
        "简单数字",
        "负数种子",
        "时间种子"
    };

    public SeedSettingsGui(UltimateManhurt plugin, Player player, Room room) {
        super(plugin, player, "世界种子设置", 54);
        this.room = room;
        this.settings = room.getSettings();
        this.currentSeed = new StringBuilder();
        this.isNegative = false;
        setupGui();
    }

    @Override
    protected void setupGui() {
        // 填充边框
        fillBorder(Material.GRAY_STAINED_GLASS_PANE);

        // 设置数字按钮 (0-9)
        setupNumberButtons();

        // 设置功能按钮
        setupFunctionButtons();

        // 设置预设种子按钮
        setupPresetButtons();

        // 设置控制按钮
        setupControlButtons();

        // 显示当前输入
        updateCurrentSeedDisplay();
    }
    
    /**
     * 设置数字按钮 (0-9)
     * 布局在左侧区域
     */
    private void setupNumberButtons() {
        // 第一行: 1 2 3 (slots 10, 11, 12)
        setItem(10, createNumberButton(1));
        setItem(11, createNumberButton(2));
        setItem(12, createNumberButton(3));

        // 第二行: 4 5 6 (slots 19, 20, 21)
        setItem(19, createNumberButton(4));
        setItem(20, createNumberButton(5));
        setItem(21, createNumberButton(6));

        // 第三行: 7 8 9 (slots 28, 29, 30)
        setItem(28, createNumberButton(7));
        setItem(29, createNumberButton(8));
        setItem(30, createNumberButton(9));

        // 第四行: ± 0 ← (slots 37, 38, 39)
        setItem(38, createNumberButton(0));
    }
    
    /**
     * 创建数字按钮
     */
    private ItemStack createNumberButton(int number) {
        return createItem(Material.STONE_BUTTON, 
            "<yellow><bold>" + number,
            "<gray>点击输入数字 " + number,
            "<gray>当前输入: <white>" + getCurrentSeedString());
    }
    
    /**
     * 设置功能按钮
     * 布局在数字按钮周围
     */
    private void setupFunctionButtons() {
        // 正负号按钮 (slot 37)
        setItem(37, createItem(Material.REDSTONE,
            "<red><bold>±",
            "<gray>切换正负号",
            "<gray>当前: <white>" + (isNegative ? "负数" : "正数")));

        // 退格按钮 (slot 39)
        setItem(39, createItem(Material.ARROW,
            "<yellow><bold>退格",
            "<gray>删除最后一位数字",
            "<gray>撤销上一次输入"));

        // 清空按钮 (slot 47)
        setItem(47, createItem(Material.BARRIER,
            "<red><bold>清空",
            "<gray>清空当前输入",
            "<gray>重新开始输入"));
    }
    
    /**
     * 设置预设种子按钮
     * 布局在右侧区域，避免与数字按钮冲突
     */
    private void setupPresetButtons() {
        // 右侧预设种子区域 (slots 14, 15, 16, 23, 24, 25)
        int[] presetSlots = {14, 15, 16, 23, 24, 25};

        for (int i = 0; i < Math.min(PRESET_SEEDS.length, presetSlots.length); i++) {
            setItem(presetSlots[i], createItem(Material.EMERALD,
                "<green><bold>" + PRESET_NAMES[i],
                "<gray>种子值: <white>" + PRESET_SEEDS[i],
                "<gray>点击使用此预设种子"));
        }
    }
    
    /**
     * 设置控制按钮
     * 布局在底部区域
     */
    private void setupControlButtons() {
        // 随机种子按钮 (slot 46)
        setItem(46, createItem(Material.ENDER_PEARL,
            "<purple><bold>随机种子",
            "<gray>生成一个随机种子",
            "<gray>每次点击都会生成不同的种子"));

        // 基于玩家名的种子 (slot 48)
        setItem(48, createItem(Material.PLAYER_HEAD,
            "<blue><bold>玩家种子",
            "<gray>基于你的用户名生成种子",
            "<gray>种子值: <white>" + player.getName().hashCode()));

        // 确认按钮 (slot 49)
        setItem(49, createItem(Material.LIME_CONCRETE,
            "<green><bold>确认设置",
            "<gray>使用当前输入的种子",
            "<gray>当前种子: <white>" + getCurrentSeedString()));

        // 返回按钮 (slot 53)
        setItem(53, createItem(Material.RED_CONCRETE,
            "<red><bold>返回",
            "<gray>返回房间设置"));
    }
    
    /**
     * 更新当前种子显示
     */
    private void updateCurrentSeedDisplay() {
        // 更新显示当前输入的物品
        setItem(4, createItem(Material.WHEAT_SEEDS,
            "<yellow><bold>当前输入",
            "<gray>种子值: <white>" + getCurrentSeedString(),
            "<gray>长度: <white>" + currentSeed.length() + " 位",
            "",
            "<yellow>提示:",
            "<gray>• 种子可以是任意数字",
            "<gray>• 0 表示随机种子",
            "<gray>• 支持负数种子"));
    }
    
    /**
     * 获取当前种子字符串
     */
    private String getCurrentSeedString() {
        if (currentSeed.length() == 0) {
            return "0";
        }
        return (isNegative ? "-" : "") + currentSeed.toString();
    }
    
    /**
     * 获取当前种子数值
     */
    private long getCurrentSeedValue() {
        if (currentSeed.length() == 0) {
            return 0;
        }
        try {
            long value = Long.parseLong(currentSeed.toString());
            return isNegative ? -value : value;
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    /**
     * 检查是否为预设种子按钮的slot
     */
    private boolean isPresetSeedSlot(int slot) {
        int[] presetSlots = {14, 15, 16, 23, 24, 25};
        for (int presetSlot : presetSlots) {
            if (slot == presetSlot) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据slot获取预设种子的索引
     */
    private int getPresetSeedIndex(int slot) {
        int[] presetSlots = {14, 15, 16, 23, 24, 25};
        for (int i = 0; i < presetSlots.length; i++) {
            if (slot == presetSlots[i]) {
                return i;
            }
        }
        return 0; // 默认返回第一个
    }

    @Override
    public void handleClick(InventoryClickEvent event) {
        event.setCancelled(true);

        if (event.getClickedInventory() != inventory) {
            return;
        }

        int slot = event.getSlot();
        ItemStack item = event.getCurrentItem();

        if (item == null || item.getType() == Material.AIR) {
            return;
        }

        playClickSound();

        // 处理不同的按钮点击
        if (slot >= 10 && slot <= 12) {
            // 第一行数字 1-3
            handleNumberInput(slot - 9);
        } else if (slot >= 19 && slot <= 21) {
            // 第二行数字 4-6
            handleNumberInput(slot - 15);
        } else if (slot >= 28 && slot <= 30) {
            // 第三行数字 7-9
            handleNumberInput(slot - 21);
        } else if (slot == 38) {
            // 数字 0
            handleNumberInput(0);
        } else if (slot == 37) {
            // 正负号切换
            handleSignToggle();
        } else if (slot == 39) {
            // 退格
            handleBackspace();
        } else if (slot == 47) {
            // 清空
            handleClear();
        } else if (isPresetSeedSlot(slot)) {
            // 预设种子
            handlePresetSeed(getPresetSeedIndex(slot));
        } else if (slot == 46) {
            // 随机种子
            handleRandomSeed();
        } else if (slot == 48) {
            // 玩家种子
            handlePlayerSeed();
        } else if (slot == 49) {
            // 确认设置
            handleConfirm();
        } else if (slot == 53) {
            // 返回
            handleBack();
        }
    }
    
    /**
     * 处理数字输入
     */
    private void handleNumberInput(int number) {
        if (currentSeed.length() < 18) { // 限制长度防止溢出
            currentSeed.append(number);
            updateCurrentSeedDisplay();
            setupControlButtons(); // 更新确认按钮显示
        } else {
            sendError("种子长度已达到最大限制！");
            playErrorSound();
        }
    }
    
    /**
     * 处理正负号切换
     */
    private void handleSignToggle() {
        isNegative = !isNegative;
        updateCurrentSeedDisplay();
        setupFunctionButtons(); // 更新正负号按钮显示
        setupControlButtons(); // 更新确认按钮显示
    }
    
    /**
     * 处理清空
     */
    private void handleClear() {
        currentSeed.setLength(0);
        isNegative = false;
        updateCurrentSeedDisplay();
        setupFunctionButtons();
        setupControlButtons();
        sendInfo("已清空输入");
    }
    
    /**
     * 处理退格
     */
    private void handleBackspace() {
        if (currentSeed.length() > 0) {
            currentSeed.setLength(currentSeed.length() - 1);
            updateCurrentSeedDisplay();
            setupControlButtons();
            sendInfo("已删除最后一位");
        } else {
            sendError("没有可删除的内容！");
            playErrorSound();
        }
    }
    
    /**
     * 处理预设种子
     */
    private void handlePresetSeed(int index) {
        long seed = PRESET_SEEDS[index];
        settings.setWorldSeed(seed);
        sendSuccess("已设置为预设种子: " + PRESET_NAMES[index] + " (" + seed + ")");
        handleBack();
    }
    
    /**
     * 处理随机种子
     */
    private void handleRandomSeed() {
        Random random = new Random();
        long seed = random.nextLong();
        settings.setWorldSeed(seed);
        sendSuccess("已设置为随机种子: " + seed);
        handleBack();
    }
    
    /**
     * 处理玩家种子
     */
    private void handlePlayerSeed() {
        long seed = player.getName().hashCode();
        settings.setWorldSeed(seed);
        sendSuccess("已设置为玩家种子: " + seed + " (基于用户名: " + player.getName() + ")");
        handleBack();
    }
    
    /**
     * 处理确认设置
     */
    private void handleConfirm() {
        long seed = getCurrentSeedValue();
        settings.setWorldSeed(seed);
        String seedText = seed == 0 ? "随机" : String.valueOf(seed);
        sendSuccess("世界种子已设置为: " + seedText);
        handleBack();
    }
    
    /**
     * 处理返回
     */
    private void handleBack() {
        plugin.getGuiManager().openRoomSettingsGui(player, room);
    }
}
