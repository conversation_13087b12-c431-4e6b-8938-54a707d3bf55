package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.kit.StartKit;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;

/**
 * 装备包复制GUI
 * 允许房主在速通者和捕猎者之间复制装备包
 */
public class StartKitCopyGui extends BaseGui {
    
    private final Room room;
    
    public StartKitCopyGui(UltimateManhurt plugin, Player player, Room room) {
        super(plugin, player, "<gold><bold>装备包复制", 27);
        this.room = room;
        setupGui();
    }
    
    @Override
    protected void setupGui() {
        // 检查权限
        if (!room.isOwner(player.getUniqueId())) {
            setItem(13, createItem(Material.BARRIER, "<red>权限不足", 
                "<gray>只有房主可以管理装备包"));
            return;
        }
        
        // 标题信息
        setItem(4, createItem(Material.CHEST, "<gold><bold>装备包复制",
            "<gray>在速通者和捕猎者之间复制装备包",
            "<gray>选择复制方向"));
        
        // 速通者装备包信息
        StartKit speedrunnerKit = room.getSettings().getSpeedrunnerKit();
        String speedrunnerKitName = speedrunnerKit != null ? speedrunnerKit.getName() : "默认装备包";
        int speedrunnerItemCount = speedrunnerKit != null ? speedrunnerKit.getItems().size() : 0;
        
        setItem(10, createItem(Material.DIAMOND_SWORD, "<green><bold>速通者装备包",
            "<gray>当前装备包: <white>" + speedrunnerKitName,
            "<gray>物品数量: <white>" + speedrunnerItemCount,
            "<gray>描述: <white>" + (speedrunnerKit != null ? speedrunnerKit.getDescription() : "默认速通者装备"),
            "",
            "<yellow>Shift+点击预览装备包"));
        
        // 捕猎者装备包信息
        StartKit hunterKit = room.getSettings().getHunterKit();
        String hunterKitName = hunterKit != null ? hunterKit.getName() : "默认装备包";
        int hunterItemCount = hunterKit != null ? hunterKit.getItems().size() : 0;
        
        setItem(16, createItem(Material.BOW, "<red><bold>捕猎者装备包",
            "<gray>当前装备包: <white>" + hunterKitName,
            "<gray>物品数量: <white>" + hunterItemCount,
            "<gray>描述: <white>" + (hunterKit != null ? hunterKit.getDescription() : "默认捕猎者装备"),
            "",
            "<yellow>Shift+点击预览装备包"));
        
        // 复制方向选择
        setItem(12, createItem(Material.ARROW, "<blue><bold>复制到捕猎者",
            "<gray>将速通者装备包复制给捕猎者",
            "<gray>源: <green>" + speedrunnerKitName,
            "<gray>目标: <red>捕猎者装备包",
            "",
            "<yellow>点击执行复制",
            "<red>注意: 这将覆盖捕猎者的当前装备包"));
        
        setItem(14, createItem(Material.ARROW, "<blue><bold>复制到速通者",
            "<gray>将捕猎者装备包复制给速通者",
            "<gray>源: <red>" + hunterKitName,
            "<gray>目标: <green>速通者装备包",
            "",
            "<yellow>点击执行复制",
            "<red>注意: 这将覆盖速通者的当前装备包"));
        
        // 双向同步
        setItem(13, createItem(Material.COMPARATOR, "<purple><bold>智能同步",
            "<gray>根据装备包复杂度自动选择复制方向",
            "<gray>将物品更多的装备包复制给物品较少的",
            "",
            "<yellow>点击智能同步",
            "<gray>当前: " + speedrunnerItemCount + " vs " + hunterItemCount + " 物品"));
        
        // 返回按钮
        setItem(22, createItem(Material.ARROW, "<red><bold>返回",
            "<gray>返回装备包管理",
            "<yellow>点击返回"));
        
        // 填充边框
        fillBorder(Material.GRAY_STAINED_GLASS_PANE);
    }
    
    @Override
    public void handleClick(InventoryClickEvent event) {
        event.setCancelled(true);
        
        if (room == null) {
            return;
        }
        
        // 检查权限
        if (!room.isOwner(player.getUniqueId())) {
            sendError("只有房主可以管理装备包！");
            playErrorSound();
            return;
        }
        
        int slot = event.getSlot();
        boolean isShiftClick = event.isShiftClick();
        
        playClickSound();
        
        switch (slot) {
            case 10: // 速通者装备包
                if (isShiftClick) {
                    handlePreviewKit(PlayerRole.SPEEDRUNNER);
                }
                break;
            case 16: // 捕猎者装备包
                if (isShiftClick) {
                    handlePreviewKit(PlayerRole.HUNTER);
                }
                break;
            case 12: // 复制到捕猎者
                handleCopyKit(PlayerRole.SPEEDRUNNER, PlayerRole.HUNTER);
                break;
            case 14: // 复制到速通者
                handleCopyKit(PlayerRole.HUNTER, PlayerRole.SPEEDRUNNER);
                break;
            case 13: // 智能同步
                handleSmartSync();
                break;
            case 22: // 返回
                handleBack();
                break;
        }
    }
    
    /**
     * 预览装备包
     */
    private void handlePreviewKit(PlayerRole role) {
        try {
            StartKit kit = role == PlayerRole.SPEEDRUNNER ? 
                room.getSettings().getSpeedrunnerKit() : 
                room.getSettings().getHunterKit();
            
            if (kit != null) {
                plugin.getStartKitManager().previewKit(player, kit);
                String roleText = role == PlayerRole.SPEEDRUNNER ? "速通者" : "捕猎者";
                sendSuccess("已打开" + roleText + "装备包预览");
            } else {
                sendError("装备包不存在");
            }
        } catch (Exception e) {
            sendError("预览装备包时发生错误");
            playErrorSound();
        }
    }
    
    /**
     * 复制装备包
     */
    private void handleCopyKit(PlayerRole source, PlayerRole target) {
        try {
            StartKit sourceKit = source == PlayerRole.SPEEDRUNNER ? 
                room.getSettings().getSpeedrunnerKit() : 
                room.getSettings().getHunterKit();
            
            if (sourceKit == null) {
                sendError("源装备包不存在");
                playErrorSound();
                return;
            }
            
            // 创建副本
            StartKit copiedKit = new StartKit(sourceKit);
            
            // 更新名称以区分
            String sourceText = source == PlayerRole.SPEEDRUNNER ? "速通者" : "捕猎者";
            String targetText = target == PlayerRole.SPEEDRUNNER ? "速通者" : "捕猎者";
            copiedKit.setName(sourceKit.getName() + " (复制自" + sourceText + ")");
            
            // 应用到目标
            if (target == PlayerRole.SPEEDRUNNER) {
                room.getSettings().setSpeedrunnerKit(copiedKit);
            } else {
                room.getSettings().setHunterKit(copiedKit);
            }
            
            sendSuccess("已将" + sourceText + "装备包复制到" + targetText);
            playSuccessSound();
            refresh();
            
        } catch (Exception e) {
            sendError("复制装备包时发生错误: " + e.getMessage());
            playErrorSound();
            plugin.getLogger().warning("复制装备包时出错: " + e.getMessage());
        }
    }
    
    /**
     * 智能同步
     */
    private void handleSmartSync() {
        try {
            StartKit speedrunnerKit = room.getSettings().getSpeedrunnerKit();
            StartKit hunterKit = room.getSettings().getHunterKit();
            
            if (speedrunnerKit == null && hunterKit == null) {
                sendError("两个装备包都不存在");
                playErrorSound();
                return;
            }
            
            int speedrunnerItems = speedrunnerKit != null ? speedrunnerKit.getItems().size() : 0;
            int hunterItems = hunterKit != null ? hunterKit.getItems().size() : 0;
            
            if (speedrunnerItems == hunterItems) {
                sendError("两个装备包物品数量相同，无法自动选择复制方向");
                playErrorSound();
                return;
            }
            
            // 将物品更多的复制给物品较少的
            if (speedrunnerItems > hunterItems) {
                handleCopyKit(PlayerRole.SPEEDRUNNER, PlayerRole.HUNTER);
                sendSuccess("智能同步: 速通者装备包 → 捕猎者装备包");
            } else {
                handleCopyKit(PlayerRole.HUNTER, PlayerRole.SPEEDRUNNER);
                sendSuccess("智能同步: 捕猎者装备包 → 速通者装备包");
            }
            
        } catch (Exception e) {
            sendError("智能同步时发生错误");
            playErrorSound();
        }
    }
    
    /**
     * 返回装备包管理
     */
    private void handleBack() {
        close();
        plugin.getGuiManager().openStartKitGui(player, room);
    }
    
    // 工具方法
    protected void sendSuccess(String message) {
        ComponentUtil.sendMessage(player, ComponentUtil.success(message));
    }

    protected void sendError(String message) {
        ComponentUtil.sendMessage(player, ComponentUtil.error(message));
    }

    protected void playClickSound() {
        player.playSound(player.getLocation(), org.bukkit.Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
    }

    protected void playErrorSound() {
        player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
    }

    protected void playSuccessSound() {
        player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.0f);
    }
}
