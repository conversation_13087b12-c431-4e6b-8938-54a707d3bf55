package com.projectSource.ultimateManhurt.banpick;

import org.bukkit.Material;

/**
 * Ban Pick物品分类 - 基于实际速通需求
 */
public enum BanPickCategory {

    MOBILITY("移动工具", "移动相关的实用物品", Material.OAK_BOAT),
    DEFENSE("防御装备", "护甲和防御工具", Material.SHIELD),
    COMBAT("战斗武器", "攻击武器和弹药", Material.DIAMOND_SWORD),
    TOOLS("挖掘工具", "镐、斧、锹等工具", Material.DIAMOND_PICKAXE),
    TACTICAL("战术物品", "战术和策略物品", Material.TNT),
    ENCHANTING("附魔相关", "附魔和强化物品", Material.ENCHANTING_TABLE),
    FOOD("食物", "各种食物", Material.GOLDEN_APPLE),
    BREWING("酿造", "药水制作相关", Material.BREWING_STAND);
    
    private final String displayName;
    private final String description;
    private final Material icon;
    
    BanPickCategory(String displayName, String description, Material icon) {
        this.displayName = displayName;
        this.description = description;
        this.icon = icon;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public Material getIcon() {
        return icon;
    }
}
