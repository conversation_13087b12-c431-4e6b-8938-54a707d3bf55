package com.projectSource.ultimateManhurt.game;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.util.Vector;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 指南针追踪系统
 * 负责让捕猎者的指南针指向最近的速通者
 */
public class CompassTracker {
    
    private final UltimateManhurt plugin;
    private final GameSession gameSession;
    private final Map<UUID, BukkitTask> trackerTasks = new ConcurrentHashMap<>();
    private final Map<UUID, UUID> manualTargets = new ConcurrentHashMap<>(); // 捕猎者ID -> 目标速通者ID
    private boolean isActive = false;
    
    public CompassTracker(UltimateManhurt plugin, GameSession gameSession) {
        this.plugin = plugin;
        this.gameSession = gameSession;
    }
    
    /**
     * 启动指南针追踪
     */
    public void startTracking() {
        if (isActive || !gameSession.getRoom().getSettings().isCompassTracking()) {
            return;
        }
        
        isActive = true;

        // 为所有捕猎者启动追踪任务
        for (UUID playerId : gameSession.getRoom().getPlayers()) {
            PlayerRole role = gameSession.getPlayerRole(playerId);
            if (role == PlayerRole.HUNTER) {
                startTrackingForPlayer(playerId);
            }
        }
        
        plugin.getLogger().info("为房间 " + gameSession.getRoom().getName() + " 启动了指南针追踪");
    }
    
    /**
     * 停止指南针追踪
     */
    public void stopTracking() {
        if (!isActive) {
            return;
        }
        
        isActive = false;
        
        // 停止所有追踪任务
        for (BukkitTask task : trackerTasks.values()) {
            if (task != null && !task.isCancelled()) {
                task.cancel();
            }
        }
        trackerTasks.clear();

        // 清理手动目标
        manualTargets.clear();

        // 清理完成，不需要重置指南针
        
        plugin.getLogger().info("为房间 " + gameSession.getRoom().getName() + " 停止了指南针追踪");
    }
    
    /**
     * 为特定玩家启动追踪
     */
    private void startTrackingForPlayer(UUID hunterId) {
        Player hunter = plugin.getServer().getPlayer(hunterId);
        if (hunter == null || !hunter.isOnline()) {
            return;
        }
        
        // 取消现有任务
        BukkitTask existingTask = trackerTasks.get(hunterId);
        if (existingTask != null && !existingTask.isCancelled()) {
            existingTask.cancel();
        }
        
        // 创建新的追踪任务
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                updateCompassForHunter(hunterId);
            }
        }.runTaskTimer(plugin, 0L, 10L); // 每0.5秒更新一次 (10 ticks)
        
        trackerTasks.put(hunterId, task);
    }
    
    /**
     * 更新捕猎者的指南针
     */
    private void updateCompassForHunter(UUID hunterId) {
        Player hunter = plugin.getServer().getPlayer(hunterId);
        if (hunter == null || !hunter.isOnline()) {
            return;
        }
        
        // 检查游戏状态
        if (gameSession.getRoom().getGameState() != GameState.RUNNING) {
            return;
        }
        
        // 检查玩家角色
        PlayerRole role = gameSession.getPlayerRole(hunterId);
        if (role != PlayerRole.HUNTER) {
            return;
        }
        
        // 检查玩家是否手持指南针
        if (!isHoldingCompass(hunter)) {
            return;
        }

        // 查找目标速通者（仅使用手动选择的目标）
        Player targetSpeedrunner = findTargetSpeedrunner(hunter);


        if (targetSpeedrunner != null) {
            updateDirectionDisplay(hunter, targetSpeedrunner);
        } else {
            // 没有目标时显示提示信息
            showNoTargetMessage(hunter);
        }
    }
    
    /**
     * 查找目标速通者（仅使用手动选择的目标）
     */
    private Player findTargetSpeedrunner(Player hunter) {
        UUID hunterId = hunter.getUniqueId();

        // 只使用手动选择的目标
        UUID manualTargetId = manualTargets.get(hunterId);
        if (manualTargetId != null) {
            Player manualTarget = plugin.getServer().getPlayer(manualTargetId);
            if (manualTarget != null && manualTarget.isOnline()) {
                // 验证目标仍然是速通者且在同一世界
                PlayerRole targetRole = gameSession.getPlayerRole(manualTargetId);
                if (targetRole == PlayerRole.SPEEDRUNNER &&
                    hunter.getWorld().equals(manualTarget.getWorld())) {
                    return manualTarget;
                }
            }
            // 手动目标无效，清除目标
            manualTargets.remove(hunterId);
        }

        // 没有手动目标时返回null，不进行自动追踪
        return null;
    }


    
    /**
     * 检查玩家是否手持指南针
     */
    private boolean isHoldingCompass(Player player) {
        ItemStack mainHand = player.getInventory().getItemInMainHand();
        ItemStack offHand = player.getInventory().getItemInOffHand();

        return isCompass(mainHand) || isCompass(offHand);
    }

    /**
     * 检查物品是否为指南针（包括特殊指南针）
     */
    private boolean isCompass(ItemStack item) {
        if (item == null || item.getType() != Material.COMPASS) {
            return false;
        }

        // 接受所有指南针类型，包括特殊的猎人指南针和普通指南针
        return true;
    }

    /**
     * 更新方向显示
     */
    private void updateDirectionDisplay(Player hunter, Player target) {
        try {
            // 确保目标在同一世界
            if (!hunter.getWorld().equals(target.getWorld())) {
                return;
            }

            Location hunterLoc = hunter.getLocation();
            Location targetLoc = target.getLocation();

            // 计算距离
            double distance = hunterLoc.distance(targetLoc);

            // 计算方向
            String direction = getDirectionArrow(hunterLoc, targetLoc, hunter.getLocation().getYaw());

            // 构建ActionBar消息
            String message = String.format("<gold>追踪: <white>%s <gray>| <yellow>%.1fm <gray>| %s",
                target.getName(), distance, direction);

            // 发送ActionBar
            hunter.sendActionBar(ComponentUtil.parse(message));

        } catch (Exception e) {
            plugin.getLogger().warning("更新方向显示时出错: " + e.getMessage());
        }
    }

    /**
     * 显示无目标消息
     */
    private void showNoTargetMessage(Player hunter) {
        hunter.sendActionBar(ComponentUtil.parse("<gray>右键点击指南针选择追踪目标"));
    }

    /**
     * 获取方向箭头
     */
    private String getDirectionArrow(Location from, Location to, float playerYaw) {
        // 计算目标相对于玩家的角度
        Vector direction = to.toVector().subtract(from.toVector()).normalize();
        double targetYaw = Math.toDegrees(Math.atan2(-direction.getX(), direction.getZ()));

        // 标准化角度到0-360度
        if (targetYaw < 0) {
            targetYaw += 360;
        }

        // 计算相对角度（目标角度 - 玩家朝向）
        double relativeYaw = targetYaw - playerYaw;

        // 标准化到-180到180度
        while (relativeYaw > 180) relativeYaw -= 360;
        while (relativeYaw < -180) relativeYaw += 360;

        // 根据相对角度返回箭头 - 确保所有返回值都是MiniMessage格式
        if (relativeYaw >= -22.5 && relativeYaw < 22.5) {
            return "<green>↑</green>"; // 前方
        } else if (relativeYaw >= 22.5 && relativeYaw < 67.5) {
            return "<yellow>↗</yellow>"; // 右前方
        } else if (relativeYaw >= 67.5 && relativeYaw < 112.5) {
            return "<gold>→</gold>"; // 右方
        } else if (relativeYaw >= 112.5 && relativeYaw < 157.5) {
            return "<red>↘</red>"; // 右后方
        } else if (relativeYaw >= 157.5 || relativeYaw < -157.5) {
            return "<dark_red>↓</dark_red>"; // 后方
        } else if (relativeYaw >= -157.5 && relativeYaw < -112.5) {
            return "<red>↙</red>"; // 左后方
        } else if (relativeYaw >= -112.5 && relativeYaw < -67.5) {
            return "<gold>←</gold>"; // 左方
        } else {
            return "<yellow>↖</yellow>"; // 左前方
        }
    }
    
    /**
     * 当玩家加入游戏时调用
     */
    public void onPlayerJoinGame(UUID playerId) {
        if (!isActive) {
            return;
        }
        
        PlayerRole role = gameSession.getPlayerRole(playerId);
        if (role == PlayerRole.HUNTER) {
            startTrackingForPlayer(playerId);

            Player hunter = plugin.getServer().getPlayer(playerId);
            if (hunter != null && hunter.isOnline()) {
                ComponentUtil.sendMessage(hunter, ComponentUtil.info("指南针追踪已启用，右键点击指南针选择追踪目标"));
            }
        }
    }
    
    /**
     * 当玩家离开游戏时调用
     */
    public void onPlayerLeaveGame(UUID playerId) {
        BukkitTask task = trackerTasks.remove(playerId);
        if (task != null && !task.isCancelled()) {
            task.cancel();
        }

        // 清理手动目标
        manualTargets.remove(playerId);
    }
    
    /**
     * 当玩家角色改变时调用
     */
    public void onPlayerRoleChanged(UUID playerId, PlayerRole newRole) {
        if (!isActive) {
            return;
        }
        
        // 停止现有追踪
        onPlayerLeaveGame(playerId);
        
        // 如果新角色是捕猎者，启动追踪
        if (newRole == PlayerRole.HUNTER) {
            onPlayerJoinGame(playerId);
        } else {
            // 如果不是捕猎者，清除手动目标
            manualTargets.remove(playerId);
        }
    }
    
    /**
     * 更新追踪设置
     */
    public void updateSettings() {
        if (!isActive) {
            return;
        }
        
        // 重启追踪以应用新设置
        stopTracking();
        startTracking();
    }
    
    /**
     * 检查追踪是否活跃
     */
    public boolean isActive() {
        return isActive;
    }
    
    /**
     * 获取当前追踪的捕猎者数量
     */
    public int getTrackedHunterCount() {
        return trackerTasks.size();
    }

    /**
     * 切换追踪目标（右键指南针功能）
     */
    public void switchTarget(UUID hunterId) {
        Player hunter = plugin.getServer().getPlayer(hunterId);
        if (hunter == null || !hunter.isOnline()) {
            return;
        }

        // 检查玩家角色
        PlayerRole role = gameSession.getPlayerRole(hunterId);
        if (role != PlayerRole.HUNTER) {
            return;
        }

        // 获取所有可用的速通者
        List<Player> availableSpeedrunners = getAvailableSpeedrunners(hunter);
        if (availableSpeedrunners.isEmpty()) {
            ComponentUtil.sendMessage(hunter, ComponentUtil.error("没有可追踪的速通者"));
            return;
        }

        // 获取当前目标
        UUID currentTargetId = manualTargets.get(hunterId);
        Player currentTarget = null;
        if (currentTargetId != null) {
            currentTarget = plugin.getServer().getPlayer(currentTargetId);
        }

        // 如果没有当前目标，使用第一个可用的速通者作为当前目标
        if (currentTarget == null && !availableSpeedrunners.isEmpty()) {
            currentTarget = availableSpeedrunners.get(0);
        }

        // 找到下一个目标
        Player nextTarget = getNextTarget(availableSpeedrunners, currentTarget);
        if (nextTarget != null) {
            manualTargets.put(hunterId, nextTarget.getUniqueId());

            // 立即更新方向显示
            updateDirectionDisplay(hunter, nextTarget);

            // 发送切换消息
            String targetName = nextTarget.getName();
            double distance = hunter.getLocation().distance(nextTarget.getLocation());
            ComponentUtil.sendMessage(hunter, ComponentUtil.info(
                "追踪目标切换至: " + targetName + " (距离: " + String.format("%.1f", distance) + "m)"));

            // 播放切换音效
            hunter.playSound(hunter.getLocation(), org.bukkit.Sound.UI_BUTTON_CLICK, 1.0f, 1.5f);
        }
    }

    /**
     * 获取所有可用的速通者
     */
    private List<Player> getAvailableSpeedrunners(Player hunter) {
        List<Player> speedrunners = new ArrayList<>();

        for (UUID playerId : gameSession.getRoom().getPlayers()) {
            // 排除捕猎者自己
            if (playerId.equals(hunter.getUniqueId())) {
                continue;
            }

            PlayerRole role = gameSession.getPlayerRole(playerId);
            if (role != PlayerRole.SPEEDRUNNER) {
                continue;
            }

            Player speedrunner = plugin.getServer().getPlayer(playerId);
            if (speedrunner == null || !speedrunner.isOnline()) {
                continue;
            }

            // 检查是否在同一世界
            if (!hunter.getWorld().equals(speedrunner.getWorld())) {
                continue;
            }

            speedrunners.add(speedrunner);
        }

        return speedrunners;
    }

    /**
     * 获取下一个目标（循环切换）
     */
    private Player getNextTarget(List<Player> availableSpeedrunners, Player currentTarget) {
        if (availableSpeedrunners.isEmpty()) {
            return null;
        }

        if (currentTarget == null) {
            return availableSpeedrunners.get(0);
        }

        // 找到当前目标在列表中的位置
        int currentIndex = -1;
        for (int i = 0; i < availableSpeedrunners.size(); i++) {
            if (availableSpeedrunners.get(i).getUniqueId().equals(currentTarget.getUniqueId())) {
                currentIndex = i;
                break;
            }
        }

        // 返回下一个目标（循环）
        int nextIndex = (currentIndex + 1) % availableSpeedrunners.size();
        return availableSpeedrunners.get(nextIndex);
    }

    /**
     * 清除手动目标（回到自动模式）
     */
    public void clearManualTarget(UUID hunterId) {
        manualTargets.remove(hunterId);
    }

    /**
     * 获取当前手动目标
     */
    public UUID getManualTarget(UUID hunterId) {
        return manualTargets.get(hunterId);
    }
}
