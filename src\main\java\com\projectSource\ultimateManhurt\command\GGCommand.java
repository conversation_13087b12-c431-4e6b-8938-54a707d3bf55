package com.projectSource.ultimateManhurt.command;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.game.GameState;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.game.rules.WinCondition;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * /gg 投降指令处理器
 * 允许玩家投降，当一个阵营超过半数投降时游戏结束
 */
public class GGCommand implements CommandExecutor {
    
    private final UltimateManhurt plugin;
    // 存储每个游戏会话的投降玩家
    private final Map<String, Set<UUID>> surrenderVotes = new ConcurrentHashMap<>();
    // 存储每个玩家的投降时间（用于5分钟有效期）
    private final Map<UUID, Long> surrenderTimestamps = new ConcurrentHashMap<>();

    // 投降有效期：5分钟（毫秒）
    private static final long SURRENDER_EXPIRY_TIME = 5 * 60 * 1000L;
    
    public GGCommand(UltimateManhurt plugin) {
        this.plugin = plugin;

        // 启动定期清理过期投降记录的任务（每分钟执行一次）
        org.bukkit.Bukkit.getScheduler().runTaskTimer(plugin, new Runnable() {
            @Override
            public void run() {
                cleanupExpiredSurrenders();
            }
        }, 1200L, 1200L);
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage("此命令只能由玩家执行");
            return true;
        }
        
        // 检查玩家是否在游戏中
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
        if (gameSession == null) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("你不在任何游戏中！"));
            return true;
        }
        
        // 检查游戏状态
        if (gameSession.getState() != GameState.RUNNING) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("游戏尚未开始或已结束！"));
            return true;
        }
        
        // 检查玩家是否已经投降且在有效期内
        String gameId = gameSession.getSessionId();
        Set<UUID> surrenderedPlayers = surrenderVotes.computeIfAbsent(gameId, k -> ConcurrentHashMap.newKeySet());

        UUID playerId = player.getUniqueId();
        long currentTime = System.currentTimeMillis();

        // 检查是否已经投降
        if (surrenderedPlayers.contains(playerId)) {
            Long surrenderTime = surrenderTimestamps.get(playerId);
            if (surrenderTime != null && (currentTime - surrenderTime) < SURRENDER_EXPIRY_TIME) {
                // 投降仍在有效期内
                long remainingTime = SURRENDER_EXPIRY_TIME - (currentTime - surrenderTime);
                long remainingMinutes = remainingTime / (60 * 1000);
                long remainingSeconds = (remainingTime % (60 * 1000)) / 1000;
                ComponentUtil.sendMessage(player, ComponentUtil.warning(
                    "你已经投降了！剩余有效时间: " + remainingMinutes + "分" + remainingSeconds + "秒"));
                return true;
            } else {
                // 投降已过期，移除记录
                surrenderedPlayers.remove(playerId);
                surrenderTimestamps.remove(playerId);
                ComponentUtil.sendMessage(player, ComponentUtil.info("你的投降已过期，可以重新投降"));
            }
        }
        
        // 记录投降和时间戳
        surrenderedPlayers.add(playerId);
        surrenderTimestamps.put(playerId, currentTime);

        // 获取玩家角色
        PlayerRole playerRole = gameSession.getPlayerRole(playerId);

        // 检查观察者是否可以投降
        if (playerRole == PlayerRole.SPECTATOR) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("观察者不能投降！你不参与游戏胜负。"));
            return true;
        }

        // 广播投降消息
        gameSession.broadcastMessage(ComponentUtil.parse(
            "<yellow>" + player.getName() + " 投降了！(GG) <gray>[5分钟有效期]"
        ));
        
        // 检查是否达到投降条件
        checkSurrenderCondition(gameSession, playerRole);
        
        return true;
    }
    
    /**
     * 检查投降条件是否满足
     */
    private void checkSurrenderCondition(GameSession gameSession, PlayerRole surrenderRole) {
        String gameId = gameSession.getSessionId();
        Set<UUID> surrenderedPlayers = surrenderVotes.get(gameId);
        
        // 统计各阵营的玩家数量和投降数量
        Map<PlayerRole, Integer> totalPlayers = new HashMap<>();
        Map<PlayerRole, Integer> surrenderedCount = new HashMap<>();
        
        // 初始化计数器
        for (PlayerRole role : PlayerRole.values()) {
            totalPlayers.put(role, 0);
            surrenderedCount.put(role, 0);
        }
        
        // 统计总玩家数
        for (UUID playerId : gameSession.getPlayerRoles().keySet()) {
            PlayerRole role = gameSession.getPlayerRole(playerId);
            totalPlayers.put(role, totalPlayers.get(role) + 1);
        }
        
        // 统计投降玩家数（只统计有效期内的）
        long currentTime = System.currentTimeMillis();
        for (UUID playerId : surrenderedPlayers) {
            Long surrenderTime = surrenderTimestamps.get(playerId);
            if (surrenderTime != null && (currentTime - surrenderTime) < SURRENDER_EXPIRY_TIME) {
                PlayerRole role = gameSession.getPlayerRole(playerId);
                if (role != null) {
                    surrenderedCount.put(role, surrenderedCount.get(role) + 1);
                }
            }
        }
        
        // 检查各阵营是否达到投降条件（超过半数）
        for (PlayerRole role : PlayerRole.values()) {
            int total = totalPlayers.get(role);
            int surrendered = surrenderedCount.get(role);
            
            if (total > 0 && surrendered > total / 2.0) {
                // 该阵营超过半数投降，游戏结束
                handleTeamSurrender(gameSession, role);
                return;
            }
        }
        
        // 显示当前投降状态
        showSurrenderStatus(gameSession, totalPlayers, surrenderedCount);
    }
    
    /**
     * 处理阵营投降
     */
    private void handleTeamSurrender(GameSession gameSession, PlayerRole surrenderRole) {
        // 广播投降结果
        String roleDisplayName = getRoleDisplayName(surrenderRole);
        gameSession.broadcastMessage(ComponentUtil.parse(
            "<red><bold>" + roleDisplayName + "阵营投降！游戏结束！</bold>"
        ));
        
        // 确定胜利条件
        WinCondition winCondition = getWinConditionForSurrender(surrenderRole);
        
        // 结束游戏
        gameSession.endGame(winCondition);
        
        // 清理投降记录
        surrenderVotes.remove(gameSession.getSessionId());

        plugin.getLogger().info("游戏 " + gameSession.getSessionId() + " 因 " + roleDisplayName + " 阵营投降而结束");
    }
    
    /**
     * 显示当前投降状态
     */
    private void showSurrenderStatus(GameSession gameSession, Map<PlayerRole, Integer> totalPlayers, Map<PlayerRole, Integer> surrenderedCount) {
        StringBuilder status = new StringBuilder();
        status.append("<yellow>当前投降状态：\n");
        
        for (PlayerRole role : PlayerRole.values()) {
            int total = totalPlayers.get(role);
            int surrendered = surrenderedCount.get(role);
            
            if (total > 0) {
                String roleDisplayName = getRoleDisplayName(role);
                int needed = (total / 2) + 1; // 需要的投降人数
                status.append("<gray>").append(roleDisplayName).append(": ")
                      .append("<white>").append(surrendered).append("/").append(needed)
                      .append(" <gray>(总共").append(total).append("人)\n");
            }
        }
        
        gameSession.broadcastMessage(ComponentUtil.parse(status.toString()));
    }
    
    /**
     * 获取角色显示名称
     */
    private String getRoleDisplayName(PlayerRole role) {
        return switch (role) {
            case SPEEDRUNNER -> "速通者";
            case HUNTER -> "捕猎者";
            case SPECTATOR -> "观察者";
        };
    }
    
    /**
     * 根据投降的角色确定胜利条件
     */
    private WinCondition getWinConditionForSurrender(PlayerRole surrenderRole) {
        return switch (surrenderRole) {
            case SPEEDRUNNER -> WinCondition.HUNTER_WIN_SURRENDER;
            case HUNTER -> WinCondition.SPEEDRUNNER_WIN_SURRENDER;
            case SPECTATOR -> throw new IllegalArgumentException("观察者不应该能够投降"); // 这种情况不应该发生
        };
    }
    
    /**
     * 清理游戏会话的投降记录
     */
    public void cleanupGameSession(String gameId) {
        Set<UUID> surrenderedPlayers = surrenderVotes.remove(gameId);
        if (surrenderedPlayers != null) {
            // 清理这些玩家的时间戳
            for (UUID playerId : surrenderedPlayers) {
                surrenderTimestamps.remove(playerId);
            }
        }
    }

    /**
     * 清理过期的投降记录
     */
    private void cleanupExpiredSurrenders() {
        long currentTime = System.currentTimeMillis();

        // 清理过期的时间戳
        surrenderTimestamps.entrySet().removeIf(entry ->
            (currentTime - entry.getValue()) >= SURRENDER_EXPIRY_TIME);

        // 清理投降记录中的过期玩家
        for (Set<UUID> surrenderedPlayers : surrenderVotes.values()) {
            surrenderedPlayers.removeIf(playerId -> {
                Long surrenderTime = surrenderTimestamps.get(playerId);
                return surrenderTime == null || (currentTime - surrenderTime) >= SURRENDER_EXPIRY_TIME;
            });
        }
    }
}
