package com.projectSource.ultimateManhurt.game;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import com.projectSource.ultimateManhurt.util.TimeUtil;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Set;
import java.util.UUID;
import java.util.function.Consumer;

/**
 * 游戏计时器
 * 负责游戏时间的管理和倒计时
 */
public class GameTimer {
    
    private final UltimateManhurt plugin;
    private final String gameId;
    private final long totalSeconds;
    private final Set<UUID> players;
    
    // 计时器状态
    private long remainingSeconds;
    private boolean isRunning = false;
    private boolean isPaused = false;
    private LocalDateTime startTime;
    private LocalDateTime pauseTime;
    private BukkitTask timerTask;
    
    // 回调函数
    private Consumer<GameTimer> onTimeUp;
    private Consumer<GameTimer> onTick;
    private Consumer<GameTimer> onWarning;
    
    // 警告时间点（秒）
    private final int[] warningTimes = {300, 180, 60, 30, 10, 5, 4, 3, 2, 1};
    private final boolean[] warningsSent = new boolean[warningTimes.length];
    
    public GameTimer(UltimateManhurt plugin, String gameId, long totalSeconds, Set<UUID> players) {
        this.plugin = plugin;
        this.gameId = gameId;
        this.totalSeconds = totalSeconds;
        this.remainingSeconds = totalSeconds;
        this.players = players;
    }
    
    /**
     * 开始计时器
     */
    public void start() {
        if (isRunning) {
            return;
        }
        
        isRunning = true;
        isPaused = false;
        startTime = LocalDateTime.now();
        
        // 重置警告状态
        Arrays.fill(warningsSent, false);
        
        // 启动计时任务
        timerTask = new BukkitRunnable() {
            @Override
            public void run() {
                tick();
            }
        }.runTaskTimer(plugin, 0L, 20L); // 每秒执行一次
        
        plugin.getLogger().info("游戏 " + gameId + " 的计时器已启动，总时长: " + TimeUtil.formatTime(totalSeconds));
    }
    
    /**
     * 暂停计时器
     */
    public void pause() {
        if (!isRunning || isPaused) {
            return;
        }
        
        isPaused = true;
        pauseTime = LocalDateTime.now();
        
        if (timerTask != null) {
            timerTask.cancel();
            timerTask = null;
        }
        
        // 通知玩家游戏暂停
        broadcastMessage(ComponentUtil.warning("游戏已暂停"));
        
        plugin.getLogger().info("游戏 " + gameId + " 的计时器已暂停");
    }
    
    /**
     * 恢复计时器
     */
    public void resume() {
        if (!isRunning || !isPaused) {
            return;
        }
        
        isPaused = false;
        
        // 重新启动计时任务
        timerTask = new BukkitRunnable() {
            @Override
            public void run() {
                tick();
            }
        }.runTaskTimer(plugin, 0L, 20L);
        
        // 通知玩家游戏恢复
        broadcastMessage(ComponentUtil.success("游戏已恢复"));
        
        plugin.getLogger().info("游戏 " + gameId + " 的计时器已恢复");
    }
    
    /**
     * 停止计时器
     */
    public void stop() {
        if (!isRunning) {
            return;
        }
        
        isRunning = false;
        isPaused = false;
        
        if (timerTask != null) {
            timerTask.cancel();
            timerTask = null;
        }
        
        plugin.getLogger().info("游戏 " + gameId + " 的计时器已停止");
    }
    
    /**
     * 计时器滴答
     */
    private void tick() {
        if (isPaused) {
            return;
        }
        
        remainingSeconds--;
        
        // 检查时间是否到了
        if (remainingSeconds <= 0) {
            remainingSeconds = 0;
            timeUp();
            return;
        }
        
        // 检查警告时间
        checkWarnings();
        
        // 执行滴答回调
        if (onTick != null) {
            onTick.accept(this);
        }
        
        // 每10秒更新一次ActionBar
        if (remainingSeconds % 10 == 0) {
            updateActionBar();
        }
    }
    
    /**
     * 检查警告时间
     */
    private void checkWarnings() {
        for (int i = 0; i < warningTimes.length; i++) {
            if (!warningsSent[i] && remainingSeconds == warningTimes[i]) {
                warningsSent[i] = true;
                sendWarning(warningTimes[i]);
                
                if (onWarning != null) {
                    onWarning.accept(this);
                }
                break;
            }
        }
    }
    
    /**
     * 发送警告消息
     */
    private void sendWarning(int seconds) {
        if (seconds >= 60) {
            int minutes = seconds / 60;
            broadcastMessage(ComponentUtil.warning("游戏将在 " + minutes + " 分钟后结束！"));
        } else if (seconds > 10) {
            broadcastMessage(ComponentUtil.warning("游戏将在 " + seconds + " 秒后结束！"));
        } else {
            // 最后10秒倒计时
            broadcastTitle(
                ComponentUtil.parse("<red><bold>" + seconds),
                ComponentUtil.parse("<yellow>游戏即将结束！"),
                10, 20, 10
            );
            
            // 播放音效
            playCountdownSound();
        }
    }
    
    /**
     * 时间到了
     */
    private void timeUp() {
        stop();
        
        // 通知时间到了
        broadcastTitle(
            ComponentUtil.parse("<red><bold>时间到！"),
            ComponentUtil.parse("<yellow>游戏结束"),
            20, 60, 20
        );
        
        broadcastMessage(ComponentUtil.error("时间到！游戏结束！"));
        
        // 播放结束音效
        playTimeUpSound();
        
        // 执行时间到回调
        if (onTimeUp != null) {
            onTimeUp.accept(this);
        }
        
        plugin.getLogger().info("游戏 " + gameId + " 时间到，游戏结束");
    }
    
    /**
     * 更新ActionBar
     */
    private void updateActionBar() {
        var timeComponent = TimeUtil.createCountdownComponent(remainingSeconds);
        
        for (UUID playerId : players) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                ComponentUtil.sendActionBar(player, timeComponent);
            }
        }
    }
    
    /**
     * 广播消息给所有玩家
     */
    private void broadcastMessage(net.kyori.adventure.text.Component message) {
        for (UUID playerId : players) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                ComponentUtil.sendMessage(player, message);
            }
        }
    }
    
    /**
     * 广播标题给所有玩家
     */
    private void broadcastTitle(net.kyori.adventure.text.Component title, 
                               net.kyori.adventure.text.Component subtitle,
                               int fadeIn, int stay, int fadeOut) {
        for (UUID playerId : players) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                ComponentUtil.sendTitle(player, title, subtitle, fadeIn, stay, fadeOut);
            }
        }
    }
    
    /**
     * 播放倒计时音效
     */
    private void playCountdownSound() {
        for (UUID playerId : players) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 2.0f);
            }
        }
    }
    
    /**
     * 播放时间到音效
     */
    private void playTimeUpSound() {
        for (UUID playerId : players) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_WITHER_SPAWN, 1.0f, 1.0f);
            }
        }
    }
    
    /**
     * 添加时间
     */
    public void addTime(long seconds) {
        remainingSeconds += seconds;
        broadcastMessage(ComponentUtil.info("游戏时间增加了 " + TimeUtil.formatTime(seconds)));
    }
    
    /**
     * 减少时间
     */
    public void removeTime(long seconds) {
        remainingSeconds = Math.max(0, remainingSeconds - seconds);
        broadcastMessage(ComponentUtil.warning("游戏时间减少了 " + TimeUtil.formatTime(seconds)));
        
        if (remainingSeconds == 0) {
            timeUp();
        }
    }
    
    /**
     * 设置剩余时间
     */
    public void setRemainingTime(long seconds) {
        remainingSeconds = Math.max(0, seconds);
        broadcastMessage(ComponentUtil.info("游戏时间设置为 " + TimeUtil.formatTime(remainingSeconds)));
        
        if (remainingSeconds == 0) {
            timeUp();
        }
    }
    
    // Getter方法
    public String getGameId() {
        return gameId;
    }
    
    public long getTotalSeconds() {
        return totalSeconds;
    }
    
    public long getRemainingSeconds() {
        return remainingSeconds;
    }
    
    public long getElapsedSeconds() {
        return totalSeconds - remainingSeconds;
    }
    
    public boolean isRunning() {
        return isRunning;
    }
    
    public boolean isPaused() {
        return isPaused;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public double getProgress() {
        return (double) getElapsedSeconds() / totalSeconds;
    }
    
    // 回调设置方法
    public void setOnTimeUp(Consumer<GameTimer> onTimeUp) {
        this.onTimeUp = onTimeUp;
    }
    
    public void setOnTick(Consumer<GameTimer> onTick) {
        this.onTick = onTick;
    }
    
    public void setOnWarning(Consumer<GameTimer> onWarning) {
        this.onWarning = onWarning;
    }

    public LocalDateTime getPauseTime() {
        return pauseTime;
    }
}
