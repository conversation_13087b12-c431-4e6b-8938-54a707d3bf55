package com.projectSource.ultimateManhurt.world.dimension;

import com.projectSource.ultimateManhurt.world.GameWorld;
import com.projectSource.ultimateManhurt.world.WorldManager;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerPortalEvent;
import org.bukkit.event.player.PlayerTeleportEvent;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 维度绑定器
 * 负责处理游戏世界之间的传送门绑定和传送事件
 */
public class DimensionBinder implements Listener {
    
    private final WorldManager worldManager;
    private final Map<String, GameWorld> worldBindings = new ConcurrentHashMap<>();
    
    public DimensionBinder(WorldManager worldManager) {
        this.worldManager = worldManager;
    }
    
    /**
     * 绑定游戏世界的维度传送
     */
    public void bindDimensions(GameWorld gameWorld) {
        // 绑定所有世界到这个游戏世界组
        worldBindings.put(gameWorld.getOverworld().getName(), gameWorld);
        worldBindings.put(gameWorld.getNether().getName(), gameWorld);
        worldBindings.put(gameWorld.getEnd().getName(), gameWorld);
    }
    
    /**
     * 解除游戏世界的维度绑定
     */
    public void unbindDimensions(GameWorld gameWorld) {
        worldBindings.remove(gameWorld.getOverworld().getName());
        worldBindings.remove(gameWorld.getNether().getName());
        worldBindings.remove(gameWorld.getEnd().getName());
    }
    
    /**
     * 处理传送门事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerPortal(PlayerPortalEvent event) {
        Player player = event.getPlayer();
        Location from = event.getFrom();
        World fromWorld = from.getWorld();
        
        if (fromWorld == null) return;
        
        // 检查是否在游戏世界中
        GameWorld gameWorld = worldBindings.get(fromWorld.getName());
        if (gameWorld == null) return;
        
        // 取消默认传送
        event.setCancelled(true);
        
        // 处理自定义传送
        handleCustomPortalTravel(player, event, gameWorld);
    }
    
    /**
     * 处理自定义传送门传送
     */
    private void handleCustomPortalTravel(Player player, PlayerPortalEvent event, GameWorld gameWorld) {
        Location from = event.getFrom();
        World fromWorld = from.getWorld();
        PlayerTeleportEvent.TeleportCause cause = event.getCause();
        
        Location destination = null;
        
        switch (cause) {
            case NETHER_PORTAL:
                destination = handleNetherPortal(from, gameWorld);
                break;
            case END_PORTAL:
                destination = handleEndPortal(from, gameWorld);
                break;
            case END_GATEWAY:
                destination = handleEndGateway(from, gameWorld);
                break;
            default:
                return;
        }
        
        if (destination != null) {
            // 确保目标位置安全
            destination = findSafeLocation(destination);
            
            // 传送玩家
            player.teleport(destination);
            
            // 可以在这里添加传送效果、声音等
            playPortalEffects(player, destination);
        }
    }
    
    /**
     * 处理下界传送门
     */
    private Location handleNetherPortal(Location from, GameWorld gameWorld) {
        World fromWorld = from.getWorld();
        
        if (fromWorld.getEnvironment() == World.Environment.NORMAL) {
            // 从主世界到下界
            return convertLocationToNether(from, gameWorld.getNether());
        } else if (fromWorld.getEnvironment() == World.Environment.NETHER) {
            // 从下界到主世界
            return convertLocationFromNether(from, gameWorld.getOverworld());
        }
        
        return null;
    }
    
    /**
     * 处理末地传送门
     */
    private Location handleEndPortal(Location from, GameWorld gameWorld) {
        World fromWorld = from.getWorld();
        
        if (fromWorld.getEnvironment() == World.Environment.NORMAL) {
            // 从主世界到末地
            return gameWorld.getEnd().getSpawnLocation();
        } else if (fromWorld.getEnvironment() == World.Environment.THE_END) {
            // 从末地回到主世界
            return gameWorld.getOverworld().getSpawnLocation();
        }
        
        return null;
    }
    
    /**
     * 处理末地折跃门
     */
    private Location handleEndGateway(Location from, GameWorld gameWorld) {
        // 末地内部传送，保持在同一个末地世界
        World endWorld = gameWorld.getEnd();
        
        // 寻找合适的传送位置（可以是末地外岛）
        Location destination = new Location(endWorld, 0, 75, 0);
        return findSafeLocation(destination);
    }
    
    /**
     * 将主世界坐标转换为下界坐标
     */
    private Location convertLocationToNether(Location overworldLocation, World netherWorld) {
        double x = overworldLocation.getX() / 8.0;
        double z = overworldLocation.getZ() / 8.0;
        double y = Math.max(10, Math.min(120, overworldLocation.getY()));
        
        Location netherLocation = new Location(netherWorld, x, y, z);
        return findOrCreateNetherPortal(netherLocation);
    }
    
    /**
     * 将下界坐标转换为主世界坐标
     */
    private Location convertLocationFromNether(Location netherLocation, World overworldWorld) {
        double x = netherLocation.getX() * 8.0;
        double z = netherLocation.getZ() * 8.0;
        double y = overworldWorld.getHighestBlockYAt((int) x, (int) z) + 1;
        
        Location overworldLocation = new Location(overworldWorld, x, y, z);
        return findOrCreateOverworldPortal(overworldLocation);
    }
    
    /**
     * 寻找或创建下界传送门
     */
    private Location findOrCreateNetherPortal(Location location) {
        World world = location.getWorld();
        int x = location.getBlockX();
        int y = location.getBlockY();
        int z = location.getBlockZ();
        
        // 在附近寻找现有传送门
        for (int dx = -16; dx <= 16; dx++) {
            for (int dz = -16; dz <= 16; dz++) {
                for (int dy = -8; dy <= 8; dy++) {
                    Block block = world.getBlockAt(x + dx, y + dy, z + dz);
                    if (block.getType() == Material.NETHER_PORTAL) {
                        return block.getLocation().add(0.5, 0, 0.5);
                    }
                }
            }
        }
        
        // 如果没有找到，创建新的传送门
        return createNetherPortal(location);
    }
    
    /**
     * 寻找或创建主世界传送门
     */
    private Location findOrCreateOverworldPortal(Location location) {
        World world = location.getWorld();
        int x = location.getBlockX();
        int z = location.getBlockZ();
        
        // 在附近寻找现有传送门
        for (int dx = -16; dx <= 16; dx++) {
            for (int dz = -16; dz <= 16; dz++) {
                int y = world.getHighestBlockYAt(x + dx, z + dz);
                for (int dy = -10; dy <= 10; dy++) {
                    Block block = world.getBlockAt(x + dx, y + dy, z + dz);
                    if (block.getType() == Material.NETHER_PORTAL) {
                        return block.getLocation().add(0.5, 0, 0.5);
                    }
                }
            }
        }
        
        // 如果没有找到，创建新的传送门
        return createOverworldPortal(location);
    }
    
    /**
     * 创建下界传送门
     */
    private Location createNetherPortal(Location location) {
        World world = location.getWorld();
        int x = location.getBlockX();
        int y = Math.max(10, Math.min(120, location.getBlockY()));
        int z = location.getBlockZ();
        
        // 寻找合适的位置
        Location portalLocation = findSuitablePortalLocation(world, x, y, z);
        
        // 创建传送门结构
        buildNetherPortal(portalLocation);
        
        return portalLocation.add(0.5, 0, 0.5);
    }
    
    /**
     * 创建主世界传送门
     */
    private Location createOverworldPortal(Location location) {
        World world = location.getWorld();
        int x = location.getBlockX();
        int z = location.getBlockZ();
        int y = world.getHighestBlockYAt(x, z) + 1;
        
        Location portalLocation = new Location(world, x, y, z);
        
        // 创建传送门结构
        buildNetherPortal(portalLocation);
        
        return portalLocation.add(0.5, 0, 0.5);
    }
    
    /**
     * 寻找合适的传送门位置
     */
    private Location findSuitablePortalLocation(World world, int x, int y, int z) {
        // 简单实现：寻找一个空旷的位置
        for (int dy = -5; dy <= 5; dy++) {
            for (int dx = -2; dx <= 2; dx++) {
                for (int dz = -2; dz <= 2; dz++) {
                    Location testLocation = new Location(world, x + dx, y + dy, z + dz);
                    if (isSuitableForPortal(testLocation)) {
                        return testLocation;
                    }
                }
            }
        }
        
        // 如果找不到合适位置，返回原位置
        return new Location(world, x, y, z);
    }
    
    /**
     * 检查位置是否适合建造传送门
     */
    private boolean isSuitableForPortal(Location location) {
        World world = location.getWorld();
        int x = location.getBlockX();
        int y = location.getBlockY();
        int z = location.getBlockZ();
        
        // 检查是否有足够的空间（4x5的区域）
        for (int dx = 0; dx < 4; dx++) {
            for (int dy = 0; dy < 5; dy++) {
                Block block = world.getBlockAt(x + dx, y + dy, z);
                if (!block.getType().isAir() && block.getType() != Material.WATER && block.getType() != Material.LAVA) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * 建造下界传送门
     */
    private void buildNetherPortal(Location location) {
        World world = location.getWorld();
        int x = location.getBlockX();
        int y = location.getBlockY();
        int z = location.getBlockZ();
        
        // 建造传送门框架
        for (int dx = 0; dx < 4; dx++) {
            world.getBlockAt(x + dx, y - 1, z).setType(Material.OBSIDIAN); // 底部
            world.getBlockAt(x + dx, y + 4, z).setType(Material.OBSIDIAN); // 顶部
        }
        
        for (int dy = 0; dy < 4; dy++) {
            world.getBlockAt(x - 1, y + dy, z).setType(Material.OBSIDIAN); // 左侧
            world.getBlockAt(x + 4, y + dy, z).setType(Material.OBSIDIAN); // 右侧
        }
        
        // 创建传送门方块
        for (int dx = 0; dx < 2; dx++) {
            for (int dy = 0; dy < 3; dy++) {
                world.getBlockAt(x + dx + 1, y + dy + 1, z).setType(Material.NETHER_PORTAL);
            }
        }
    }
    
    /**
     * 寻找安全的位置
     */
    private Location findSafeLocation(Location location) {
        World world = location.getWorld();
        int x = location.getBlockX();
        int y = location.getBlockY();
        int z = location.getBlockZ();
        
        // 向上寻找安全位置
        for (int dy = 0; dy < 10; dy++) {
            Location testLocation = new Location(world, x + 0.5, y + dy, z + 0.5);
            if (isSafeLocation(testLocation)) {
                return testLocation;
            }
        }
        
        // 向下寻找安全位置
        for (int dy = -1; dy > -10; dy--) {
            Location testLocation = new Location(world, x + 0.5, y + dy, z + 0.5);
            if (isSafeLocation(testLocation)) {
                return testLocation;
            }
        }
        
        // 如果找不到安全位置，返回原位置
        return location;
    }
    
    /**
     * 检查位置是否安全
     */
    private boolean isSafeLocation(Location location) {
        World world = location.getWorld();
        int x = location.getBlockX();
        int y = location.getBlockY();
        int z = location.getBlockZ();
        
        // 检查脚下有实体方块
        Material groundMaterial = world.getBlockAt(x, y - 1, z).getType();
        if (!groundMaterial.isSolid()) {
            return false;
        }
        
        // 检查头部和身体位置没有实体方块
        Material headMaterial = world.getBlockAt(x, y + 1, z).getType();
        Material bodyMaterial = world.getBlockAt(x, y, z).getType();
        
        return (headMaterial.isAir() || headMaterial == Material.NETHER_PORTAL) 
            && (bodyMaterial.isAir() || bodyMaterial == Material.NETHER_PORTAL);
    }
    
    /**
     * 播放传送门效果
     */
    private void playPortalEffects(Player player, Location destination) {
        // 播放传送门音效
        player.playSound(destination, org.bukkit.Sound.BLOCK_PORTAL_TRAVEL, 1.0f, 1.0f);
        
        // 可以添加粒子效果等
        World world = destination.getWorld();
        if (world != null) {
            world.spawnParticle(org.bukkit.Particle.PORTAL, destination, 50, 1, 1, 1, 0.1);
        }
    }
}
