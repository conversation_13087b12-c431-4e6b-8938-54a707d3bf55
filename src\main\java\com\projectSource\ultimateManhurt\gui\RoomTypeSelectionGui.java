package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.room.RoomType;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.List;

/**
 * 房间类型选择GUI
 */
public class RoomTypeSelectionGui extends BaseGui {
    
    private String roomName;
    
    public RoomTypeSelectionGui(UltimateManhurt plugin, Player player, String roomName) {
        super(plugin, player, "选择房间类型", 27);
        this.roomName = roomName;
        setupGui();
    }

    @Override
    protected void setupGui() {
        // 标题
        setItem(4, createItem(Material.NETHER_STAR, "<gold><bold>选择房间类型",
            "<gray>房间名称: <white>" + roomName,
            "",
            "<yellow>选择你想要创建的房间类型"
        ));
        
        // 公开房间
        List<String> publicLore = new ArrayList<>();
        publicLore.add("<gray>类型: <green>公开房间");
        publicLore.add("<gray>描述: <white>任何人都可以加入，计算战绩");
        publicLore.add("");
        publicLore.add("<gray>• 最大人数: <white>20人");
        publicLore.add("<gray>• 最小人数: <white>2人");
        publicLore.add("<gray>• 默认时长: <white>30分钟");
        publicLore.add("<gray>• 加入方式: <white>公开");
        publicLore.add("<gray>• 计算战绩: <green>✓ 是");
        publicLore.add("<gray>• 显示计分板: <green>✓ 是");
        publicLore.add("<gray>• ELO系统: <green>✓ 启用");
        publicLore.add("");
        publicLore.add("<green>点击创建公开房间");

        setItem(11, createItem(Material.EMERALD_BLOCK, "<green><bold>🌍 公开房间", publicLore.toArray(new String[0])));

        // 私人房间
        List<String> privateLore = new ArrayList<>();
        privateLore.add("<gray>类型: <blue>私人房间");
        privateLore.add("<gray>描述: <white>仅限邀请加入，不计算战绩");
        privateLore.add("");
        privateLore.add("<gray>• 最大人数: <white>20人");
        privateLore.add("<gray>• 最小人数: <white>1人");
        privateLore.add("<gray>• 默认时长: <white>60分钟");
        privateLore.add("<gray>• 加入方式: <white>仅邀请");
        privateLore.add("<gray>• 计算战绩: <red>✗ 否");
        privateLore.add("<gray>• 显示计分板: <green>✓ 是");
        privateLore.add("<gray>• ELO系统: <green>✓ 启用");
        privateLore.add("");
        privateLore.add("<blue>点击创建私人房间");

        setItem(15, createItem(Material.DIAMOND_BLOCK, "<blue><bold>🔒 私人房间", privateLore.toArray(new String[0])));
        
        // 返回按钮
        setItem(22, createItem(Material.ARROW, "<yellow>返回", 
            "<gray>返回房间列表"));
        
        // 装饰性物品
        for (int i = 0; i < inventory.getSize(); i++) {
            if (inventory.getItem(i) == null) {
                setItem(i, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));
            }
        }
    }
    
    @Override
    public void handleClick(InventoryClickEvent event) {
        event.setCancelled(true);
        
        if (event.getClickedInventory() != inventory) {
            return;
        }
        
        ItemStack item = event.getCurrentItem();
        if (item == null || item.getType() == Material.AIR) {
            return;
        }
        
        this.playClickSound();

        int slot = event.getSlot();

        switch (slot) {
            case 11: // 公开房间
                createRoom(RoomType.PUBLIC);
                break;
            case 15: // 私人房间
                createRoom(RoomType.PRIVATE);
                break;
            case 22: // 返回
                this.close();
                this.plugin.getGuiManager().openRoomListGui(this.player);
                break;
        }
    }
    
    /**
     * 创建指定类型的房间
     */
    private void createRoom(RoomType roomType) {
        // 检查玩家是否已在房间中
        if (this.plugin.getRoomManager().isPlayerInRoom(this.player.getUniqueId())) {
            this.sendError("你已经在一个房间中了！");
            this.playErrorSound();
            return;
        }

        this.plugin.getLogger().info("准备创建房间: " + roomName + ", 类型: " + roomType.getDisplayName());

        try {
            Room room = this.plugin.getRoomManager().createRoom(
                roomName,
                this.player.getUniqueId(),
                roomType
            );

            this.plugin.getLogger().info("房间创建成功: " + roomName + ", 类型: " + roomType.getDisplayName());
            this.sendSuccess("成功创建" + roomType.getDisplayName() + ": " + roomName);
            this.playSuccessSound();
            
            // 关闭当前GUI并打开房间设置GUI
            close();
            this.plugin.getGuiManager().openRoomSettingsGui(this.player, room);
            
        } catch (Exception e) {
            this.plugin.getLogger().severe("创建房间异常: " + e.getMessage());
            e.printStackTrace();
            this.sendError("创建房间失败: " + e.getMessage());
            this.playErrorSound();
        }
    }
}
