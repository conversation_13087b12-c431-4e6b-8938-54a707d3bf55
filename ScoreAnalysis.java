/**
 * 积分分析和重新设计工具
 */
public class ScoreAnalysis {
    
    // 当前的里程碑分数
    static class CurrentMilestone {
        String name;
        int currentScore;
        String category;
        
        CurrentMilestone(String name, int currentScore, String category) {
            this.name = name;
            this.currentScore = currentScore;
            this.category = category;
        }
    }
    
    public static void main(String[] args) {
        System.out.println("=== 积分里程碑分析与重新设计 ===\n");
        
        // 当前所有里程碑
        CurrentMilestone[] milestones = {
            // 基础进度 (9个)
            new CurrentMilestone("获得木头", 5, "基础进度"),
            new CurrentMilestone("制作工作台", 10, "基础进度"),
            new CurrentMilestone("制作木制工具", 10, "基础进度"),
            new CurrentMilestone("挖到石头", 15, "基础进度"),
            new CurrentMilestone("制作石制工具", 20, "基础进度"),
            new CurrentMilestone("制作熔炉", 25, "基础进度"),
            new CurrentMilestone("冶炼铁锭", 30, "基础进度"),
            new CurrentMilestone("制作铁制工具", 35, "基础进度"),
            new CurrentMilestone("制作铁制盔甲", 40, "基础进度"),
            
            // 重要里程碑 (5个)
            new CurrentMilestone("发现钻石", 50, "重要里程碑"),
            new CurrentMilestone("制作钻石工具", 60, "重要里程碑"),
            new CurrentMilestone("制作钻石盔甲", 70, "重要里程碑"),
            new CurrentMilestone("建造下界传送门", 80, "重要里程碑"),
            new CurrentMilestone("进入下界", 100, "重要里程碑"),
            
            // 下界进度 (4个)
            new CurrentMilestone("击杀烈焰人", 130, "下界进度"),
            new CurrentMilestone("获得烈焰棒", 140, "下界进度"),
            new CurrentMilestone("制作酿造台", 150, "下界进度"),
            new CurrentMilestone("制作末影之眼", 180, "下界进度"),
            
            // 末地准备 (2个)
            new CurrentMilestone("激活末地传送门", 250, "末地准备"),
            new CurrentMilestone("进入末地", 300, "末地准备"),
            
            // 最终目标 (2个)
            new CurrentMilestone("对末影龙造成伤害", 350, "最终目标"),
            new CurrentMilestone("击杀末影龙", 500, "最终目标"),
            
            // 生存奖励 (2个)
            new CurrentMilestone("生存奖励", 20, "生存奖励"),
            new CurrentMilestone("无死亡奖励", 50, "生存奖励"),
            
            // 速度奖励 (3个)
            new CurrentMilestone("快速进入下界", 50, "速度奖励"),
            new CurrentMilestone("快速进入末地", 100, "速度奖励"),
            new CurrentMilestone("快速击杀末影龙", 200, "速度奖励"),
            
            // 效率奖励 (2个)
            new CurrentMilestone("效率奖励", 30, "效率奖励"),
            new CurrentMilestone("资源节约奖励", 25, "效率奖励"),
            
            // 特殊成就 (4个)
            new CurrentMilestone("击杀捕猎者", 40, "特殊成就"),
            new CurrentMilestone("逃脱捕猎者", 20, "特殊成就"),
            new CurrentMilestone("收集所有材料", 80, "特殊成就"),
            new CurrentMilestone("制作全套装备", 60, "特殊成就")
        };
        
        // 分析当前分数
        analyzeCurrentScores(milestones);
        
        // 设计新的分数分配
        designNewScores(milestones);
    }
    
    private static void analyzeCurrentScores(CurrentMilestone[] milestones) {
        System.out.println("1. 当前分数分析:");
        
        int totalScore = 0;
        java.util.Map<String, Integer> categoryScores = new java.util.HashMap<>();
        java.util.Map<String, Integer> categoryCounts = new java.util.HashMap<>();
        
        for (CurrentMilestone milestone : milestones) {
            totalScore += milestone.currentScore;
            categoryScores.merge(milestone.category, milestone.currentScore, Integer::sum);
            categoryCounts.merge(milestone.category, 1, Integer::sum);
        }
        
        System.out.println("当前总分: " + totalScore + "分");
        System.out.println("目标总分: 850分");
        System.out.println("需要调整: " + (850 - totalScore) + "分");
        System.out.println();
        
        System.out.println("各分类当前分数:");
        for (String category : categoryScores.keySet()) {
            int score = categoryScores.get(category);
            int count = categoryCounts.get(category);
            System.out.println(String.format("  %s: %d分 (%d个里程碑, 平均%.1f分)", 
                category, score, count, (double)score/count));
        }
        System.out.println();
    }
    
    private static void designNewScores(CurrentMilestone[] milestones) {
        System.out.println("2. 新分数设计 (目标850分):");
        System.out.println();
        
        // 设计原则：
        // 1. 基础进度：占总分的15% (约130分)
        // 2. 重要里程碑：占总分的25% (约210分)
        // 3. 下界进度：占总分的20% (约170分)
        // 4. 末地准备：占总分的15% (约130分)
        // 5. 最终目标：占总分的15% (约130分)
        // 6. 奖励系统：占总分的10% (约70分)
        
        System.out.println("=== 新分数分配方案 ===");
        System.out.println();
        
        System.out.println("基础进度 (9个里程碑, 目标130分):");
        System.out.println("  获得木头: 5分 → 3分");
        System.out.println("  制作工作台: 10分 → 5分");
        System.out.println("  制作木制工具: 10分 → 8分");
        System.out.println("  挖到石头: 15分 → 10分");
        System.out.println("  制作石制工具: 20分 → 12分");
        System.out.println("  制作熔炉: 25分 → 15分");
        System.out.println("  冶炼铁锭: 30分 → 18分");
        System.out.println("  制作铁制工具: 35分 → 25分");
        System.out.println("  制作铁制盔甲: 40分 → 34分");
        System.out.println("  小计: 200分 → 130分 (-70分)");
        System.out.println();
        
        System.out.println("重要里程碑 (5个里程碑, 目标210分):");
        System.out.println("  发现钻石: 50分 → 40分");
        System.out.println("  制作钻石工具: 60分 → 50分");
        System.out.println("  制作钻石盔甲: 70分 → 60分");
        System.out.println("  建造下界传送门: 80分 → 30分");
        System.out.println("  进入下界: 100分 → 30分");
        System.out.println("  小计: 360分 → 210分 (-150分)");
        System.out.println();
        
        System.out.println("下界进度 (4个里程碑, 目标170分):");
        System.out.println("  击杀烈焰人: 130分 → 40分");
        System.out.println("  获得烈焰棒: 140分 → 50分");
        System.out.println("  制作酿造台: 150分 → 30分");
        System.out.println("  制作末影之眼: 180分 → 50分");
        System.out.println("  小计: 600分 → 170分 (-430分)");
        System.out.println();
        
        System.out.println("末地准备 (2个里程碑, 目标130分):");
        System.out.println("  激活末地传送门: 250分 → 60分");
        System.out.println("  进入末地: 300分 → 70分");
        System.out.println("  小计: 550分 → 130分 (-420分)");
        System.out.println();
        
        System.out.println("最终目标 (2个里程碑, 目标130分):");
        System.out.println("  对末影龙造成伤害: 350分 → 50分");
        System.out.println("  击杀末影龙: 500分 → 80分");
        System.out.println("  小计: 850分 → 130分 (-720分)");
        System.out.println();
        
        System.out.println("奖励系统 (11个里程碑, 目标70分):");
        System.out.println("  生存奖励: 20分 → 5分");
        System.out.println("  无死亡奖励: 50分 → 15分");
        System.out.println("  快速进入下界: 50分 → 10分");
        System.out.println("  快速进入末地: 100分 → 15分");
        System.out.println("  快速击杀末影龙: 200分 → 20分");
        System.out.println("  效率奖励: 30分 → 5分");
        System.out.println("  资源节约奖励: 25分 → 0分 (移除)");
        System.out.println("  击杀捕猎者: 40分 → 0分 (移除)");
        System.out.println("  逃脱捕猎者: 20分 → 0分 (移除)");
        System.out.println("  收集所有材料: 80分 → 0分 (移除)");
        System.out.println("  制作全套装备: 60分 → 0分 (移除)");
        System.out.println("  小计: 675分 → 70分 (-605分)");
        System.out.println();
        
        System.out.println("=== 总结 ===");
        System.out.println("新总分: 130 + 210 + 170 + 130 + 130 + 70 = 840分");
        System.out.println("与目标850分相差: 10分 (可接受范围)");
        System.out.println();
        
        System.out.println("主要调整策略:");
        System.out.println("1. 大幅降低后期里程碑分数，避免分数过于集中");
        System.out.println("2. 移除部分不常用或难以实现的奖励里程碑");
        System.out.println("3. 保持早期里程碑的激励作用");
        System.out.println("4. 让分数分布更加均匀，提升游戏体验");
    }
}
