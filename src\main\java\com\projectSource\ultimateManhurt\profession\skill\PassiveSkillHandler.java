package com.projectSource.ultimateManhurt.profession.skill;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.profession.Profession;
import com.projectSource.ultimateManhurt.profession.util.PseudoRandom;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.entity.Wolf;
import org.bukkit.entity.EntityType;
import org.bukkit.attribute.Attribute;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataContainer;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.NamespacedKey;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;

import java.util.*;

/**
 * 被动技能处理器
 */
public class PassiveSkillHandler {

    /**
     * 莱娜光环数据类 - 简化版（移除禁用机制）
     */
    private static class LenaAuraData {
        private BukkitTask auraTask;
        private boolean isActive;

        public LenaAuraData() {
            this.isActive = false;
        }

        public void setAuraTask(BukkitTask task) {
            if (this.auraTask != null) {
                this.auraTask.cancel();
            }
            this.auraTask = task;
            this.isActive = true;
        }

        public void stopAura() {
            if (this.auraTask != null) {
                this.auraTask.cancel();
                this.auraTask = null;
            }
            this.isActive = false;
        }

        public boolean isActive() {
            return isActive;
        }
    }

    /**
     * 萨满狗数据类 - 明确区分初始狗和奖励狗，支持多个独立的复活任务
     */
    private static class ShamanDogData {
        private final List<Wolf> initialDogs = new ArrayList<>();  // 初始狗（游戏开始3分钟后获得的2只，死亡后90秒复活）
        private final List<Wolf> rewardDogs = new ArrayList<>();   // 奖励狗（击杀猎人获得的额外狗，死亡后不会复活）
        private final List<BukkitTask> respawnTasks = new ArrayList<>(); // 多个复活任务

        public List<Wolf> getInitialDogs() { return initialDogs; }
        public List<Wolf> getRewardDogs() { return rewardDogs; }

        // 获取所有狗的列表
        public List<Wolf> getAllDogs() {
            List<Wolf> allDogs = new ArrayList<>();
            allDogs.addAll(initialDogs);
            allDogs.addAll(rewardDogs);
            return allDogs;
        }

        // 检查狗是否是初始狗（可以复活）
        public boolean isInitialDog(Wolf dog) {
            return initialDogs.contains(dog);
        }

        // 移除狗（从对应的列表中）
        public void removeDog(Wolf dog) {
            initialDogs.remove(dog);
            rewardDogs.remove(dog);
        }

        // 添加复活任务
        public void addRespawnTask(BukkitTask task) {
            respawnTasks.add(task);
        }

        // 移除已完成的复活任务
        public void removeRespawnTask(BukkitTask task) {
            respawnTasks.remove(task);
        }

        public void cleanup() {
            // 清理所有狗
            for (Wolf dog : getAllDogs()) {
                if (dog != null && !dog.isDead()) {
                    dog.remove();
                }
            }
            initialDogs.clear();
            rewardDogs.clear();

            // 取消所有复活任务
            for (BukkitTask task : respawnTasks) {
                if (task != null) {
                    task.cancel();
                }
            }
            respawnTasks.clear();
        }
    }

    private final UltimateManhurt plugin;

    // 猪灵光环任务
    private BukkitTask piglinAuraTask;

    // 萨满狗伙伴管理 <萨满UUID, 狗数据>
    private final Map<UUID, ShamanDogData> shamanDogs = new HashMap<>();

    // 恐惧魔王灵魂虹吸buff数据 <恐惧魔王UUID, buff结束时间>
    private final Map<UUID, Long> fearLordSoulSiphonBuff = new ConcurrentHashMap<>();

    // 跳跃禁用数据 <玩家UUID, 禁用结束时间>
    private final Map<UUID, Long> jumpDisabled = new ConcurrentHashMap<>();

    // 春秋蝉触发次数记录 <玩家UUID, 触发次数>
    private final Map<UUID, Integer> springAutumnCicadaTriggerCount = new ConcurrentHashMap<>();

    // 船长朗姆酒数据类
    private static class CaptainRumData {
        private final List<Double> pendingDamage = new ArrayList<>();
        private long rumEndTime = 0;

        public boolean isActive() {
            return System.currentTimeMillis() < rumEndTime;
        }

        public void activate() {
            this.rumEndTime = System.currentTimeMillis() + 10000; // 10秒持续时间
            this.pendingDamage.clear();
        }

        public void addPendingDamage(double damage) {
            pendingDamage.add(damage);
        }

        public List<Double> getPendingDamage() {
            return new ArrayList<>(pendingDamage);
        }

        public void clearPendingDamage() {
            pendingDamage.clear();
        }
    }

    // 船长朗姆酒状态 <船长UUID, 朗姆酒数据>
    private final Map<UUID, CaptainRumData> captainRumStates = new ConcurrentHashMap<>();

    // 莱娜光环系统 - 重新设计
    private final Map<UUID, LenaAuraData> lenaAuraSystem = new ConcurrentHashMap<>();

    // 森之祝福效果 - 记录哪些玩家受到森之祝福光环影响（用于闪避检查）
    private final Map<UUID, Long> forestBlessingEffectPlayers = new ConcurrentHashMap<>();

    // 机器人方块放置计数器
    private final Map<UUID, Integer> robotBlockCounter = new ConcurrentHashMap<>();

    // 暗夜领主暗夜猎影被动技能状态 <玩家UUID, 夜视任务>
    private final Map<UUID, BukkitTask> nightLordNightVisionTasks = new ConcurrentHashMap<>();

    private final PseudoRandom pseudoRandom;
    private final Random random;
    
    public PassiveSkillHandler(UltimateManhurt plugin, PseudoRandom pseudoRandom) {
        this.plugin = plugin;
        this.pseudoRandom = pseudoRandom;
        this.random = new Random();

        // 启动猪灵光环任务
        startPiglinAuraTask();

        // 启动定期清理过期虹吸效果的任务
        startSoulSiphonCleanupTask();
    }

    /**
     * 启动灵魂虹吸buff清理任务
     */
    private void startSoulSiphonCleanupTask() {
        Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            cleanupExpiredSoulSiphonBuff();
        }, 100L, 100L); // 每5秒检查一次
    }

    /**
     * 清理过期的灵魂虹吸buff
     */
    private void cleanupExpiredSoulSiphonBuff() {
        long currentTime = System.currentTimeMillis();

        fearLordSoulSiphonBuff.entrySet().removeIf(entry -> {
            UUID fearLordId = entry.getKey();
            Long buffEndTime = entry.getValue();

            if (currentTime >= buffEndTime) {
                // buff过期，移除药水效果
                Player fearLord = Bukkit.getPlayer(fearLordId);
                if (fearLord != null && fearLord.isOnline()) {
                    fearLord.removePotionEffect(PotionEffectType.SPEED);
                    fearLord.removePotionEffect(PotionEffectType.STRENGTH);
                    ComponentUtil.sendMessage(fearLord, ComponentUtil.info("灵魂虹吸效果已结束"));
                }
                return true; // 移除过期的buff记录
            }

            return false; // 保留未过期的buff
        });
    }
    
    /**
     * 处理末影人闪烁被动技能
     * 受到一次攻击会闪烁到玩家身后，并且该玩家获得8s的失明效果。45s cd
     */
    public void handleEndermanBlink(Player player, Player attacker) {
        String skillName = "闪烁";

        // 使用统一的冷却检查
        int cooldownTime = Profession.ENDERMAN.getPassiveSkillCooldown();
        if (!checkAndSetPassiveSkillCooldown(player, skillName, cooldownTime)) {
            return; // 在冷却中，不能触发
        }

        // 计算攻击者身后的位置
        Location attackerLocation = attacker.getLocation();
        org.bukkit.util.Vector direction = attackerLocation.getDirection().normalize();

        // 在攻击者身后2格的位置
        Location behindLocation = attackerLocation.clone().subtract(direction.multiply(2));
        behindLocation.setY(attackerLocation.getY()); // 保持相同高度

        // 确保位置安全（不在方块内）
        Location safeLocation = findSafeLocationNear(behindLocation);
        if (safeLocation != null) {
            // 传送玩家
            boolean teleportSuccess = player.teleport(safeLocation);

            if (teleportSuccess) {
                // 给攻击者施加8秒失明效果
                attacker.addPotionEffect(new org.bukkit.potion.PotionEffect(
                    org.bukkit.potion.PotionEffectType.BLINDNESS, 100, 0)); // 8秒 = 160 tick

                // 播放效果
                player.playSound(player.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);
                attacker.playSound(attacker.getLocation(), Sound.ENTITY_ENDERMAN_SCREAM, 1.0f, 0.8f);

                // 消息反馈
                ComponentUtil.sendMessage(player, ComponentUtil.info("闪烁到 " + attacker.getName() + " 身后！"));
                ComponentUtil.sendMessage(attacker, ComponentUtil.warning(player.getName() + " 闪烁到你身后并让你失明了！"));

                // 冷却时间已由checkAndSetPassiveSkillCooldown()统一处理
            } else {
                ComponentUtil.sendMessage(player, ComponentUtil.error("传送失败，请稍后再试"));
            }
        } else {
            ComponentUtil.sendMessage(player, ComponentUtil.error("无法找到安全的传送位置"));
        }
    }
    
    /**
     * 处理屠夫腐肉堆积被动技能
     * 受到近战伤害有概率 (30% 伪随机) 获得 抗性三效果3s
     */
    public void handleButcherFleshStack(Player player) {
        UUID playerId = player.getUniqueId();
        String eventName = "腐肉堆积";

        // 30%概率触发
        if (pseudoRandom.checkTrigger(playerId, eventName, 0.30)) {
            // 给予抗性三效果3秒
            player.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, 60, 2)); // 3秒 = 60 ticks
            
            // 播放效果
            player.playSound(player.getLocation(), Sound.ENTITY_ZOMBIE_AMBIENT, 1.0f, 0.8f);
            ComponentUtil.sendMessage(player, ComponentUtil.info("腐肉堆积触发！获得抗性效果"));
        }
    }
    
    /**
     * 处理骷髅积少成多被动技能
     * 射箭有概率会额外获得一根箭矢 (50% 伪随机)
     */
    public void handleSkeletonAccumulation(Player player) {
        UUID playerId = player.getUniqueId();
        String eventName = "积少成多";
        
        // 50%概率触发
        if (pseudoRandom.checkTrigger(playerId, eventName, 0.5)) {
            // 给予一根箭矢
            ItemStack arrow = new ItemStack(Material.ARROW, 1);
            player.getInventory().addItem(arrow);
            
            // 播放效果
            player.playSound(player.getLocation(), Sound.ENTITY_ITEM_PICKUP, 1.0f, 1.2f);
            ComponentUtil.sendMessage(player, ComponentUtil.info("积少成多触发！获得额外箭矢"));
        }
    }
    
    /**
     * 处理探险家寻宝直觉被动技能
     * 挖掘矿物时有40%概率获得两倍额外矿物，20%的概率获得三倍矿物，10%的概率获得四倍矿物，获得的情况下获得3点饱食度，触发相互独立。
     */
    public void handleExplorerTreasureInstinct(Player player, Material minedMaterial) {
        UUID playerId = player.getUniqueId();

        // 检查是否是矿物
        if (!isMineralBlock(minedMaterial)) {
            return;
        }

        // 获得基础矿物掉落
        ItemStack baseMineral = getDropFromMineral(minedMaterial);
        if (baseMineral == null) {
            return;
        }

        int totalExtraItems = 0;
        boolean anyTrigger = false;

        // 40%概率获得两倍额外矿物 (相当于额外1个)
        if (pseudoRandom.checkTrigger(playerId, "寻宝直觉_2倍", 0.40)) {
            totalExtraItems += 1;
            anyTrigger = true;
            plugin.getLogger().info("玩家 " + player.getName() + " 触发寻宝直觉2倍效果");
        }

        // 20%概率获得三倍矿物 (相当于额外2个)
        if (pseudoRandom.checkTrigger(playerId, "寻宝直觉_3倍", 0.20)) {
            totalExtraItems += 2;
            anyTrigger = true;
            plugin.getLogger().info("玩家 " + player.getName() + " 触发寻宝直觉3倍效果");
        }

        // 10%概率获得四倍矿物 (相当于额外3个)
        if (pseudoRandom.checkTrigger(playerId, "寻宝直觉_4倍", 0.10)) {
            totalExtraItems += 3;
            anyTrigger = true;
            plugin.getLogger().info("玩家 " + player.getName() + " 触发寻宝直觉4倍效果");
        }

        // 如果有任何触发，给予额外矿物和饱食度
        if (anyTrigger) {
            // 给予额外矿物
            if (totalExtraItems > 0) {
                ItemStack extraMinerals = baseMineral.clone();
                extraMinerals.setAmount(totalExtraItems);
                player.getInventory().addItem(extraMinerals);
            }

            // 给予3点饱食度
            int currentFood = player.getFoodLevel();
            player.setFoodLevel(Math.min(20, currentFood + 3));

            // 播放效果和消息
            player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.5f);
            player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_BURP, 0.5f, 1.2f); // 饱食度音效

            ComponentUtil.sendMessage(player, ComponentUtil.info(
                "寻宝直觉触发！获得 " + totalExtraItems + " 个额外矿物和3点饱食度"));
        }
    }
    
    /**
     * 处理铁傀儡强击被动技能
     * 受到攻击有15%概率会免疫该次伤害并且将伤害 * 1.0 返回给攻击者
     */
    public boolean handleIronGolemCounterAttack(Player victim, Player attacker, double damage) {
        UUID playerId = victim.getUniqueId();
        String eventName = "强击";

        // 检查是否为队友
        com.projectSource.ultimateManhurt.game.GameSession gameSession =
            plugin.getGameManager().getGameSessionByPlayer(victim.getUniqueId());

        if (gameSession != null) {
            com.projectSource.ultimateManhurt.game.PlayerRole victimRole = gameSession.getPlayerRole(victim.getUniqueId());
            com.projectSource.ultimateManhurt.game.PlayerRole attackerRole = gameSession.getPlayerRole(attacker.getUniqueId());

            // 如果是队友，不触发强击
            if (victimRole == attackerRole && victimRole != null) {
                return false; // 不免疫伤害，不反击队友
            }
        }

        // 15%概率触发
        if (pseudoRandom.checkTrigger(playerId, eventName, 0.15)) {
            // 计算反伤 - 100%反伤
            double counterDamage = damage * 1.0;

            // 对攻击者造成反伤
            attacker.damage(counterDamage);

            // 播放效果
            victim.playSound(victim.getLocation(), Sound.BLOCK_ANVIL_LAND, 1.0f, 1.2f);
            attacker.playSound(attacker.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

            ComponentUtil.sendMessage(victim, ComponentUtil.info("强击触发！免疫伤害并反击"));
            ComponentUtil.sendMessage(attacker, ComponentUtil.warning("受到反击伤害！"));

            return true; // 免疫伤害
        }

        return false; // 不免疫伤害
    }
    
    /**
     * 处理猎人自然祝福被动技能
     * 每45s 都会提供随机Buff
     */
    public void handleHunterNatureBlessing(Player player) {
        String skillName = "自然祝福";

        // 使用统一的冷却检查
        int cooldownTime = Profession.HUNTER.getPassiveSkillCooldown();
        if (!checkAndSetPassiveSkillCooldown(player, skillName, cooldownTime)) {
            return; // 在冷却中，不能触发
        }

        // 随机选择一个Buff
        PotionEffect effect = getRandomNatureBlessing();

        if (effect != null) {
            player.addPotionEffect(effect);

            // 播放效果
            player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.5f);
            ComponentUtil.sendMessage(player, ComponentUtil.info("自然祝福触发！获得" + getEffectName(effect.getType())));
        }
    }
    
    /**
     * 寻找安全位置
     */
    private Location findSafeLocation(Player player) {
        Location playerLoc = player.getLocation();

        // 获取玩家面向的方向向量
        org.bukkit.util.Vector direction = playerLoc.getDirection().normalize();

        // 尝试不同距离和角度的组合
        double[] distances = {20, 25, 30, 15, 35};
        double[] angles = {0, 0.5, -0.5, 1.0, -1.0}; // 弧度

        for (double distance : distances) {
            for (double angleOffset : angles) {
                org.bukkit.util.Vector teleportDirection = rotateVector(direction, angleOffset);

                double x = playerLoc.getX() + teleportDirection.getX() * distance;
                double z = playerLoc.getZ() + teleportDirection.getZ() * distance;

                // 尝试几个不同的Y坐标
                for (int yOffset = 5; yOffset >= -10; yOffset -= 2) {
                    Location testLoc = new Location(playerLoc.getWorld(), x, playerLoc.getY() + yOffset, z);

                    if (isSafeLocation(testLoc)) {
                        return testLoc.clone().add(0.5, 0.1, 0.5);
                    }
                }
            }
        }

        // 最后尝试向上传送
        Location upLoc = playerLoc.clone().add(0, 15, 0);
        if (isSafeLocation(upLoc)) {
            return upLoc;
        }

        return null;
    }

    /**
     * 在指定位置附近寻找安全位置
     */
    private Location findSafeLocationNear(Location targetLocation) {
        if (targetLocation.getWorld() == null) return null;

        // 首先检查目标位置本身
        if (isSafeLocation(targetLocation)) {
            return targetLocation.clone().add(0.5, 0.1, 0.5);
        }

        // 在目标位置周围搜索安全位置
        double[] offsets = {0, 1, -1, 2, -2, 3, -3};

        for (double xOffset : offsets) {
            for (double zOffset : offsets) {
                for (double yOffset : offsets) {
                    Location testLoc = targetLocation.clone().add(xOffset, yOffset, zOffset);
                    if (isSafeLocation(testLoc)) {
                        return testLoc.clone().add(0.5, 0.1, 0.5);
                    }
                }
            }
        }

        return null;
    }

    /**
     * 检查位置是否安全
     */
    private boolean isSafeLocation(Location loc) {
        if (loc.getWorld() == null) return false;

        // 检查Y坐标范围
        if (loc.getY() < -60 || loc.getY() > 320) return false;

        // 检查脚下有方块，头部和身体位置为空气
        Material footBlock = loc.getBlock().getType();
        if (footBlock.isAir()) return false;

        // 检查身体和头部位置是空气
        if (!loc.clone().add(0, 1, 0).getBlock().getType().isAir()) return false;
        if (!loc.clone().add(0, 2, 0).getBlock().getType().isAir()) return false;

        // 检查危险方块
        if (isDangerousBlock(footBlock)) return false;

        return true;
    }
    


    /**
     * 检查是否是危险方块
     */
    private boolean isDangerousBlock(Material material) {
        return switch (material) {
            case LAVA, FIRE, SOUL_FIRE, MAGMA_BLOCK, CACTUS,
                 SWEET_BERRY_BUSH, WITHER_ROSE, POWDER_SNOW -> true;
            default -> false;
        };
    }
    
    /**
     * 检查是否是矿物方块
     */
    private boolean isMineralBlock(Material material) {
        return switch (material) {
            case COAL_ORE, DEEPSLATE_COAL_ORE,
                 IRON_ORE, DEEPSLATE_IRON_ORE,
                 GOLD_ORE, DEEPSLATE_GOLD_ORE,
                 DIAMOND_ORE, DEEPSLATE_DIAMOND_ORE,
                 EMERALD_ORE, DEEPSLATE_EMERALD_ORE,
                 LAPIS_ORE, DEEPSLATE_LAPIS_ORE,
                 REDSTONE_ORE, DEEPSLATE_REDSTONE_ORE,
                 COPPER_ORE, DEEPSLATE_COPPER_ORE,
                 NETHER_GOLD_ORE, NETHER_QUARTZ_ORE -> true;
            default -> false;
        };
    }
    
    /**
     * 从矿物方块获取掉落物
     */
    private ItemStack getDropFromMineral(Material material) {
        return switch (material) {
            case COAL_ORE, DEEPSLATE_COAL_ORE -> new ItemStack(Material.COAL);
            case IRON_ORE, DEEPSLATE_IRON_ORE -> new ItemStack(Material.RAW_IRON);
            case GOLD_ORE, DEEPSLATE_GOLD_ORE, NETHER_GOLD_ORE -> new ItemStack(Material.RAW_GOLD);
            case DIAMOND_ORE, DEEPSLATE_DIAMOND_ORE -> new ItemStack(Material.DIAMOND);
            case EMERALD_ORE, DEEPSLATE_EMERALD_ORE -> new ItemStack(Material.EMERALD);
            case LAPIS_ORE, DEEPSLATE_LAPIS_ORE -> new ItemStack(Material.LAPIS_LAZULI, 4 + random.nextInt(5));
            case REDSTONE_ORE, DEEPSLATE_REDSTONE_ORE -> new ItemStack(Material.REDSTONE, 4 + random.nextInt(2));
            case COPPER_ORE, DEEPSLATE_COPPER_ORE -> new ItemStack(Material.RAW_COPPER, 2 + random.nextInt(4));
            case NETHER_QUARTZ_ORE -> new ItemStack(Material.QUARTZ);
            default -> null;
        };
    }
    
    /**
     * 获取随机自然祝福效果
     */
    private PotionEffect getRandomNatureBlessing() {
        PotionEffect[] blessings = {
            new PotionEffect(PotionEffectType.SPEED, 220, 0), // Speed I, 11 seconds
            new PotionEffect(PotionEffectType.REGENERATION, 220, 1), // Regeneration II, 11 seconds
            new PotionEffect(PotionEffectType.RESISTANCE, 140, 1), // Resistance II, 7 seconds
            new PotionEffect(PotionEffectType.STRENGTH, 100, 0), // Strength I, 11 seconds
            new PotionEffect(PotionEffectType.REGENERATION, 100, 2), // Regeneration III, 5 seconds
            new PotionEffect(PotionEffectType.ABSORPTION, 160, 1), // Absorption II, 8 seconds
            new PotionEffect(PotionEffectType.SPEED, 160, 1) // Speed II, 8 seconds
        };
        
        return blessings[random.nextInt(blessings.length)];
    }
    
    /**
     * 获取效果名称
     */
    private String getEffectName(PotionEffectType type) {
        if (type == PotionEffectType.SPEED) return "速度";
        if (type == PotionEffectType.REGENERATION) return "再生";
        if (type == PotionEffectType.RESISTANCE) return "抗性";
        if (type == PotionEffectType.STRENGTH) return "力量";
        if (type == PotionEffectType.ABSORPTION) return "伤害吸收";
        return type.getKey().getKey();
    }

    /**
     * 在水平面上旋转向量
     */
    private org.bukkit.util.Vector rotateVector(org.bukkit.util.Vector vector, double angleRadians) {
        double cos = Math.cos(angleRadians);
        double sin = Math.sin(angleRadians);

        double newX = vector.getX() * cos - vector.getZ() * sin;
        double newZ = vector.getX() * sin + vector.getZ() * cos;

        return new org.bukkit.util.Vector(newX, vector.getY(), newZ);
    }

    /**
     * 处理暗影刺客背刺被动技能
     * 从背后攻击敌人造成目标最大生命值25%的额外伤害，8秒冷却时间
     */
    public void handleShadowAssassinBackstab(Player attacker, Player victim, org.bukkit.event.entity.EntityDamageByEntityEvent event) {
        UUID attackerId = attacker.getUniqueId();
        String skillName = "背刺";

        // 检查是否为队友
        com.projectSource.ultimateManhurt.game.GameSession gameSession =
            plugin.getGameManager().getGameSessionByPlayer(attacker.getUniqueId());

        if (gameSession != null) {
            com.projectSource.ultimateManhurt.game.PlayerRole attackerRole = gameSession.getPlayerRole(attacker.getUniqueId());
            com.projectSource.ultimateManhurt.game.PlayerRole victimRole = gameSession.getPlayerRole(victim.getUniqueId());

            // 如果是队友，不触发背刺
            if (attackerRole == victimRole && attackerRole != null) {
                return; // 不对队友使用背刺
            }
        }

        // 检查冷却时间
        if (plugin.getProfessionManager().getSkillCooldown().isOnCooldown(attackerId, skillName)) {
            return; // 冷却中，不触发背刺
        }

        // 计算受害者的朝向
        org.bukkit.util.Vector victimDirection = victim.getLocation().getDirection().normalize();

        // 计算从受害者到攻击者的向量
        org.bukkit.util.Vector toAttacker = attacker.getLocation().toVector().subtract(victim.getLocation().toVector()).normalize();

        // 计算受害者朝向与攻击者位置的夹角
        double dotProduct = victimDirection.dot(toAttacker);

        // 如果夹角大于120度，说明是背刺
        if (dotProduct < -0.5) { // 约120度以上的角度才算背刺
            // 原伤害 + 目标最大生命值25%的额外伤害
            double originalDamage = event.getDamage();
            double victimMaxHealth = victim.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
            double extraDamage = victimMaxHealth * 0.25;
            double totalDamage = originalDamage + extraDamage;

            // 设置为原伤害 + 额外伤害
            event.setDamage(totalDamage);

            // 设置冷却时间
            plugin.getProfessionManager().getSkillCooldown().setCooldown(attackerId, skillName, 8);

            // 触发Boss Bar更新
            plugin.getProfessionManager().getSkillBossBarManager().createSkillBossBar(attacker);

            // 播放效果
            attacker.playSound(attacker.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_ATTACK_CRIT, 1.0f, 1.2f);
            victim.playSound(victim.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

            // 消息反馈
            ComponentUtil.sendMessage(attacker, ComponentUtil.info("背刺成功！造成原伤害+" + String.format("%.1f", extraDamage) + "额外伤害(25%最大血量)"));
            ComponentUtil.sendMessage(victim, ComponentUtil.warning("被 " + attacker.getName() + " 背刺了！"));


        }
    }

    /**
     * 处理蜘蛛毒液狂飙被动技能
     * 攻击蜘蛛的人会获得3s中毒效果，每次伤害重置cd
     */
    public void handleSpiderVenomRage(Player spider, Player attacker) {
        // 给攻击者施加3秒中毒效果
        attacker.addPotionEffect(new org.bukkit.potion.PotionEffect(
            org.bukkit.potion.PotionEffectType.POISON, 60, 0)); // 3秒 = 60 tick

        // 播放效果
        attacker.playSound(attacker.getLocation(), org.bukkit.Sound.ENTITY_SPIDER_HURT, 1.0f, 1.0f);
        spider.playSound(spider.getLocation(), org.bukkit.Sound.ENTITY_SPIDER_AMBIENT, 1.0f, 1.2f);

        // 消息反馈
        ComponentUtil.sendMessage(attacker, ComponentUtil.warning("攻击蜘蛛后中毒了！"));
        ComponentUtil.sendMessage(spider, ComponentUtil.info("毒液狂飙触发！" + attacker.getName() + " 中毒了"));


    }



    /**
     * 处理萨满动物伙伴被动技能 - 简化版：等待游戏开始后3分钟生成狗
     */
    public void handleShamanAnimalCompanion(Player shaman) {
        UUID shamanId = shaman.getUniqueId();

        // 清理可能存在的旧狗
        clearShamanDogs(shamanId);

        // 创建新的狗数据（先不生成狗）
        ShamanDogData dogData = new ShamanDogData();
        shamanDogs.put(shamanId, dogData);

        ComponentUtil.sendMessage(shaman, ComponentUtil.info("动物伙伴激活！狗伙伴将在游戏开始3分钟后召唤"));
        plugin.getLogger().info("萨满 " + shaman.getName() + " 的动物伙伴技能已激活，将在游戏开始后生成狗伙伴");
    }

    /**
     * 游戏开始时为萨满生成狗伙伴 - 简化版
     * 由GameSession在游戏开始时调用
     */
    public void onGameStart() {
        // 为所有萨满玩家启动狗生成计时器
        for (UUID shamanId : shamanDogs.keySet()) {
            Player shaman = Bukkit.getPlayer(shamanId);
            if (shaman != null && shaman.isOnline() &&
                plugin.getProfessionManager().getPlayerProfession(shamanId) == Profession.SHAMAN) {

                // 延迟3分钟后生成狗
                Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    if (shaman.isOnline() &&
                        plugin.getProfessionManager().getPlayerProfession(shamanId) == Profession.SHAMAN) {

                        ShamanDogData dogData = shamanDogs.get(shamanId);
                        if (dogData != null) {
                            // 生成两只初始狗
                            for (int i = 0; i < 2; i++) {
                                Wolf dog = spawnDogForShaman(shaman);
                                if (dog != null) {
                                    dogData.getInitialDogs().add(dog);
                                }
                            }

                            if (!dogData.getInitialDogs().isEmpty()) {
                                ComponentUtil.sendMessage(shaman, ComponentUtil.info("狗伙伴已召唤！获得了 " + dogData.getInitialDogs().size() + " 只狗伙伴"));
                                plugin.getLogger().info("萨满 " + shaman.getName() + " 在游戏开始3分钟后获得了 " + dogData.getInitialDogs().size() + " 只狗伙伴");
                            }
                        }
                    }
                }, 3600L); // 3分钟延迟 (3600 tick = 180秒 = 3分钟)
            }
        }
    }



    /**
     * 为萨满生成一只狗
     */
    private Wolf spawnDogForShaman(Player shaman) {
        Location spawnLoc = findSafeDogSpawnLocation(shaman.getLocation());
        if (spawnLoc == null) {
            return null;
        }

        Wolf dog = (Wolf) spawnLoc.getWorld().spawnEntity(spawnLoc, EntityType.WOLF);

        // 设置狗的属性
        dog.setOwner(shaman);
        dog.setTamed(true);
        dog.setAdult();
        dog.customName(Component.text(shaman.getName() + "的狗伙伴", NamedTextColor.GOLD));
        dog.setCustomNameVisible(true);

        // 设置30点生命值
        dog.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).setBaseValue(30.0);
        dog.setHealth(30.0);

        // 设置5点攻击力
        dog.getAttribute(org.bukkit.attribute.Attribute.ATTACK_DAMAGE).setBaseValue(5.0);

        // 设置狗默认攻击猎人
        setDogToAttackHunters(dog, shaman);

        return dog;
    }

    /**
     * 寻找安全的狗生成位置
     */
    private Location findSafeDogSpawnLocation(Location center) {
        for (int attempts = 0; attempts < 20; attempts++) {
            double angle = random.nextDouble() * 2 * Math.PI;
            double distance = 2 + random.nextDouble() * 4; // 2-6格距离

            double x = center.getX() + Math.cos(angle) * distance;
            double z = center.getZ() + Math.sin(angle) * distance;

            // 从萨满位置开始向上寻找安全位置
            Location testLoc = new Location(center.getWorld(), x, center.getY(), z);

            // 寻找安全的Y坐标，优先向上寻找
            for (int yOffset = 0; yOffset <= 10; yOffset++) {
                testLoc.setY(center.getY() + yOffset);
                if (isSafeDogLocation(testLoc)) {
                    // 确保狗不会生成在地下
                    testLoc.setY(testLoc.getY() + 0.5); // 稍微抬高一点
                    return testLoc;
                }
            }

            // 如果向上找不到，尝试向下找
            for (int yOffset = -1; yOffset >= -5; yOffset--) {
                testLoc.setY(center.getY() + yOffset);
                if (isSafeDogLocation(testLoc)) {
                    testLoc.setY(testLoc.getY() + 0.5); // 稍微抬高一点
                    return testLoc;
                }
            }
        }

        // 如果找不到安全位置，在萨满正上方生成
        Location safeLoc = center.clone().add(0, 1, 0);
        return safeLoc;
    }

    /**
     * 检查位置是否适合狗生成
     */
    private boolean isSafeDogLocation(Location loc) {
        Material ground = loc.getBlock().getType();
        Material above = loc.clone().add(0, 1, 0).getBlock().getType();
        Material above2 = loc.clone().add(0, 2, 0).getBlock().getType();
        Material below = loc.clone().add(0, -1, 0).getBlock().getType();

        // 检查地面是否安全（不是空气，不是危险方块）
        boolean groundSafe = ground.isSolid() &&
                           ground != Material.LAVA &&
                           ground != Material.WATER &&
                           ground != Material.CACTUS &&
                           ground != Material.MAGMA_BLOCK;

        // 检查上方空间是否足够
        boolean spaceAbove = (above.isAir() || above == Material.SHORT_GRASS || above == Material.TALL_GRASS) &&
                           (above2.isAir() || above2 == Material.SHORT_GRASS || above2 == Material.TALL_GRASS);

        // 检查不会掉落（下方有支撑）
        boolean hasSupport = below.isSolid() || ground.isSolid();

        return groundSafe && spaceAbove && hasSupport;
    }

    /**
     * 处理萨满击杀猎人，奖励额外的狗 - 奖励狗死亡后不会复活
     */
    public void handleShamanKillReward(Player shaman) {
        UUID shamanId = shaman.getUniqueId();
        ShamanDogData dogData = shamanDogs.get(shamanId);

        if (dogData == null) {
            dogData = new ShamanDogData();
            shamanDogs.put(shamanId, dogData);
        }

        // 生成奖励狗（不会复活）
        Wolf newDog = spawnDogForShaman(shaman);
        if (newDog != null) {
            dogData.getRewardDogs().add(newDog);
            ComponentUtil.sendMessage(shaman, ComponentUtil.info("击杀奖励！获得了一只新的狗伙伴"));
            plugin.getLogger().info("萨满 " + shaman.getName() + " 因击杀获得新狗，当前狗数量: " + dogData.getAllDogs().size());
        }
    }

    /**
     * 处理狗死亡 - 简化版
     */
    public void handleDogDeath(Wolf dog, Player shaman) {
        UUID shamanId = shaman.getUniqueId();
        ShamanDogData dogData = shamanDogs.get(shamanId);

        if (dogData != null) {
            // 扣除萨满20%最大生命值
            double maxHealth = shaman.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
            double currentHealth = shaman.getHealth();
            double damage = maxHealth * 0.20;
            double newHealth = Math.max(1.0, currentHealth - damage);
            shaman.setHealth(newHealth);

            // 明确区分初始狗和奖励狗的复活机制
            boolean isInitialDog = dogData.isInitialDog(dog);
            dogData.removeDog(dog);

            if (isInitialDog) {
                // 只有初始狗（游戏开始3分钟后获得的两只狗）会复活
                scheduleDogRespawn(shaman, dogData, true);
                ComponentUtil.sendMessage(shaman, ComponentUtil.warning("初始狗伙伴死亡！你失去了20%最大生命值，90秒后复活"));
                plugin.getLogger().info("萨满 " + shaman.getName() + " 的初始狗死亡，将在90秒后复活");
            } else {
                // 奖励狗（击杀猎人获得的额外狗）死亡后不会复活
                ComponentUtil.sendMessage(shaman, ComponentUtil.warning("奖励狗伙伴死亡！你失去了20%最大生命值，不会复活"));
                plugin.getLogger().info("萨满 " + shaman.getName() + " 的奖励狗死亡，不会复活");
            }

            plugin.getLogger().info("萨满 " + shaman.getName() + " 的狗死亡，剩余狗数量: " + dogData.getAllDogs().size());
        }
    }

    /**
     * 安排狗复活 - 支持多只狗独立复活
     */
    private void scheduleDogRespawn(Player shaman, ShamanDogData dogData, boolean isInitialDog) {
        // 使用数组来存储任务引用，以便在lambda中使用
        final BukkitTask[] taskRef = new BukkitTask[1];

        BukkitTask respawnTask = Bukkit.getScheduler().runTaskLater(plugin, () -> {
            // 检查萨满是否还在线且还是萨满职业
            if (shaman.isOnline() &&
                plugin.getProfessionManager().getPlayerProfession(shaman.getUniqueId()) == Profession.SHAMAN) {

                Wolf newDog = spawnDogForShaman(shaman);
                if (newDog != null) {
                    if (isInitialDog) {
                        dogData.getInitialDogs().add(newDog);
                    } else {
                        dogData.getRewardDogs().add(newDog);
                    }
                    ComponentUtil.sendMessage(shaman, ComponentUtil.info("狗伙伴复活了！"));
                }
            }

            // 任务完成后从列表中移除
            if (taskRef[0] != null) {
                dogData.removeRespawnTask(taskRef[0]);
            }
        }, 1800L); // 90秒 = 1800 tick

        // 存储任务引用
        taskRef[0] = respawnTask;

        // 添加到复活任务列表中，而不是覆盖
        dogData.addRespawnTask(respawnTask);
    }

    /**
     * 清理萨满的所有狗 - 简化版
     */
    public void clearShamanDogs(UUID shamanId) {
        ShamanDogData dogData = shamanDogs.get(shamanId);
        if (dogData != null) {
            dogData.cleanup(); // 使用内置的清理方法
        }
        shamanDogs.remove(shamanId);
    }

    /**
     * 启动猪灵光环任务
     * 每2秒检查一次所有猪灵玩家，应用光环效果
     */
    private void startPiglinAuraTask() {
        piglinAuraTask = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            // 遍历所有在线玩家，找到猪灵职业的玩家
            for (Player player : Bukkit.getOnlinePlayers()) {
                Profession profession = plugin.getProfessionManager().getPlayerProfession(player.getUniqueId());
                if (profession == Profession.PIGLIN) {
                    // 应用猪灵光环效果
                    applyPiglinAura(player);
                }
            }
        }, 0L, 40L); // 每2秒执行一次 (40 tick = 2秒)
    }

    /**
     * 应用猪灵光环效果
     */
    private void applyPiglinAura(Player piglin) {
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(piglin.getUniqueId());
        if (gameSession == null) {
            return;
        }

        Location piglinLoc = piglin.getLocation();

        // 给范围内的友军（猎人）速度加成
        for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
            if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) == com.projectSource.ultimateManhurt.game.PlayerRole.HUNTER) {
                // 检查是否在同一世界
                if (!onlinePlayer.getWorld().equals(piglinLoc.getWorld())) {
                    continue; // 不在同一世界，跳过
                }

                double distance = onlinePlayer.getLocation().distance(piglinLoc);
                if (distance <= 30.0) { // 30格范围
                    onlinePlayer.addPotionEffect(new org.bukkit.potion.PotionEffect(
                        org.bukkit.potion.PotionEffectType.SPEED, 60, 0)); // 3秒速度1（比任务间隔稍长）
                }
            }
        }

        // 给范围内的速通者高亮标记（伤害加深在伤害事件中处理）
        for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
            if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) == com.projectSource.ultimateManhurt.game.PlayerRole.SPEEDRUNNER) {
                // 检查是否在同一世界
                if (!onlinePlayer.getWorld().equals(piglinLoc.getWorld())) {
                    continue; // 不在同一世界，跳过
                }

                double distance = onlinePlayer.getLocation().distance(piglinLoc);
                if (distance <= 30.0) { // 30格范围
                    // 高亮效果
                    onlinePlayer.addPotionEffect(new org.bukkit.potion.PotionEffect(
                        org.bukkit.potion.PotionEffectType.GLOWING, 60, 0)); // 3秒发光
                }
            }
        }
    }

    /**
     * 停止光环任务（在插件卸载时调用） - 简化版
     */
    public void shutdown() {
        if (piglinAuraTask != null) {
            piglinAuraTask.cancel();
            piglinAuraTask = null;
        }

        // 清理所有萨满的狗（使用简化的清理方法）
        for (UUID shamanId : new ArrayList<>(shamanDogs.keySet())) {
            clearShamanDogs(shamanId);
        }
        shamanDogs.clear();
    }



    /**
     * 设置狗默认攻击猎人
     */
    private void setDogToAttackHunters(Wolf dog, Player shaman) {
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(shaman.getUniqueId());
        if (gameSession == null) {
            return;
        }

        // 寻找最近的猎人作为目标
        Player nearestHunter = null;
        double nearestDistance = Double.MAX_VALUE;

        for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
            // 检查是否是猎人
            if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) != com.projectSource.ultimateManhurt.game.PlayerRole.HUNTER) {
                continue;
            }

            // 检查是否在同一世界
            if (!onlinePlayer.getWorld().equals(dog.getWorld())) {
                continue;
            }

            // 计算距离
            double distance = dog.getLocation().distance(onlinePlayer.getLocation());
            if (distance < nearestDistance && distance <= 50.0) { // 50格范围内
                nearestDistance = distance;
                nearestHunter = onlinePlayer;
            }
        }

        // 如果找到猎人，设置为攻击目标
        if (nearestHunter != null) {
            dog.setTarget(nearestHunter);
            plugin.getLogger().info("萨满 " + shaman.getName() + " 的狗开始攻击最近的猎人 " + nearestHunter.getName());
        }
    }

    /**
     * 命令萨满的狗攻击指定目标
     */
    public void commandShamanDogsToAttack(Player shaman, Player target) {
        UUID shamanId = shaman.getUniqueId();
        ShamanDogData dogData = shamanDogs.get(shamanId);

        if (dogData == null || dogData.getAllDogs().isEmpty()) {
            return; // 没有狗
        }

        // 检查目标是否有效
        if (target == null || !target.isOnline() || target.isDead()) {
            return;
        }

        // 检查萨满和目标是否在同一世界
        if (!shaman.getWorld().equals(target.getWorld())) {
            return;
        }

        // 命令所有有效的狗攻击目标
        int dogsCommanded = 0;
        for (Wolf dog : dogData.getAllDogs()) {
            if (dog != null && !dog.isDead() && dog.isValid()) {
                // 检查狗是否在同一世界
                if (!dog.getWorld().equals(target.getWorld())) {
                    continue; // 不在同一世界，跳过
                }

                // 检查狗是否在合理范围内（50格内）
                double distance = dog.getLocation().distance(target.getLocation());
                if (distance <= 50.0) {
                    dog.setTarget(target);
                    dogsCommanded++;
                }
            }
        }

        if (dogsCommanded > 0) {
            plugin.getLogger().info("萨满 " + shaman.getName() + " 的 " + dogsCommanded + " 只狗开始攻击 " + target.getName());
        }
    }

    /**
     * 处理恐惧魔王灵魂虹吸被动技能 - 重新设计
     * 击杀玩家后回复最大生命值的20%并获得18秒的力量1效果
     */
    public void handleFearLordSoulSiphonKill(Player fearLord, Player victim) {
        UUID fearLordId = fearLord.getUniqueId();

        // 设置18秒的buff结束时间
        long buffEndTime = System.currentTimeMillis() + 18000;
        fearLordSoulSiphonBuff.put(fearLordId, buffEndTime);

        // 回复最大生命值的20%
        double maxHealth = fearLord.getAttribute(Attribute.MAX_HEALTH).getValue();
        double currentHealth = fearLord.getHealth();
        double healAmount = maxHealth * 0.20;
        double newHealth = Math.min(maxHealth, currentHealth + healAmount);
        fearLord.setHealth(newHealth);

        // 应用力量1效果（18秒）
        fearLord.addPotionEffect(new PotionEffect(PotionEffectType.STRENGTH, 360, 0)); // 18秒力量1

        // 播放效果
        fearLord.playSound(fearLord.getLocation(), Sound.ENTITY_WITHER_AMBIENT, 1.0f, 1.2f);
        fearLord.spawnParticle(Particle.SOUL, fearLord.getLocation().add(0, 1, 0), 20, 0.5, 0.5, 0.5, 0.1);

        // 消息反馈
        ComponentUtil.sendMessage(fearLord, ComponentUtil.info("灵魂虹吸！击杀 " + victim.getName() + " 回复生命值并获得18秒力量效果！"));

        plugin.getLogger().info("恐惧魔王 " + fearLord.getName() + " 击杀 " + victim.getName() + " 回复了 " + String.format("%.1f", healAmount) + " 点生命值");
    }



    /**
     * 重置恐惧魔王的属性到基础值
     */
    public void resetFearLordAttributes(Player fearLord) {
        if (fearLord == null || !fearLord.isOnline()) {
            return;
        }

        // 重置攻击力到基础值（通常是1.0）
        fearLord.getAttribute(Attribute.ATTACK_DAMAGE).setBaseValue(1.0);

        // 重置最大生命值到基础值（通常是20.0）
        fearLord.getAttribute(Attribute.MAX_HEALTH).setBaseValue(20.0);

        // 清理虹吸buff记录
        fearLordSoulSiphonBuff.remove(fearLord.getUniqueId());

        // 移除可能存在的药水效果
        fearLord.removePotionEffect(PotionEffectType.SPEED);
        fearLord.removePotionEffect(PotionEffectType.STRENGTH);
    }



    /**
     * 启动莱娜森之祝福光环 - 重新实现
     */
    public void startLenaForestBlessing(Player lena) {
        UUID lenaId = lena.getUniqueId();

        // 获取或创建光环数据
        LenaAuraData auraData = lenaAuraSystem.computeIfAbsent(lenaId, k -> new LenaAuraData());

        // 启动光环任务
        BukkitTask auraTask = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            Player currentLena = Bukkit.getPlayer(lenaId);
            if (currentLena == null || !currentLena.isOnline()) {
                // 玩家离线，停止光环
                auraData.stopAura();
                lenaAuraSystem.remove(lenaId);
                return;
            }

            // 检查是否还是莱娜职业
            Profession profession = plugin.getProfessionManager().getPlayerProfession(lenaId);
            if (profession != Profession.LENA) {
                // 不再是莱娜，停止光环
                auraData.stopAura();
                lenaAuraSystem.remove(lenaId);
                return;
            }

            // 直接应用森之祝福光环效果（移除禁用机制）
            applyForestBlessingAura(currentLena);

        }, 0L, 40L); // 每2秒执行一次

        auraData.setAuraTask(auraTask);
    }

    /**
     * 应用森之祝福光环效果 - 新版本：提供闪避效果和白天生命恢复
     */
    private void applyForestBlessingAura(Player lena) {
        UUID lenaId = lena.getUniqueId();
        Location lenaLoc = lena.getLocation();
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(lenaId);
        if (gameSession == null) return;

        // 检查是否是白天
        boolean isDaytime = lena.getWorld().getTime() >= 0 && lena.getWorld().getTime() < 12000;

        // 对周围10格内的友方玩家应用效果
        for (Player nearbyPlayer : Bukkit.getOnlinePlayers()) {
            if (gameSession.getPlayerRole(nearbyPlayer.getUniqueId()) == com.projectSource.ultimateManhurt.game.PlayerRole.SPEEDRUNNER) {
                double distance = nearbyPlayer.getLocation().distance(lenaLoc);
                if (distance <= 10.0) {
                    // 标记玩家受到森之祝福光环影响（用于闪避检查）
                    setForestBlessingEffect(nearbyPlayer.getUniqueId());

                    // 白天额外提供生命恢复加成
                    if (isDaytime) {
                        nearbyPlayer.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, 60, 0)); // 3秒生命恢复1
                    }

                    // 视觉效果
                    if (random.nextInt(10) == 0) { // 10%概率显示粒子效果
                        nearbyPlayer.spawnParticle(org.bukkit.Particle.HAPPY_VILLAGER,
                            nearbyPlayer.getLocation().add(0, 1, 0), 3, 0.5, 0.5, 0.5, 0.1);
                    }
                }
            }
        }
    }

    /**
     * 莱娜受到攻击处理 - 移除禁用机制，光环不再被禁用
     */
    public void handleLenaAuraDisable(Player lena) {
        // 光环不再被禁用，此方法保留以避免调用错误，但不执行任何操作
    }

    /**
     * 检查莱娜光环是否被禁用 - 移除禁用机制，始终返回false
     */
    public boolean isLenaAuraDisabled(UUID lenaId) {
        return false; // 光环不再被禁用
    }

    /**
     * 停止莱娜森之祝福光环 - 重新实现
     */
    public void stopLenaForestBlessing(UUID lenaId) {
        LenaAuraData auraData = lenaAuraSystem.remove(lenaId);
        if (auraData != null) {
            auraData.stopAura();
        }

        // 清理该莱娜的效果标记（只清理她自己的）
        forestBlessingEffectPlayers.remove(lenaId);
    }

    /**
     * 设置森之祝福效果（标记玩家受到光环影响）
     */
    private void setForestBlessingEffect(UUID playerId) {
        // 设置效果过期时间（当前时间 + 5秒）
        long expiryTime = System.currentTimeMillis() + 5000;
        forestBlessingEffectPlayers.put(playerId, expiryTime);
    }

    /**
     * 检查玩家是否受到森之祝福效果影响
     */
    public boolean hasForestBlessingEffect(UUID playerId) {
        Long expiryTime = forestBlessingEffectPlayers.get(playerId);
        if (expiryTime == null) {
            return false;
        }

        long currentTime = System.currentTimeMillis();
        if (currentTime > expiryTime) {
            // 效果已过期，移除标记
            forestBlessingEffectPlayers.remove(playerId);
            return false;
        }

        return true;
    }

    /**
     * 处理森之祝福闪避效果
     * 35%概率闪避攻击
     */
    public boolean handleForestBlessingDodge(Player victim) {
        UUID victimId = victim.getUniqueId();

        // 检查是否受到森之祝福效果影响
        if (!hasForestBlessingEffect(victimId)) {
            return false; // 不受森之祝福影响，不能闪避
        }

        // 35%概率闪避
        if (random.nextDouble() < 0.35) {
            // 播放闪避效果
            victim.playSound(victim.getLocation(), Sound.ENTITY_PLAYER_ATTACK_SWEEP, 1.0f, 1.5f);
            victim.spawnParticle(org.bukkit.Particle.SWEEP_ATTACK,
                victim.getLocation().add(0, 1, 0), 5, 0.5, 0.5, 0.5, 0.1);

            ComponentUtil.sendMessage(victim, ComponentUtil.info("森之祝福！成功闪避攻击"));

            return true; // 闪避成功
        }

        return false; // 闪避失败
    }

    /**
     * 强制清理所有灵魂虹吸效果（游戏结束时调用） - 重新实现
     */
    public void forceCleanupAllSoulSiphon() {
        // 遍历所有虹吸buff数据
        for (Map.Entry<UUID, Long> entry : fearLordSoulSiphonBuff.entrySet()) {
            UUID fearLordId = entry.getKey();
            Player fearLord = Bukkit.getPlayer(fearLordId);

            if (fearLord != null && fearLord.isOnline()) {
                // 移除药水效果
                fearLord.removePotionEffect(PotionEffectType.SPEED);
                fearLord.removePotionEffect(PotionEffectType.STRENGTH);
            }
        }

        // 清理所有虹吸buff数据
        fearLordSoulSiphonBuff.clear();
    }

    /**
     * 禁用玩家跳跃指定时间
     */
    public void disableJump(UUID playerId, long durationMs) {
        long endTime = System.currentTimeMillis() + durationMs;
        jumpDisabled.put(playerId, endTime);
    }

    /**
     * 检查玩家是否被禁用跳跃
     */
    public boolean isJumpDisabled(UUID playerId) {
        Long disabledEndTime = jumpDisabled.get(playerId);
        if (disabledEndTime == null) {
            return false;
        }

        long currentTime = System.currentTimeMillis();
        if (currentTime >= disabledEndTime) {
            // 禁用时间已过期，清理状态
            jumpDisabled.remove(playerId);
            return false;
        }

        return true; // 仍在禁用期间
    }

    /**
     * 立即解除玩家的跳跃禁用
     */
    public void enableJump(UUID playerId) {
        jumpDisabled.remove(playerId);
    }

    /**
     * 统一的被动技能冷却检查和设置
     * 用于所有需要冷却的被动技能
     */
    public boolean checkAndSetPassiveSkillCooldown(Player player, String skillName, int cooldownSeconds) {
        UUID playerId = player.getUniqueId();

        // 检查冷却时间
        if (plugin.getProfessionManager().getSkillCooldown().isOnCooldown(playerId, skillName)) {
            return false; // 在冷却中，不能触发
        }

        // 设置冷却时间
        plugin.getProfessionManager().getSkillCooldown().setCooldown(playerId, skillName, cooldownSeconds);

        // 触发Boss Bar更新
        plugin.getProfessionManager().getSkillBossBarManager().createSkillBossBar(player);

        return true; // 可以触发技能
    }

    /**
     * 处理船长朗姆酒被动技能
     * 当血量低于75%时触发，接下来10秒内对船长的伤害降低95%
     * 但在15秒后会逐渐返还给船长(总伤害分5段，每段间隔1.5秒)，但不致死。60秒冷却
     */
    public double handleCaptainRumPassive(Player captain, double damage, org.bukkit.event.entity.EntityDamageEvent.DamageCause cause) {
        UUID captainId = captain.getUniqueId();
        String skillName = "朗姆酒";

        // 检查是否需要触发朗姆酒效果
        double currentHealth = captain.getHealth();
        double maxHealth = captain.getAttribute(Attribute.MAX_HEALTH).getValue();
        double healthPercentage = currentHealth / maxHealth;

        // 检查伤害后是否会致死或血量低于75%
        boolean wouldBeFatal = (currentHealth - damage) <= 0;
        boolean lowHealth = healthPercentage < 0.75;

        CaptainRumData rumData = captainRumStates.computeIfAbsent(captainId, k -> new CaptainRumData());

        // 检查触发条件：(血量低于75% 或 即将致死) 且 使用统一的冷却检查
        int cooldownTime = Profession.CAPTAIN.getPassiveSkillCooldown();
        if ((lowHealth || wouldBeFatal) && checkAndSetPassiveSkillCooldown(captain, skillName, cooldownTime)) {
            // 触发朗姆酒效果
            rumData.activate();

            // 播放效果
            captain.playSound(captain.getLocation(), Sound.ENTITY_GENERIC_DRINK, 1.0f, 0.8f);
            captain.spawnParticle(Particle.BUBBLE_POP, captain.getLocation().add(0, 1, 0), 20, 0.5, 0.5, 0.5, 0.1);

            ComponentUtil.sendMessage(captain, ComponentUtil.info("朗姆酒效果激活！伤害减免95%"));

            // 15秒后开始返还伤害
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                startDamageReturn(captainId);
            }, 300L); // 15秒 = 300 tick
        }

        // 如果朗姆酒效果激活中，减免95%伤害并记录
        if (rumData.isActive()) {
            double reducedDamage = damage * 0.05; // 减免95%，只承受5%
            double blockedDamage = damage * 0.95; // 被阻挡的95%伤害

            rumData.addPendingDamage(blockedDamage);

            return reducedDamage;
        }

        return damage; // 正常伤害
    }

    /**
     * 判断是否为魔法伤害
     */
    private boolean isMagicDamage(org.bukkit.event.entity.EntityDamageEvent.DamageCause cause) {
        return switch (cause) {
            case MAGIC, WITHER, POISON, DRAGON_BREATH, SONIC_BOOM -> true;
            default -> false;
        };
    }

    /**
     * 开始返还伤害给船长
     */
    private void startDamageReturn(UUID captainId) {
        CaptainRumData rumData = captainRumStates.get(captainId);
        if (rumData == null) return;

        List<Double> pendingDamage = rumData.getPendingDamage();
        if (pendingDamage.isEmpty()) return;

        // 计算总伤害并分成5段
        double totalDamage = pendingDamage.stream().mapToDouble(Double::doubleValue).sum();
        double damagePerSegment = totalDamage / 5.0;

        Player captain = Bukkit.getPlayer(captainId);
        if (captain != null) {
            ComponentUtil.sendMessage(captain, ComponentUtil.warning("朗姆酒效果结束，开始承受延迟伤害"));
        }

        // 分5段返还伤害，每段间隔1.5秒
        for (int i = 0; i < 5; i++) {
            final int segment = i + 1;
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                Player currentCaptain = Bukkit.getPlayer(captainId);
                if (currentCaptain != null && currentCaptain.isOnline()) {
                    // 计算伤害，但不致死
                    double currentHealth = currentCaptain.getHealth();
                    double finalDamage = Math.min(damagePerSegment, currentHealth - 0.5); // 至少保留0.5血

                    if (finalDamage > 0) {
                        currentCaptain.damage(finalDamage);
                        ComponentUtil.sendMessage(currentCaptain, ComponentUtil.warning("承受延迟伤害 (" + segment + "/5)"));
                    }
                }
            }, (long) (30 * i)); // 每1.5秒 = 30 tick
        }

        // 清理数据
        rumData.clearPendingDamage();
    }

    /**
     * 处理暗影刺客背刺被动技能
     * 从背后攻击敌人造成原伤害+目标最大生命值25%的额外伤害，8秒冷却时间
     */
    public double handleShadowAssassinBackstab(Player assassin, Player target, double originalDamage) {
        String skillName = "背刺";

        // 检查是否从背后攻击
        if (!isAttackingFromBehind(assassin, target)) {
            return originalDamage; // 不是背刺，返回原伤害
        }

        // 使用统一的冷却检查
        int cooldownTime = Profession.SHADOW_ASSASSIN.getPassiveSkillCooldown();
        if (!checkAndSetPassiveSkillCooldown(assassin, skillName, cooldownTime)) {
            return originalDamage; // 在冷却中，返回原伤害
        }

        // 计算背刺伤害：原伤害 + 目标最大生命值的25%
        double maxHealth = target.getAttribute(Attribute.MAX_HEALTH).getValue();
        double backstabDamage = originalDamage + (maxHealth * 0.25);

        // 播放背刺效果
        assassin.playSound(assassin.getLocation(), Sound.ENTITY_PLAYER_ATTACK_CRIT, 1.0f, 0.8f);
        target.spawnParticle(Particle.CRIT, target.getLocation().add(0, 1, 0), 15, 0.5, 0.5, 0.5, 0.1);

        ComponentUtil.sendMessage(assassin, ComponentUtil.info("背刺成功！造成额外伤害"));
        ComponentUtil.sendMessage(target, ComponentUtil.warning("被背刺了！"));

        return backstabDamage;
    }

    /**
     * 检查是否从背后攻击
     */
    private boolean isAttackingFromBehind(Player attacker, Player target) {
        org.bukkit.util.Vector targetDirection = target.getLocation().getDirection().normalize();
        org.bukkit.util.Vector attackDirection = attacker.getLocation().toVector().subtract(target.getLocation().toVector()).normalize();

        // 计算角度，如果小于90度（点积大于0）则认为是从背后攻击
        double dotProduct = targetDirection.dot(attackDirection);
        return dotProduct > 0.3; // 允许一定的角度偏差
    }

    /**
     * 处理僵尸尸鬼狂怒被动技能
     * 攻击速通方会窃取速通方的饱食度(根据最终造成伤害，窃取0.5倍)
     */
    public void handleZombieGhoulRage(Player zombie, Player target, double damage) {
        // 根据伤害计算窃取的饱食度 (0.5倍)
        double stealAmount = damage * 0.5;

        // 只窃取饱食度，不窃取饱和度
        int targetFoodLevel = target.getFoodLevel();
        int zombieFoodLevel = zombie.getFoodLevel();

        // 计算实际窃取的饱食度
        int foodToSteal = (int) Math.min(stealAmount, targetFoodLevel);
        if (foodToSteal > 0) {
            target.setFoodLevel(Math.max(0, targetFoodLevel - foodToSteal));
            zombie.setFoodLevel(Math.min(20, zombieFoodLevel + foodToSteal));
        }

        // 播放效果
        zombie.playSound(zombie.getLocation(), Sound.ENTITY_ZOMBIE_AMBIENT, 1.0f, 0.8f);
        target.playSound(target.getLocation(), Sound.ENTITY_PLAYER_BURP, 1.0f, 0.5f);

        ComponentUtil.sendMessage(zombie, ComponentUtil.info("窃取了 " + target.getName() + " 的 " + foodToSteal + " 点饱食度"));
        ComponentUtil.sendMessage(target, ComponentUtil.warning("饱食度被僵尸窃取了 " + foodToSteal + " 点"));

        plugin.getLogger().info("僵尸 " + zombie.getName() + " 攻击 " + target.getName() + " 造成 " + damage + " 伤害，窃取了 " + foodToSteal + " 点饱食度");
    }

    /**
     * 处理机器人分则能成被动技能
     * 每放置一组方块获得一个金苹果
     */
    public void handleRobotBlockPlacement(Player robot) {
        UUID robotId = robot.getUniqueId();

        // 增加方块计数
        int currentCount = robotBlockCounter.getOrDefault(robotId, 0) + 1;
        robotBlockCounter.put(robotId, currentCount);

        // 检查是否达到64个方块
        if (currentCount >= 64) {
            // 重置计数器
            robotBlockCounter.put(robotId, 0);

            // 给予金苹果
            ItemStack goldenApple = new ItemStack(Material.GOLDEN_APPLE, 1);
            robot.getInventory().addItem(goldenApple);

            // 播放音效
            robot.playSound(robot.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 0.5f, 1.2f);

            ComponentUtil.sendMessage(robot, ComponentUtil.info("分则能成！放置64个方块获得金苹果"));

            plugin.getLogger().info("机器人 " + robot.getName() + " 放置64个方块获得金苹果");
        } else {
            // 显示进度
            if (currentCount % 16 == 0) { // 每16个方块提示一次
                ComponentUtil.sendMessage(robot, ComponentUtil.info("方块进度: " + currentCount + "/64"));
            }
        }
    }

    /**
     * 处理女巫药水专精被动技能
     * 水瓶转为魔药或毒药（30秒冷却）
     */
    public void handleWitchPotionMastery(Player witch, ItemStack item) {
        String skillName = "药水专精";

        // 使用统一的被动技能冷却检查
        int cooldownTime = Profession.WITCH.getPassiveSkillCooldown();
        if (!checkAndSetPassiveSkillCooldown(witch, skillName, cooldownTime)) {
            return; // 在冷却中，不能触发
        }
        if (item.getType() == Material.POTION) {
            // 普通水瓶转为魔药

            // 使用PDC标记为魔药
            org.bukkit.inventory.meta.ItemMeta meta = item.getItemMeta();
            if (meta != null) {
                NamespacedKey magicPotionKey = new NamespacedKey(plugin, "magic_potion");
                meta.getPersistentDataContainer().set(magicPotionKey, PersistentDataType.BOOLEAN, true);

                // 使用Component设置名称
                Component magicPotionName = Component.text("魔药")
                    .color(NamedTextColor.GOLD)
                    .decoration(TextDecoration.ITALIC, false);
                meta.displayName(magicPotionName);

                // 添加不可见的附魔效果
                meta.addEnchant(Enchantment.UNBREAKING, 1, true);
                meta.addItemFlags(ItemFlag.HIDE_ENCHANTS);

                item.setItemMeta(meta);
            }

            ComponentUtil.sendMessage(witch, ComponentUtil.info("水瓶转化为魔药！"));

        } else if (item.getType() == Material.SPLASH_POTION) {
            // 喷溅水瓶转为毒药
            org.bukkit.inventory.meta.ItemMeta meta = item.getItemMeta();
            if (meta != null) {
                NamespacedKey poisonPotionKey = new NamespacedKey(plugin, "poison_potion");
                meta.getPersistentDataContainer().set(poisonPotionKey, PersistentDataType.BOOLEAN, true);

                // 使用Component设置名称
                Component poisonPotionName = Component.text("毒药")
                    .color(NamedTextColor.DARK_RED)
                    .decoration(TextDecoration.ITALIC, false);
                meta.displayName(poisonPotionName);

                // 添加不可见的附魔效果
                meta.addEnchant(Enchantment.UNBREAKING, 1, true);
                meta.addItemFlags(ItemFlag.HIDE_ENCHANTS);

                item.setItemMeta(meta);
            }

            ComponentUtil.sendMessage(witch, ComponentUtil.info("喷溅水瓶转化为毒药！"));
        }

        // 播放转换音效
        witch.playSound(witch.getLocation(), Sound.BLOCK_BREWING_STAND_BREW, 1.0f, 1.2f);
    }

    /**
     * 使用魔药效果
     */
    public void useMagicPotion(Player player) {
        Random random = new Random();
        int effect = random.nextInt(8); // 0-7，8种可能的效果

        switch (effect) {
            case 0:
                player.addPotionEffect(new PotionEffect(PotionEffectType.SATURATION, 200, 0)); // 饱和 10s
                ComponentUtil.sendMessage(player, ComponentUtil.info("魔药效果：饱和"));
                break;
            case 1:
                player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 300, 1)); // 速度2 15s
                ComponentUtil.sendMessage(player, ComponentUtil.info("魔药效果：速度II"));
                break;
            case 2:
                player.addPotionEffect(new PotionEffect(PotionEffectType.INSTANT_HEALTH, 1, 2)); // 瞬间治疗3
                ComponentUtil.sendMessage(player, ComponentUtil.info("魔药效果：瞬间治疗III"));
                break;
            case 3:
                player.addPotionEffect(new PotionEffect(PotionEffectType.ABSORPTION, 300, 1)); // 伤害吸收2 15s
                ComponentUtil.sendMessage(player, ComponentUtil.info("魔药效果：伤害吸收II"));
                break;
            case 4:
                player.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, 220, 0)); // 抗性提升1 11s
                ComponentUtil.sendMessage(player, ComponentUtil.info("魔药效果：抗性提升I"));
                break;
            case 5:
                player.addPotionEffect(new PotionEffect(PotionEffectType.FIRE_RESISTANCE, 1200, 0)); // 抗火1 60s
                ComponentUtil.sendMessage(player, ComponentUtil.info("魔药效果：抗火"));
                break;
            case 6:
                player.addPotionEffect(new PotionEffect(PotionEffectType.WATER_BREATHING, 1200, 0)); // 水下呼吸 60s
                ComponentUtil.sendMessage(player, ComponentUtil.info("魔药效果：水下呼吸"));
                break;
            case 7:
                ComponentUtil.sendMessage(player, ComponentUtil.warning("魔药没有效果..."));
                break;
        }

        // 播放音效
        player.playSound(player.getLocation(), Sound.ENTITY_GENERIC_DRINK, 1.0f, 1.0f);
    }

    /**
     * 使用毒药效果（对敌人）
     */
    public void applyPoisonEffect(Player target) {
        Random random = new Random();
        int effect = random.nextInt(5); // 0-4，5种可能的效果

        switch (effect) {
            case 0:
                target.addPotionEffect(new PotionEffect(PotionEffectType.INSTANT_DAMAGE, 1, 2)); // 瞬间伤害3
                ComponentUtil.sendMessage(target, ComponentUtil.warning("受到毒药效果：瞬间伤害III"));
                break;
            case 1:
                target.addPotionEffect(new PotionEffect(PotionEffectType.POISON, 360, 1)); // 中毒2 18s
                ComponentUtil.sendMessage(target, ComponentUtil.warning("受到毒药效果：中毒II"));
                break;
            case 2:
                target.addPotionEffect(new PotionEffect(PotionEffectType.WEAKNESS, 220, 1)); // 虚弱2 11s
                ComponentUtil.sendMessage(target, ComponentUtil.warning("受到毒药效果：虚弱II"));
                break;
            case 3:
                target.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 300, 1)); // 迟缓2 15s
                ComponentUtil.sendMessage(target, ComponentUtil.warning("受到毒药效果：迟缓II"));
                break;
            case 4:
                ComponentUtil.sendMessage(target, ComponentUtil.info("毒药没有效果..."));
                break;
        }

        // 播放音效
        target.playSound(target.getLocation(), Sound.ENTITY_SPLASH_POTION_BREAK, 1.0f, 0.8f);
    }

    /**
     * 检查物品是否是魔药
     */
    public boolean isMagicPotion(ItemStack item) {
        if (item == null || item.getItemMeta() == null) return false;

        NamespacedKey magicPotionKey = new NamespacedKey(plugin, "magic_potion");
        return item.getItemMeta().getPersistentDataContainer().has(magicPotionKey, PersistentDataType.BOOLEAN);
    }

    /**
     * 检查物品是否是毒药
     */
    public boolean isPoisonPotion(ItemStack item) {
        if (item == null || item.getItemMeta() == null) return false;

        NamespacedKey poisonPotionKey = new NamespacedKey(plugin, "poison_potion");
        return item.getItemMeta().getPersistentDataContainer().has(poisonPotionKey, PersistentDataType.BOOLEAN);
    }

    /**
     * 处理春秋蝉被动技能（只对方源生效）
     * 当方源血量降低到13%时，有概率让所有玩家回到上一个创建的光阴分身状态
     * 360秒冷却（冷却期间概率递减）
     */
    public boolean handleFangyuanSpringAutumnCicada(Player player) {
        return handleFangyuanSpringAutumnCicada(player, 0.0);
    }

    /**
     * 处理春秋蝉被动技能（只对方源生效）- 带伤害参数版本
     * 当方源血量降低到13%或即将致死时，有概率让所有玩家回到上一个创建的光阴分身状态
     * 360秒冷却（冷却期间概率递减）
     */
    public boolean handleFangyuanSpringAutumnCicada(Player player, double incomingDamage) {
        UUID playerId = player.getUniqueId();
        String skillName = "春秋蝉";

        // 检查血量是否低于13%或即将致死
        double currentHealth = player.getHealth();
        double maxHealth = player.getAttribute(Attribute.MAX_HEALTH).getValue();
        double healthPercentage = currentHealth / maxHealth;
        boolean wouldBeFatal = (currentHealth - incomingDamage) <= 0;
        boolean lowHealth = healthPercentage <= 0.13;

        if (!lowHealth && !wouldBeFatal) {
            return false; // 血量不够低且不会致死，不触发
        }

        // 检查是否有时光分身记录
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(playerId);
        if (gameSession == null) {
            return false;
        }

        String sessionId = gameSession.getSessionId();
        if (!plugin.getProfessionManager().getActiveSkillHandler().hasTimeState(sessionId)) {
            return false; // 没有时光分身记录
        }

        // 检查游戏中是否有方源玩家
        boolean hasFangyuan = false;
        UUID fangyuanId = null;
        for (UUID targetId : gameSession.getPlayerRoles().keySet()) {
            Profession profession = plugin.getProfessionManager().getPlayerProfession(targetId);
            if (profession == Profession.FANGYUAN) {
                hasFangyuan = true;
                fangyuanId = targetId;
                break;
            }
        }

        if (!hasFangyuan) {
            return false; // 游戏中没有方源，不能触发春秋蝉
        }

        // 计算当前概率（每次触发后降低20%，最低至20.9%）
        int triggerCount = springAutumnCicadaTriggerCount.getOrDefault(playerId, 0);
        double currentProbability = 1.0; // 初始100%概率

        // 每次触发后概率 *= 0.8，直到降至0.2097152 (20.9%)
        for (int i = 0; i < triggerCount; i++) {
            currentProbability *= 0.8;
            if (currentProbability <= 0.2097152) {
                currentProbability = 0.2097152;
                break;
            }
        }

        // 检查被动技能冷却并调整概率
        int cooldownTime = Profession.FANGYUAN.getPassiveSkillCooldown();
        if (plugin.getProfessionManager().getSkillCooldown().isOnCooldown(playerId, skillName)) {
            // 在冷却中，根据冷却进度调整概率
            int remainingCooldown = plugin.getProfessionManager().getSkillCooldown().getRemainingCooldown(playerId, skillName);
            double cooldownProgress = (double)(cooldownTime - remainingCooldown) / cooldownTime; // 已完成的冷却比例
            currentProbability *= cooldownProgress; // 概率 = 基础概率 * 冷却进度
        }

        // 伪随机检查
        if (!pseudoRandom.checkTrigger(playerId, "fangyuan_spring_autumn_cicada", currentProbability)) {
            return false; // 概率检查失败
        }

        // 触发成功，增加触发次数
        springAutumnCicadaTriggerCount.put(playerId, triggerCount + 1);

        // 概率检查通过后，设置冷却时间（如果不在冷却中）
        if (!plugin.getProfessionManager().getSkillCooldown().isOnCooldown(playerId, skillName)) {
            plugin.getProfessionManager().getSkillCooldown().setCooldown(playerId, skillName, cooldownTime);
            plugin.getProfessionManager().getSkillBossBarManager().createSkillBossBar(player);
        }

        // 获取时光分身状态并恢复所有玩家位置
        var timeState = plugin.getProfessionManager().getActiveSkillHandler().getGameTimeState(sessionId);
        if (timeState != null) {
            // 恢复所有玩家状态
            int restoredCount = 0;
            for (UUID targetId : timeState.getPlayerIds()) {
                Player targetPlayer = Bukkit.getPlayer(targetId);
                if (targetPlayer != null && targetPlayer.isOnline()) {
                    var snapshot = timeState.getPlayerSnapshot(targetId);
                    if (snapshot != null) {
                        snapshot.restoreToPlayer(targetPlayer);
                        restoredCount++;

                        // 播放恢复效果
                        targetPlayer.playSound(targetPlayer.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.2f);
                        targetPlayer.spawnParticle(Particle.PORTAL, targetPlayer.getLocation().add(0, 1, 0), 30, 1, 1, 1, 0.1);
                    }
                }
            }

            // 计算下次触发概率
            double nextProbability = currentProbability * 0.8;
            if (nextProbability < 0.2097152) {
                nextProbability = 0.2097152;
            }

            // 显示触发信息，包含当前使用的概率和冷却状态
            String probabilityInfo = String.format("%.1f", currentProbability * 100) + "%";

            if (plugin.getProfessionManager().getSkillCooldown().isOnCooldown(playerId, skillName)) {
                int remainingCooldown = plugin.getProfessionManager().getSkillCooldown().getRemainingCooldown(playerId, skillName);
                ComponentUtil.sendMessage(player, ComponentUtil.info("春秋蝉触发！所有人回到了时光分身状态（触发概率: " + probabilityInfo + "，冷却剩余: " + remainingCooldown + "秒，恢复了 " + restoredCount + " 名玩家）"));
            } else {
                String nextProbabilityInfo = String.format("%.1f", nextProbability * 100) + "%";
                ComponentUtil.sendMessage(player, ComponentUtil.info("春秋蝉触发！所有人回到了时光分身状态（触发概率: " + probabilityInfo + "，下次基础概率: " + nextProbabilityInfo + "，恢复了 " + restoredCount + " 名玩家）"));
            }

            // 广播给其他玩家
            for (UUID targetId : gameSession.getPlayerRoles().keySet()) {
                Player targetPlayer = Bukkit.getPlayer(targetId);
                if (targetPlayer != null && targetPlayer.isOnline() && !targetPlayer.equals(player)) {
                    ComponentUtil.sendMessage(targetPlayer, ComponentUtil.warning("方源 " + player.getName() + " 触发了春秋蝉！所有人回到了时光分身状态"));
                }
            }

            return true;
        }

        return false;
    }

    /**
     * 清除玩家被动技能状态
     */
    public void clearPlayerStates(UUID playerId) {
        springAutumnCicadaTriggerCount.remove(playerId);
        fearLordSoulSiphonBuff.remove(playerId);
        jumpDisabled.remove(playerId);
    }

    /**
     * 处理暗夜领主暗夜猎影被动技能
     * 在夜晚时获得速度2、生命恢复1、夜视效果
     */
    public void handleNightLordNightHunting(Player player) {
        // 检查是否是夜晚
        var world = player.getWorld();
        long time = world.getTime();
        boolean isNight = time >= 12000 && time <= 24000; // 夜晚时间范围

        if (isNight) {
            // 应用夜晚增益效果，延长持续时间避免闪烁
            player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 140, 1, false, false)); // 速度2，7秒
            player.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, 140, 0, false, false)); // 生命恢复1，7秒
            player.addPotionEffect(new PotionEffect(PotionEffectType.NIGHT_VISION, 300, 0, false, false)); // 夜视，15秒
        }
    }

    /**
     * 启动暗夜领主的夜晚检测任务
     */
    public void startNightLordNightDetection(Player player) {
        UUID playerId = player.getUniqueId();

        // 取消之前的任务
        BukkitTask existingTask = nightLordNightVisionTasks.get(playerId);
        if (existingTask != null) {
            existingTask.cancel();
        }

        // 启动新的检测任务，每5秒检查一次
        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            Player currentPlayer = Bukkit.getPlayer(playerId);
            if (currentPlayer == null || !currentPlayer.isOnline()) {
                // 玩家离线，取消任务
                nightLordNightVisionTasks.remove(playerId);
                return;
            }

            // 检查是否还是暗夜领主职业
            Profession profession = plugin.getProfessionManager().getPlayerProfession(playerId);
            if (profession != Profession.NIGHT_LORD) {
                // 不再是暗夜领主，取消任务
                BukkitTask taskToCancel = nightLordNightVisionTasks.remove(playerId);
                if (taskToCancel != null) {
                    taskToCancel.cancel();
                }
                return;
            }

            // 应用暗夜猎影效果
            handleNightLordNightHunting(currentPlayer);
        }, 0L, 100L); // 立即开始，每5秒执行一次

        nightLordNightVisionTasks.put(playerId, task);
    }

    /**
     * 停止暗夜领主的夜晚检测任务
     */
    public void stopNightLordNightDetection(UUID playerId) {
        BukkitTask task = nightLordNightVisionTasks.remove(playerId);
        if (task != null) {
            task.cancel();
        }
    }

    // 村民互助光环数据
    private final ConcurrentHashMap<UUID, BukkitTask> villagerAuraTasks = new ConcurrentHashMap<>();

    // 赏金猎人暗影步状态
    private final ConcurrentHashMap<UUID, Boolean> bountyHunterShadowStepActive = new ConcurrentHashMap<>();

    /**
     * 处理村民互助被动技能
     * 20格内每有一个队友，村民和其队友获得5%伤害减免（最多25%）
     * 队友受伤时，村民有20%概率为其回复2点生命值
     */
    public void handleVillagerMutualAid(Player villager) {
        UUID villagerId = villager.getUniqueId();
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(villagerId);

        if (gameSession == null) {
            return;
        }

        Location villagerLoc = villager.getLocation();
        int teammateCount = 0;

        // 计算20格内的队友数量
        for (UUID targetId : gameSession.getPlayerRoles().keySet()) {
            Player targetPlayer = Bukkit.getPlayer(targetId);
            if (targetPlayer != null && targetPlayer.isOnline() && !targetPlayer.equals(villager)) {
                // 检查是否是队友
                if (gameSession.getPlayerRole(targetId) == gameSession.getPlayerRole(villagerId)) {
                    // 检查距离
                    if (targetPlayer.getWorld().equals(villagerLoc.getWorld()) &&
                        targetPlayer.getLocation().distance(villagerLoc) <= 20.0) {
                        teammateCount++;
                    }
                }
            }
        }

        // 计算伤害减免（每个队友5%，最多25%）
        int damageReduction = Math.min(teammateCount * 5, 25);

        // 这里可以通过给予抗性效果来模拟伤害减免
        if (damageReduction > 0) {
            int resistanceLevel = Math.min(damageReduction / 10, 2); // 转换为抗性等级
            villager.addPotionEffect(new PotionEffect(
                PotionEffectType.RESISTANCE, 120, resistanceLevel, false, false)); // 6秒抗性效果
        }
    }

    /**
     * 启动村民互助光环检测
     */
    public void startVillagerMutualAid(Player villager) {
        UUID villagerId = villager.getUniqueId();

        // 取消之前的任务
        BukkitTask existingTask = villagerAuraTasks.get(villagerId);
        if (existingTask != null) {
            existingTask.cancel();
        }

        // 启动新的检测任务，每6秒检查一次
        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            Player currentPlayer = Bukkit.getPlayer(villagerId);
            if (currentPlayer == null || !currentPlayer.isOnline()) {
                villagerAuraTasks.remove(villagerId);
                return;
            }

            // 检查是否还是村民职业
            Profession profession = plugin.getProfessionManager().getPlayerProfession(villagerId);
            if (profession != Profession.VILLAGER) {
                BukkitTask taskToCancel = villagerAuraTasks.remove(villagerId);
                if (taskToCancel != null) {
                    taskToCancel.cancel();
                }
                return;
            }

            // 应用村民互助效果
            handleVillagerMutualAid(currentPlayer);
        }, 0L, 120L); // 立即开始，每6秒执行一次

        villagerAuraTasks.put(villagerId, task);
    }

    /**
     * 停止村民互助光环检测
     */
    public void stopVillagerMutualAid(UUID villagerId) {
        BukkitTask task = villagerAuraTasks.remove(villagerId);
        if (task != null) {
            task.cancel();
        }
    }

    /**
     * 处理村民互助的治疗效果
     * 当队友受伤时，村民有20%概率为其回复2点生命值
     */
    public void handleVillagerHealTeammate(Player villager, Player injuredTeammate) {
        UUID villagerId = villager.getUniqueId();
        String eventName = "村民互助治疗";

        // 20%概率触发治疗
        if (pseudoRandom.checkTrigger(villagerId, eventName, 0.2)) {
            double currentHealth = injuredTeammate.getHealth();
            double maxHealth = injuredTeammate.getAttribute(Attribute.MAX_HEALTH).getValue();
            double newHealth = Math.min(currentHealth + 2.0, maxHealth);

            injuredTeammate.setHealth(newHealth);

            // 播放治疗效果
            injuredTeammate.playSound(injuredTeammate.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 0.5f, 1.2f);
            injuredTeammate.spawnParticle(Particle.HEART, injuredTeammate.getLocation().add(0, 1, 0), 3, 0.3, 0.3, 0.3, 0.1);

            ComponentUtil.sendMessage(villager, ComponentUtil.info("村民互助触发！治疗了 " + injuredTeammate.getName()));
            ComponentUtil.sendMessage(injuredTeammate, ComponentUtil.info("受到村民 " + villager.getName() + " 的治疗！"));
        }
    }

    /**
     * 设置赏金猎人暗影步状态
     */
    public void setBountyHunterShadowStepActive(UUID playerId, boolean active) {
        if (active) {
            bountyHunterShadowStepActive.put(playerId, true);
        } else {
            bountyHunterShadowStepActive.remove(playerId);
        }
    }

    /**
     * 检查赏金猎人是否在暗影步状态
     */
    public boolean isBountyHunterShadowStepActive(UUID playerId) {
        return bountyHunterShadowStepActive.getOrDefault(playerId, false);
    }

    /**
     * 处理赏金猎人忍术被动技能
     * 攻击敌人时20%概率偷取1-3个物品，7%概率偷取装备
     */
    public void handleBountyHunterNinjutsu(Player bountyHunter, Player target) {
        UUID hunterId = bountyHunter.getUniqueId();
        String itemStealEvent = "忍术偷取物品";
        String equipStealEvent = "忍术偷取装备";

        // 检查是否在暗影步状态（必定触发）
        boolean guaranteedTrigger = isBountyHunterShadowStepActive(hunterId);

        // 20%概率偷取物品（暗影步状态下必定触发）
        if (guaranteedTrigger || pseudoRandom.checkTrigger(hunterId, itemStealEvent, 0.2)) {
            stealRandomItems(bountyHunter, target, 1 + new Random().nextInt(3)); // 1-3个物品
        }

        // 7%概率偷取装备（暗影步状态下也必定触发）
        if (guaranteedTrigger || pseudoRandom.checkTrigger(hunterId, equipStealEvent, 0.07)) {
            stealRandomEquipment(bountyHunter, target);
        }
    }

    /**
     * 偷取随机物品
     */
    private void stealRandomItems(Player thief, Player target, int itemCount) {
        ItemStack[] inventory = target.getInventory().getContents();
        List<Integer> validSlots = new ArrayList<>();

        // 找到所有有物品的槽位
        for (int i = 0; i < inventory.length; i++) {
            if (inventory[i] != null && inventory[i].getType() != Material.AIR) {
                validSlots.add(i);
            }
        }

        if (validSlots.isEmpty()) {
            ComponentUtil.sendMessage(thief, ComponentUtil.warning("目标背包为空，无法偷取"));
            return;
        }

        Random random = new Random();
        int stolenCount = 0;

        for (int i = 0; i < itemCount && !validSlots.isEmpty(); i++) {
            int randomIndex = random.nextInt(validSlots.size());
            int slotIndex = validSlots.get(randomIndex);

            ItemStack item = inventory[slotIndex];
            if (item != null && item.getAmount() > 0) {
                // 只偷取1个物品（不是整组）
                ItemStack stolenItem = item.clone();
                stolenItem.setAmount(1);

                // 从目标背包中移除
                if (item.getAmount() == 1) {
                    inventory[slotIndex] = null;
                    validSlots.remove(randomIndex);
                } else {
                    item.setAmount(item.getAmount() - 1);
                }

                // 添加到小偷背包
                thief.getInventory().addItem(stolenItem);
                stolenCount++;
            }
        }

        target.getInventory().setContents(inventory);

        if (stolenCount > 0) {
            ComponentUtil.sendMessage(thief, ComponentUtil.info("忍术触发！偷取了 " + stolenCount + " 个物品"));
            ComponentUtil.sendMessage(target, ComponentUtil.warning("被 " + thief.getName() + " 偷取了 " + stolenCount + " 个物品！"));
        }
    }

    /**
     * 偷取随机装备
     */
    private void stealRandomEquipment(Player thief, Player target) {
        ItemStack[] armor = target.getInventory().getArmorContents();
        List<Integer> validArmorSlots = new ArrayList<>();

        // 找到所有有装备的槽位
        for (int i = 0; i < armor.length; i++) {
            if (armor[i] != null && armor[i].getType() != Material.AIR) {
                validArmorSlots.add(i);
            }
        }

        if (validArmorSlots.isEmpty()) {
            ComponentUtil.sendMessage(thief, ComponentUtil.warning("目标没有装备，无法偷取"));
            return;
        }

        Random random = new Random();
        int randomIndex = random.nextInt(validArmorSlots.size());
        int armorSlot = validArmorSlots.get(randomIndex);

        ItemStack equipment = armor[armorSlot];
        if (equipment != null) {
            // 降低装备耐久度（使用现代API）
            if (equipment.getType().getMaxDurability() > 0) {
                org.bukkit.inventory.meta.ItemMeta meta = equipment.getItemMeta();
                if (meta instanceof org.bukkit.inventory.meta.Damageable damageable) {
                    int currentDamage = damageable.getDamage();
                    int maxDurability = equipment.getType().getMaxDurability();
                    int additionalDamage = (int) (maxDurability * 0.1); // 增加10%的损坏
                    int newDamage = Math.min(currentDamage + additionalDamage, maxDurability - 1);
                    damageable.setDamage(newDamage);
                    equipment.setItemMeta(meta);
                }
            }

            // 移除装备并给小偷
            armor[armorSlot] = null;
            target.getInventory().setArmorContents(armor);
            thief.getInventory().addItem(equipment);

            ComponentUtil.sendMessage(thief, ComponentUtil.info("忍术触发！偷取了装备: " + equipment.getType().name()));
            ComponentUtil.sendMessage(target, ComponentUtil.warning("被 " + thief.getName() + " 偷取了装备！"));
        }
    }
}
