package com.projectSource.ultimateManhurt.banpick;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.game.GameState;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.CraftItemEvent;
import org.bukkit.event.inventory.PrepareItemCraftEvent;
import org.bukkit.inventory.ItemStack;

/**
 * Ban Pick合成限制监听器
 * 只禁止合成被Ban的物品，不影响使用
 */
public class BanPickCraftingListener implements Listener {
    
    private final UltimateManhurt plugin;
    
    public BanPickCraftingListener(UltimateManhurt plugin) {
        this.plugin = plugin;
    }
    
    /**
     * 监听合成事件，阻止被Ban物品的合成
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onCraftItem(CraftItemEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        ItemStack result = event.getRecipe().getResult();
        
        // 检查是否在游戏中
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
        if (gameSession == null || gameSession.getState() != GameState.RUNNING) {
            return;
        }
        
        // 检查Ban Pick是否已完成
        BanPickManager banPickManager = gameSession.getBanPickManager();
        if (banPickManager == null || banPickManager.getCurrentPhase() != BanPickPhase.COMPLETED) {
            return;
        }
        
        // 检查合成的物品是否被Ban
        BanPickItem banPickItem = BanPickItem.fromMaterial(result.getType());
        if (banPickItem != null) {
            // 根据玩家角色检查对应的Ban列表
            com.projectSource.ultimateManhurt.game.PlayerRole playerRole = gameSession.getPlayerRole(player.getUniqueId());
            boolean isBanned = false;
            String banReason = "";

            if (playerRole == com.projectSource.ultimateManhurt.game.PlayerRole.SPEEDRUNNER) {
                // 速通者：检查捕猎者Ban的物品
                if (banPickManager.getHunterBannedItems().contains(banPickItem)) {
                    isBanned = true;
                    banReason = "捕猎者已禁用此物品的合成";
                }
            } else if (playerRole == com.projectSource.ultimateManhurt.game.PlayerRole.HUNTER) {
                // 捕猎者：检查速通者Ban的物品
                if (banPickManager.getSpeedrunnerBannedItems().contains(banPickItem)) {
                    isBanned = true;
                    banReason = "速通者已禁用此物品的合成";
                }
            }

            if (isBanned) {
                // 取消合成
                event.setCancelled(true);

                // 发送提示消息
                ComponentUtil.sendMessage(player, ComponentUtil.error(
                    String.format("<red>%s 的合成已被禁用！%s", banPickItem.getDisplayName(), banReason)));

                // 播放错误音效
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);

                plugin.getLogger().info(String.format("玩家 %s (%s) 尝试合成被Ban的物品: %s - %s",
                    player.getName(), playerRole.name(), banPickItem.getDisplayName(), banReason));
            }
        }
    }
    
    /**
     * 监听合成准备事件，提前显示Ban状态
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onPrepareItemCraft(PrepareItemCraftEvent event) {
        if (event.getView().getPlayer() == null) {
            return;
        }
        
        Player player = (Player) event.getView().getPlayer();
        ItemStack result = event.getRecipe() != null ? event.getRecipe().getResult() : null;
        
        if (result == null) {
            return;
        }
        
        // 检查是否在游戏中
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
        if (gameSession == null || gameSession.getState() != GameState.RUNNING) {
            return;
        }
        
        // 检查Ban Pick是否已完成
        BanPickManager banPickManager = gameSession.getBanPickManager();
        if (banPickManager == null || banPickManager.getCurrentPhase() != BanPickPhase.COMPLETED) {
            return;
        }
        
        // 检查合成的物品是否被Ban
        BanPickItem banPickItem = BanPickItem.fromMaterial(result.getType());
        if (banPickItem != null) {
            // 根据玩家角色检查对应的Ban列表
            com.projectSource.ultimateManhurt.game.PlayerRole playerRole = gameSession.getPlayerRole(player.getUniqueId());
            boolean isBanned = false;

            if (playerRole == com.projectSource.ultimateManhurt.game.PlayerRole.SPEEDRUNNER) {
                // 速通者：检查捕猎者Ban的物品
                isBanned = banPickManager.getHunterBannedItems().contains(banPickItem);
            } else if (playerRole == com.projectSource.ultimateManhurt.game.PlayerRole.HUNTER) {
                // 捕猎者：检查速通者Ban的物品
                isBanned = banPickManager.getSpeedrunnerBannedItems().contains(banPickItem);
            }

            if (isBanned) {
                // 清除合成结果，阻止显示
                event.getInventory().setResult(null);

                plugin.getLogger().fine(String.format("阻止玩家 %s (%s) 预览被Ban物品的合成: %s",
                    player.getName(), playerRole.name(), banPickItem.getDisplayName()));
            }
        }
    }
    

}
