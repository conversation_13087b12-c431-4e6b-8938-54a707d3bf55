package com.projectSource.ultimateManhurt.tablist;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.game.GameState;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.room.Room;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextColor;
import org.bukkit.entity.Player;

/**
 * 玩家Tablist
 * 管理单个玩家的Tab列表显示
 */
public class PlayerTablist {

    private final UltimateManhurt plugin;
    private final Player player;
    private GameSession gameSession;
    private Room room;
    private TablistType type;

    // 动画相关
    private int animationFrame = 0;
    private Component[] headerFrames;

    /**
     * 游戏中的Tablist构造器
     */
    public PlayerTablist(UltimateManhurt plugin, Player player, GameSession gameSession) {
        this.plugin = plugin;
        this.player = player;
        this.gameSession = gameSession;
        this.type = TablistType.GAME;
        this.headerFrames = createGameHeaderFrames();
    }

    /**
     * 房间等待中的Tablist构造器
     */
    public PlayerTablist(UltimateManhurt plugin, Player player, Room room) {
        this.plugin = plugin;
        this.player = player;
        this.room = room;
        this.type = TablistType.ROOM;
        this.headerFrames = createRoomHeaderFrames();
    }

    /**
     * 大厅默认Tablist构造器
     */
    public PlayerTablist(UltimateManhurt plugin, Player player) {
        this.plugin = plugin;
        this.player = player;
        this.type = TablistType.LOBBY;
        this.headerFrames = createLobbyHeaderFrames();
    }

    /**
     * 显示Tablist
     */
    public void show() {
        updateContent();
    }

    /**
     * 隐藏Tablist
     */
    public void hide() {
        player.sendPlayerListHeaderAndFooter(Component.empty(), Component.empty());
        player.playerListName(Component.text(player.getName()));
    }

    /**
     * 更新Tablist内容
     */
    public void update() {
        updateContent();
        updatePlayerDisplayName();
    }

    /**
     * 更新内容
     */
    public void updateContent() {
        Component header = buildHeader();
        Component footer = buildFooter();

        player.sendPlayerListHeaderAndFooter(header, footer);
    }

    /**
     * 更新玩家显示名称
     */
    private void updatePlayerDisplayName() {
        if (!plugin.getConfigManager().getBoolean("tablist.player-display.enabled", true)) {
            return;
        }

        Component displayName = TablistUtil.buildPlayerDisplayName(player, plugin, gameSession, room);
        player.playerListName(displayName);
    }

    /**
     * 构建头部
     */
    private Component buildHeader() {
        if (headerFrames == null || headerFrames.length == 0) {
            return Component.empty();
        }

        Component currentFrame = headerFrames[animationFrame % headerFrames.length];
        animationFrame++;

        return currentFrame;
    }

    /**
     * 构建底部
     */
    private Component buildFooter() {
        switch (type) {
            case GAME:
                return TablistUtil.buildGameFooter(player, gameSession, plugin);
            case ROOM:
                return TablistUtil.buildRoomFooter(player, room, plugin);
            case LOBBY:
            default:
                return TablistUtil.buildLobbyFooter(player, plugin);
        }
    }

    /**
     * 创建游戏头部动画帧
     */
    private Component[] createGameHeaderFrames() {
        return new Component[]{
                Component.text()
                        .append(Component.newline())
                        .append(Component.text("⚔ ", TextColor.color(0xFF6B35)))
                        .append(Component.text("ULTIMATE MANHUNT", TextColor.color(0xFFD700)))
                        .append(Component.text(" ⚔", TextColor.color(0xFF6B35)))
                        .append(Component.newline())
                        .append(Component.text("◆ ", TextColor.color(0xFF6B35)))
                        .append(Component.text("猎杀游戏进行中", TextColor.color(0x87CEEB)))
                        .append(Component.text(" ◆", TextColor.color(0xFF6B35)))
                        .append(Component.newline())
                        .build(),

                Component.text()
                        .append(Component.newline())
                        .append(Component.text("⚡ ", TextColor.color(0xFF6B35)))
                        .append(Component.text("ULTIMATE MANHUNT", TextColor.color(0xFFD700)))
                        .append(Component.text(" ⚡", TextColor.color(0xFF6B35)))
                        .append(Component.newline())
                        .append(Component.text("◇ ", TextColor.color(0xFF6B35)))
                        .append(Component.text("猎杀游戏进行中", TextColor.color(0x87CEEB)))
                        .append(Component.text(" ◇", TextColor.color(0xFF6B35)))
                        .append(Component.newline())
                        .build()
        };
    }

    /**
     * 创建房间头部动画帧
     */
    private Component[] createRoomHeaderFrames() {
        return new Component[]{
                Component.text()
                        .append(Component.newline())
                        .append(Component.text("🏠 ", TextColor.color(0xFF6B35)))
                        .append(Component.text("ULTIMATE MANHUNT", TextColor.color(0xFFD700)))
                        .append(Component.text(" 🏠", TextColor.color(0xFF6B35)))
                        .append(Component.newline())
                        .append(Component.text("◆ ", TextColor.color(0xFF6B35)))
                        .append(Component.text("等待游戏开始", TextColor.color(0x87CEEB)))
                        .append(Component.text(" ◆", TextColor.color(0xFF6B35)))
                        .append(Component.newline())
                        .build()
        };
    }

    /**
     * 创建大厅头部动画帧
     */
    private Component[] createLobbyHeaderFrames() {
        return new Component[]{
                Component.text()
                        .append(Component.newline())
                        .append(Component.text("✦ ", TextColor.color(0xFF6B35)))
                        .append(Component.text("ULTIMATE MANHUNT", TextColor.color(0xFFD700)))
                        .append(Component.text(" ✦", TextColor.color(0xFF6B35)))
                        .append(Component.newline())
                        .append(Component.text("◆ ", TextColor.color(0xFF6B35)))
                        .append(Component.text("游戏大厅", TextColor.color(0x87CEEB)))
                        .append(Component.text(" ◆", TextColor.color(0xFF6B35)))
                        .append(Component.newline())
                        .build(),

                Component.text()
                        .append(Component.newline())
                        .append(Component.text("⭐ ", TextColor.color(0xFF6B35)))
                        .append(Component.text("ULTIMATE MANHUNT", TextColor.color(0xFFD700)))
                        .append(Component.text(" ⭐", TextColor.color(0xFF6B35)))
                        .append(Component.newline())
                        .append(Component.text("◇ ", TextColor.color(0xFF6B35)))
                        .append(Component.text("游戏大厅", TextColor.color(0x87CEEB)))
                        .append(Component.text(" ◇", TextColor.color(0xFF6B35)))
                        .append(Component.newline())
                        .build()
        };
    }

    // Getters
    public GameSession getGameSession() {
        return gameSession;
    }

    public Room getRoom() {
        return room;
    }

    public TablistType getType() {
        return type;
    }

    public Player getPlayer() {
        return player;
    }

    /**
     * Tablist类型枚举
     */
    public enum TablistType {
        GAME,    // 游戏中
        ROOM,    // 房间等待
        LOBBY    // 大厅
    }
}
