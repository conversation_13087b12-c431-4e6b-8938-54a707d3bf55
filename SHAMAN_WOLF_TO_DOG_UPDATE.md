# 萨满狼伙伴改为狗伙伴 + 技能调整

## 🔄 主要变更

### 1. 萨满动物伙伴：狼 → 狗
**变更原因：** 用户要求将萨满的狼伙伴改为狗伙伴

**变更内容：**
- 🐺 **狼伙伴** → 🐕 **狗伙伴**
- 所有相关描述和代码都已更新

### 2. 萨满龙卷风暴技能增强
**持续时间：** 5秒 → 8秒
**最大伤害：** 1.5*数量 → 2.25*数量

### 3. 猪灵集结技能增强
**抗性持续时间：** 10秒 → 30秒

## 🐕 狗伙伴系统更新

### 职业描述更新
```java
SHAMAN(
    "萨满",
    "自然召唤师，拥有狗伙伴和风暴控制", // 狼伙伴 → 狗伙伴
    PlayerRole.SPEEDRUNNER,
    Material.BONE,
    NamedTextColor.GREEN,
    "🐕", // 🐺 → 🐕
    // 被动技能
    "动物伙伴",
    "出生获得两只狗，狗拥有80点生命值。狗死亡后会扣除萨满20%的最大生命值，每次击杀猎人额外多获得一只狗。每只狗死亡后过90s会复活", // 狼 → 狗
    0, // 被动技能无冷却
```

### 代码变量名更新
**数据结构：**
```java
// 萨满狗伙伴管理 <萨满UUID, 狗列表>
private final Map<UUID, List<Wolf>> shamanDogs = new HashMap<>(); // shamanWolves → shamanDogs

// 狗复活任务 <狗UUID, 复活任务>
private final Map<UUID, BukkitTask> dogRespawnTasks = new HashMap<>(); // wolfRespawnTasks → dogRespawnTasks
```

**方法名更新：**
- `spawnWolfForShaman()` → `spawnDogForShaman()`
- `findSafeWolfSpawnLocation()` → `findSafeDogSpawnLocation()`
- `isSafeWolfLocation()` → `isSafeDogLocation()`
- `handleWolfDeath()` → `handleDogDeath()`
- `scheduleWolfRespawn()` → `scheduleDogRespawn()`
- `clearShamanWolves()` → `clearShamanDogs()`

### 狗属性设置
```java
/**
 * 为萨满生成一只狗
 */
private Wolf spawnDogForShaman(Player shaman) {
    Wolf dog = (Wolf) spawnLoc.getWorld().spawnEntity(spawnLoc, EntityType.WOLF);
    
    // 设置狗的属性
    dog.setOwner(shaman);
    dog.setTamed(true);
    dog.setAdult();
    dog.customName(Component.text(shaman.getName() + "的狗伙伴", NamedTextColor.GOLD)); // 现代化API
    dog.setCustomNameVisible(true);
    
    // 设置80点生命值
    dog.getAttribute(Attribute.MAX_HEALTH).setBaseValue(80.0);
    dog.setHealth(80.0);
    
    // 增强狗的攻击力
    dog.getAttribute(Attribute.ATTACK_DAMAGE).setBaseValue(8.0);
    
    return dog;
}
```

### 消息更新
**所有相关消息都已更新：**
- "动物伙伴激活！获得了2只狗伙伴"
- "击杀奖励！获得了一只新的狗伙伴"
- "狗伙伴死亡！你失去了20%最大生命值"
- "狗伙伴复活了！"

## ⚡ 技能增强详情

### 萨满龙卷风暴增强
**修改前：**
```java
// 持续5秒的风场效果
for (int i = 0; i < 5; i++) {
    // 最大伤害 1.5*数量
    double damage = Math.min(1.5, damageMultiplier[0]) * hunterCount;
    damageMultiplier[0] = Math.min(1.5, damageMultiplier[0] + 0.25);
}
// 5秒无敌状态
long endTime = System.currentTimeMillis() + 5000;
```

**修改后：**
```java
// 持续8秒的风场效果
for (int i = 0; i < 8; i++) {
    // 最大伤害 2.25*数量
    double damage = Math.min(2.25, damageMultiplier[0]) * hunterCount;
    damageMultiplier[0] = Math.min(2.25, damageMultiplier[0] + 0.25);
}
// 8秒无敌状态
long endTime = System.currentTimeMillis() + 8000;
```

**增强效果：**
- ✅ **持续时间：** 5秒 → 8秒（+60%）
- ✅ **最大伤害：** 1.5倍 → 2.25倍（+50%）
- ✅ **无敌时间：** 同步延长到8秒

### 猪灵集结增强
**修改前：**
```java
// 给予抗性1加成
onlinePlayer.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, 200, 0)); // 10秒抗性1
player.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, 200, 0)); // 10秒抗性1
```

**修改后：**
```java
// 给予抗性1加成
onlinePlayer.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, 600, 0)); // 30秒抗性1
player.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, 600, 0)); // 30秒抗性1
```

**增强效果：**
- ✅ **抗性持续时间：** 10秒 → 30秒（+200%）
- ✅ **战术价值：** 更长的保护时间

## 🔧 技术实现亮点

### 1. 现代化API使用
```java
// 旧API（已弃用）
wolf.setCustomName("§6" + shaman.getName() + "的狼伙伴");

// 新API（推荐）
dog.customName(Component.text(shaman.getName() + "的狗伙伴", NamedTextColor.GOLD));
```

### 2. 正确的属性常量
```java
// 修复前
wolf.getAttribute(Attribute.GENERIC_MAX_HEALTH).setBaseValue(80.0);
wolf.getAttribute(Attribute.GENERIC_ATTACK_DAMAGE).setBaseValue(8.0);

// 修复后
dog.getAttribute(Attribute.MAX_HEALTH).setBaseValue(80.0);
dog.getAttribute(Attribute.ATTACK_DAMAGE).setBaseValue(8.0);
```

### 3. 完整的变量名一致性
所有相关的变量名、方法名、注释都已统一更新，确保代码的一致性和可读性。

## 🎮 游戏体验影响

### 萨满玩家体验
**狗伙伴系统：**
- ✅ **功能不变：** 所有机制保持一致，只是名称改变
- ✅ **视觉更新：** 狗伙伴名称显示为金色
- ✅ **属性一致：** 80血+8攻击力保持不变

**龙卷风暴增强：**
- ✅ **更强控制：** 8秒风场提供更持久的区域控制
- ✅ **更高伤害：** 最大2.25倍伤害提升威胁性
- ✅ **更安全：** 8秒无敌状态确保技能期间安全

### 猪灵玩家体验
**集结技能增强：**
- ✅ **更强保护：** 30秒抗性1提供更持久的团队保护
- ✅ **战术价值：** 更长的保护时间支持更多战术选择

### 敌方玩家体验
**面对萨满：**
- ⚠️ **更强威胁：** 龙卷风暴持续时间和伤害都增强
- ⚠️ **更难应对：** 需要更谨慎地处理萨满的技能

**面对猪灵：**
- ⚠️ **持久保护：** 猪灵团队获得更长时间的抗性保护

## 📊 数值对比

### 萨满龙卷风暴
| 属性 | 修改前 | 修改后 | 变化 |
|------|--------|--------|------|
| **持续时间** | 5秒 | 8秒 | +60% |
| **无敌时间** | 5秒 | 8秒 | +60% |
| **最大伤害倍数** | 1.5x | 2.25x | +50% |
| **伤害递增** | 每秒+0.25x | 每秒+0.25x | 不变 |

### 猪灵集结
| 属性 | 修改前 | 修改后 | 变化 |
|------|--------|--------|------|
| **抗性持续时间** | 10秒 | 30秒 | +200% |
| **抗性等级** | 1级 | 1级 | 不变 |
| **传送机制** | 不变 | 不变 | 不变 |

## 🎯 更新总结

成功完成了萨满狗伙伴系统和技能增强：

- ✅ **狼→狗转换：** 完整更新所有相关代码和描述
- ✅ **现代化API：** 使用Component和正确的属性常量
- ✅ **龙卷风暴增强：** 8秒持续+2.25倍最大伤害
- ✅ **猪灵集结增强：** 30秒抗性保护
- ✅ **代码一致性：** 所有变量名和方法名统一更新
- ✅ **功能完整性：** 所有原有功能保持不变

### 关键改进点
1. **术语统一：** 狼伙伴完全改为狗伙伴
2. **技能增强：** 萨满和猪灵技能都得到显著增强
3. **代码质量：** 使用现代化API和正确的常量
4. **用户体验：** 更强的技能效果和更好的视觉反馈

现在萨满拥有强力的狗伙伴支援和增强的龙卷风暴，猪灵也有了更持久的团队保护能力！🐕⚡🐷

**重要提醒：** 建议在游戏中测试狗伙伴的生成、战斗和复活机制，以及增强后的技能效果。
