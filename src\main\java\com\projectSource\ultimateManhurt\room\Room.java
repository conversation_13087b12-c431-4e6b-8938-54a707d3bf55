package com.projectSource.ultimateManhurt.room;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.game.GameState;
import com.projectSource.ultimateManhurt.game.PlayerRole;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 房间实体类
 * 代表一个游戏房间
 */
public class Room {

    private final UltimateManhurt plugin;
    private final String id;
    private String name;
    private String description;
    private String password;
    private UUID ownerId;
    private final RoomType type;
    private final RoomSettings settings;
    private final LocalDateTime createdTime;
    
    // 玩家管理
    private final Map<UUID, PlayerRole> players = new ConcurrentHashMap<>();
    private final Set<UUID> invitedPlayers = ConcurrentHashMap.newKeySet();
    
    // 房间状态
    private GameState gameState = GameState.WAITING;
    private boolean isLocked = false;
    private LocalDateTime lastActivity;
    
    public Room(UltimateManhurt plugin, String id, String name, UUID ownerId, RoomType type) {
        this.plugin = plugin;
        this.id = id;
        this.name = name;
        this.ownerId = ownerId;
        this.type = type;
        this.settings = new RoomSettings(type);
        this.createdTime = LocalDateTime.now();
        this.lastActivity = LocalDateTime.now();
        this.description = "";

        // 房主自动加入房间
        this.players.put(ownerId, PlayerRole.SPECTATOR);
    }
    
    // 基础Getter和Setter
    public String getId() {
        return id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
        updateActivity();
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
        updateActivity();
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
        updateActivity();
    }
    
    public boolean hasPassword() {
        return password != null && !password.isEmpty();
    }
    
    public boolean checkPassword(String inputPassword) {
        if (!hasPassword()) return true;
        return password.equals(inputPassword);
    }
    
    public UUID getOwnerId() {
        return ownerId;
    }
    
    public RoomType getType() {
        return type;
    }
    
    public RoomSettings getSettings() {
        return settings;
    }
    
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
    
    public LocalDateTime getLastActivity() {
        return lastActivity;
    }
    
    public GameState getGameState() {
        return gameState;
    }
    
    public void setGameState(GameState gameState) {
        this.gameState = gameState;
        updateActivity();
    }
    
    public boolean isLocked() {
        return isLocked;
    }
    
    public void setLocked(boolean locked) {
        isLocked = locked;
        updateActivity();
    }
    
    /**
     * 更新最后活动时间
     */
    private void updateActivity() {
        this.lastActivity = LocalDateTime.now();
    }
    
    // 玩家管理方法
    
    /**
     * 添加玩家到房间
     */
    public boolean addPlayer(UUID playerId, PlayerRole role) {
        if (players.size() >= settings.getMaxPlayers()) {
            return false;
        }
        
        if (players.containsKey(playerId)) {
            return false;
        }
        
        players.put(playerId, role);
        updateActivity();
        return true;
    }
    
    /**
     * 移除玩家
     */
    public boolean removePlayer(UUID playerId) {
        boolean removed = players.remove(playerId) != null;
        if (removed) {
            invitedPlayers.remove(playerId);
            updateActivity();
        }
        return removed;
    }
    
    /**
     * 获取玩家角色
     */
    public PlayerRole getPlayerRole(UUID playerId) {
        return players.get(playerId);
    }
    
    /**
     * 设置玩家角色
     */
    public boolean setPlayerRole(UUID playerId, PlayerRole role) {
        if (!players.containsKey(playerId)) {
            return false;
        }

        // 检查角色数量限制
        if (!canAssignRole(role)) {
            return false;
        }

        PlayerRole oldRole = players.get(playerId);
        players.put(playerId, role);
        updateActivity();

        // 如果玩家变成观察者，清理其职业状态
        if (role == PlayerRole.SPECTATOR && oldRole != PlayerRole.SPECTATOR) {
            plugin.getProfessionManager().removePlayerProfession(playerId);
        }

        // 通知GameSession角色变化（如果游戏正在进行）
        if (gameState == GameState.RUNNING || gameState == GameState.STARTING) {
            GameSession gameSession = plugin.getGameManager().getGameSessionByRoom(id);
            if (gameSession != null) {
                gameSession.notifyPlayerRoleChanged(playerId, role);
            }
        }

        return true;
    }
    
    /**
     * 检查是否可以分配指定角色
     */
    private boolean canAssignRole(PlayerRole role) {
        long currentCount = players.values().stream()
                .filter(r -> r == role)
                .count();
        
        switch (role) {
            case SPEEDRUNNER:
                return currentCount < settings.getMaxSpeedrunners();
            case HUNTER:
                return currentCount < settings.getMaxHunters();
            case SPECTATOR:
                return settings.isAllowSpectators();
            default:
                return false;
        }
    }
    
    /**
     * 获取所有玩家
     */
    public Set<UUID> getPlayers() {
        return new HashSet<>(players.keySet());
    }

    /**
     * 获取所有玩家及其角色
     */
    public Map<UUID, PlayerRole> getPlayersWithRoles() {
        return new HashMap<>(players);
    }
    
    /**
     * 获取指定角色的玩家
     */
    public Set<UUID> getPlayersByRole(PlayerRole role) {
        Set<UUID> result = new HashSet<>();
        for (Map.Entry<UUID, PlayerRole> entry : players.entrySet()) {
            if (entry.getValue() == role) {
                result.add(entry.getKey());
            }
        }
        return result;
    }
    
    /**
     * 获取玩家数量
     */
    public int getPlayerCount() {
        return players.size();
    }
    
    /**
     * 获取指定角色的玩家数量
     */
    public int getPlayerCount(PlayerRole role) {
        return (int) players.values().stream()
                .filter(r -> r == role)
                .count();
    }
    
    /**
     * 检查玩家是否在房间中
     */
    public boolean containsPlayer(UUID playerId) {
        return players.containsKey(playerId);
    }
    
    /**
     * 检查玩家是否是房主
     */
    public boolean isOwner(UUID playerId) {
        return ownerId.equals(playerId);
    }

    /**
     * 转移房主
     */
    public boolean transferOwner(UUID newOwnerId) {
        if (!players.containsKey(newOwnerId)) {
            return false; // 新房主必须在房间中
        }

        if (ownerId.equals(newOwnerId)) {
            return false; // 不能转移给自己
        }

        this.ownerId = newOwnerId;
        updateActivity();
        return true;
    }

    /**
     * 重置房间状态（游戏结束后调用）
     */
    public void resetAfterGame() {
        this.gameState = GameState.WAITING;
        updateActivity();
    }
    
    /**
     * 检查房间是否已满
     */
    public boolean isFull() {
        return players.size() >= settings.getMaxPlayers();
    }
    
    /**
     * 检查房间是否为空
     */
    public boolean isEmpty() {
        return players.isEmpty();
    }
    
    // 邀请系统
    
    /**
     * 邀请玩家
     */
    public boolean invitePlayer(UUID playerId) {
        if (players.containsKey(playerId)) {
            return false; // 玩家已在房间中
        }
        
        if (isFull()) {
            return false; // 房间已满
        }
        
        invitedPlayers.add(playerId);
        updateActivity();
        return true;
    }
    
    /**
     * 取消邀请
     */
    public boolean uninvitePlayer(UUID playerId) {
        boolean removed = invitedPlayers.remove(playerId);
        if (removed) {
            updateActivity();
        }
        return removed;
    }
    
    /**
     * 检查玩家是否被邀请
     */
    public boolean isInvited(UUID playerId) {
        return invitedPlayers.contains(playerId);
    }
    
    /**
     * 获取被邀请的玩家列表
     */
    public Set<UUID> getInvitedPlayers() {
        return new HashSet<>(invitedPlayers);
    }
    
    // 房间状态检查
    
    /**
     * 检查是否可以开始游戏
     */
    public boolean canStartGame() {
        if (!gameState.canStart()) {
            return false;
        }
        
        int playerCount = getPlayerCount();
        if (playerCount < type.getMinPlayers()) {
            return false;
        }
        
        // 检查是否有足够的速通者和捕猎者
        int speedrunners = getPlayerCount(PlayerRole.SPEEDRUNNER);
        int hunters = getPlayerCount(PlayerRole.HUNTER);
        
        return speedrunners > 0 && hunters > 0;
    }
    
    /**
     * 检查玩家是否可以加入
     */
    public boolean canJoin(UUID playerId) {
        if (players.containsKey(playerId)) {
            return false; // 已在房间中
        }
        
        if (isFull()) {
            return false; // 房间已满
        }
        
        if (isLocked) {
            return false; // 房间已锁定
        }
        
        if (!gameState.canJoin()) {
            return false; // 游戏状态不允许加入
        }
        
        // 检查房间类型限制
        if (type.requiresInvitation() && !isInvited(playerId)) {
            return false; // 私人房间需要邀请
        }
        
        return true;
    }
    
    /**
     * 检查玩家是否可以离开
     */
    public boolean canLeave(UUID playerId) {
        if (!players.containsKey(playerId)) {
            return false; // 不在房间中
        }
        
        return gameState.canLeave();
    }
    
    /**
     * 获取房间信息摘要
     */
    public String getSummary() {
        return String.format("房间[%s] %s (%d/%d) - %s", 
                id, name, getPlayerCount(), settings.getMaxPlayers(), gameState.getDisplayName());
    }
    
    @Override
    public String toString() {
        return "Room{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", type=" + type +
                ", players=" + players.size() +
                ", gameState=" + gameState +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Room room = (Room) obj;
        return id.equals(room.id);
    }
    
    @Override
    public int hashCode() {
        return id.hashCode();
    }
}
