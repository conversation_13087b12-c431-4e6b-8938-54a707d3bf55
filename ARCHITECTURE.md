# Ultimate Manhunt Plugin 架构设计

## 包结构
```
com.projectSource.ultimateManhurt/
├── UltimateManhurt.java                    # 主插件类
├── api/                                    # API接口
│   ├── GameAPI.java                       # 游戏API接口
│   ├── RoomAPI.java                       # 房间API接口
│   └── WorldAPI.java                      # 世界API接口
├── command/                               # 命令处理
│   ├── ManhuntCommand.java               # 主命令
│   └── subcommands/                      # 子命令
│       ├── CreateRoomCommand.java
│       ├── JoinRoomCommand.java
│       └── LeaveRoomCommand.java
├── config/                               # 配置管理
│   ├── ConfigManager.java               # 配置管理器
│   ├── Messages.java                    # 消息配置
│   └── Settings.java                    # 设置配置
├── game/                                # 游戏核心逻辑
│   ├── GameManager.java                 # 游戏管理器
│   ├── GameState.java                   # 游戏状态枚举
│   ├── GameSession.java                 # 游戏会话
│   ├── GameTimer.java                   # 游戏计时器
│   ├── PlayerRole.java                  # 玩家角色枚举
│   └── rules/                           # 游戏规则
│       ├── ManhuntRules.java           # Manhunt规则
│       └── WinCondition.java           # 胜利条件
├── room/                                # 房间系统
│   ├── Room.java                        # 房间实体
│   ├── RoomManager.java                 # 房间管理器
│   ├── RoomSettings.java                # 房间设置
│   ├── RoomType.java                    # 房间类型枚举
│   └── invitation/                      # 邀请系统
│       ├── Invitation.java             # 邀请实体
│       └── InvitationManager.java      # 邀请管理器
├── world/                               # 世界管理
│   ├── WorldManager.java                # 世界管理器
│   ├── GameWorld.java                   # 游戏世界实体
│   ├── WorldGenerator.java              # 世界生成器
│   └── dimension/                       # 维度管理
│       ├── DimensionBinder.java        # 维度绑定器
│       └── DimensionType.java          # 维度类型枚举
├── gui/                                 # GUI界面
│   ├── GuiManager.java                  # GUI管理器
│   ├── BaseGui.java                     # GUI基类
│   ├── RoomSettingsGui.java             # 房间设置GUI
│   ├── RoomListGui.java                 # 房间列表GUI
│   └── components/                      # GUI组件
│       ├── GuiButton.java              # GUI按钮
│       └── GuiSlider.java              # GUI滑块
├── scoreboard/                          # 计分板系统
│   ├── ScoreboardManager.java           # 计分板管理器
│   ├── GameScoreboard.java              # 游戏计分板
│   └── ScoreboardUpdater.java           # 计分板更新器
├── listener/                            # 事件监听器
│   ├── PlayerListener.java              # 玩家事件
│   ├── WorldListener.java               # 世界事件
│   ├── GameListener.java                # 游戏事件
│   └── GuiListener.java                 # GUI事件
├── util/                                # 工具类
│   ├── ComponentUtil.java               # Component工具
│   ├── ColorUtil.java                   # 颜色工具
│   ├── TimeUtil.java                    # 时间工具
│   └── LocationUtil.java                # 位置工具
└── data/                                # 数据存储
    ├── DataManager.java                 # 数据管理器
    ├── PlayerData.java                  # 玩家数据
    └── storage/                         # 存储实现
        ├── FileStorage.java            # 文件存储
        └── DatabaseStorage.java        # 数据库存储
```

## 核心技术要点

### 1. 现代Component API使用
- 使用 `net.kyori.adventure.text.Component` 替代过期的 `ChatColor`
- 支持渐变色和复杂文本格式
- 使用 `MiniMessage` 进行文本解析

### 2. 世界管理策略
- 每个游戏房间创建独立的世界组（主世界+下界+末地）
- 使用自定义世界生成器确保世界隔离
- 实现维度传送事件拦截和重定向

### 3. 游戏状态管理
- 使用状态机模式管理游戏流程
- 支持暂停、恢复、结束等操作
- 实现游戏数据的持久化

### 4. GUI系统设计
- 基于Inventory的现代GUI框架
- 支持动态更新和交互
- 使用Builder模式构建复杂界面

### 5. 计分板系统
- 类Hypixel风格的动态计分板
- 支持动画效果和实时更新
- 使用现代Scoreboard API
