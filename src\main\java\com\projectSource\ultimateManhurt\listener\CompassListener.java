package com.projectSource.ultimateManhurt.listener;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.block.Action;
import org.bukkit.inventory.ItemStack;

/**
 * 指南针交互监听器
 * 处理指南针右键切换目标功能
 */
public class CompassListener implements Listener {
    
    private final UltimateManhurt plugin;
    
    public CompassListener(UltimateManhurt plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        
        // 检查是否是右键点击指南针
        if (event.getAction() != Action.RIGHT_CLICK_AIR && event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }
        
        if (item == null || item.getType() != Material.COMPASS) {
            return;
        }
        
        // 获取玩家所在的游戏会话
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
        if (gameSession == null) {
            return;
        }
        
        // 检查玩家角色
        PlayerRole role = gameSession.getPlayerRole(player.getUniqueId());
        if (role != PlayerRole.HUNTER) {
            return;
        }
        
        // 检查指南针追踪是否启用
        if (!gameSession.getRoom().getSettings().isCompassTracking()) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("指南针追踪未启用"));
            return;
        }
        
        // 取消事件以防止其他行为
        event.setCancelled(true);
        
        // 切换追踪目标
        gameSession.getCompassTracker().switchTarget(player.getUniqueId());
    }
}
