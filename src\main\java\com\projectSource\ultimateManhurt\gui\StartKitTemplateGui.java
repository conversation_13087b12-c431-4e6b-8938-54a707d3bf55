package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.kit.StartKit;
import com.projectSource.ultimateManhurt.kit.StartKitTemplate;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * StartKit模板选择GUI
 * 显示所有可用的装备包模板
 */
public class StartKitTemplateGui extends BaseGui {
    
    private final Room room;
    private final PlayerRole targetRole; // null表示显示所有模板
    
    public StartKitTemplateGui(UltimateManhurt plugin, Player player, Room room, PlayerRole targetRole) {
        super(plugin, player, "装备包模板", 54);
        this.room = room;
        this.targetRole = targetRole;
        setupGui();
    }
    
    @Override
    protected void setupGui() {
        // 标题
        String title = targetRole != null ? 
            (targetRole == PlayerRole.SPEEDRUNNER ? "速通者" : "捕猎者") + "装备包模板" : 
            "所有装备包模板";
        setItem(4, createItem(Material.BOOKSHELF, "<gold><bold>" + title,
            "<gray>选择一个模板应用到装备包"));
        
        // 显示模板
        setupTemplates();
        
        // 控制按钮
        setupControlButtons();
        
        // 装饰性物品
        fillEmptySlots();
    }
    
    /**
     * 设置模板显示
     */
    private void setupTemplates() {
        Map<String, StartKit> allTemplates = StartKitTemplate.getAllTemplates();

        // 预设模板区域标题
        setItem(9, createItem(Material.BOOKSHELF, "<blue><bold>预设模板",
            "<gray>系统提供的标准装备包模板"));

        // 空装备包
        setupTemplate(10, "empty", allTemplates.get("empty"), Material.BARRIER);

        // 基础装备包
        setupTemplate(11, "basic", allTemplates.get("basic"), Material.WOODEN_SWORD);

        // 默认速通者装备包
        setupTemplate(19, "default_speedrunner", allTemplates.get("default_speedrunner"), Material.STONE_SWORD);

        // 默认捕猎者装备包
        setupTemplate(20, "default_hunter", allTemplates.get("default_hunter"), Material.IRON_SWORD);

        // 专业速通者装备包
        setupTemplate(28, "pro_speedrunner", allTemplates.get("pro_speedrunner"), Material.DIAMOND_SWORD);

        // 专业捕猎者装备包
        setupTemplate(29, "pro_hunter", allTemplates.get("pro_hunter"), Material.NETHERITE_SWORD);

        // 自定义模板区域
        setupCustomTemplates();

        // 如果有目标角色，高亮推荐模板
        if (targetRole != null) {
            highlightRecommendedTemplates();
        }
    }
    
    /**
     * 设置单个模板
     */
    private void setupTemplate(int slot, String templateId, StartKit template, Material material) {
        List<String> lore = new ArrayList<>();
        lore.add("<gray>模板名称: <white>" + template.getName());
        lore.add("<gray>描述: <white>" + template.getDescription());
        lore.add("<gray>装备摘要: <white>" + template.getSummary());
        lore.add("<gray>物品数量: <white>" + template.getItemCount());
        lore.add("");
        
        // 根据目标角色显示不同的操作提示
        if (targetRole != null) {
            String roleText = targetRole == PlayerRole.SPEEDRUNNER ? "速通者" : "捕猎者";
            lore.add("<yellow>左键: 应用到" + roleText + "装备包");
            lore.add("<yellow>右键: 预览模板内容");
        } else {
            lore.add("<yellow>左键: 选择应用角色");
            lore.add("<yellow>右键: 预览模板内容");
        }
        
        setItem(slot, createItem(material, "<aqua><bold>" + StartKitTemplate.getTemplateDisplayName(templateId),
            lore.toArray(new String[0])));
    }

    /**
     * 设置自定义模板
     */
    private void setupCustomTemplates() {
        // 自定义模板区域标题
        setItem(36, createItem(Material.ENDER_CHEST, "<color:#8d5caa><bold>自定义模板",
            "<gray>你创建的个人装备包模板",
            "<yellow>点击查看所有自定义模板"));

        // 获取玩家的自定义模板
        List<StartKit> playerTemplates = plugin.getCustomTemplateManager().getPlayerTemplates(player.getUniqueId());

        if (playerTemplates.isEmpty()) {
            // 没有自定义模板时显示提示
            setItem(37, createItem(Material.BARRIER, "<gray>暂无自定义模板",
                "<gray>你还没有创建任何自定义模板",
                "<yellow>在装备包编辑器中点击\"保存为模板\""));
        } else {
            // 显示前几个自定义模板 (最多显示8个)
            for (int i = 0; i < Math.min(playerTemplates.size(), 8); i++) {
                StartKit template = playerTemplates.get(i);
                int slot = 37 + i; // 槽位37-44

                if (slot <= 44) { // 确保不超出范围
                    setupCustomTemplate(slot, template);
                }
            }

            // 如果模板数量超过8个，显示"查看更多"按钮
            if (playerTemplates.size() > 8) {
                setItem(44, createItem(Material.ARROW, "<yellow>查看更多模板",
                    "<gray>你有 " + playerTemplates.size() + " 个自定义模板",
                    "<yellow>点击查看所有模板"));
            }
        }
    }

    /**
     * 设置单个自定义模板
     */
    private void setupCustomTemplate(int slot, StartKit template) {
        List<String> lore = new ArrayList<>();
        lore.add("<gray>模板名称: <white>" + template.getName());
        lore.add("<gray>描述: <white>" + template.getDescription());
        lore.add("<gray>装备摘要: <white>" + template.getSummary());
        lore.add("<gray>物品数量: <white>" + template.getItemCount());
        lore.add("<gray>额外物品: <white>" + template.getExtraItems().size() + " 个");
        lore.add("");

        // 根据目标角色显示不同的操作提示
        if (targetRole != null) {
            String roleText = targetRole == PlayerRole.SPEEDRUNNER ? "速通者" : "捕猎者";
            lore.add("<yellow>左键: 应用到" + roleText + "装备包");
            lore.add("<yellow>右键: 预览模板内容");
            lore.add("<yellow>Shift+右键: 删除模板");
        } else {
            lore.add("<yellow>左键: 选择应用角色");
            lore.add("<yellow>右键: 预览模板内容");
            lore.add("<yellow>Shift+右键: 删除模板");
        }

        // 根据模板内容选择图标
        Material icon = getCustomTemplateIcon(template);

        setItem(slot, createItem(icon, "<aqua>" + template.getName(), lore.toArray(new String[0])));
    }

    /**
     * 获取自定义模板图标
     */
    private Material getCustomTemplateIcon(StartKit template) {
        // 检查模板中的主要武器来决定图标
        for (ItemStack item : template.getItems().values()) {
            if (item != null) {
                Material type = item.getType();
                if (type.name().contains("SWORD")) {
                    return type;
                } else if (type == Material.BOW) {
                    return Material.BOW;
                } else if (type.name().contains("PICKAXE")) {
                    return type;
                }
            }
        }

        // 默认图标
        return Material.CHEST;
    }

    /**
     * 高亮推荐模板
     */
    private void highlightRecommendedTemplates() {
        List<StartKit> recommended = StartKitTemplate.getRecommendedTemplates(targetRole);

        // 为推荐模板添加特殊标记
        // 这里可以通过修改物品的附魔光效来实现高亮
        // 由于简化实现，这里只是添加到lore中
    }
    
    /**
     * 设置控制按钮
     */
    private void setupControlButtons() {
        // 自定义装备包
        setItem(45, createItem(Material.CRAFTING_TABLE, "<green><bold>自定义装备包",
            "<gray>创建完全自定义的装备包",
            "<yellow>点击进入编辑器"));
        
        // 当前装备包信息
        if (targetRole != null) {
            StartKit currentKit = targetRole == PlayerRole.SPEEDRUNNER ? 
                room.getSettings().getSpeedrunnerKit() : 
                room.getSettings().getHunterKit();
            
            String roleText = targetRole == PlayerRole.SPEEDRUNNER ? "速通者" : "捕猎者";
            setItem(46, createItem(Material.CHEST, "<blue><bold>当前" + roleText + "装备包",
                "<gray>名称: <white>" + currentKit.getName(),
                "<gray>摘要: <white>" + currentKit.getSummary(),
                "<yellow>点击预览当前装备包"));
        }
        
        // 帮助信息
        setItem(47, createItem(Material.BOOK, "<yellow><bold>帮助",
            "<gray>装备包模板说明：",
            "<gray>• 空装备包: 不提供任何装备",
            "<gray>• 基础装备包: 最基本的生存工具",
            "<gray>• 默认装备包: 适合新手玩家",
            "<gray>• 专业装备包: 适合有经验的玩家"));
        
        // 返回
        setItem(53, createItem(Material.ARROW, "<red>返回",
            "<gray>返回装备包管理"));
    }
    
    /**
     * 填充空槽位
     */
    private void fillEmptySlots() {
        for (int i = 0; i < inventory.getSize(); i++) {
            if (inventory.getItem(i) == null) {
                setItem(i, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));
            }
        }
    }
    
    @Override
    public void handleClick(InventoryClickEvent event) {
        event.setCancelled(true);
        
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        if (event.getClickedInventory() != this.inventory) {
            return;
        }
        
        ItemStack item = event.getCurrentItem();
        if (item == null || item.getType() == Material.AIR) {
            return;
        }
        
        this.playClickSound();
        
        int slot = event.getSlot();
        boolean isRightClick = event.isRightClick();
        boolean isShiftClick = event.isShiftClick();

        // 预设模板点击处理
        String templateId = getTemplateIdBySlot(slot);
        if (templateId != null) {
            handleTemplateClick(templateId, isRightClick);
            return;
        }

        // 自定义模板区域点击处理
        if (slot == 36) {
            // 点击自定义模板标题，打开自定义模板管理界面
            handleCustomTemplateManagement();
            return;
        }

        // 具体自定义模板点击处理 (槽位37-44)
        if (slot >= 37 && slot <= 44) {
            handleCustomTemplateClick(slot, isRightClick, isShiftClick);
            return;
        }

        // 控制按钮处理
        switch (slot) {
            case 45: // 自定义装备包
                handleCustomKit();
                break;
            case 46: // 当前装备包
                handleCurrentKit();
                break;
            case 47: // 帮助
                handleHelp();
                break;
            case 53: // 返回
                handleBack();
                break;
        }
    }
    
    /**
     * 根据槽位获取模板ID
     */
    private String getTemplateIdBySlot(int slot) {
        switch (slot) {
            case 10: return "empty";
            case 11: return "basic";
            case 19: return "default_speedrunner";
            case 20: return "default_hunter";
            case 28: return "pro_speedrunner";
            case 29: return "pro_hunter";
            default: return null;
        }
    }
    
    /**
     * 处理模板点击
     */
    private void handleTemplateClick(String templateId, boolean isRightClick) {
        StartKit template = StartKitTemplate.getAllTemplates().get(templateId);
        
        if (isRightClick) {
            // 预览模板
            plugin.getStartKitManager().previewKit(player, template);
        } else {
            // 应用模板
            if (targetRole != null) {
                applyTemplate(templateId, targetRole);
            } else {
                // 让用户选择应用到哪个角色
                close();
                plugin.getGuiManager().openStartKitTemplateApplyGui(player, room, templateId);
            }
        }
    }
    
    /**
     * 应用模板到指定角色
     */
    private void applyTemplate(String templateId, PlayerRole role) {
        StartKit template = StartKitTemplate.getAllTemplates().get(templateId);
        StartKit newKit = new StartKit(template); // 创建副本
        
        if (role == PlayerRole.SPEEDRUNNER) {
            room.getSettings().setSpeedrunnerKit(newKit);
        } else {
            room.getSettings().setHunterKit(newKit);
        }
        
        String roleText = role == PlayerRole.SPEEDRUNNER ? "速通者" : "捕猎者";
        sendSuccess("已将 " + template.getName() + " 应用到" + roleText + "装备包");
        playSuccessSound();
        
        // 返回装备包管理
        close();
        plugin.getGuiManager().openStartKitGui(player, room);
    }
    
    /**
     * 自定义装备包
     */
    private void handleCustomKit() {
        if (targetRole != null) {
            close();
            plugin.getGuiManager().openStartKitEditorGui(player, room, targetRole);
        } else {
            sendError("请先选择要编辑的角色装备包");
        }
    }
    
    /**
     * 查看当前装备包
     */
    private void handleCurrentKit() {
        if (targetRole != null) {
            StartKit currentKit = targetRole == PlayerRole.SPEEDRUNNER ? 
                room.getSettings().getSpeedrunnerKit() : 
                room.getSettings().getHunterKit();
            plugin.getStartKitManager().previewKit(player, currentKit);
        }
    }
    
    /**
     * 处理自定义模板管理
     */
    private void handleCustomTemplateManagement() {
        close();
        plugin.getGuiManager().openCustomTemplateGui(player, room, targetRole);
    }

    /**
     * 处理自定义模板点击
     */
    private void handleCustomTemplateClick(int slot, boolean isRightClick, boolean isShiftClick) {
        // 获取玩家的自定义模板
        List<StartKit> playerTemplates = plugin.getCustomTemplateManager().getPlayerTemplates(player.getUniqueId());

        // 计算模板索引
        int templateIndex = slot - 37; // 槽位37对应索引0

        if (templateIndex >= 0 && templateIndex < playerTemplates.size()) {
            StartKit template = playerTemplates.get(templateIndex);

            if (isShiftClick && isRightClick) {
                // Shift+右键：删除模板
                handleDeleteCustomTemplate(template);
            } else if (isRightClick) {
                // 右键：预览模板
                plugin.getStartKitManager().previewKit(player, template);
            } else {
                // 左键：应用模板
                handleApplyCustomTemplate(template);
            }
        }
    }

    /**
     * 应用自定义模板
     */
    private void handleApplyCustomTemplate(StartKit template) {
        if (targetRole != null) {
            StartKit newKit = new StartKit(template);

            if (targetRole == PlayerRole.SPEEDRUNNER) {
                room.getSettings().setSpeedrunnerKit(newKit);
            } else {
                room.getSettings().setHunterKit(newKit);
            }

            String roleText = targetRole == PlayerRole.SPEEDRUNNER ? "速通者" : "捕猎者";
            sendSuccess("已将自定义模板 \"" + template.getName() + "\" 应用到" + roleText + "装备包");
            playSuccessSound();

            // 返回装备包管理
            close();
            plugin.getGuiManager().openStartKitGui(player, room);
        } else {
            sendError("请先选择要应用的角色");
        }
    }

    /**
     * 删除自定义模板
     */
    private void handleDeleteCustomTemplate(StartKit template) {
        if (plugin.getCustomTemplateManager().deleteCustomTemplate(player, template.getTemplateId())) {
            sendSuccess("模板 \"" + template.getName() + "\" 删除成功");
            playSuccessSound();
            // 刷新界面
            refresh();
        }
    }

    /**
     * 显示帮助
     */
    private void handleHelp() {
        sendInfo("装备包模板帮助已显示在物品描述中");
    }

    /**
     * 返回装备包管理
     */
    private void handleBack() {
        close();
        plugin.getGuiManager().openStartKitGui(player, room);
    }
}
