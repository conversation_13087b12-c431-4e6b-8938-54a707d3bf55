package com.projectSource.ultimateManhurt.room;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.VictoryMode;
import com.projectSource.ultimateManhurt.game.scoring.MilestoneSettings;
import com.projectSource.ultimateManhurt.kit.StartKit;
import org.bukkit.Difficulty;
import org.bukkit.GameMode;
import org.bukkit.configuration.MemorySection;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.inventory.ItemStack;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.logging.Level;

/**
 * 房间设置管理器
 * 负责保存和加载房间设置配置文件
 */
public class RoomSettingsManager {
    
    private final UltimateManhurt plugin;
    private final File settingsFolder;
    
    public RoomSettingsManager(UltimateManhurt plugin) {
        this.plugin = plugin;
        this.settingsFolder = new File(plugin.getDataFolder(), "room-settings");
        
        // 创建设置文件夹
        if (!settingsFolder.exists()) {
            settingsFolder.mkdirs();
        }
    }
    
    /**
     * 保存房间设置到文件
     */
    public boolean saveSettings(RoomSettings settings, String fileName, UUID creatorId) {
        try {
            // 确保文件名以.yml结尾
            if (!fileName.endsWith(".yml")) {
                fileName += ".yml";
            }
            
            File settingsFile = new File(settingsFolder, fileName);
            YamlConfiguration config = new YamlConfiguration();
            
            // 保存元数据
            config.set("metadata.name", fileName.replace(".yml", ""));
            config.set("metadata.creator", creatorId.toString());
            config.set("metadata.created-at", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            config.set("metadata.version", "1.0");
            
            // 保存基础设置
            config.set("basic.game-duration-minutes", settings.getGameDurationMinutes());
            config.set("basic.world-seed", settings.getWorldSeed());
            config.set("basic.difficulty", settings.getDifficulty().name());
            config.set("basic.spectator-game-mode", settings.getSpectatorGameMode().name());
            
            // 保存玩家设置
            config.set("players.max-players", settings.getMaxPlayers());
            config.set("players.max-speedrunners", settings.getMaxSpeedrunners());
            config.set("players.max-hunters", settings.getMaxHunters());
            config.set("players.allow-spectators", settings.isAllowSpectators());
            
            // 保存游戏规则
            config.set("game-rules.pvp-enabled", settings.isPvpEnabled());
            config.set("game-rules.friendly-fire", settings.isFriendlyFire());
            config.set("game-rules.keep-inventory", settings.isKeepInventory());
            config.set("game-rules.natural-regeneration", settings.isNaturalRegeneration());
            config.set("game-rules.show-death-messages", settings.isShowDeathMessages());
            
            // 保存世界设置
            config.set("world.generate-structures", settings.isGenerateStructures());
            config.set("world.generate-bonus-chest", settings.isGenerateBonusChest());
            config.set("world.enable-command-blocks", settings.isEnableCommandBlocks());
            config.set("world.do-daylight-cycle", settings.isDoDaylightCycle());
            config.set("world.do-weather-cycle", settings.isDoWeatherCycle());
            config.set("world.custom-spawn-logic", settings.isCustomSpawnLogic());
            config.set("world.random-spawn-enabled", settings.isRandomSpawnEnabled());
            config.set("world.random-spawn-distance", settings.getRandomSpawnDistance());

            // 保存特殊功能
            config.set("special.compass-tracking", settings.isCompassTracking());
            config.set("special.compass-update-interval", settings.getCompassUpdateInterval());
            config.set("special.locator-bar", settings.isLocatorBar());
            config.set("special.ender-pearl-cooldown", settings.isEnderPearlCooldown());
            config.set("special.ender-pearl-cooldown-seconds", settings.getEnderPearlCooldownSeconds());
            config.set("special.nether-portal-delay", settings.isNetherPortalDelay());
            config.set("special.nether-portal-delay-seconds", settings.getNetherPortalDelaySeconds());
            
            // 保存Ban Pick系统
            config.set("ban-pick.enabled", settings.isBanPickEnabled());
            config.set("ban-pick.phase-time-seconds", settings.getBanPickPhaseTimeSeconds());
            
            // 保存职业系统
            config.set("profession.enabled", settings.isProfessionSystemEnabled());
            
            // 保存豁免设置
            config.set("immunity.enabled", settings.isImmunityEnabled());
            config.set("immunity.duration-seconds", settings.getImmunityDurationSeconds());
            
            // 保存胜利条件
            config.set("victory.dragon-respawn", settings.isDragonRespawn());
            config.set("victory.timeout-hunters-win", settings.isTimeoutHuntersWin());
            config.set("victory.speedrunner-lives", settings.getSpeedrunnerLives());
            config.set("victory.hunter-lives", settings.getHunterLives());
            config.set("victory.allow-speedrunner-respawn", settings.isAllowSpeedrunnerRespawn());
            config.set("victory.allow-hunter-respawn", settings.isAllowHunterRespawn());
            
            // 保存血量设置
            config.set("health.speedrunner-max-health", settings.getSpeedrunnerMaxHealth());
            config.set("health.hunter-max-health", settings.getHunterMaxHealth());
            
            // 保存胜利模式设置
            config.set("victory-mode.mode", settings.getVictoryMode().name());
            config.set("victory-mode.target-score", settings.getTargetScore());
            
            // 保存守卫模式设置
            saveGuardModeSettings(config, settings);
            
            // 保存StartKit设置
            saveStartKitSettings(config, settings);
            
            // 保存里程碑设置
            saveMilestoneSettings(config, settings);
            
            // 保存自定义规则
            if (!settings.getCustomRules().isEmpty()) {
                for (Map.Entry<String, Object> entry : settings.getCustomRules().entrySet()) {
                    config.set("custom-rules." + entry.getKey(), entry.getValue());
                }
            }
            
            // 保存到文件
            config.save(settingsFile);
            plugin.getLogger().info("成功保存房间设置: " + fileName);
            return true;
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "保存房间设置失败: " + fileName, e);
            return false;
        }
    }
    
    /**
     * 从文件加载房间设置
     */
    public RoomSettings loadSettings(String fileName) {
        try {
            // 确保文件名以.yml结尾
            if (!fileName.endsWith(".yml")) {
                fileName += ".yml";
            }
            
            File settingsFile = new File(settingsFolder, fileName);
            if (!settingsFile.exists()) {
                plugin.getLogger().warning("房间设置文件不存在: " + fileName);
                return null;
            }
            
            YamlConfiguration config = YamlConfiguration.loadConfiguration(settingsFile);
            RoomSettings settings = new RoomSettings();
            
            // 加载基础设置
            settings.setGameDurationMinutes(config.getInt("basic.game-duration-minutes", 30));
            settings.setWorldSeed(config.getLong("basic.world-seed", 0));
            
            String difficultyName = config.getString("basic.difficulty", "NORMAL");
            try {
                settings.setDifficulty(Difficulty.valueOf(difficultyName));
            } catch (IllegalArgumentException e) {
                settings.setDifficulty(Difficulty.NORMAL);
            }
            
            String gameModeName = config.getString("basic.spectator-game-mode", "SPECTATOR");
            try {
                settings.setSpectatorGameMode(GameMode.valueOf(gameModeName));
            } catch (IllegalArgumentException e) {
                settings.setSpectatorGameMode(GameMode.SPECTATOR);
            }
            
            // 加载玩家设置
            settings.setMaxPlayers(config.getInt("players.max-players", 10));
            settings.setMaxSpeedrunners(config.getInt("players.max-speedrunners", 1));
            settings.setMaxHunters(config.getInt("players.max-hunters", 9));
            settings.setAllowSpectators(config.getBoolean("players.allow-spectators", true));
            
            // 加载游戏规则
            settings.setPvpEnabled(config.getBoolean("game-rules.pvp-enabled", true));
            settings.setFriendlyFire(config.getBoolean("game-rules.friendly-fire", false));
            settings.setKeepInventory(config.getBoolean("game-rules.keep-inventory", false));
            settings.setNaturalRegeneration(config.getBoolean("game-rules.natural-regeneration", true));
            settings.setShowDeathMessages(config.getBoolean("game-rules.show-death-messages", true));
            
            // 加载世界设置
            settings.setGenerateStructures(config.getBoolean("world.generate-structures", true));
            settings.setGenerateBonusChest(config.getBoolean("world.generate-bonus-chest", false));
            settings.setEnableCommandBlocks(config.getBoolean("world.enable-command-blocks", false));
            settings.setDoDaylightCycle(config.getBoolean("world.do-daylight-cycle", true));
            settings.setDoWeatherCycle(config.getBoolean("world.do-weather-cycle", true));
            settings.setCustomSpawnLogic(config.getBoolean("world.custom-spawn-logic", false));
            settings.setRandomSpawnEnabled(config.getBoolean("world.random-spawn-enabled", false));
            settings.setRandomSpawnDistance(config.getInt("world.random-spawn-distance", 100));

            // 加载特殊功能
            settings.setCompassTracking(config.getBoolean("special.compass-tracking", true));
            settings.setCompassUpdateInterval(config.getInt("special.compass-update-interval", 5));
            settings.setLocatorBar(config.getBoolean("special.locator-bar", true));
            settings.setEnderPearlCooldown(config.getBoolean("special.ender-pearl-cooldown", true));
            settings.setEnderPearlCooldownSeconds(config.getInt("special.ender-pearl-cooldown-seconds", 15));
            settings.setNetherPortalDelay(config.getBoolean("special.nether-portal-delay", true));
            settings.setNetherPortalDelaySeconds(config.getInt("special.nether-portal-delay-seconds", 4));
            
            // 加载Ban Pick系统
            settings.setBanPickEnabled(config.getBoolean("ban-pick.enabled", false));
            settings.setBanPickPhaseTimeSeconds(config.getInt("ban-pick.phase-time-seconds", 30));
            
            // 加载职业系统
            settings.setProfessionSystemEnabled(config.getBoolean("profession.enabled", false));
            
            // 加载豁免设置
            settings.setImmunityEnabled(config.getBoolean("immunity.enabled", true));
            settings.setImmunityDurationSeconds(config.getInt("immunity.duration-seconds", 60));
            
            // 加载胜利条件
            settings.setDragonRespawn(config.getBoolean("victory.dragon-respawn", false));
            settings.setTimeoutHuntersWin(config.getBoolean("victory.timeout-hunters-win", true));
            settings.setSpeedrunnerLives(config.getInt("victory.speedrunner-lives", 3));
            settings.setHunterLives(config.getInt("victory.hunter-lives", 0));
            settings.setAllowSpeedrunnerRespawn(config.getBoolean("victory.allow-speedrunner-respawn", true));
            settings.setAllowHunterRespawn(config.getBoolean("victory.allow-hunter-respawn", true));
            
            // 加载血量设置
            settings.setSpeedrunnerMaxHealth(config.getDouble("health.speedrunner-max-health", 20.0));
            settings.setHunterMaxHealth(config.getDouble("health.hunter-max-health", 20.0));
            
            // 加载胜利模式设置
            String victoryModeName = config.getString("victory-mode.mode", "DRAGON_MODE");
            try {
                settings.setVictoryMode(VictoryMode.valueOf(victoryModeName));
            } catch (IllegalArgumentException e) {
                settings.setVictoryMode(VictoryMode.DRAGON_MODE);
            }
            settings.setTargetScore(config.getInt("victory-mode.target-score", 500));
            
            // 加载守卫模式设置
            loadGuardModeSettings(config, settings);
            
            // 加载StartKit设置
            loadStartKitSettings(config, settings);
            
            // 加载里程碑设置
            loadMilestoneSettings(config, settings);
            
            // 加载自定义规则
            if (config.contains("custom-rules")) {
                for (String key : config.getConfigurationSection("custom-rules").getKeys(false)) {
                    settings.getCustomRules().put(key, config.get("custom-rules." + key));
                }
            }
            
            plugin.getLogger().info("成功加载房间设置: " + fileName);
            return settings;
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "加载房间设置失败: " + fileName, e);
            return null;
        }
    }
    
    /**
     * 获取所有可用的设置文件
     */
    public List<String> getAvailableSettings() {
        List<String> settings = new ArrayList<>();
        
        if (!settingsFolder.exists()) {
            return settings;
        }
        
        File[] files = settingsFolder.listFiles((dir, name) -> name.endsWith(".yml"));
        if (files != null) {
            for (File file : files) {
                settings.add(file.getName().replace(".yml", ""));
            }
        }
        
        return settings;
    }
    
    /**
     * 删除设置文件
     */
    public boolean deleteSettings(String fileName) {
        try {
            if (!fileName.endsWith(".yml")) {
                fileName += ".yml";
            }
            
            File settingsFile = new File(settingsFolder, fileName);
            if (settingsFile.exists()) {
                boolean deleted = settingsFile.delete();
                if (deleted) {
                    plugin.getLogger().info("成功删除房间设置: " + fileName);
                }
                return deleted;
            }
            return false;
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "删除房间设置失败: " + fileName, e);
            return false;
        }
    }
    
    /**
     * 获取设置文件信息
     */
    public SettingsFileInfo getSettingsInfo(String fileName) {
        try {
            if (!fileName.endsWith(".yml")) {
                fileName += ".yml";
            }

            File settingsFile = new File(settingsFolder, fileName);
            if (!settingsFile.exists()) {
                return null;
            }

            YamlConfiguration config = YamlConfiguration.loadConfiguration(settingsFile);

            String name = config.getString("metadata.name", fileName.replace(".yml", ""));
            String creator = config.getString("metadata.creator", "未知");
            String createdAt = config.getString("metadata.created-at", "未知");
            String version = config.getString("metadata.version", "1.0");

            return new SettingsFileInfo(name, creator, createdAt, version, settingsFile.lastModified());

        } catch (Exception e) {
            plugin.getLogger().log(Level.WARNING, "获取设置文件信息失败: " + fileName, e);
            return null;
        }
    }

    /**
     * 保存守卫模式设置
     */
    private void saveGuardModeSettings(YamlConfiguration config, RoomSettings settings) {
        config.set("guard-mode.wither-max-health", settings.getWitherMaxHealth());
        config.set("guard-mode.wither-healing-reduction", settings.getWitherHealingReduction());
        config.set("guard-mode.wither-attack-interval", settings.getWitherAttackInterval());
        config.set("guard-mode.wither-attack-damage", settings.getWitherAttackDamage());
        config.set("guard-mode.wither-shield-duration", settings.getWitherShieldDuration());
        config.set("guard-mode.wither-shield-reduction", settings.getWitherShieldReduction());
        config.set("guard-mode.hunter-spawn-distance", settings.getHunterSpawnDistance());
        config.set("guard-mode.wither-can-move", settings.isWitherCanMove());
        config.set("guard-mode.wither-target-hunters-only", settings.isWitherTargetHuntersOnly());
        config.set("guard-mode.wither-destroy-blocks", settings.isWitherDestroyBlocks());
        config.set("guard-mode.wither-effect-duration", settings.getWitherEffectDuration());
        config.set("guard-mode.wither-effect-level", settings.getWitherEffectLevel());
    }

    /**
     * 加载守卫模式设置
     */
    private void loadGuardModeSettings(YamlConfiguration config, RoomSettings settings) {
        settings.setWitherMaxHealth(config.getDouble("guard-mode.wither-max-health", 300.0));
        settings.setWitherHealingReduction(config.getDouble("guard-mode.wither-healing-reduction", 0.5));
        settings.setWitherAttackInterval(config.getInt("guard-mode.wither-attack-interval", 5));
        settings.setWitherAttackDamage(config.getDouble("guard-mode.wither-attack-damage", 10.0));
        settings.setWitherShieldDuration(config.getInt("guard-mode.wither-shield-duration", 300));
        settings.setWitherShieldReduction(config.getDouble("guard-mode.wither-shield-reduction", 0.75));
        settings.setHunterSpawnDistance(config.getInt("guard-mode.hunter-spawn-distance", 200));
        settings.setWitherCanMove(config.getBoolean("guard-mode.wither-can-move", false));
        settings.setWitherTargetHuntersOnly(config.getBoolean("guard-mode.wither-target-hunters-only", true));
        settings.setWitherDestroyBlocks(config.getBoolean("guard-mode.wither-destroy-blocks", false));
        settings.setWitherEffectDuration(config.getInt("guard-mode.wither-effect-duration", 60));
        settings.setWitherEffectLevel(config.getInt("guard-mode.wither-effect-level", 0));
    }

    /**
     * 保存StartKit设置
     */
    private void saveStartKitSettings(YamlConfiguration config, RoomSettings settings) {
        config.set("start-kit.enabled", settings.isStartKitEnabled());

        // 保存速通者装备包
        if (settings.getSpeedrunnerKit() != null) {
            saveStartKit(config, "start-kit.speedrunner", settings.getSpeedrunnerKit());
        }

        // 保存捕猎者装备包
        if (settings.getHunterKit() != null) {
            saveStartKit(config, "start-kit.hunter", settings.getHunterKit());
        }
    }

    /**
     * 保存单个StartKit
     */
    private void saveStartKit(YamlConfiguration config, String path, StartKit kit) {
        config.set(path + ".name", kit.getName());
        config.set(path + ".description", kit.getDescription());
        config.set(path + ".template-id", kit.getTemplateId());

        // 保存主要物品
        Map<String, Object> itemsMap = new HashMap<>();
        for (Map.Entry<Integer, ItemStack> entry : kit.getItems().entrySet()) {
            itemsMap.put(entry.getKey().toString(), entry.getValue().serialize());
        }
        config.set(path + ".items", itemsMap);

        // 保存额外物品
        List<Map<String, Object>> extraItemsList = new ArrayList<>();
        for (ItemStack item : kit.getExtraItems()) {
            extraItemsList.add(item.serialize());
        }
        config.set(path + ".extra-items", extraItemsList);
    }

    /**
     * 加载StartKit设置
     */
    private void loadStartKitSettings(YamlConfiguration config, RoomSettings settings) {
        settings.setStartKitEnabled(config.getBoolean("start-kit.enabled", true));

        // 加载速通者装备包
        if (config.contains("start-kit.speedrunner")) {
            StartKit speedrunnerKit = loadStartKit(config, "start-kit.speedrunner");
            if (speedrunnerKit != null) {
                settings.setSpeedrunnerKit(speedrunnerKit);
            }
        }

        // 加载捕猎者装备包
        if (config.contains("start-kit.hunter")) {
            StartKit hunterKit = loadStartKit(config, "start-kit.hunter");
            if (hunterKit != null) {
                settings.setHunterKit(hunterKit);
            }
        }
    }

    /**
     * 加载单个StartKit
     */
    private StartKit loadStartKit(YamlConfiguration config, String path) {
        try {
            String name = config.getString(path + ".name", "未命名装备包");
            String description = config.getString(path + ".description", "");
            String templateId = config.getString(path + ".template-id", UUID.randomUUID().toString());

            StartKit kit = new StartKit(name, description);
            kit.setTemplateId(templateId);

            // 加载主要物品
            if (config.contains(path + ".items")) {
                var itemsSection = config.getConfigurationSection(path + ".items");
                if (itemsSection != null) {
                    for (String slotKey : itemsSection.getKeys(false)) {
                        try {
                            int slot = Integer.parseInt(slotKey);
                            var itemSection = itemsSection.getConfigurationSection(slotKey);
                            if (itemSection != null) {
                                Map<String, Object> itemData = itemSection.getValues(true);
                                ItemStack item = ItemStack.deserialize(itemData);
                                kit.setItem(slot, item);
                            }
                        } catch (Exception e) {
                            plugin.getLogger().warning("加载装备包物品失败: " + e.getMessage());
                        }
                    }
                }
            }

            // 加载额外物品
            if (config.contains(path + ".extra-items")) {
                List<?> extraItemsList = config.getList(path + ".extra-items");
                if (extraItemsList != null) {
                    for (Object itemObj : extraItemsList) {
                        try {
                            Map<String, Object> itemData;
                            if (itemObj instanceof MemorySection) {
                                // 如果是MemorySection，获取其值
                                itemData = ((MemorySection) itemObj).getValues(true);
                            } else {
                                // 否则尝试直接转换
                                @SuppressWarnings("unchecked")
                                Map<String, Object> castedData = (Map<String, Object>) itemObj;
                                itemData = castedData;
                            }
                            ItemStack item = ItemStack.deserialize(itemData);
                            kit.addExtraItem(item);
                        } catch (Exception e) {
                            plugin.getLogger().warning("加载装备包额外物品失败: " + e.getMessage());
                        }
                    }
                }
            }

            return kit;
        } catch (Exception e) {
            plugin.getLogger().warning("加载装备包失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 保存里程碑设置
     */
    private void saveMilestoneSettings(YamlConfiguration config, RoomSettings settings) {
        MilestoneSettings milestoneSettings = settings.getMilestoneSettings();
        if (milestoneSettings != null) {
            // 这里需要根据MilestoneSettings的具体实现来保存
            // 暂时保存一个标记表示存在里程碑设置
            config.set("milestone.enabled", true);
            config.set("milestone.settings-summary", milestoneSettings.getSettingsSummary());
        } else {
            config.set("milestone.enabled", false);
        }
    }

    /**
     * 加载里程碑设置
     */
    private void loadMilestoneSettings(YamlConfiguration config, RoomSettings settings) {
        if (config.getBoolean("milestone.enabled", false)) {
            // 创建新的里程碑设置实例
            MilestoneSettings milestoneSettings = new MilestoneSettings();
            settings.setMilestoneSettings(milestoneSettings);
        }
    }

    /**
     * 设置文件信息类
     */
    public static class SettingsFileInfo {
        private final String name;
        private final String creator;
        private final String createdAt;
        private final String version;
        private final long lastModified;

        public SettingsFileInfo(String name, String creator, String createdAt, String version, long lastModified) {
            this.name = name;
            this.creator = creator;
            this.createdAt = createdAt;
            this.version = version;
            this.lastModified = lastModified;
        }

        public String getName() { return name; }
        public String getCreator() { return creator; }
        public String getCreatedAt() { return createdAt; }
        public String getVersion() { return version; }
        public long getLastModified() { return lastModified; }
    }
}
