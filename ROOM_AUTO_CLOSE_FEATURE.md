# 房间自动关闭功能

## 功能描述

当房间内所有玩家都离线超过1分钟时，系统会自动关闭房间。如果房间内有正在进行的游戏，会强制结算游戏但不计入分数。如果有玩家在1分钟内重连，则取消自动关闭。

## 实现原理

### 1. 触发机制
- **玩家离线时**: 检查房间内是否还有在线玩家
- **玩家重连时**: 检查是否需要取消自动关闭计时器

### 2. 计时器管理
- 使用 `BukkitTask` 实现延迟执行
- 每个房间最多只有一个活跃的自动关闭计时器
- 计时器延迟时间：60秒（1200 ticks）

### 3. 状态检查
- 定期检查房间内玩家的在线状态
- 如果有玩家重连，立即取消计时器
- 如果所有玩家仍然离线，执行强制关闭

## 核心代码实现

### RoomManager 新增字段
```java
// 房间自动关闭管理
private final Map<String, BukkitTask> roomAutoCloseTimers = new ConcurrentHashMap<>();
private static final long AUTO_CLOSE_DELAY = 60 * 20L; // 60秒 = 1200 ticks
```

### 主要方法

#### 1. checkRoomAutoClose(String roomId)
检查房间是否需要启动或取消自动关闭计时器：
- 遍历房间内所有玩家
- 检查是否有在线玩家
- 有在线玩家：取消计时器
- 无在线玩家：启动计时器

#### 2. startAutoCloseTimer(String roomId)
启动房间自动关闭计时器：
- 取消现有计时器（如果存在）
- 创建新的延迟任务
- 60秒后执行强制关闭检查

#### 3. cancelAutoCloseTimer(String roomId)
取消房间自动关闭计时器：
- 移除并取消对应的 BukkitTask
- 记录取消原因（玩家重连）

#### 4. forceCloseRoom(String roomId)
强制关闭房间：
- 检查是否有正在进行的游戏
- 如果有游戏，调用 `forceEndGame(false)` 不计分结束
- 删除房间

### GameManager 新增方法

#### getGameSessionByRoom(String roomId)
根据房间ID获取游戏会话：
```java
public GameSession getGameSessionByRoom(String roomId) {
    for (GameSession session : activeSessions.values()) {
        if (session.getRoom().getId().equals(roomId)) {
            return session;
        }
    }
    return null;
}
```

### GameSession 新增方法

#### forceEndGame(boolean countScore)
强制结束游戏（可选择是否计分）：
```java
public void forceEndGame(boolean countScore) {
    endGame(WinCondition.ADMIN_END, countScore);
}
```

#### endGame(WinCondition winCondition, boolean countScore)
结束游戏的重载方法，支持选择是否计分：
- `countScore = true`: 正常计分和更新统计
- `countScore = false`: 不计分，显示异常结束提示

## 事件集成

### PlayerListener 修改

#### onPlayerQuit 事件
```java
// 检查房间是否需要自动关闭
plugin.getRoomManager().checkRoomAutoClose(room.getId());
```

#### onPlayerJoin 事件
```java
// 检查房间是否需要取消自动关闭
plugin.getRoomManager().checkRoomAutoClose(gameSession.getRoom().getId());

// 检查玩家是否在房间中（处理房间重连）
Room room = plugin.getRoomManager().getRoomByPlayer(playerId);
if (room != null && gameSession == null) {
    plugin.getRoomManager().checkRoomAutoClose(room.getId());
}
```

## 工作流程

### 场景1：所有玩家离线超时
1. 最后一个玩家离线 → `PlayerQuitEvent`
2. 检查房间内无在线玩家 → 启动60秒计时器
3. 60秒后检查仍无在线玩家 → 强制关闭房间
4. 如果有正在进行的游戏 → 强制结束游戏（不计分）
5. 删除房间

### 场景2：玩家在超时前重连
1. 玩家重连 → `PlayerJoinEvent`
2. 检查房间有在线玩家 → 取消自动关闭计时器
3. 房间继续正常运行

### 场景3：部分玩家重连
1. 部分玩家重连 → 取消计时器
2. 这些玩家再次离线 → 重新启动计时器
3. 循环直到所有玩家都离线超时或有玩家稳定在线

## 日志记录

系统会记录以下关键事件：
- 启动自动关闭计时器
- 取消自动关闭计时器（玩家重连）
- 强制关闭房间
- 强制结束游戏（不计分）

## 配置选项

目前超时时间硬编码为60秒，未来可以考虑添加配置选项：
```yaml
room:
  auto-close:
    enabled: true
    timeout-seconds: 60
    force-end-games: true
    count-score-on-force-end: false
```

## 注意事项

1. **线程安全**: 使用 `ConcurrentHashMap` 确保多线程安全
2. **内存泄漏**: 房间删除时会自动清理对应的计时器
3. **重复检查**: 计时器执行前会再次检查玩家在线状态
4. **游戏状态**: 只有正在进行的游戏才会被强制结束
5. **数据一致性**: 强制结束的游戏不会影响玩家统计和排名

## 测试建议

1. **基本功能测试**:
   - 创建房间，所有玩家离线，等待60秒验证房间关闭
   - 玩家在59秒时重连，验证计时器取消

2. **游戏中测试**:
   - 游戏进行中所有玩家离线，验证游戏强制结束且不计分
   - 游戏结束后玩家离线，验证房间正常关闭

3. **边界情况测试**:
   - 房主离线，其他玩家在线
   - 多个玩家频繁上下线
   - 服务器重启时的计时器清理

4. **性能测试**:
   - 大量房间同时触发自动关闭
   - 计时器的内存使用情况

## 相关文件

- `src/main/java/com/projectSource/ultimateManhurt/room/RoomManager.java`
- `src/main/java/com/projectSource/ultimateManhurt/game/GameManager.java`
- `src/main/java/com/projectSource/ultimateManhurt/game/GameSession.java`
- `src/main/java/com/projectSource/ultimateManhurt/listener/PlayerListener.java`
