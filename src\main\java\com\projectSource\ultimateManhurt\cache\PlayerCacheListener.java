package com.projectSource.ultimateManhurt.cache;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;

/**
 * 玩家缓存事件监听器
 * 在玩家加入和离开时更新缓存
 */
public class PlayerCacheListener implements Listener {
    
    private final UltimateManhurt plugin;
    
    public PlayerCacheListener(UltimateManhurt plugin) {
        this.plugin = plugin;
    }
    
    /**
     * 玩家加入时缓存信息
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(PlayerJoinEvent event) {
        // 缓存玩家信息
        plugin.getPlayerCacheManager().cachePlayer(event.getPlayer());
    }
    
    /**
     * 玩家离开时更新缓存
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerQuit(PlayerQuitEvent event) {
        // 更新缓存信息
        plugin.getPlayerCacheManager().cachePlayer(event.getPlayer());
    }
}
