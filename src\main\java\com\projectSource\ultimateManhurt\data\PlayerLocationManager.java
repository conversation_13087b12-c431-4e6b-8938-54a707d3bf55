package com.projectSource.ultimateManhurt.data;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.Player;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 玩家位置管理器
 * 负责保存和恢复玩家在游戏中的位置
 */
public class PlayerLocationManager {
    
    private final UltimateManhurt plugin;
    private final Map<UUID, SavedLocation> savedLocations = new ConcurrentHashMap<>();
    
    public PlayerLocationManager(UltimateManhurt plugin) {
        this.plugin = plugin;
    }
    
    /**
     * 保存玩家位置
     */
    public void savePlayerLocation(Player player) {
        UUID playerId = player.getUniqueId();
        Location location = player.getLocation();
        
        SavedLocation savedLocation = new SavedLocation(
            location.getWorld().getName(),
            location.getX(),
            location.getY(),
            location.getZ(),
            location.getYaw(),
            location.getPitch(),
            player.getHealth(),
            player.getFoodLevel(),
            player.getGameMode(),
            System.currentTimeMillis()
        );
        
        savedLocations.put(playerId, savedLocation);
        plugin.getLogger().info("保存玩家 " + player.getName() + " 的位置: " + 
            location.getWorld().getName() + " (" + 
            String.format("%.1f, %.1f, %.1f", location.getX(), location.getY(), location.getZ()) + ")");
    }
    
    /**
     * 恢复玩家位置
     */
    public boolean restorePlayerLocation(Player player) {
        UUID playerId = player.getUniqueId();
        SavedLocation savedLocation = savedLocations.get(playerId);
        
        if (savedLocation == null) {
            return false;
        }
        
        // 检查世界是否存在
        World world = Bukkit.getWorld(savedLocation.worldName);
        if (world == null) {
            plugin.getLogger().warning("无法恢复玩家 " + player.getName() + " 的位置：世界 " + savedLocation.worldName + " 不存在");
            return false;
        }
        
        // 检查保存的位置是否过期（超过1小时）
        long currentTime = System.currentTimeMillis();
        if (currentTime - savedLocation.timestamp > 3600000) { // 1小时
            plugin.getLogger().info("玩家 " + player.getName() + " 的保存位置已过期，不进行恢复");
            savedLocations.remove(playerId);
            return false;
        }
        
        // 恢复位置
        Location location = new Location(world, 
            savedLocation.x, savedLocation.y, savedLocation.z, 
            savedLocation.yaw, savedLocation.pitch);
        
        player.teleport(location);
        
        // 恢复玩家状态
        player.setHealth(Math.min(savedLocation.health, player.getMaxHealth()));
        player.setFoodLevel(savedLocation.foodLevel);
        player.setGameMode(savedLocation.gameMode);
        
        plugin.getLogger().info("恢复玩家 " + player.getName() + " 的位置: " + 
            location.getWorld().getName() + " (" + 
            String.format("%.1f, %.1f, %.1f", location.getX(), location.getY(), location.getZ()) + ")");
        
        return true;
    }
    
    /**
     * 移除保存的位置
     */
    public void removePlayerLocation(UUID playerId) {
        savedLocations.remove(playerId);
    }
    
    /**
     * 检查是否有保存的位置
     */
    public boolean hasPlayerLocation(UUID playerId) {
        return savedLocations.containsKey(playerId);
    }
    
    /**
     * 清理过期的位置数据
     */
    public void cleanupExpiredLocations() {
        long currentTime = System.currentTimeMillis();
        savedLocations.entrySet().removeIf(entry -> {
            boolean expired = currentTime - entry.getValue().timestamp > 3600000; // 1小时
            if (expired) {
                plugin.getLogger().info("清理过期的位置数据: " + entry.getKey());
            }
            return expired;
        });
    }
    
    /**
     * 获取保存的位置数量
     */
    public int getSavedLocationCount() {
        return savedLocations.size();
    }
    
    /**
     * 清理所有位置数据
     */
    public void clearAllLocations() {
        savedLocations.clear();
        plugin.getLogger().info("清理所有保存的位置数据");
    }
    
    /**
     * 保存的位置数据类
     */
    private static class SavedLocation {
        final String worldName;
        final double x, y, z;
        final float yaw, pitch;
        final double health;
        final int foodLevel;
        final org.bukkit.GameMode gameMode;
        final long timestamp;
        
        SavedLocation(String worldName, double x, double y, double z, float yaw, float pitch,
                     double health, int foodLevel, org.bukkit.GameMode gameMode, long timestamp) {
            this.worldName = worldName;
            this.x = x;
            this.y = y;
            this.z = z;
            this.yaw = yaw;
            this.pitch = pitch;
            this.health = health;
            this.foodLevel = foodLevel;
            this.gameMode = gameMode;
            this.timestamp = timestamp;
        }
    }
}
