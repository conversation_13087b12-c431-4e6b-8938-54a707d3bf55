package com.projectSource.ultimateManhurt.listener;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.game.PlayerRole;

import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.entity.EnderDragon;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.player.PlayerRespawnEvent;
import org.bukkit.event.player.PlayerTeleportEvent;

import java.util.UUID;

/**
 * 游戏事件监听器
 */
public class GameListener implements Listener {

    private final UltimateManhurt plugin;

    public GameListener(UltimateManhurt plugin) {
        this.plugin = plugin;
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerDamageByEntity(EntityDamageByEntityEvent event) {
        if (!(event.getEntity() instanceof Player victim)) {
            return;
        }

        if (!(event.getDamager() instanceof Player attacker)) {
            return;
        }

        // 检查是否在游戏中
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(victim.getUniqueId());
        if (gameSession == null) {
            return;
        }

        // 使用游戏规则处理伤害
        gameSession.getRules().handlePlayerDamage(event);
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        // 记录末影人受到攻击的时间（用于传送技能限制）
        if (event.getEntity() instanceof Player victim) {
            com.projectSource.ultimateManhurt.profession.Profession profession =
                plugin.getProfessionManager().getPlayerProfession(victim.getUniqueId());
            if (profession == com.projectSource.ultimateManhurt.profession.Profession.ENDERMAN) {
                plugin.getProfessionManager().getActiveSkillHandler().recordEndermanDamage(victim.getUniqueId());
            }
        }

        // 检查是否是弓箭攻击玩家（友军伤害检查）
        if (event.getDamager() instanceof org.bukkit.entity.Arrow arrow && event.getEntity() instanceof Player victim) {
            if (handleArrowFriendlyFire(arrow, victim, event)) {
                return; // 如果是友军伤害，已经处理完毕
            }
        }

        // 检查是否是凋零攻击任何实体
        if (event.getDamager() instanceof org.bukkit.entity.Wither wither) {
            if (handleWitherAttackEntity(wither, event)) {
                return; // 如果是守卫凋零的攻击，已经处理完毕
            }
        }

        // 检查是否是凋零骷髅头攻击任何实体
        if (event.getDamager() instanceof org.bukkit.entity.WitherSkull skull) {
            // 检查是否是恐惧魔王的影压技能
            if (skull.hasMetadata("fear_lord_shadow_strike")) {
                // 取消恐惧魔王影压技能的原生伤害，让ProfessionListener处理
                event.setCancelled(true);
                return;
            }

            if (handleWitherSkullAttackEntity(skull, event)) {
                return; // 如果是守卫凋零骷髅头的攻击，已经处理完毕
            }
        }

        // 检查是否是对凋零的伤害
        if (!(event.getEntity() instanceof org.bukkit.entity.Wither wither)) {
            return;
        }

        // 检查伤害来源是否是玩家
        if (!(event.getDamager() instanceof Player attacker)) {
            return;
        }

        // 检查是否在游戏中
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(attacker.getUniqueId());
        if (gameSession == null) {
            return;
        }

        // 检查是否是守卫模式
        if (gameSession.getRoom().getSettings().getVictoryMode() != com.projectSource.ultimateManhurt.game.VictoryMode.GUARD_MODE) {
            return;
        }

        // 检查是否是守卫凋零
        com.projectSource.ultimateManhurt.game.guard.GuardModeManager guardManager = gameSession.getGuardModeManager();
        if (guardManager.getGuardWither() == wither) {
            // 检查攻击者的角色
            PlayerRole attackerRole = gameSession.getPlayerRole(attacker.getUniqueId());
            if (attackerRole == com.projectSource.ultimateManhurt.game.PlayerRole.SPEEDRUNNER) {
                // 速通者不能攻击守卫凋零
                event.setCancelled(true);
                ComponentUtil.sendMessage(attacker, ComponentUtil.error("你不能攻击守卫凋零！你的任务是保护它！"));
                plugin.getLogger().info("阻止了速通者 " + attacker.getName() + " 攻击守卫凋零");
                return;
            }

            // 处理对守卫凋零的伤害（只有捕猎者能造成伤害）
            guardManager.handleWitherDamage(event);
        }
    }

    /**
     * 处理凋零攻击任何实体
     */
    private boolean handleWitherAttackEntity(org.bukkit.entity.Wither wither, EntityDamageByEntityEvent event) {
        return handleGuardWitherAttack(wither, event, "守卫凋零");
    }

    /**
     * 处理凋零骷髅头攻击任何实体
     */
    private boolean handleWitherSkullAttackEntity(org.bukkit.entity.WitherSkull skull, EntityDamageByEntityEvent event) {
        // 检查骷髅头是否来自凋零
        if (!(skull.getShooter() instanceof org.bukkit.entity.Wither wither)) {
            return false;
        }
        return handleGuardWitherAttack(wither, event, "守卫凋零骷髅头");
    }

    /**
     * 统一处理守卫凋零相关的攻击
     */
    private boolean handleGuardWitherAttack(org.bukkit.entity.Wither wither, EntityDamageByEntityEvent event, String attackerName) {
        // 验证是否是守卫凋零
        GuardWitherInfo info = getGuardWitherInfo(wither);
        if (info == null) {
            return false;
        }

        plugin.getLogger().info("检测到" + attackerName + "攻击实体: " + event.getEntity().getType());

        // 如果被攻击者是玩家
        if (event.getEntity() instanceof Player victim) {
            return handleGuardWitherAttackPlayer(victim, event, info.gameSession, attackerName);
        } else {
            // 攻击的是其他生物，守卫凋零不应该攻击其他生物
            event.setCancelled(true);
            plugin.getLogger().info("阻止了" + attackerName + "对 " + event.getEntity().getType() + " 的攻击");
            return true;
        }
    }

    /**
     * 处理守卫凋零攻击玩家
     */
    private boolean handleGuardWitherAttackPlayer(Player victim, EntityDamageByEntityEvent event, GameSession gameSession, String attackerName) {
        PlayerRole victimRole = gameSession.getPlayerRole(victim.getUniqueId());

        if (victimRole == com.projectSource.ultimateManhurt.game.PlayerRole.SPEEDRUNNER) {
            // 速通者不应该受到守卫凋零的伤害
            event.setCancelled(true);
            plugin.getLogger().info("阻止了" + attackerName + "对速通者 " + victim.getName() + " 的攻击");
        } else if (victimRole == com.projectSource.ultimateManhurt.game.PlayerRole.HUNTER) {
            // 捕猎者受到伤害，处理凋零效果
            plugin.getLogger().info(attackerName + "攻击了捕猎者: " + victim.getName());

            // 如果是骷髅头攻击，需要特殊处理凋零效果
            if (attackerName.contains("骷髅头")) {
                // 取消原生的凋零效果，使用我们自定义的
                new org.bukkit.scheduler.BukkitRunnable() {
                    @Override
                    public void run() {
                        if (victim.isOnline() && !victim.isDead()) {
                            // 移除原生的凋零效果（30秒凋零II）
                            victim.removePotionEffect(org.bukkit.potion.PotionEffectType.WITHER);

                            // 使用配置的凋零效果参数
                            com.projectSource.ultimateManhurt.room.RoomSettings settings = gameSession.getRoom().getSettings();
                            int duration = settings.getWitherEffectDuration();
                            int level = settings.getWitherEffectLevel();

                            victim.addPotionEffect(new org.bukkit.potion.PotionEffect(
                                org.bukkit.potion.PotionEffectType.WITHER, duration, level));

                            String levelText = level == 0 ? "I" : level == 1 ? "II" : "III";
                            ComponentUtil.sendMessage(victim, ComponentUtil.warning("你被凋零效果" + levelText + "感染了！"));
                        }
                    }
                }.runTaskLater(plugin, 2L); // 延迟2tick确保原生效果已经应用
            }
        }

        return true;
    }

    /**
     * 获取守卫凋零信息
     */
    private GuardWitherInfo getGuardWitherInfo(org.bukkit.entity.Wither wither) {
        // 查找游戏会话
        GameSession gameSession = findGameSessionByWorld(wither.getWorld());
        if (gameSession == null) {
            return null;
        }

        // 检查是否是守卫模式
        if (gameSession.getRoom().getSettings().getVictoryMode() != com.projectSource.ultimateManhurt.game.VictoryMode.GUARD_MODE) {
            return null;
        }

        // 检查是否是守卫凋零
        com.projectSource.ultimateManhurt.game.guard.GuardModeManager guardManager = gameSession.getGuardModeManager();
        if (guardManager.getGuardWither() != wither) {
            return null; // 不是守卫凋零
        }

        return new GuardWitherInfo(gameSession);
    }

    /**
     * 守卫凋零信息类
     */
    private static class GuardWitherInfo {
        final GameSession gameSession;

        GuardWitherInfo(GameSession gameSession) {
            this.gameSession = gameSession;
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityExplode(org.bukkit.event.entity.EntityExplodeEvent event) {
        handleWitherExplosion(event);
    }

    /**
     * 处理凋零相关的爆炸事件
     */
    private void handleWitherExplosion(org.bukkit.event.entity.EntityExplodeEvent event) {
        GameSession gameSession = null;
        org.bukkit.entity.Wither guardWither = null;

        // 检查是否是凋零爆炸
        if (event.getEntity() instanceof org.bukkit.entity.Wither wither) {
            gameSession = findGameSessionByWorld(event.getLocation().getWorld());
            if (gameSession != null && isGuardModeWithGuardWither(gameSession, wither)) {
                guardWither = wither;
            }
        }
        // 检查是否是凋零骷髅头爆炸
        else if (event.getEntity() instanceof org.bukkit.entity.WitherSkull skull) {
            // 检查是否是恐惧魔王的影压技能
            if (skull.hasMetadata("fear_lord_shadow_strike")) {
                // 完全取消恐惧魔王影压技能的爆炸效果
                event.setCancelled(true);
                return;
            }

            // 检查是否是守卫凋零的骷髅头
            if (skull.getShooter() instanceof org.bukkit.entity.Wither wither) {
                gameSession = findGameSessionByWorld(event.getLocation().getWorld());
                if (gameSession != null && isGuardModeWithGuardWither(gameSession, wither)) {
                    guardWither = wither;
                }
            }
        }

        if (gameSession == null || guardWither == null) {
            return;
        }

        // 如果设置为不破坏方块，则清空爆炸方块列表
        if (!gameSession.getRoom().getSettings().isWitherDestroyBlocks()) {
            event.blockList().clear();
        }
    }

    /**
     * 处理爆炸伤害事件（防止守卫凋零的爆炸伤害到速通者）
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityDamageByEntityExplosion(EntityDamageByEntityEvent event) {
        // 检查是否是爆炸伤害
        if (event.getCause() != org.bukkit.event.entity.EntityDamageEvent.DamageCause.ENTITY_EXPLOSION) {
            return;
        }

        // 检查受害者是否是玩家
        if (!(event.getEntity() instanceof Player victim)) {
            return;
        }

        GameSession gameSession = null;
        org.bukkit.entity.Wither guardWither = null;

        // 检查伤害来源
        if (event.getDamager() instanceof org.bukkit.entity.Wither wither) {
            gameSession = plugin.getGameManager().getGameSessionByPlayer(victim.getUniqueId());
            if (gameSession != null && isGuardModeWithGuardWither(gameSession, wither)) {
                guardWither = wither;
            }
        } else if (event.getDamager() instanceof org.bukkit.entity.WitherSkull skull) {
            // 检查是否是恐惧魔王的影压技能
            if (skull.hasMetadata("fear_lord_shadow_strike")) {
                // 取消恐惧魔王影压技能的爆炸伤害，让ProfessionListener处理
                event.setCancelled(true);
                return;
            }

            if (skull.getShooter() instanceof org.bukkit.entity.Wither wither) {
                gameSession = plugin.getGameManager().getGameSessionByPlayer(victim.getUniqueId());
                if (gameSession != null && isGuardModeWithGuardWither(gameSession, wither)) {
                    guardWither = wither;
                }
            }
        }

        if (gameSession == null || guardWither == null) {
            return;
        }

        // 检查被攻击者的角色
        PlayerRole victimRole = gameSession.getPlayerRole(victim.getUniqueId());
        if (victimRole == com.projectSource.ultimateManhurt.game.PlayerRole.SPEEDRUNNER) {
            // 速通者不应该受到守卫凋零爆炸的伤害
            event.setCancelled(true);
            plugin.getLogger().info("守卫凋零爆炸对速通者 " + victim.getName() + " 的伤害已被阻止");
        }
    }

    /**
     * 通过世界查找游戏会话
     */
    private GameSession findGameSessionByWorld(org.bukkit.World world) {
        String worldName = world.getName();
        String roomId = plugin.getWorldManager().getRoomIdByWorld(worldName);
        if (roomId != null) {
            return plugin.getGameManager().getGameSessionByRoom(roomId);
        }
        return null;
    }

    /**
     * 检查是否是守卫模式且是守卫凋零
     */
    private boolean isGuardModeWithGuardWither(GameSession gameSession, org.bukkit.entity.Wither wither) {
        if (gameSession.getRoom().getSettings().getVictoryMode() != com.projectSource.ultimateManhurt.game.VictoryMode.GUARD_MODE) {
            return false;
        }

        com.projectSource.ultimateManhurt.game.guard.GuardModeManager guardManager = gameSession.getGuardModeManager();
        return guardManager.getGuardWither() == wither;
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerDeath(PlayerDeathEvent event) {
        Player player = event.getEntity();
        UUID playerId = player.getUniqueId();

        // 检查是否在游戏中
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(playerId);
        if (gameSession == null) {
            return;
        }

        // 使用游戏规则处理死亡
        gameSession.getRules().handlePlayerDeath(event);

        // 记录击杀者
        Player killer = player.getKiller();
        if (killer != null) {
            gameSession.recordKill(killer.getUniqueId());

            // 发送击杀消息
            PlayerRole killerRole = gameSession.getPlayerRole(killer.getUniqueId());
            PlayerRole victimRole = gameSession.getPlayerRole(playerId);

            if (killerRole != null && victimRole != null) {
                String message = String.format("%s %s 击败了 %s %s",
                    killerRole.getEmoji(), killer.getName(),
                    victimRole.getEmoji(), player.getName());

                // 广播给游戏中的所有玩家
                broadcastToGame(gameSession, ComponentUtil.parse("<yellow>" + message));
            }
        }
    }

    /**
     * 处理凋零死亡
     */
    private void handleWitherDeath(org.bukkit.entity.Wither wither) {
        // 首先通过世界查找游戏会话
        GameSession gameSession = findGameSessionByWorld(wither.getWorld());
        if (gameSession == null) {
            return;
        }

        // 检查是否是守卫模式
        if (gameSession.getRoom().getSettings().getVictoryMode() != com.projectSource.ultimateManhurt.game.VictoryMode.GUARD_MODE) {
            return;
        }

        // 检查是否是守卫凋零
        com.projectSource.ultimateManhurt.game.guard.GuardModeManager guardManager = gameSession.getGuardModeManager();
        if (guardManager.getGuardWither() != wither) {
            return; // 不是守卫凋零
        }

        plugin.getLogger().info("守卫凋零已死亡，准备结束游戏");

        // 查找击杀凋零的玩家
        Player killer = wither.getKiller();
        if (killer != null) {
            // 检查击杀者是否是捕猎者
            PlayerRole killerRole = gameSession.getPlayerRole(killer.getUniqueId());
            if (killerRole == PlayerRole.HUNTER) {
                // 广播胜利消息
                broadcastToGame(gameSession, ComponentUtil.parse(
                    "<red><bold>" + killer.getName() + " 击败了守卫凋零！捕猎者获胜！</bold>"
                ));
            } else {
                // 非捕猎者击杀了凋零（不应该发生，但处理一下）
                broadcastToGame(gameSession, ComponentUtil.parse(
                    "<red><bold>守卫凋零被击败！捕猎者获胜！</bold>"
                ));
            }
        } else {
            // 没有明确的击杀者，可能是环境伤害等
            broadcastToGame(gameSession, ComponentUtil.parse(
                "<red><bold>守卫凋零被击败！捕猎者获胜！</bold>"
            ));
        }

        // 处理凋零死亡，结束游戏
        gameSession.getRules().handleWitherDeath();
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityTarget(org.bukkit.event.entity.EntityTargetEvent event) {
        // 检查是否是凋零设置目标
        if (!(event.getEntity() instanceof org.bukkit.entity.Wither wither)) {
            return;
        }

        // 查找游戏会话
        GameSession gameSession = findGameSessionByWorld(wither.getWorld());
        if (gameSession == null) {
            return;
        }

        // 检查是否是守卫模式
        if (gameSession.getRoom().getSettings().getVictoryMode() != com.projectSource.ultimateManhurt.game.VictoryMode.GUARD_MODE) {
            return;
        }

        // 检查是否是守卫凋零
        com.projectSource.ultimateManhurt.game.guard.GuardModeManager guardManager = gameSession.getGuardModeManager();
        if (guardManager.getGuardWither() != wither) {
            return; // 不是守卫凋零
        }

        // AI关闭的守卫凋零不应该设置任何目标（理论上这个事件不应该触发）
        event.setCancelled(true);
        plugin.getLogger().warning("守卫凋零尝试设置目标（AI已关闭，这不应该发生）: " +
            (event.getTarget() != null ? event.getTarget().getType() : "null"));
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onEntitySpawn(org.bukkit.event.entity.EntitySpawnEvent event) {
        // 检查是否是凋零生成
        if (!(event.getEntity() instanceof org.bukkit.entity.Wither wither)) {
            return;
        }

        // 查找游戏会话
        GameSession gameSession = findGameSessionByWorld(event.getLocation().getWorld());
        if (gameSession == null) {
            return;
        }

        // 检查是否是守卫模式
        if (gameSession.getRoom().getSettings().getVictoryMode() != com.projectSource.ultimateManhurt.game.VictoryMode.GUARD_MODE) {
            return;
        }

        // 检查是否是守卫凋零
        com.projectSource.ultimateManhurt.game.guard.GuardModeManager guardManager = gameSession.getGuardModeManager();
        if (guardManager.getGuardWither() == wither) {
            // 立即禁用AI
            wither.setAI(false);
            plugin.getLogger().info("在凋零生成事件中禁用了守卫凋零的AI");
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerRespawn(PlayerRespawnEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();

        // 检查是否在游戏中
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(playerId);
        if (gameSession == null) {
            return;
        }

        // 使用游戏规则处理重生
        gameSession.getRules().handlePlayerRespawn(event);
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityDeath(EntityDeathEvent event) {
        // 检查是否是末影龙死亡
        if (event.getEntity() instanceof EnderDragon dragon) {
            handleDragonDeath(dragon);
            return;
        }

        // 检查是否是凋零死亡
        if (event.getEntity() instanceof org.bukkit.entity.Wither wither) {
            handleWitherDeath(wither);
            return;
        }
    }

    /**
     * 处理末影龙死亡
     */
    private void handleDragonDeath(EnderDragon dragon) {
        // 查找击杀末影龙的玩家
        Player killer = dragon.getKiller();
        if (killer == null) {
            return;
        }

        // 检查是否在游戏中
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(killer.getUniqueId());
        if (gameSession == null) {
            return;
        }

        // 检查击杀者是否是速通者
        PlayerRole killerRole = gameSession.getPlayerRole(killer.getUniqueId());
        if (killerRole == PlayerRole.SPEEDRUNNER) {
            // 检查当前胜利模式是否需要末影龙
            if (gameSession.getRoom().getSettings().getVictoryMode().requiresDragon()) {
                // 只有在末影龙模式下才因击杀末影龙而胜利
                broadcastToGame(gameSession, ComponentUtil.parse(
                    "<green><bold>" + killer.getName() + " 击败了末影龙！速通者获胜！</bold>"
                ));

                gameSession.getRules().handleDragonDeath();
            } else {
                // 在其他模式下，击杀末影龙只是一个普通事件，不触发胜利
                broadcastToGame(gameSession, ComponentUtil.parse(
                    "<yellow>" + killer.getName() + " 击败了末影龙！"
                ));
            }
        }
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerTeleport(PlayerTeleportEvent event) {
        if (event.getCause() != PlayerTeleportEvent.TeleportCause.ENDER_PEARL) {
            return;
        }

        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();

        // 检查是否在游戏中
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(playerId);
        if (gameSession == null) {
            return;
        }

        // 检查末影珍珠冷却
        if (!gameSession.getRules().canUseEnderPearl(player)) {
            event.setCancelled(true);

            // 返还末影珍珠给玩家
            org.bukkit.inventory.ItemStack enderPearl = new org.bukkit.inventory.ItemStack(org.bukkit.Material.ENDER_PEARL, 1);
            player.getInventory().addItem(enderPearl);

            long lastUse = gameSession.getLastEnderPearlUse(playerId);
            long cooldown = gameSession.getRoom().getSettings().getEnderPearlCooldownSeconds();
            long remaining = cooldown - (System.currentTimeMillis() - lastUse) / 1000;

            ComponentUtil.sendMessage(player, ComponentUtil.error(
                "末影珍珠冷却中！还需等待 " + remaining + " 秒"
            ));
            return;
        }

        // 记录末影珍珠使用
        gameSession.getRules().recordEnderPearlUse(player);
    }

    /**
     * 向游戏中的所有玩家广播消息
     */
    private void broadcastToGame(GameSession gameSession, net.kyori.adventure.text.Component message) {
        for (UUID playerId : gameSession.getPlayerRoles().keySet()) {
            Player player = plugin.getServer().getPlayer(playerId);
            if (player != null && player.isOnline()) {
                ComponentUtil.sendMessage(player, message);
            }
        }
    }

    /**
     * 处理弓箭友军伤害
     */
    private boolean handleArrowFriendlyFire(org.bukkit.entity.Arrow arrow, Player victim, EntityDamageByEntityEvent event) {
        // 检查射箭者是否是玩家
        if (!(arrow.getShooter() instanceof Player shooter)) {
            return false; // 不是玩家射的箭，不处理
        }

        // 检查射箭者和受害者是否在游戏中
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(shooter.getUniqueId());
        if (gameSession == null) {
            return false; // 不在游戏中，不处理
        }

        // 检查受害者是否也在同一游戏中
        GameSession victimGameSession = plugin.getGameManager().getGameSessionByPlayer(victim.getUniqueId());
        if (victimGameSession == null || !victimGameSession.equals(gameSession)) {
            return false; // 受害者不在同一游戏中，不处理
        }

        // 检查是否是同阵营
        com.projectSource.ultimateManhurt.game.PlayerRole shooterRole = gameSession.getPlayerRole(shooter.getUniqueId());
        com.projectSource.ultimateManhurt.game.PlayerRole victimRole = gameSession.getPlayerRole(victim.getUniqueId());

        if (shooterRole == victimRole && shooterRole != com.projectSource.ultimateManhurt.game.PlayerRole.SPECTATOR) {
            // 同阵营友军伤害，取消伤害
            event.setCancelled(true);

            // 发送提示消息
            ComponentUtil.sendMessage(shooter, ComponentUtil.warning("不能攻击同阵营的队友！"));
            ComponentUtil.sendMessage(victim, ComponentUtil.info(shooter.getName() + " 的箭被友军保护阻挡了"));

            plugin.getLogger().info("阻止了 " + shooter.getName() + " 对同阵营队友 " + victim.getName() + " 的弓箭伤害");
            return true;
        }

        return false; // 不是友军伤害，继续正常处理
    }
}
