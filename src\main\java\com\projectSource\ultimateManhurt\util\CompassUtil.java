package com.projectSource.ultimateManhurt.util;

import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;

/**
 * 指南针工具类
 * 用于创建和识别特殊的猎人指南针
 */
public class CompassUtil {
    
    private static final NamespacedKey PROTECTED_ITEM_KEY = new NamespacedKey("ultimatemanhunt", "protected_item");
    private static final String HUNTER_COMPASS_VALUE = "hunter_compass";
    
    /**
     * 创建猎人专用指南针
     */
    public static ItemStack createHunterCompass() {
        ItemStack compass = new ItemStack(Material.COMPASS);
        ItemMeta meta = compass.getItemMeta();
        
        if (meta != null) {
            // 设置显示名称
            meta.displayName(ComponentUtil.parse("<red><bold>🧭 猎人指南针"));
            
            // 设置描述
            java.util.List<net.kyori.adventure.text.Component> lore = new java.util.ArrayList<>();
            lore.add(ComponentUtil.parse("<gray>指向最近的速通者"));
            lore.add(ComponentUtil.parse("<gray>右键切换追踪目标"));
            lore.add(ComponentUtil.parse(""));
            lore.add(ComponentUtil.parse("<yellow>死亡时不会掉落"));
            meta.lore(lore);
            
            // 添加特殊标识，用于死亡保护识别
            meta.getPersistentDataContainer().set(
                PROTECTED_ITEM_KEY,
                PersistentDataType.STRING,
                HUNTER_COMPASS_VALUE
            );
            
            compass.setItemMeta(meta);
        }
        
        return compass;
    }
    
    /**
     * 检查是否为猎人指南针
     */
    public static boolean isHunterCompass(ItemStack item) {
        if (item == null || item.getType() != Material.COMPASS) {
            return false;
        }
        
        ItemMeta meta = item.getItemMeta();
        if (meta == null) {
            return false;
        }
        
        // 检查特殊标识
        String value = meta.getPersistentDataContainer().get(PROTECTED_ITEM_KEY, PersistentDataType.STRING);
        return HUNTER_COMPASS_VALUE.equals(value);
    }
    
    /**
     * 检查是否为受保护的物品（通用方法）
     */
    public static boolean isProtectedItem(ItemStack item) {
        if (item == null) {
            return false;
        }
        
        ItemMeta meta = item.getItemMeta();
        if (meta == null) {
            return false;
        }
        
        // 检查是否有保护标识
        return meta.getPersistentDataContainer().has(PROTECTED_ITEM_KEY, PersistentDataType.STRING);
    }
    
    /**
     * 获取保护物品的类型
     */
    public static String getProtectedItemType(ItemStack item) {
        if (item == null) {
            return null;
        }
        
        ItemMeta meta = item.getItemMeta();
        if (meta == null) {
            return null;
        }
        
        return meta.getPersistentDataContainer().get(PROTECTED_ITEM_KEY, PersistentDataType.STRING);
    }
}
