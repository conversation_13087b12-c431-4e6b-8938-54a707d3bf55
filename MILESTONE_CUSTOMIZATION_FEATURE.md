# 里程碑自定义功能实现报告

## 功能概述

实现了里程碑分数的房主自定义功能，允许房主在新的GUI界面中设置每个里程碑的开关状态和自定义分数。速通者在游戏开始时会获得一本包含里程碑内容和分数的指南书籍。

## 实现的功能

### 1. 里程碑设置管理类 (MilestoneSettings.java)
- 管理每个房间的里程碑配置
- 支持里程碑的开关状态设置
- 支持自定义分数设置
- 提供批量操作和统计功能
- 支持设置的克隆和重置

**主要方法：**
- `isMilestoneEnabled(milestone)` - 检查里程碑是否启用
- `setMilestoneEnabled(milestone, enabled)` - 设置里程碑启用状态
- `getMilestoneScore(milestone)` - 获取里程碑自定义分数
- `setMilestoneScore(milestone, score)` - 设置里程碑自定义分数
- `resetToDefaults()` - 重置所有设置为默认值
- `getTotalPossibleScore()` - 获取所有启用里程碑的总分数

### 2. 房间设置集成 (RoomSettings.java)
- 在房间设置中添加了里程碑设置字段
- 自动初始化里程碑设置
- 支持设置的序列化和克隆
- 提供getter/setter方法

**修改内容：**
- 添加 `milestoneSettings` 字段
- 修改构造函数以初始化里程碑设置
- 在clone方法中包含里程碑设置的深拷贝

### 3. 里程碑设置GUI (MilestoneSettingsGui.java)
- 提供直观的图形界面管理里程碑
- 支持分页显示所有里程碑
- 每个里程碑显示详细信息（名称、描述、难度、分数）
- 支持快速操作（全部启用/禁用、重置默认）

**操作方式：**
- 左键：切换里程碑启用状态
- 右键：修改分数（每次+10分）
- Shift+右键：重置为默认分数
- 全部启用/禁用按钮
- 重置默认设置按钮

### 4. 积分系统集成 (ScoreSystem.java)
- 修改积分计算逻辑，使用房间的里程碑设置
- 禁用的里程碑不会给予分数
- 使用自定义分数而非固定分数
- 保持所有现有功能的兼容性

**修改内容：**
- `addScore()` 方法检查里程碑是否启用
- 使用 `milestoneSettings.getMilestoneScore()` 获取自定义分数
- 更新广播消息以显示正确的分数

### 5. 里程碑指南书籍 (MilestoneGuideBook.java)
- 为速通者生成包含所有里程碑信息的书籍
- 按类别组织里程碑（基础进度、重要里程碑、下界进度、末地挑战、奖励里程碑）
- 显示每个里程碑的状态、分数、描述和难度
- 包含统计信息和总结页面

**书籍内容：**
- 封面页：房间里程碑设置概览
- 分类页面：按难度和类型分组的里程碑
- 总结页：统计信息和难度分布
- 自定义设置标识

### 6. GUI集成 (GuiManager.java & RoomSettingsGui.java)
- 在房间设置GUI中添加里程碑设置入口
- 提供便捷的访问方式
- 只有房主可以访问里程碑设置
- 显示当前里程碑设置的摘要信息

### 7. 游戏流程集成 (StartKitManager.java)
- 在游戏开始时自动为速通者发放里程碑指南书籍
- **仅在积分模式和混合模式下发放**（生命模式和末影龙模式不发放）
- 与现有的装备包分发系统无缝集成
- 提供错误处理和日志记录

## 使用方法

### 房主设置里程碑
1. 进入房间设置GUI
2. 点击"里程碑设置"按钮
3. 在里程碑设置界面中：
   - 左键点击里程碑切换启用/禁用状态
   - 右键点击里程碑修改分数
   - 使用快捷按钮进行批量操作
4. 设置会自动保存

### 速通者获取指南
1. **仅在积分模式和混合模式下**游戏开始时自动获得里程碑指南书籍
2. 右键点击书籍阅读
3. 书籍包含所有里程碑的详细信息和当前房间的自定义设置
4. 生命模式和末影龙模式不会发放指南书籍（因为不使用积分系统）

## 技术特点

### 兼容性
- 完全向后兼容现有系统
- 默认设置保持原有行为
- 不影响现有的积分计算逻辑

### 性能优化
- 使用HashMap进行快速查找
- 延迟初始化和缓存机制
- 最小化GUI刷新次数

### 用户体验
- 直观的图形界面
- 清晰的状态指示
- 详细的帮助信息
- 错误处理和用户反馈

### 数据安全
- 输入验证（分数不能为负数）
- 异常处理
- 日志记录
- 设置的深拷贝防止意外修改

## 文件清单

### 新增文件
- `src/main/java/com/projectSource/ultimateManhurt/game/scoring/MilestoneSettings.java`
- `src/main/java/com/projectSource/ultimateManhurt/gui/MilestoneSettingsGui.java`
- `src/main/java/com/projectSource/ultimateManhurt/game/scoring/MilestoneGuideBook.java`

### 修改文件
- `src/main/java/com/projectSource/ultimateManhurt/room/RoomSettings.java`
- `src/main/java/com/projectSource/ultimateManhurt/game/scoring/ScoreSystem.java`
- `src/main/java/com/projectSource/ultimateManhurt/game/scoring/ScoreMilestone.java`
- `src/main/java/com/projectSource/ultimateManhurt/gui/GuiManager.java`
- `src/main/java/com/projectSource/ultimateManhurt/gui/RoomSettingsGui.java`
- `src/main/java/com/projectSource/ultimateManhurt/kit/StartKitManager.java`

### 重要修正
1. **文本格式修正**：将所有书籍内容从老式的§代码改为现代的MiniMessage格式
2. **胜利模式限制**：里程碑指南书籍仅在积分模式和混合模式下发放，生命模式和末影龙模式不发放

## 测试建议

1. **基础功能测试**
   - 创建房间并进入里程碑设置
   - 测试里程碑的启用/禁用功能
   - 测试分数修改功能
   - 验证设置保存和加载

2. **游戏流程测试**
   - 在积分模式/混合模式下开始游戏并验证速通者获得指南书籍
   - 在生命模式/末影龙模式下验证速通者不会获得指南书籍
   - 完成里程碑并验证分数计算正确
   - 测试禁用里程碑不给分数

3. **GUI测试**
   - 测试分页功能
   - 测试批量操作
   - 验证权限控制（只有房主可以修改）

4. **兼容性测试**
   - 测试现有房间的兼容性
   - 验证默认设置的正确性
   - 测试与其他功能的集成

## 总结

成功实现了完整的里程碑自定义功能，包括：
- ✅ 里程碑设置管理系统
- ✅ 直观的GUI界面
- ✅ 自定义分数支持
- ✅ 里程碑指南书籍
- ✅ 游戏流程集成
- ✅ 完整的用户体验

该功能为房主提供了灵活的游戏配置选项，为速通者提供了清晰的游戏指导，大大增强了游戏的可定制性和用户体验。
