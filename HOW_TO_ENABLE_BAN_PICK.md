# 如何开启Ban Pick系统

## 🎯 开启步骤

### 1. 进入房间设置
1. 作为房主，在房间中输入命令：`/manhunt settings`
2. 或者右键点击房间设置物品打开设置GUI

### 2. 找到Ban Pick选项
在房间设置GUI中，找到**Ban Pick系统**选项：
- **图标**: 屏障方块 (Material.BARRIER)
- **位置**: 在末影珍珠冷却选项旁边
- **状态**: 显示当前是否启用

### 3. 启用Ban Pick
- **点击**Ban Pick系统选项来切换开关
- **绿色**表示已启用
- **红色**表示已禁用

### 4. 保存设置
- 设置会自动保存
- 可以看到GUI中的状态更新

## 🎮 使用流程

### 启用Ban Pick后的游戏流程

1. **房主开始游戏**
   ```
   /manhunt start
   ```

2. **自动进入Ban Pick阶段**
   - 如果启用了Ban Pick，游戏不会立即开始
   - 而是进入Ban Pick阶段

3. **Ban Pick界面自动打开**
   - 所有玩家会自动打开Ban Pick GUI
   - 显示当前阶段和可选物品

4. **按流程进行选择**
   ```
   第1-4轮: 速通者Pick (选择4个物品)
   第5-6轮: 捕猎者Ban (禁用2个物品)
   第7-10轮: 捕猎者Pick (选择4个物品)
   第11-12轮: 速通者Ban (禁用2个物品)
   ```

5. **Ban Pick完成后开始游戏**
   - 显示最终Ban Pick结果
   - 自动传送玩家到游戏世界
   - 开始正常的Manhunt游戏

## ⚙️ 高级设置

### Ban Pick阶段时间设置
可以在房间设置中调整每个Ban Pick阶段的时间：
- **默认**: 30秒每阶段
- **设置方法**: 通过`banPickPhaseTimeSeconds`参数
- **范围**: 建议15-60秒

### 示例配置代码
```java
// 启用Ban Pick
room.getSettings().setBanPickEnabled(true);

// 设置每阶段时间为45秒
room.getSettings().setBanPickPhaseTimeSeconds(45);
```

## 🎯 GUI界面说明

### Ban Pick GUI功能
- **物品展示**: 按分类显示所有可Ban/Pick的物品
- **状态指示**: 
  - ❌ 红色 = 已被Ban
  - ✓ 蓝色 = 捕猎者已Pick
  - ✓ 绿色 = 速通者已Pick
  - 👆 黄色 = 你已投票
- **倒计时**: 显示当前阶段剩余时间
- **阶段信息**: 显示当前是谁的回合

### 操作方法
- **投票**: 点击物品进行投票
- **修改投票**: 可以在时间内修改选择
- **自动更新**: GUI会实时更新状态

## 🚫 Ban效果说明

### 被Ban物品的限制
- **禁止合成**: 无法通过合成台制作
- **允许使用**: 已有的物品可以正常使用
- **允许获得**: 可以通过其他途径获得
  - 战利品箱
  - 村民交易
  - 击杀掉落
  - 自然生成

### 被Pick物品的保护
- **确保可用**: 保证可以合成和使用
- **不会被Ban**: 即使对手想Ban也无法禁用

## 🎪 注意事项

### 游戏要求
- **最低玩家数**: 建议至少2人（1个捕猎者 + 1个速通者）
- **角色分配**: 确保有明确的捕猎者和速通者角色
- **时间充足**: 完整的Ban Pick需要6-12分钟

### 常见问题

**Q: Ban Pick界面没有打开？**
A: 检查是否在房间设置中启用了Ban Pick系统

**Q: 投票没有反应？**
A: 确认当前是你的阵营的回合，只有轮到的阵营才能投票

**Q: 时间太短/太长？**
A: 房主可以在设置中调整每阶段的时间

**Q: 游戏没有开始？**
A: Ban Pick完成后会自动开始，请等待所有阶段完成

### 故障排除

1. **重新加载设置**
   ```
   /manhunt reload
   ```

2. **检查权限**
   - 确保房主有修改设置的权限
   - 确保玩家有参与游戏的权限

3. **重启房间**
   - 如果遇到问题，可以重新创建房间
   - 重新设置Ban Pick选项

## 📊 推荐设置

### 竞技模式
- **Ban Pick**: 启用
- **阶段时间**: 30秒
- **其他设置**: 标准竞技设置

### 休闲模式
- **Ban Pick**: 可选
- **阶段时间**: 45秒
- **其他设置**: 宽松设置

### 快速游戏
- **Ban Pick**: 禁用
- **直接开始**: 跳过Ban Pick阶段

## 总结

Ban Pick系统为Manhunt游戏增加了重要的策略元素：

- ✅ **简单开启**: 在房间设置中一键启用
- ✅ **自动流程**: 启用后自动进入Ban Pick阶段
- ✅ **直观界面**: 清晰的GUI和状态显示
- ✅ **灵活配置**: 可调整时间和其他参数
- ✅ **无缝集成**: 与现有游戏流程完美结合

现在你可以在房间设置中轻松开启Ban Pick，为你的Manhunt游戏增添更多策略乐趣！
