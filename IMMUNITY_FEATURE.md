# 豁免功能实现文档

## 功能概述

为UltimateManhurt插件添加了开局豁免功能，让hunter在游戏开始后的一段时间内无法伤害speedrunner，给speedrunner一个准备时间。

## 实现的功能

### 1. 房间设置中的豁免配置
- **豁免开关**: 可以启用/禁用豁免功能
- **豁免时长**: 可以设置豁免持续时间（0-300秒）
- **默认设置**: 豁免开启，持续60秒

### 2. 游戏中的豁免机制
- **伤害阻止**: Hunter在豁免期内无法对Speedrunner造成伤害
- **提示消息**: 当Hunter尝试攻击时会收到提示消息，显示剩余豁免时间
- **视觉效果**: Speedrunner在豁免期内会有发光效果
- **音效提示**: 豁免开始和结束时播放相应音效

### 3. 豁免期倒计时
- **ActionBar显示**: 在最后10秒显示剩余时间
- **音效倒计时**: 最后5秒播放倒计时音效
- **粒子效果**: 显示快乐村民粒子效果
- **标题提醒**: 豁免结束时显示标题提醒所有玩家

## 修改的文件

### 1. RoomSettings.java
- 添加了 `immunityEnabled` 字段（默认true）
- 添加了 `immunityDurationSeconds` 字段（默认60秒）
- 添加了相应的getter/setter方法
- 在clone()和isValid()方法中添加了对新字段的处理

### 2. 新增 ImmunityManager.java
- 负责管理豁免状态
- 处理豁免期的开始和结束
- 管理视觉效果和音效
- 提供豁免状态查询方法

### 3. GameSession.java
- 集成了ImmunityManager
- 在游戏开始时启动豁免期
- 在游戏结束时清理豁免管理器
- 将broadcastMessage方法改为public以供ImmunityManager使用

### 4. ManhuntRules.java
- 修改了canDamagePlayer方法
- 添加了豁免期检查逻辑
- 使用ImmunityManager来判断是否在豁免期内

### 5. RoomSettingsGui.java
- 在游戏规则设置中添加了豁免保护开关
- 添加了豁免时长设置选项
- 实现了handleImmunityDuration方法处理时长设置

## 使用方法

### 房主设置豁免功能
1. 进入房间设置界面
2. 在游戏规则部分找到"豁免保护"选项
3. 点击切换开关来启用/禁用豁免功能
4. 点击"豁免时长"来设置豁免持续时间（0-300秒）

### 游戏中的豁免体验
1. 游戏开始后，Speedrunner会获得发光效果
2. Hunter尝试攻击Speedrunner时会收到提示消息
3. 在豁免期最后10秒，Speedrunner会在ActionBar看到倒计时
4. 豁免期结束时，所有玩家会收到标题提醒

## 技术细节

### 豁免检查逻辑
- 只有Hunter攻击Speedrunner时才进行豁免检查
- 豁免期基于游戏开始时间计算
- 只在游戏运行状态下生效

### 视觉效果
- 使用GLOWING药水效果让Speedrunner发光
- 使用HAPPY_VILLAGER粒子效果增强视觉反馈
- 使用ActionBar显示倒计时信息

### 音效设计
- 豁免开始: BLOCK_BEACON_ACTIVATE (音调1.5)
- 豁免结束: BLOCK_BEACON_DEACTIVATE (音调1.0)
- 倒计时: BLOCK_NOTE_BLOCK_PLING (音调2.0)

## 配置选项

### 默认设置
```java
private boolean immunityEnabled = true; // 启用豁免
private int immunityDurationSeconds = 60; // 60秒豁免时间
```

### 可配置范围
- 豁免时长: 0-300秒
- 0秒表示禁用豁免功能

## 兼容性

- 与现有的PVP设置兼容
- 与友军伤害设置兼容
- 不影响其他游戏机制
- 支持所有游戏模式

## 注意事项

1. 豁免功能只影响Hunter对Speedrunner的伤害
2. 不影响环境伤害（如摔落、怪物攻击等）
3. 豁免期间Speedrunner仍可以正常移动和使用物品
4. 房主可以随时在设置中调整豁免参数

## 测试建议

1. 创建房间并设置不同的豁免时长
2. 测试Hunter攻击Speedrunner时的提示消息
3. 验证豁免期结束后PVP正常工作
4. 检查视觉效果和音效是否正常播放
5. 测试豁免功能的开关是否生效
