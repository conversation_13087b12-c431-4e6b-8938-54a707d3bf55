package com.projectSource.ultimateManhurt.command;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.UUID;
import org.jetbrains.annotations.NotNull;

/**
 * 主命令处理器
 */
public class ManhuntCommand implements CommandExecutor {
    
    private final UltimateManhurt plugin;
    
    public ManhuntCommand(UltimateManhurt plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(@NotNull CommandSender sender, @NotNull Command command, @NotNull String label, String @NotNull [] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage("此命令只能由玩家执行！");
            return true;
        }

        if (args.length == 0) {
            showHelp(player);
            return true;
        }

        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "help":
                showHelp(player);
                break;
            case "testbossbar":
                handleTestBossBar(player);
                break;
            case "create":
                handleCreateRoom(player, args);
                break;
            case "join":
                handleJoinRoom(player, args);
                break;
            case "leave":
                handleLeaveRoom(player);
                break;
            case "list":
                handleListRooms(player);
                break;
            case "gui":
                handleOpenGui(player, args);
                break;
            case "invite":
                handleInvite(player, args);
                break;
            case "accept":
                handleAccept(player);
                break;
            case "decline":
                handleDecline(player);
                break;
            case "role":
                handleSetRole(player, args);
                break;
            case "start":
                handleStartGame(player);
                break;
            case "stats":
                handleStats(player, args);
                break;
            case "leaderboard":
            case "lb":
                handleLeaderboard(player);
                break;
            case "tablist":
                handleTablist(player, args);
                break;
            case "professiontest":
                handleProfessionTest(player, args);
                break;
            default:
                ComponentUtil.sendMessage(player, ComponentUtil.error("未知命令！使用 /manhunt help 查看帮助。"));
                break;
        }

        return true;
    }
    
    /**
     * 显示帮助信息
     */
    private void showHelp(Player player) {
        ComponentUtil.sendMessage(player, ComponentUtil.title("Ultimate Manhunt"));
        ComponentUtil.sendMessage(player, ComponentUtil.info("可用命令:"));
        ComponentUtil.sendMessage(player, ComponentUtil.parse("<gray>• <aqua>/manhunt help <gray>- 显示帮助"));
        ComponentUtil.sendMessage(player, ComponentUtil.parse("<gray>• <aqua>/manhunt create <房间名> [类型] <gray>- 创建房间"));
        ComponentUtil.sendMessage(player, ComponentUtil.parse("<gray>• <aqua>/manhunt join <房间名> <gray>- 加入房间"));
        ComponentUtil.sendMessage(player, ComponentUtil.parse("<gray>• <aqua>/manhunt leave <gray>- 离开房间"));
        ComponentUtil.sendMessage(player, ComponentUtil.parse("<gray>• <aqua>/manhunt list <gray>- 查看房间列表"));
        ComponentUtil.sendMessage(player, ComponentUtil.parse("<gray>• <aqua>/manhunt gui [force] <gray>- 打开GUI界面"));
        ComponentUtil.sendMessage(player, ComponentUtil.parse("<gray>• <aqua>/manhunt invite <玩家名> [房间名] <gray>- 邀请玩家加入私人房间"));
        ComponentUtil.sendMessage(player, ComponentUtil.parse("<gray>• <aqua>/manhunt accept <gray>- 接受房间邀请"));
        ComponentUtil.sendMessage(player, ComponentUtil.parse("<gray>• <aqua>/manhunt decline <gray>- 拒绝房间邀请"));
        ComponentUtil.sendMessage(player, ComponentUtil.parse("<gray>• <aqua>/manhunt role <玩家名> <角色> <gray>- 设置玩家角色（房主专用）"));
        ComponentUtil.sendMessage(player, ComponentUtil.parse("<gray>• <aqua>/manhunt start <gray>- 开始游戏（房主专用）"));
        ComponentUtil.sendMessage(player, ComponentUtil.parse("<gray>• <aqua>/manhunt stats [玩家] <gray>- 查看统计"));
        ComponentUtil.sendMessage(player, ComponentUtil.parse("<gray>• <aqua>/manhunt leaderboard <gray>- 查看排行榜"));
        ComponentUtil.sendMessage(player, ComponentUtil.parse("<gray>• <aqua>/manhunt tablist <子命令> <gray>- Tablist管理"));
        ComponentUtil.sendMessage(player, ComponentUtil.info("可用角色: speedrunner(速通者), hunter(捕猎者), spectator(观察者)"));
        ComponentUtil.sendMessage(player, ComponentUtil.info("可用房间类型: public(公开), private(私人)"));
        ComponentUtil.sendMessage(player, ComponentUtil.info("Tablist子命令: reload(重载配置), toggle(切换显示), refresh(刷新)"));
        ComponentUtil.sendMessage(player, ComponentUtil.separator());
    }

    /**
     * 处理创建房间
     */
    private void handleCreateRoom(Player player, String[] args) {
        if (args.length < 2) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("用法: /manhunt create <房间名> [类型]"));
            ComponentUtil.sendMessage(player, ComponentUtil.info("可用类型: public(公开), private(私人)"));
            return;
        }

        // 检查玩家是否已在房间中
        if (plugin.getRoomManager().isPlayerInRoom(player.getUniqueId())) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("你已经在一个房间中了！"));
            return;
        }

        // 解析房间类型
        com.projectSource.ultimateManhurt.room.RoomType roomType = com.projectSource.ultimateManhurt.room.RoomType.PUBLIC; // 默认公开
        String roomName;

        if (args.length >= 3) {
            // 有房间类型参数
            String typeStr = args[args.length - 1].toLowerCase();
            roomName = String.join(" ", java.util.Arrays.copyOfRange(args, 1, args.length - 1));

            switch (typeStr) {
                case "public":
                case "pub":
                    roomType = com.projectSource.ultimateManhurt.room.RoomType.PUBLIC;
                    break;
                case "private":
                case "priv":
                    roomType = com.projectSource.ultimateManhurt.room.RoomType.PRIVATE;
                    break;
                default:
                    // 不是有效的房间类型，将其作为房间名的一部分
                    roomName = String.join(" ", java.util.Arrays.copyOfRange(args, 1, args.length));
                    break;
            }
        } else {
            // 没有房间类型参数，使用默认类型
            roomName = String.join(" ", java.util.Arrays.copyOfRange(args, 1, args.length));
        }

        // 创建房间
        try {
            com.projectSource.ultimateManhurt.room.Room room = plugin.getRoomManager()
                    .createRoom(roomName, player.getUniqueId(), roomType);

            ComponentUtil.sendMessage(player, ComponentUtil.success(
                "成功创建" + roomType.getDisplayName() + ": " + roomName));
        } catch (Exception e) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("创建房间失败: " + e.getMessage()));
        }
    }

    /**
     * 处理加入房间
     */
    private void handleJoinRoom(Player player, String[] args) {
        if (args.length < 2) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("用法: /manhunt join <房间名>"));
            return;
        }

        String roomName = String.join(" ", java.util.Arrays.copyOfRange(args, 1, args.length));

        // 查找房间
        com.projectSource.ultimateManhurt.room.Room room = plugin.getRoomManager().getAllRooms().stream()
                .filter(r -> r.getName().equalsIgnoreCase(roomName))
                .findFirst()
                .orElse(null);

        if (room == null) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("找不到房间: " + roomName));
            return;
        }

        // 尝试加入房间
        if (plugin.getRoomManager().joinRoom(player.getUniqueId(), room.getId(),
                com.projectSource.ultimateManhurt.game.PlayerRole.SPECTATOR)) {
            ComponentUtil.sendMessage(player, ComponentUtil.success("成功加入房间: " + roomName));
        } else {
            ComponentUtil.sendMessage(player, ComponentUtil.error("无法加入房间: " + roomName));
        }
    }

    /**
     * 处理离开房间
     */
    private void handleLeaveRoom(Player player) {
        if (!plugin.getRoomManager().isPlayerInRoom(player.getUniqueId())) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("你当前不在任何房间中！"));
            return;
        }

        // 获取玩家所在的房间
        com.projectSource.ultimateManhurt.room.Room room = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
        if (room == null) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除！"));
            return;
        }

        // 检查是否可以离开房间
        if (!room.canLeave(player.getUniqueId())) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("游戏进行中无法离开房间！请等待游戏结束。"));
            return;
        }

        if (plugin.getRoomManager().leaveRoom(player.getUniqueId())) {
            ComponentUtil.sendMessage(player, ComponentUtil.success("成功离开房间"));
        } else {
            ComponentUtil.sendMessage(player, ComponentUtil.error("离开房间失败"));
        }
    }

    /**
     * 处理房间列表
     */
    private void handleListRooms(Player player) {
        var rooms = plugin.getRoomManager().getPublicRooms();

        if (rooms.isEmpty()) {
            ComponentUtil.sendMessage(player, ComponentUtil.info("当前没有可用的房间"));
            return;
        }

        ComponentUtil.sendMessage(player, ComponentUtil.info("可用房间列表:"));
        for (com.projectSource.ultimateManhurt.room.Room room : rooms) {
            ComponentUtil.sendMessage(player, ComponentUtil.parse(
                "<gray>• <aqua>" + room.getName() + " <gray>(" +
                room.getPlayerCount() + "/" + room.getSettings().getMaxPlayers() + ") - " +
                room.getGameState().getDisplayName()
            ));
        }
    }

    /**
     * 处理打开GUI
     */
    private void handleOpenGui(Player player, String[] args) {
        boolean forceOpen = args.length > 1 && "force".equalsIgnoreCase(args[1]);

        if (forceOpen) {
            plugin.getGuiManager().forceOpenRoomListGui(player);
            ComponentUtil.sendMessage(player, ComponentUtil.warning("已强制打开房间列表GUI（忽略载具状态）"));
        } else {
            plugin.getGuiManager().openRoomListGui(player);
            ComponentUtil.sendMessage(player, ComponentUtil.info("已打开房间列表GUI"));
        }
    }

    /**
     * 处理设置角色
     */
    private void handleSetRole(Player player, String[] args) {
        if (args.length < 3) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("用法: /manhunt role <玩家名> <角色>"));
            ComponentUtil.sendMessage(player, ComponentUtil.info("角色: speedrunner(速通者), hunter(捕猎者), spectator(观察者)"));
            return;
        }

        // 检查玩家是否在房间中
        com.projectSource.ultimateManhurt.room.Room room = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
        if (room == null) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("你当前不在任何房间中！"));
            return;
        }

        // 检查是否是房主
        if (!room.isOwner(player.getUniqueId())) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("只有房主可以设置玩家角色！"));
            return;
        }

        String targetPlayerName = args[1];
        String roleName = args[2].toLowerCase();

        // 查找目标玩家
        org.bukkit.entity.Player targetPlayer = plugin.getServer().getPlayer(targetPlayerName);
        if (targetPlayer == null) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("找不到玩家: " + targetPlayerName));
            return;
        }

        // 检查目标玩家是否在房间中
        if (!room.containsPlayer(targetPlayer.getUniqueId())) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("玩家 " + targetPlayerName + " 不在房间中！"));
            return;
        }

        // 解析角色
        com.projectSource.ultimateManhurt.game.PlayerRole role;
        switch (roleName) {
            case "speedrunner":
            case "速通者":
                role = com.projectSource.ultimateManhurt.game.PlayerRole.SPEEDRUNNER;
                break;
            case "hunter":
            case "捕猎者":
                role = com.projectSource.ultimateManhurt.game.PlayerRole.HUNTER;
                break;
            case "spectator":
            case "观察者":
                role = com.projectSource.ultimateManhurt.game.PlayerRole.SPECTATOR;
                break;
            default:
                ComponentUtil.sendMessage(player, ComponentUtil.error("未知角色: " + roleName));
                ComponentUtil.sendMessage(player, ComponentUtil.info("可用角色: speedrunner, hunter, spectator"));
                return;
        }

        // 设置角色
        if (room.setPlayerRole(targetPlayer.getUniqueId(), role)) {
            ComponentUtil.sendMessage(player, ComponentUtil.success("已将 " + targetPlayerName + " 的角色设置为 " + role.getDisplayName()));
            ComponentUtil.sendMessage(targetPlayer, ComponentUtil.info("你的角色已被设置为 " + role.getDisplayName()));
        } else {
            ComponentUtil.sendMessage(player, ComponentUtil.error("设置角色失败！可能是角色数量限制或其他原因。"));
        }
    }

    /**
     * 处理开始游戏
     */
    private void handleStartGame(Player player) {
        // 检查玩家是否在房间中
        com.projectSource.ultimateManhurt.room.Room room = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
        if (room == null) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("你当前不在任何房间中！"));
            return;
        }

        // 检查是否是房主
        if (!room.isOwner(player.getUniqueId())) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("只有房主可以开始游戏！"));
            return;
        }

        // 检查是否可以开始游戏
        if (!room.canStartGame()) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("无法开始游戏！"));

            // 提供详细原因
            int speedrunners = room.getPlayerCount(com.projectSource.ultimateManhurt.game.PlayerRole.SPEEDRUNNER);
            int hunters = room.getPlayerCount(com.projectSource.ultimateManhurt.game.PlayerRole.HUNTER);

            if (speedrunners == 0) {
                ComponentUtil.sendMessage(player, ComponentUtil.info("需要至少1个速通者"));
            }
            if (hunters == 0) {
                ComponentUtil.sendMessage(player, ComponentUtil.info("需要至少1个捕猎者"));
            }

            ComponentUtil.sendMessage(player, ComponentUtil.info("使用 /manhunt role <玩家名> <角色> 来设置玩家角色"));
            return;
        }

        // 开始游戏
        ComponentUtil.sendMessage(player, ComponentUtil.info("正在创建游戏世界，这可能需要几分钟时间..."));
        ComponentUtil.sendMessage(player, ComponentUtil.warning("请耐心等待，不要关闭游戏或重复执行命令！"));

        plugin.getGameManager().createGameSession(room)
            .thenAccept(gameSession -> {
                // 在主线程中执行
                org.bukkit.Bukkit.getScheduler().runTask(plugin, () -> {
                    try {
                        ComponentUtil.sendMessage(player, ComponentUtil.success("游戏世界创建完成！正在启动游戏..."));

                        boolean started = gameSession.startGame();
                        if (started) {
                            ComponentUtil.sendMessage(player, ComponentUtil.success("游戏开始！祝你好运！"));
                        } else {
                            ComponentUtil.sendMessage(player, ComponentUtil.error("开始游戏失败！"));
                        }
                    } catch (Exception e) {
                        ComponentUtil.sendMessage(player, ComponentUtil.error("启动游戏时发生错误：" + e.getMessage()));
                        plugin.getLogger().severe("启动游戏失败: " + e.getMessage());
                    }
                });
            })
            .exceptionally(throwable -> {
                // 在主线程中处理异常
                org.bukkit.Bukkit.getScheduler().runTask(plugin, () -> {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("创建游戏世界失败：" + throwable.getMessage()));
                    ComponentUtil.sendMessage(player, ComponentUtil.info("请稍后再试，或联系管理员"));
                    plugin.getLogger().severe("创建游戏会话失败: " + throwable.getMessage());
                    throwable.printStackTrace();
                });
                return null;
            });
    }

    /**
     * 处理统计查看
     */
    private void handleStats(Player player, String[] args) {
        UUID targetPlayerId;

        if (args.length > 1) {
            // 查看指定玩家的统计
            Player targetPlayer = plugin.getServer().getPlayer(args[1]);
            if (targetPlayer == null) {
                ComponentUtil.sendMessage(player, ComponentUtil.error("玩家 " + args[1] + " 不在线或不存在！"));
                return;
            }
            targetPlayerId = targetPlayer.getUniqueId();
        } else {
            // 查看自己的统计
            targetPlayerId = player.getUniqueId();
        }

        // 通过GuiManager打开玩家统计GUI
        plugin.getGuiManager().openPlayerStatsGui(player, targetPlayerId);
    }

    /**
     * 处理排行榜查看
     */
    private void handleLeaderboard(Player player) {
        // 通过GuiManager打开排行榜GUI
        plugin.getGuiManager().openLeaderboardGui(player);
    }

    /**
     * 处理邀请命令
     */
    private void handleInvite(Player player, String[] args) {
        if (args.length < 2) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("用法: /manhunt invite <玩家名> [房间名]"));
            return;
        }

        String targetName = args[1];
        String roomName = null;

        // 如果指定了房间名，使用指定的房间；否则使用玩家当前所在的房间
        if (args.length >= 3) {
            roomName = String.join(" ", java.util.Arrays.copyOfRange(args, 2, args.length));
        } else {
            // 获取玩家当前所在的房间
            com.projectSource.ultimateManhurt.room.Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
            if (currentRoom == null) {
                ComponentUtil.sendMessage(player, ComponentUtil.error("你不在任何房间中！请指定房间名或先加入一个房间。"));
                return;
            }
            roomName = currentRoom.getName();
        }

        plugin.getRoomManager().sendInvite(player, targetName, roomName);
    }

    /**
     * 处理接受邀请命令
     */
    private void handleAccept(Player player) {
        plugin.getRoomManager().acceptInvite(player);
    }

    /**
     * 处理拒绝邀请命令
     */
    private void handleDecline(Player player) {
        plugin.getRoomManager().declineInvite(player);
    }

    /**
     * 处理Tablist命令
     */
    private void handleTablist(Player player, String[] args) {
        if (args.length < 2) {
            ComponentUtil.sendMessage(player, ComponentUtil.info("Tablist命令用法:"));
            ComponentUtil.sendMessage(player, ComponentUtil.info("  /manhunt tablist reload - 重新加载Tablist配置"));
            ComponentUtil.sendMessage(player, ComponentUtil.info("  /manhunt tablist toggle - 切换Tablist显示"));
            ComponentUtil.sendMessage(player, ComponentUtil.info("  /manhunt tablist refresh - 刷新Tablist"));
            return;
        }

        String subCommand = args[1].toLowerCase();
        switch (subCommand) {
            case "reload":
                if (!player.hasPermission("ultimatemanhurt.admin")) {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("你没有权限执行此命令！"));
                    return;
                }

                // 重新加载配置
                plugin.reloadConfig();
                ComponentUtil.sendMessage(player, ComponentUtil.success("Tablist配置已重新加载！"));
                break;

            case "toggle":
                // 切换玩家的Tablist显示
                boolean currentEnabled = plugin.getConfigManager().getBoolean("tablist.player-display.enabled", true);
                // 这里可以实现个人设置，暂时使用全局设置
                if (currentEnabled) {
                    plugin.getTablistManager().removeTablist(player);
                    ComponentUtil.sendMessage(player, ComponentUtil.info("已隐藏Tablist显示"));
                } else {
                    // 重新创建适当的Tablist
                    createAppropriateTablist(player);
                    ComponentUtil.sendMessage(player, ComponentUtil.info("已显示Tablist"));
                }
                break;

            case "refresh":
                // 刷新玩家的Tablist
                createAppropriateTablist(player);
                ComponentUtil.sendMessage(player, ComponentUtil.success("Tablist已刷新！"));
                break;

            default:
                ComponentUtil.sendMessage(player, ComponentUtil.error("未知的Tablist子命令！"));
                break;
        }
    }

    /**
     * 为玩家创建合适的Tablist
     */
    private void createAppropriateTablist(Player player) {
        // 检查玩家是否在游戏中
        com.projectSource.ultimateManhurt.game.GameSession gameSession =
            plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
        if (gameSession != null) {
            plugin.getTablistManager().createGameTablist(player, gameSession);
            return;
        }

        // 检查玩家是否在房间中
        com.projectSource.ultimateManhurt.room.Room room =
            plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
        if (room != null) {
            plugin.getTablistManager().createRoomTablist(player, room);
            return;
        }

        // 默认创建大厅Tablist
        plugin.getTablistManager().createDefaultTablist(player);
    }

    /**
     * 处理职业测试命令
     */
    private void handleProfessionTest(Player player, String[] args) {
        // 检查权限
        if (!player.hasPermission("manhunt.admin")) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("你没有权限使用此命令"));
            return;
        }

        // 创建新的参数数组（去掉第一个参数）
        String[] newArgs = new String[args.length - 1];
        System.arraycopy(args, 1, newArgs, 0, newArgs.length);

        // 调用职业测试命令处理器
        com.projectSource.ultimateManhurt.profession.command.ProfessionTestCommand testCommand =
            new com.projectSource.ultimateManhurt.profession.command.ProfessionTestCommand(plugin);
        testCommand.onCommand(player, null, "professiontest", newArgs);
    }

    /**
     * 处理测试Boss Bar命令
     */
    private void handleTestBossBar(Player player) {
        if (!player.hasPermission("ultimatemanhurt.admin")) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("你没有权限使用此命令"));
            return;
        }

        // 强制创建测试Boss Bar
        plugin.getProfessionManager().getSkillBossBarManager().forceCreateBossBar(player);
        ComponentUtil.sendMessage(player, ComponentUtil.success("已为你创建测试Boss Bar"));
    }
}
