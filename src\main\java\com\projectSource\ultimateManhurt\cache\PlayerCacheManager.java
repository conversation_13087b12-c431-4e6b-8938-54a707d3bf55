package com.projectSource.ultimateManhurt.cache;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.SkullMeta;


import java.io.File;
import java.io.IOException;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 玩家缓存管理器
 * 缓存玩家的名字和头颅信息，用于离线玩家的显示
 */
public class PlayerCacheManager {
    
    private final UltimateManhurt plugin;
    private final File cacheFile;
    private final Map<UUID, PlayerCacheData> playerCache = new ConcurrentHashMap<>();
    
    public PlayerCacheManager(UltimateManhurt plugin) {
        this.plugin = plugin;
        this.cacheFile = new File(plugin.getDataFolder(), "player_cache.yml");
        initialize();
    }
    
    /**
     * 初始化缓存管理器
     */
    private void initialize() {
        loadCache();
        plugin.getLogger().info("玩家缓存管理器已初始化，缓存了 " + playerCache.size() + " 个玩家信息");
    }
    
    /**
     * 缓存玩家信息
     */
    public void cachePlayer(Player player) {
        UUID playerId = player.getUniqueId();
        String playerName = player.getName();
        
        // 创建或更新缓存数据
        PlayerCacheData cacheData = playerCache.computeIfAbsent(playerId, k -> new PlayerCacheData());
        cacheData.setName(playerName);
        cacheData.setLastSeen(System.currentTimeMillis());
        
        // 异步保存缓存
        Bukkit.getScheduler().runTaskAsynchronously(plugin, this::saveCache);
        
        plugin.getLogger().fine("缓存玩家信息: " + playerName + " (" + playerId + ")");
    }
    
    /**
     * 获取玩家名字
     */
    public String getPlayerName(UUID playerId) {
        // 优先从在线玩家获取
        Player onlinePlayer = Bukkit.getPlayer(playerId);
        if (onlinePlayer != null) {
            // 顺便更新缓存
            cachePlayer(onlinePlayer);
            return onlinePlayer.getName();
        }
        
        // 从缓存获取
        PlayerCacheData cacheData = playerCache.get(playerId);
        if (cacheData != null && cacheData.getName() != null) {
            return cacheData.getName();
        }
        
        // 尝试从OfflinePlayer获取（可能会阻塞）
        try {
            OfflinePlayer offlinePlayer = Bukkit.getOfflinePlayer(playerId);
            if (offlinePlayer.hasPlayedBefore() && offlinePlayer.getName() != null) {
                // 更新缓存
                PlayerCacheData newCacheData = playerCache.computeIfAbsent(playerId, k -> new PlayerCacheData());
                newCacheData.setName(offlinePlayer.getName());
                newCacheData.setLastSeen(System.currentTimeMillis());
                
                return offlinePlayer.getName();
            }
        } catch (Exception e) {
            plugin.getLogger().warning("获取离线玩家名字失败: " + playerId + " - " + e.getMessage());
        }
        
        // 最后返回未知玩家
        return "未知玩家";
    }
    
    /**
     * 获取玩家头颅
     */
    public ItemStack getPlayerHead(UUID playerId) {
        ItemStack head = new ItemStack(Material.PLAYER_HEAD);
        SkullMeta meta = (SkullMeta) head.getItemMeta();

        if (meta == null) {
            plugin.getLogger().warning("无法获取头颅ItemMeta: " + playerId);
            return head;
        }

        // 优先从在线玩家获取
        Player onlinePlayer = Bukkit.getPlayer(playerId);
        if (onlinePlayer != null) {
            meta.setOwningPlayer(onlinePlayer);
            head.setItemMeta(meta);
            plugin.getLogger().info("设置在线玩家头颅: " + onlinePlayer.getName() + " (" + playerId + ")");

            // 验证头颅是否正确设置
            SkullMeta verifyMeta = (SkullMeta) head.getItemMeta();
            if (verifyMeta != null && verifyMeta.getOwningPlayer() != null) {
                plugin.getLogger().info("头颅拥有者验证成功: " + verifyMeta.getOwningPlayer().getName());
            } else {
                plugin.getLogger().warning("头颅拥有者验证失败！");
            }
            return head;
        }

        // 尝试从OfflinePlayer获取
        try {
            OfflinePlayer offlinePlayer = Bukkit.getOfflinePlayer(playerId);
            // 检查玩家是否存在且有名字
            if (offlinePlayer.getName() != null && !offlinePlayer.getName().isEmpty()) {
                meta.setOwningPlayer(offlinePlayer);
                head.setItemMeta(meta);
                plugin.getLogger().info("设置离线玩家头颅: " + offlinePlayer.getName() + " (" + playerId + ")");

                // 验证头颅是否正确设置
                SkullMeta verifyMeta = (SkullMeta) head.getItemMeta();
                if (verifyMeta != null && verifyMeta.getOwningPlayer() != null) {
                    plugin.getLogger().info("离线头颅拥有者验证成功: " + verifyMeta.getOwningPlayer().getName());
                } else {
                    plugin.getLogger().warning("离线头颅拥有者验证失败！");
                }
                return head;
            } else {
                plugin.getLogger().warning("离线玩家没有有效名字: " + playerId);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("获取离线玩家头颅失败: " + playerId + " - " + e.getMessage());
        }

        // 如果有缓存的名字，尝试通过名字设置头颅
        PlayerCacheData cacheData = playerCache.get(playerId);
        if (cacheData != null && cacheData.getName() != null) {
            try {
                // 通过玩家名字获取OfflinePlayer
                OfflinePlayer offlinePlayerByName = Bukkit.getOfflinePlayer(cacheData.getName());
                if (offlinePlayerByName != null) {
                    meta.setOwningPlayer(offlinePlayerByName);
                    head.setItemMeta(meta);
                    plugin.getLogger().info("通过缓存名字设置头颅: " + cacheData.getName());

                    // 验证头颅是否正确设置
                    SkullMeta verifyMeta = (SkullMeta) head.getItemMeta();
                    if (verifyMeta != null && verifyMeta.getOwningPlayer() != null) {
                        plugin.getLogger().info("缓存名字头颅拥有者验证成功: " + verifyMeta.getOwningPlayer().getName());
                    } else {
                        plugin.getLogger().warning("缓存名字头颅拥有者验证失败！");
                    }
                    return head;
                }
            } catch (Exception e) {
                plugin.getLogger().warning("通过缓存名字设置头颅失败: " + cacheData.getName() + " - " + e.getMessage());
            }
        }

        // 返回默认头颅
        head.setItemMeta(meta);
        plugin.getLogger().warning("返回默认头颅，无法获取玩家皮肤: " + playerId);
        return head;
    }

    /**
     * 创建带有自定义名称和描述的玩家头颅
     */
    public ItemStack createPlayerHeadWithMeta(UUID playerId, String displayName, String... lore) {
        // 获取基础头颅（已包含正确的拥有者信息）
        ItemStack head = getPlayerHead(playerId);

        // 设置自定义显示名称和描述
        var meta = head.getItemMeta();
        if (meta != null) {
            if (displayName != null && !displayName.isEmpty()) {
                // 使用 ComponentUtil 解析 MiniMessage 格式
                meta.displayName(ComponentUtil.parse(displayName));
            }

            if (lore.length > 0) {
                java.util.List<net.kyori.adventure.text.Component> loreComponents = new java.util.ArrayList<>();
                for (String line : lore) {
                    // 使用 ComponentUtil 解析每行描述
                    loreComponents.add(ComponentUtil.parse(line));
                }
                meta.lore(loreComponents);
            }

            head.setItemMeta(meta);

            // 验证头颅拥有者是否保持
            if (meta instanceof SkullMeta) {
                SkullMeta skullMeta = (SkullMeta) meta;
                if (skullMeta.getOwningPlayer() != null) {
                    plugin.getLogger().info("createPlayerHeadWithMeta 头颅拥有者: " + skullMeta.getOwningPlayer().getName() + " for " + playerId);
                } else {
                    plugin.getLogger().warning("createPlayerHeadWithMeta 头颅没有拥有者！playerId: " + playerId);
                }
            }
        }

        return head;
    }

    /**
     * 获取玩家显示名（带缓存时间信息）
     */
    public String getPlayerDisplayName(UUID playerId) {
        String name = getPlayerName(playerId);
        
        // 如果是离线玩家，添加最后在线时间信息
        Player onlinePlayer = Bukkit.getPlayer(playerId);
        if (onlinePlayer == null) {
            PlayerCacheData cacheData = playerCache.get(playerId);
            if (cacheData != null && cacheData.getLastSeen() > 0) {
                long timeDiff = System.currentTimeMillis() - cacheData.getLastSeen();
                String timeAgo = formatTimeAgo(timeDiff);
                return name + " <gray>(" + timeAgo + "前)</gray>";
            }
        }
        
        return name;
    }
    
    /**
     * 格式化时间差
     */
    private String formatTimeAgo(long timeDiff) {
        long seconds = timeDiff / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return days + "天";
        } else if (hours > 0) {
            return hours + "小时";
        } else if (minutes > 0) {
            return minutes + "分钟";
        } else {
            return seconds + "秒";
        }
    }
    
    /**
     * 检查玩家是否在线
     */
    public boolean isPlayerOnline(UUID playerId) {
        return Bukkit.getPlayer(playerId) != null;
    }
    
    /**
     * 加载缓存
     */
    private void loadCache() {
        if (!cacheFile.exists()) {
            return;
        }
        
        try {
            FileConfiguration config = YamlConfiguration.loadConfiguration(cacheFile);
            
            for (String key : config.getKeys(false)) {
                try {
                    UUID playerId = UUID.fromString(key);
                    String name = config.getString(key + ".name");
                    long lastSeen = config.getLong(key + ".lastSeen", 0);
                    
                    if (name != null) {
                        PlayerCacheData cacheData = new PlayerCacheData();
                        cacheData.setName(name);
                        cacheData.setLastSeen(lastSeen);
                        playerCache.put(playerId, cacheData);
                    }
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("无效的UUID在缓存中: " + key);
                }
            }
            
            plugin.getLogger().info("成功加载 " + playerCache.size() + " 个玩家缓存");
        } catch (Exception e) {
            plugin.getLogger().severe("加载玩家缓存失败: " + e.getMessage());
        }
    }
    
    /**
     * 保存缓存
     */
    private void saveCache() {
        try {
            FileConfiguration config = new YamlConfiguration();
            
            for (Map.Entry<UUID, PlayerCacheData> entry : playerCache.entrySet()) {
                String key = entry.getKey().toString();
                PlayerCacheData data = entry.getValue();
                
                config.set(key + ".name", data.getName());
                config.set(key + ".lastSeen", data.getLastSeen());
            }
            
            config.save(cacheFile);
            plugin.getLogger().fine("成功保存 " + playerCache.size() + " 个玩家缓存");
        } catch (IOException e) {
            plugin.getLogger().severe("保存玩家缓存失败: " + e.getMessage());
        }
    }
    
    /**
     * 清理过期缓存（超过30天的缓存）
     */
    public void cleanupExpiredCache() {
        long thirtyDaysAgo = System.currentTimeMillis() - (30L * 24 * 60 * 60 * 1000);
        
        playerCache.entrySet().removeIf(entry -> {
            PlayerCacheData data = entry.getValue();
            return data.getLastSeen() < thirtyDaysAgo;
        });
        
        // 异步保存
        Bukkit.getScheduler().runTaskAsynchronously(plugin, this::saveCache);
        
        plugin.getLogger().info("清理过期玩家缓存完成，当前缓存数量: " + playerCache.size());
    }
    
    /**
     * 关闭缓存管理器
     */
    public void shutdown() {
        saveCache();
        playerCache.clear();
        plugin.getLogger().info("玩家缓存管理器已关闭");
    }

    /**
     * 测试头颅获取 - 用于调试
     */
    public void testPlayerHead(Player requester, UUID targetPlayerId) {
        plugin.getLogger().info("=== 头颅测试开始 ===");
        plugin.getLogger().info("目标玩家ID: " + targetPlayerId);

        // 测试基本信息获取
        String playerName = getPlayerName(targetPlayerId);
        plugin.getLogger().info("获取到的玩家名字: " + playerName);

        // 测试头颅获取
        ItemStack head = getPlayerHead(targetPlayerId);
        org.bukkit.inventory.meta.SkullMeta meta = (org.bukkit.inventory.meta.SkullMeta) head.getItemMeta();

        if (meta != null && meta.getOwningPlayer() != null) {
            plugin.getLogger().info("头颅拥有者: " + meta.getOwningPlayer().getName());
            plugin.getLogger().info("头颅拥有者UUID: " + meta.getOwningPlayer().getUniqueId());
        } else {
            plugin.getLogger().warning("头颅没有拥有者！");
        }

        // 给请求者一个测试头颅
        meta.displayName(ComponentUtil.parse("<gold>测试头颅: " + playerName));
        head.setItemMeta(meta);
        requester.getInventory().addItem(head);

        ComponentUtil.sendMessage(requester, ComponentUtil.info("已给你一个测试头颅，检查是否显示正确的皮肤"));
        plugin.getLogger().info("=== 头颅测试结束 ===");
    }
    
    /**
     * 玩家缓存数据类
     */
    private static class PlayerCacheData {
        private String name;
        private long lastSeen;
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public long getLastSeen() {
            return lastSeen;
        }
        
        public void setLastSeen(long lastSeen) {
            this.lastSeen = lastSeen;
        }
    }
}
