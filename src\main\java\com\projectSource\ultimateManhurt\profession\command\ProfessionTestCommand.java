package com.projectSource.ultimateManhurt.profession.command;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.profession.Profession;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

/**
 * 职业系统测试命令
 */
public class ProfessionTestCommand implements CommandExecutor {
    
    private final UltimateManhurt plugin;
    
    public ProfessionTestCommand(UltimateManhurt plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage("此命令只能由玩家执行");
            return true;
        }
        
        if (args.length == 0) {
            showHelp(player);
            return true;
        }
        
        switch (args[0].toLowerCase()) {
            case "set":
                if (args.length < 2) {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("用法: /professiontest set <职业名>"));
                    return true;
                }
                handleSetProfession(player, args[1]);
                break;
                
            case "get":
                handleGetProfession(player);
                break;
                
            case "clear":
                handleClearProfession(player);
                break;
                
            case "list":
                handleListProfessions(player);
                break;
                
            case "skill":
                handleUseSkill(player);
                break;
                
            default:
                showHelp(player);
                break;
        }
        
        return true;
    }
    
    private void showHelp(Player player) {
        ComponentUtil.sendMessage(player, ComponentUtil.info("=== 职业系统测试命令 ==="));
        ComponentUtil.sendMessage(player, ComponentUtil.info("/professiontest set <职业名> - 设置职业"));
        ComponentUtil.sendMessage(player, ComponentUtil.info("/professiontest get - 查看当前职业"));
        ComponentUtil.sendMessage(player, ComponentUtil.info("/professiontest clear - 清除职业"));
        ComponentUtil.sendMessage(player, ComponentUtil.info("/professiontest list - 列出所有职业"));
        ComponentUtil.sendMessage(player, ComponentUtil.info("/professiontest skill - 使用主动技能"));
    }
    
    private void handleSetProfession(Player player, String professionName) {
        Profession profession = Profession.getByName(professionName);
        if (profession == null) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("未找到职业: " + professionName));
            return;
        }
        
        boolean success = plugin.getProfessionManager().setProfession(player.getUniqueId(), profession);
        if (success) {
            ComponentUtil.sendMessage(player, ComponentUtil.success("成功设置职业: " + profession.getDisplayName()));
        } else {
            ComponentUtil.sendMessage(player, ComponentUtil.error("设置职业失败"));
        }
    }
    
    private void handleGetProfession(Player player) {
        Profession profession = plugin.getProfessionManager().getPlayerProfession(player.getUniqueId());
        if (profession == null) {
            ComponentUtil.sendMessage(player, ComponentUtil.info("你当前没有职业"));
        } else {
            ComponentUtil.sendMessage(player, ComponentUtil.info("当前职业: " + profession.getDisplayName()));
            ComponentUtil.sendMessage(player, ComponentUtil.info("描述: " + profession.getDescription()));
            ComponentUtil.sendMessage(player, ComponentUtil.info("被动技能: " + profession.getPassiveSkillName() + " - " + profession.getPassiveSkillDescription()));
            ComponentUtil.sendMessage(player, ComponentUtil.info("主动技能: " + profession.getActiveSkillName() + " - " + profession.getActiveSkillDescription()));
        }
    }
    
    private void handleClearProfession(Player player) {
        plugin.getProfessionManager().removePlayerProfession(player.getUniqueId());
        ComponentUtil.sendMessage(player, ComponentUtil.success("已清除职业"));
    }
    
    private void handleListProfessions(Player player) {
        ComponentUtil.sendMessage(player, ComponentUtil.info("=== 所有职业 ==="));
        for (Profession profession : Profession.values()) {
            ComponentUtil.sendMessage(player, profession.getDetailComponent());
        }
    }
    
    private void handleUseSkill(Player player) {
        Profession profession = plugin.getProfessionManager().getPlayerProfession(player.getUniqueId());
        if (profession == null) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("你没有职业，无法使用技能"));
            return;
        }
        
        boolean success = plugin.getProfessionManager().useActiveSkill(player.getUniqueId());
        if (!success) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("技能使用失败"));
        }
    }
}
