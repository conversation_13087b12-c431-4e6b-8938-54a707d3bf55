package com.projectSource.ultimateManhurt.profession.util;

import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 伪随机分布系统
 * 确保概率事件的公平性，避免连续的好运或厄运
 */
public class PseudoRandom {
    
    // 玩家伪随机状态映射 <玩家UUID, <事件名称, 当前概率>>
    private final ConcurrentHashMap<UUID, ConcurrentHashMap<String, Double>> playerStates;
    
    public PseudoRandom() {
        this.playerStates = new ConcurrentHashMap<>();
    }
    
    /**
     * 检查事件是否触发（使用伪随机分布）
     * 
     * @param playerId 玩家UUID
     * @param eventName 事件名称
     * @param baseProbability 基础概率（0.0-1.0）
     * @return 是否触发
     */
    public boolean checkTrigger(UUID playerId, String eventName, double baseProbability) {
        if (baseProbability <= 0) return false;
        if (baseProbability >= 1) return true;
        
        // 获取或初始化玩家状态
        ConcurrentHashMap<String, Double> playerEventStates = 
            playerStates.computeIfAbsent(playerId, k -> new ConcurrentHashMap<>());
        
        // 获取当前概率，如果不存在则使用基础概率
        double currentProbability = playerEventStates.getOrDefault(eventName, baseProbability);
        
        // 生成随机数
        boolean triggered = Math.random() < currentProbability;
        
        if (triggered) {
            // 事件触发，重置概率到基础值
            playerEventStates.put(eventName, baseProbability);
        } else {
            // 事件未触发，增加下次触发的概率
            double newProbability = Math.min(1.0, currentProbability + baseProbability * 0.25);
            playerEventStates.put(eventName, newProbability);
        }
        
        return triggered;
    }
    
    /**
     * 重置玩家的指定事件概率
     */
    public void resetEventProbability(UUID playerId, String eventName, double baseProbability) {
        ConcurrentHashMap<String, Double> playerEventStates = playerStates.get(playerId);
        if (playerEventStates != null) {
            playerEventStates.put(eventName, baseProbability);
        }
    }
    
    /**
     * 清除玩家的所有伪随机状态
     */
    public void clearPlayerStates(UUID playerId) {
        playerStates.remove(playerId);
    }
    
    /**
     * 获取玩家指定事件的当前概率
     */
    public double getCurrentProbability(UUID playerId, String eventName, double baseProbability) {
        ConcurrentHashMap<String, Double> playerEventStates = playerStates.get(playerId);
        if (playerEventStates == null) {
            return baseProbability;
        }
        return playerEventStates.getOrDefault(eventName, baseProbability);
    }
    
    /**
     * 清理所有状态（用于内存管理）
     */
    public void cleanup() {
        playerStates.clear();
    }
}
