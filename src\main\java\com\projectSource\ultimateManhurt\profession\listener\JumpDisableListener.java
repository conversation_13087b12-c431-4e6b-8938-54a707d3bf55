package com.projectSource.ultimateManhurt.profession.listener;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.util.Vector;

/**
 * 跳跃禁用监听器
 * 用于拦截被禁用跳跃的玩家的跳跃行为
 */
public class JumpDisableListener implements Listener {
    
    private final UltimateManhurt plugin;
    
    public JumpDisableListener(UltimateManhurt plugin) {
        this.plugin = plugin;
    }
    
    /**
     * 监听玩家移动事件，检测跳跃行为
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerMove(PlayerMoveEvent event) {
        Player player = event.getPlayer();
        
        // 检查玩家是否被禁用跳跃
        if (!plugin.getProfessionManager().getPassiveSkillHandler().isJumpDisabled(player.getUniqueId())) {
            return; // 没有被禁用跳跃，允许正常移动
        }
        
        // 检查是否是跳跃行为
        if (isJumping(event)) {
            // 取消跳跃，将玩家拉回地面
            cancelJump(event);
            
            // 给予反馈消息（限制频率，避免刷屏）
            if (System.currentTimeMillis() % 1000 < 50) { // 大约每秒最多一次提示
                ComponentUtil.sendMessage(player, ComponentUtil.warning("你当前无法跳跃！"));
            }
        }
    }
    
    /**
     * 检测是否是跳跃行为
     */
    private boolean isJumping(PlayerMoveEvent event) {
        // 检查Y轴向上移动且玩家在地面上
        if (event.getTo() == null || event.getFrom() == null) {
            return false;
        }
        
        double yDiff = event.getTo().getY() - event.getFrom().getY();
        
        // Y轴向上移动超过0.1格，且玩家速度向上，判定为跳跃
        if (yDiff > 0.1) {
            Vector velocity = event.getPlayer().getVelocity();
            return velocity.getY() > 0.1;
        }
        
        return false;
    }
    
    /**
     * 取消跳跃，将玩家拉回地面
     */
    private void cancelJump(PlayerMoveEvent event) {
        Player player = event.getPlayer();
        
        // 将玩家的Y坐标设置为起始位置，取消跳跃
        if (event.getTo() != null && event.getFrom() != null) {
            event.getTo().setY(event.getFrom().getY());
            
            // 清除向上的速度
            Vector velocity = player.getVelocity();
            if (velocity.getY() > 0) {
                velocity.setY(0);
                player.setVelocity(velocity);
            }
        }
    }
}
