# UltimateManhurt Tablist系统 - 问题修复总结

## 修复的问题

### 1. 递归更新错误 (Recursive update)

**问题描述：**
```
java.lang.IllegalStateException: Recursive update
at java.util.concurrent.ConcurrentHashMap.putVal(ConcurrentHashMap.java:1075)
```

**原因分析：**
在 `PlayerJoinEvent` 处理过程中，多个监听器同时访问 `DataManager.getPlayerData()` 方法，导致递归调用：

1. `PlayerListener.onPlayerJoin()` → `DataManager.loadPlayerData()`
2. `RankDisplayManager.onPlayerJoin()` → `DataManager.getPlayerData()`
3. `DataManager.getPlayerData()` 使用 `computeIfAbsent()` → `loadPlayerData()`
4. `loadPlayerData()` → `playerDataCache.put()` → 递归更新

**修复方案：**

#### 1.1 修改 DataManager.getPlayerData() 方法
```java
// 修复前
public PlayerData getPlayerData(UUID playerId) {
    return playerDataCache.computeIfAbsent(playerId, this::loadPlayerData);
}

// 修复后
public PlayerData getPlayerData(UUID playerId) {
    // 先检查缓存，避免递归调用
    PlayerData data = playerDataCache.get(playerId);
    if (data != null) {
        return data;
    }
    
    // 如果缓存中没有，则加载数据
    return loadPlayerData(playerId);
}
```

#### 1.2 调整事件监听器优先级和执行时机
```java
// RankDisplayManager - 延迟执行
@EventHandler(priority = EventPriority.MONITOR)
public void onPlayerJoin(PlayerJoinEvent event) {
    Player player = event.getPlayer();
    
    // 延迟1tick执行，确保PlayerListener先处理完数据加载
    plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
        if (player.isOnline()) {
            updatePlayerRankDisplay(player);
        }
    }, 1L);
}

// TablistListener - 延迟执行
@EventHandler(priority = EventPriority.MONITOR)
public void onPlayerJoin(PlayerJoinEvent event) {
    Player player = event.getPlayer();

    // 延迟2tick创建Tablist，确保数据加载和其他系统初始化完成
    new BukkitRunnable() {
        @Override
        public void run() {
            if (player.isOnline()) {
                createAppropriateTablist(player);
                plugin.getTablistManager().updateAllPlayerDisplayNames();
            }
        }
    }.runTaskLater(plugin, 2L);
}
```

### 2. GameSession.getGameTime() 方法缺失

**问题描述：**
`TablistUtil.buildGameTimeBar()` 调用了不存在的 `gameSession.getGameTime()` 方法。

**修复方案：**
在 `GameSession` 类中添加 `getGameTime()` 方法：

```java
/**
 * 获取游戏时间（毫秒）
 */
public long getGameTime() {
    if (timer == null) {
        return 0L;
    }
    return timer.getElapsedSeconds() * 1000L;
}
```

### 3. Room.getPlayers() 返回类型问题

**问题描述：**
`TablistUtil.buildRoleCountBar()` 需要访问玩家角色信息，但 `Room.getPlayers()` 只返回 `Set<UUID>`。

**修复方案：**
在 `Room` 类中添加新方法：

```java
/**
 * 获取所有玩家及其角色
 */
public Map<UUID, PlayerRole> getPlayersWithRoles() {
    return new HashMap<>(players);
}
```

并修改 `TablistUtil` 中的调用：
```java
// 修复前
long currentCount = room.getPlayers().values().stream()
    .filter(r -> r == role)
    .count();

// 修复后
long currentCount = room.getPlayersWithRoles().values().stream()
    .filter(r -> r == role)
    .count();
```

### 4. 玩家名称颜色基于段位系统

**问题描述：**
原来的 `getPlayerNameColor()` 方法基于权限系统，需要改为基于段位系统。

**修复方案：**
重写 `getPlayerNameColor()` 方法：

```java
/**
 * 获取玩家名称颜色（基于段位）
 */
private static TextColor getPlayerNameColor(Player player) {
    // 首先检查管理员权限
    if (player.hasPermission("ultimatemanhurt.admin")) {
        return NamedTextColor.RED;
    }
    
    // 获取玩家段位并返回对应颜色
    try {
        PlayerData playerData = UltimateManhurt.getInstance()
            .getDataManager().getPlayerData(player.getUniqueId());
        
        if (playerData != null) {
            int elo = EloSystem.getPlayerElo(playerData);
            Rank rank = EloSystem.getRank(elo);
            
            // 根据段位返回颜色
            switch (rank) {
                case GRANDMASTER: return TextColor.color(0xFF0000); // 红色
                case MASTER: return TextColor.color(0x800080);      // 紫色
                case DIAMOND: return TextColor.color(0x00FFFF);     // 青色
                case PLATINUM: return TextColor.color(0x0080FF);    // 蓝色
                case GOLD: return TextColor.color(0xFFD700);        // 金色
                case SILVER: return TextColor.color(0xC0C0C0);      // 银色
                case BRONZE: return TextColor.color(0xCD7F32);      // 铜色
                case IRON:
                default: return TextColor.color(0x808080);          // 灰色
            }
        }
    } catch (Exception e) {
        // 如果获取段位失败，使用默认颜色
    }
    
    return NamedTextColor.WHITE; // 默认白色
}
```

### 5. TablistManager 方法调用问题

**问题描述：**
`TablistManager.updateRoomTablist()` 方法调用了错误的方法。

**修复方案：**
```java
// 修复前
for (UUID playerId : room.getPlayers().keySet()) {

// 修复后  
for (UUID playerId : room.getPlayers()) {
```

## 测试建议

1. **玩家加入测试：** 确保玩家加入服务器时不再出现递归更新错误
2. **段位显示测试：** 验证玩家名称颜色是否正确反映段位
3. **游戏时间显示测试：** 确保游戏中的时间条正常显示
4. **房间信息显示测试：** 验证房间等待界面的角色分配显示正确
5. **性能测试：** 确保延迟执行不会影响用户体验

## 注意事项

1. **事件执行顺序：** 现在的执行顺序是 PlayerListener → RankDisplayManager (1tick后) → TablistListener (2tick后)
2. **线程安全：** 所有的延迟执行都在主线程中进行，确保线程安全
3. **玩家在线检查：** 延迟执行前都会检查玩家是否仍在线
4. **错误处理：** 段位获取失败时会使用默认颜色，不会影响系统稳定性

## 相关文件

- `src/main/java/com/projectSource/ultimateManhurt/data/DataManager.java`
- `src/main/java/com/projectSource/ultimateManhurt/ranking/RankDisplayManager.java`
- `src/main/java/com/projectSource/ultimateManhurt/tablist/TablistListener.java`
- `src/main/java/com/projectSource/ultimateManhurt/tablist/TablistUtil.java`
- `src/main/java/com/projectSource/ultimateManhurt/tablist/TablistManager.java`
- `src/main/java/com/projectSource/ultimateManhurt/game/GameSession.java`
- `src/main/java/com/projectSource/ultimateManhurt/room/Room.java`
