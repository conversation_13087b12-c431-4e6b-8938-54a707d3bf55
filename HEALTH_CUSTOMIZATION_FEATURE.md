# 血量自定义功能实现报告

## 功能概述

实现了房主可以自定义速通者和捕猎者血量的功能。房主可以在房间设置GUI中分别调整速通者和捕猎者的最大血量，范围为1-100血（默认20血）。

## 实现的功能

### 1. 房间设置扩展 (RoomSettings.java)
- 添加了速通者和捕猎者的血量设置字段
- 提供了安全的getter/setter方法，确保血量在1-100范围内
- 在clone方法中包含血量设置的深拷贝

**新增字段：**
```java
private double speedrunnerMaxHealth = 20.0; // 速通者最大血量（默认20血）
private double hunterMaxHealth = 20.0; // 捕猎者最大血量（默认20血）
```

**新增方法：**
- `getSpeedrunnerMaxHealth()` / `setSpeedrunnerMaxHealth(double)`
- `getHunterMaxHealth()` / `setHunterMaxHealth(double)`

### 2. 房间设置GUI扩展 (RoomSettingsGui.java)
- 在房间设置界面中添加了血量调整控件
- 支持鼠标操作和聊天输入两种模式
- 提供直观的血量显示和操作提示

**GUI布局调整：**
- Slot 41: 速通者血量设置
- Slot 42: 捕猎者血量设置
- 重新调整了其他按钮的位置以避免冲突

**操作方式：**
- **鼠标模式**：
  - 左键：-2血量，Shift+左键：-10血量
  - 右键：+2血量，Shift+右键：+10血量
- **聊天输入模式**：精确输入1-100的数值

### 3. 游戏流程集成 (GameSession.java)
- 在游戏开始时根据玩家角色设置相应的最大血量
- 使用现代的Bukkit属性API设置血量
- 修复了弃用的getMaxHealth()方法

**血量应用逻辑：**
```java
// 根据角色设置最大血量
if (role == PlayerRole.SPEEDRUNNER) {
    maxHealth = room.getSettings().getSpeedrunnerMaxHealth();
} else if (role == PlayerRole.HUNTER) {
    maxHealth = room.getSettings().getHunterMaxHealth();
}

// 使用现代API设置血量
player.getAttribute(Attribute.MAX_HEALTH).setBaseValue(maxHealth);
player.setHealth(maxHealth);
```

## 技术特点

### 安全性
- **输入验证**：血量值限制在1-100范围内
- **边界检查**：防止设置无效的血量值
- **异常处理**：处理无效输入并提供用户友好的错误信息

### 用户体验
- **直观显示**：实时显示当前血量设置
- **多种操作方式**：支持鼠标快速调整和精确输入
- **即时反馈**：操作后立即显示结果和确认信息

### 兼容性
- **向后兼容**：默认血量为20，不影响现有游戏
- **现代API**：使用Bukkit 1.11+的属性API，避免弃用警告
- **无缝集成**：与现有的房间设置系统完美集成

## 使用方法

### 房主设置血量
1. 进入房间设置GUI
2. 找到"速通者血量"和"捕猎者血量"设置项
3. 根据当前操作模式进行调整：
   - **鼠标模式**：左右键调整，Shift键加速调整
   - **聊天输入模式**：点击后在聊天栏输入精确数值
4. 设置会自动保存并在下次游戏开始时生效

### 血量设置效果
- **游戏开始时**：玩家的最大血量会根据其角色自动设置
- **角色切换时**：如果玩家在游戏中切换角色，血量会相应调整
- **重生时**：玩家重生后会恢复到设定的最大血量

## 界面展示

### 血量设置项显示
```
🔴 速通者血量
当前: 20 血
范围: 1-100血（默认20血）

当前模式: 鼠标操作
左键: -2血量 | 右键: +2血量
Shift+左键: -10血量 | Shift+右键: +10血量
```

### 操作反馈
- 成功设置：`✅ 速通者血量已设置为: 30 血`
- 达到边界：`❌ 速通者血量已达到最大值(100血)！`
- 无效输入：`❌ 血量必须在1-100之间！`

## 文件修改清单

### 修改的文件
1. **src/main/java/com/projectSource/ultimateManhurt/room/RoomSettings.java**
   - 添加血量字段和相关方法
   - 更新clone方法

2. **src/main/java/com/projectSource/ultimateManhurt/gui/RoomSettingsGui.java**
   - 添加血量设置GUI组件
   - 重新调整按钮布局
   - 添加血量处理方法

3. **src/main/java/com/projectSource/ultimateManhurt/game/GameSession.java**
   - 在resetPlayerState方法中添加血量设置逻辑
   - 修复弃用的API调用

### 新增功能方法
- `handleSpeedrunnerHealth()` - 处理速通者血量设置
- `handleHunterHealth()` - 处理捕猎者血量设置
- `handleSpeedrunnerHealthMouseOperation()` - 鼠标操作处理
- `handleHunterHealthMouseOperation()` - 鼠标操作处理
- `handleSpeedrunnerHealthChatInput()` - 聊天输入处理
- `handleHunterHealthChatInput()` - 聊天输入处理

## 测试建议

### 基础功能测试
1. **血量设置测试**
   - 测试鼠标操作模式的血量调整
   - 测试聊天输入模式的精确设置
   - 验证边界值处理（1血和100血）

2. **游戏流程测试**
   - 创建房间并设置不同的血量值
   - 开始游戏并验证玩家血量是否正确设置
   - 测试角色切换时血量的变化

3. **界面测试**
   - 验证血量设置项的显示和布局
   - 测试操作模式切换
   - 检查错误处理和用户反馈

### 兼容性测试
1. **现有功能测试**
   - 验证其他房间设置功能正常工作
   - 确保血量设置不影响其他游戏机制
   - 测试房间克隆和保存功能

2. **边界情况测试**
   - 测试极端血量值（1血和100血）
   - 验证无效输入的处理
   - 测试网络延迟情况下的操作

## 总结

成功实现了完整的血量自定义功能，包括：
- ✅ 房间设置中的血量配置
- ✅ 直观的GUI界面操作
- ✅ 游戏流程中的血量应用
- ✅ 完整的错误处理和用户反馈
- ✅ 现代API的使用和兼容性

该功能为房主提供了更多的游戏平衡选项，可以根据不同的游戏策略和玩家水平调整血量设置，增强了游戏的可定制性和趣味性。
