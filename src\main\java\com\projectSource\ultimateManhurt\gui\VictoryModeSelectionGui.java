package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.VictoryMode;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

/**
 * 胜利模式选择GUI
 * 让房主选择游戏的胜利条件模式
 */
public class VictoryModeSelectionGui extends BaseGui {
    
    private final Room room;
    
    public VictoryModeSelectionGui(UltimateManhurt plugin, Player player, Room room) {
        super(plugin, player, "选择胜利模式", 45);
        this.room = room;
        setupGui();
    }

    @Override
    protected void setupGui() {
        // 清空界面
        inventory.clear();
        
        // 设置胜利模式选项
        setupVictoryModes();
        
        // 设置控制按钮
        setupControlButtons();
        
        // 设置装饰
        fillEmptySlots();
    }
    
    /**
     * 设置胜利模式选项
     */
    private void setupVictoryModes() {
        VictoryMode currentMode = room.getSettings().getVictoryMode();
        
        // 生命模式
        setItem(10, createVictoryModeItem(VictoryMode.LIFE_MODE, currentMode));

        // 末影龙模式
        setItem(12, createVictoryModeItem(VictoryMode.DRAGON_MODE, currentMode));

        // 积分模式
        setItem(14, createVictoryModeItem(VictoryMode.SCORE_MODE, currentMode));

        // 混合模式
        setItem(16, createVictoryModeItem(VictoryMode.HYBRID_MODE, currentMode));

        // 守卫模式
        setItem(28, createVictoryModeItem(VictoryMode.GUARD_MODE, currentMode));
    }
    
    /**
     * 创建胜利模式物品
     */
    private ItemStack createVictoryModeItem(VictoryMode mode, VictoryMode currentMode) {
        boolean isSelected = mode == currentMode;
        String[] description = mode.getDetailedDescription();
        
        // 构建描述数组
        String[] lore = new String[description.length + 4];
        System.arraycopy(description, 0, lore, 0, description.length);
        lore[description.length] = "";
        lore[description.length + 1] = "<gray>推荐时长: <white>" + mode.getRecommendedDuration() + " 分钟";
        if (mode.requiresScoreSystem()) {
            lore[description.length + 2] = "<gray>推荐积分: <white>" + mode.getRecommendedTargetScore() + " 分";
        } else {
            lore[description.length + 2] = "";
        }

        if (isSelected) {
            lore[description.length + 3] = "<green><bold>✓ 当前选择";
        } else {
            lore[description.length + 3] = "<yellow>点击选择此模式";
        }

        return createItem(
            mode.getIcon(),
            (isSelected ? "<green><bold>" : "<white>") + mode.getDisplayName(),
            lore
        );
    }
    
    /**
     * 设置控制按钮
     */
    private void setupControlButtons() {
        // 确认按钮
        setItem(40, createItem(Material.EMERALD, "<green>确认选择", "<gray>点击确认并返回房间设置"));
        
        // 返回按钮
        setItem(44, createBackButton());
    }
    
    @Override
    public void handleClick(InventoryClickEvent event) {
        // 立即取消事件，防止物品被拿起
        event.setCancelled(true);

        if (event.getClickedInventory() != inventory) {
            return;
        }

        ItemStack item = event.getCurrentItem();
        if (item == null || item.getType() == Material.AIR) {
            return;
        }

        // 播放点击音效
        playClickSound();

        int slot = event.getSlot();

        switch (slot) {
            case 10: // 生命模式
                selectVictoryMode(VictoryMode.LIFE_MODE);
                break;
            case 12: // 末影龙模式
                selectVictoryMode(VictoryMode.DRAGON_MODE);
                break;
            case 14: // 积分模式
                selectVictoryMode(VictoryMode.SCORE_MODE);
                break;
            case 16: // 混合模式
                selectVictoryMode(VictoryMode.HYBRID_MODE);
                break;
            case 28: // 守卫模式
                selectVictoryMode(VictoryMode.GUARD_MODE);
                break;
            case 40: // 确认
                handleConfirm();
                break;
            case 44: // 返回
                handleBack();
                break;
        }
    }
    
    /**
     * 选择胜利模式
     */
    private void selectVictoryMode(VictoryMode mode) {
        room.getSettings().setVictoryMode(mode);
        
        // 根据模式自动调整相关设置
        adjustSettingsForMode(mode);
        
        // 刷新界面
        refresh();
        
        // 播放音效
        playSuccessSound();
        
        ComponentUtil.sendMessage(player, ComponentUtil.success("已选择胜利模式: " + mode.getDisplayName()));
    }
    
    /**
     * 根据胜利模式调整相关设置
     */
    private void adjustSettingsForMode(VictoryMode mode) {
        // 调整游戏时长为推荐时长
        room.getSettings().setGameDurationMinutes(mode.getRecommendedDuration());

        // 调整目标积分
        if (mode.requiresScoreSystem()) {
            room.getSettings().setTargetScore(mode.getRecommendedTargetScore());
        }

        // 根据不同模式调整生命系统设置
        switch (mode) {
            case LIFE_MODE:
                // 生命模式：速通者有3条生命
                room.getSettings().setSpeedrunnerLives(3);
                room.getSettings().setAllowSpeedrunnerRespawn(true);
                room.getSettings().setHunterLives(0); // 捕猎者无限生命
                room.getSettings().setAllowHunterRespawn(true);
                break;

            case DRAGON_MODE:
                // 末影龙模式：速通者有无限生命，专注于击杀末影龙
                room.getSettings().setSpeedrunnerLives(0); // 无限生命
                room.getSettings().setAllowSpeedrunnerRespawn(true);
                room.getSettings().setHunterLives(0); // 捕猎者无限生命
                room.getSettings().setAllowHunterRespawn(true);
                break;

            case SCORE_MODE:
                // 积分模式：速通者有5条生命，平衡积分获取难度
                room.getSettings().setSpeedrunnerLives(5); // 更多生命用于积分收集
                room.getSettings().setAllowSpeedrunnerRespawn(true);
                room.getSettings().setHunterLives(0); // 捕猎者无限生命
                room.getSettings().setAllowHunterRespawn(true);
                break;

            case HYBRID_MODE:
                // 混合模式：平衡设置
                room.getSettings().setSpeedrunnerLives(4); // 中等生命数
                room.getSettings().setAllowSpeedrunnerRespawn(true);
                room.getSettings().setHunterLives(0); // 捕猎者无限生命
                room.getSettings().setAllowHunterRespawn(true);
                break;

            case GUARD_MODE:
                // 守卫模式：速通者有3条生命，捕猎者有无限生命
                room.getSettings().setSpeedrunnerLives(3); // 守卫者需要生命限制
                room.getSettings().setAllowSpeedrunnerRespawn(true);
                room.getSettings().setHunterLives(0); // 攻城者无限生命
                room.getSettings().setAllowHunterRespawn(true);
                // 设置推荐游戏时长
                room.getSettings().setGameDurationMinutes(mode.getRecommendedDuration());
                break;
        }

        plugin.getLogger().info("玩家 " + player.getName() + " 选择了胜利模式: " + mode.getDisplayName() +
            ", 速通者生命数: " + room.getSettings().getSpeedrunnerLives());
    }
    
    /**
     * 处理确认
     */
    private void handleConfirm() {
        close();
        
        // 返回房间设置GUI
        Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
        if (currentRoom != null) {
            plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
        } else {
            ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
            plugin.getGuiManager().openRoomListGui(player);
        }
    }
    
    /**
     * 处理返回
     */
    private void handleBack() {
        close();
        
        // 返回房间设置GUI
        Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
        if (currentRoom != null) {
            plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
        } else {
            ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
            plugin.getGuiManager().openRoomListGui(player);
        }
    }
    
    /**
     * 填充空槽位
     */
    private void fillEmptySlots() {
        ItemStack filler = createItem(Material.GRAY_STAINED_GLASS_PANE, " ");
        
        for (int i = 0; i < size; i++) {
            if (inventory.getItem(i) == null) {
                setItem(i, filler);
            }
        }
    }
}
