# 巫毒诅咒和酿造系统修复报告

## ⚡ 巫毒诅咒时间调整

### 修复内容
将巫毒诅咒从8秒每秒一跳改为12秒每1.5秒一跳

### 修复前
```java
private void startVoodooCurseEffect() {
    new BukkitRunnable() {
        int seconds = 0;
        
        @Override
        public void run() {
            if (seconds >= 8) { // 8秒后结束
                this.cancel();
                return;
            }
            
            // 每秒执行伤害
            for (Map.Entry<UUID, Long> entry : voodooAffectedPlayers.entrySet()) {
                // 造成5%当前生命值伤害
            }
            
            seconds++;
        }
    }.runTaskTimer(plugin, 0L, 20L); // 每20 tick（1秒）执行一次
}

// 标记持续时间
voodooAffectedPlayers.put(target.getUniqueId(), System.currentTimeMillis() + 8000); // 8秒
```

### 修复后
```java
private void startVoodooCurseEffect() {
    new BukkitRunnable() {
        int ticks = 0;
        
        @Override
        public void run() {
            if (ticks >= 240) { // 12秒 = 240 tick
                this.cancel();
                return;
            }
            
            // 每30 tick执行一次伤害
            if (ticks % 30 == 0) {
                for (Map.Entry<UUID, Long> entry : voodooAffectedPlayers.entrySet()) {
                    // 造成5%当前生命值伤害
                }
            }
            
            ticks++;
        }
    }.runTaskTimer(plugin, 0L, 1L); // 每1 tick执行，但伤害每30 tick一次
}

// 标记持续时间
voodooAffectedPlayers.put(target.getUniqueId(), System.currentTimeMillis() + 12000); // 12秒
```

### 改进特性
- ✅ **持续时间**：从8秒延长到12秒
- ✅ **伤害频率**：从每20 tick（1秒）改为每30 tick（1.5秒）
- ✅ **总伤害次数**：从8次改为8次（12秒÷1.5秒=8次）
- ✅ **伤害强度**：每次仍然是5%当前生命值
- ✅ **死亡条件**：血量低于25%最大生命值时立即死亡

## 🧪 酿造系统修复

### 问题描述
女巫无法通过酿造台合成魔药和毒药，只能通过背包右键转换

### 修复前
- ❌ 只能通过背包右键点击转换
- ❌ 无法使用酿造台正常合成
- ❌ 不符合Minecraft原版机制

### 修复后
```java
/**
 * 处理酿造事件（女巫药水转换）
 */
@EventHandler
public void onBrew(BrewEvent event) {
    BrewerInventory inventory = event.getContents();
    
    // 检查是否有女巫在附近（10格内）
    Player nearbyWitch = null;
    for (Player player : Bukkit.getOnlinePlayers()) {
        Profession profession = plugin.getProfessionManager().getPlayerProfession(player.getUniqueId());
        if (profession == Profession.WITCH) {
            double distance = player.getLocation().distance(event.getBlock().getLocation());
            if (distance <= 10.0) {
                nearbyWitch = player;
                break;
            }
        }
    }
    
    if (nearbyWitch == null) return;
    
    // 延迟处理，确保酿造完成
    final Player witch = nearbyWitch;
    Bukkit.getScheduler().runTaskLater(plugin, () -> {
        // 检查酿造台中的药水
        for (int i = 0; i < 3; i++) { // 酿造台有3个药水槽位
            ItemStack item = inventory.getItem(i);
            if (item != null) {
                // 检查是否是普通药水或喷溅药水
                if (item.getType() == Material.POTION && !passiveSkillHandler.isMagicPotion(item)) {
                    // 转换为魔药
                    passiveSkillHandler.handleWitchPotionMastery(witch, item);
                } else if (item.getType() == Material.SPLASH_POTION && !passiveSkillHandler.isPoisonPotion(item)) {
                    // 转换为毒药
                    passiveSkillHandler.handleWitchPotionMastery(witch, item);
                }
            }
        }
    }, 1L); // 延迟1 tick
}
```

### 改进特性
- ✅ **自动检测**：女巫在10格内时自动转换酿造台中的药水
- ✅ **延迟处理**：确保酿造完成后再转换
- ✅ **批量转换**：一次酿造可以转换多个药水
- ✅ **避免重复**：不会重复转换已转换的药水
- ✅ **符合原版**：使用正常的酿造机制

## 📊 巫毒诅咒效果对比

| 属性 | 修复前 | 修复后 | 变化 |
|------|--------|--------|------|
| **持续时间** | 8秒 | 12秒 | +50% |
| **伤害频率** | 每1秒 | 每1.5秒 | -33% |
| **总伤害次数** | 8次 | 8次 | 不变 |
| **单次伤害** | 5%当前血量 | 5%当前血量 | 不变 |
| **死亡条件** | <25%最大血量 | <25%最大血量 | 不变 |

### 平衡性分析
- **持续时间增加**：给予敌人更多时间逃脱或反击
- **伤害频率降低**：减少了瞬间爆发伤害
- **总伤害保持**：整体威胁性基本不变
- **战术性增强**：需要更好的时机选择

## 🔧 技术实现

### 巫毒诅咒时间控制
```java
// 任务执行频率：每1 tick
.runTaskTimer(plugin, 0L, 1L)

// 伤害执行条件：每30 tick一次
if (ticks % 30 == 0) {
    // 执行伤害逻辑
}

// 总持续时间：240 tick = 12秒
if (ticks >= 240) {
    this.cancel();
}
```

### 酿造事件监听
```java
// 监听酿造完成事件
@EventHandler
public void onBrew(BrewEvent event)

// 检查女巫范围
double distance = player.getLocation().distance(event.getBlock().getLocation());
if (distance <= 10.0) {
    // 在范围内，可以转换
}

// 延迟处理确保酿造完成
Bukkit.getScheduler().runTaskLater(plugin, () -> {
    // 转换逻辑
}, 1L);
```

## 🎮 用户体验改进

### 巫毒诅咒
- ✅ **更长持续时间**：给予更多战术选择空间
- ✅ **较低伤害频率**：减少瞬间爆发，增加反应时间
- ✅ **保持威胁性**：总伤害次数不变，仍然致命

### 酿造系统
- ✅ **符合直觉**：使用酿造台就能自动转换
- ✅ **范围检测**：女巫需要在附近才能转换
- ✅ **批量处理**：一次酿造多个药水都会转换
- ✅ **避免冲突**：不会影响其他玩家的正常酿造

## ✅ 修复完成清单

### 巫毒诅咒调整
- ✅ 持续时间从8秒改为12秒
- ✅ 伤害频率从每20 tick改为每30 tick
- ✅ 保持总伤害次数为8次
- ✅ 保持单次伤害为5%当前生命值

### 酿造系统
- ✅ 添加了BrewEvent监听
- ✅ 实现了10格范围检测
- ✅ 添加了延迟处理机制
- ✅ 实现了批量药水转换
- ✅ 避免了重复转换问题

### 代码质量
- ✅ 添加了必要的导入
- ✅ 优化了事件处理逻辑
- ✅ 提高了代码可读性
- ✅ 确保了类型安全

现在巫毒诅咒持续12秒每1.5秒一跳，女巫可以通过酿造台正常合成魔药和毒药！⚡🧙‍♀️✨
