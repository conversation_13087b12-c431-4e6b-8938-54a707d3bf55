package com.projectSource.ultimateManhurt.listener;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.util.ComponentUtil;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerChangedWorldEvent;
import org.bukkit.event.player.PlayerPortalEvent;
import org.bukkit.event.world.WorldLoadEvent;
import org.bukkit.event.world.WorldUnloadEvent;

import java.util.UUID;

/**
 * 世界事件监听器
 */
public class WorldListener implements Listener {

    private final UltimateManhurt plugin;

    public WorldListener(UltimateManhurt plugin) {
        this.plugin = plugin;
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerPortal(PlayerPortalEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();

        // 检查是否在游戏中
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(playerId);
        if (gameSession == null) {
            return; // 不在游戏中，使用默认传送门行为
        }

        // 检查传送门冷却
        if (!gameSession.getRules().canEnterPortal(player)) {
            event.setCancelled(true);

            long lastUse = gameSession.getLastPortalUse(playerId);
            long delay = gameSession.getRoom().getSettings().getNetherPortalDelaySeconds();
            long remaining = delay - (System.currentTimeMillis() - lastUse) / 1000;

            ComponentUtil.sendMessage(player, ComponentUtil.warning(
                "传送门冷却中！还需等待 " + remaining + " 秒"
            ));
            return;
        }

        // 记录传送门使用
        gameSession.getRules().recordPortalUse(player);

        // DimensionBinder会处理实际的传送逻辑
        plugin.getLogger().info("玩家 " + player.getName() + " 使用传送门");
    }

    @EventHandler
    public void onPlayerChangedWorld(PlayerChangedWorldEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();

        // 检查是否在游戏中
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(playerId);
        if (gameSession == null) {
            return;
        }

        // 检查是否切换到了游戏世界
        String worldName = player.getWorld().getName();
        String roomId = plugin.getWorldManager().getRoomIdByWorld(worldName);

        if (roomId != null && roomId.equals(gameSession.getRoom().getId())) {
            // 玩家进入了游戏世界
            ComponentUtil.sendMessage(player, ComponentUtil.info("欢迎来到游戏世界！"));

            // 创建或更新计分板
            plugin.getScoreboardManager().createGameScoreboard(player, gameSession);

            // 根据维度发送不同的消息
            switch (player.getWorld().getEnvironment()) {
                case NORMAL:
                    ComponentUtil.sendMessage(player, ComponentUtil.parse("<green>你现在在主世界"));
                    break;
                case NETHER:
                    ComponentUtil.sendMessage(player, ComponentUtil.parse("<red>你现在在下界"));
                    break;
                case THE_END:
                    ComponentUtil.sendMessage(player, ComponentUtil.parse("<dark_purple>你现在在末地"));
                    break;
                case CUSTOM:
                    ComponentUtil.sendMessage(player, ComponentUtil.parse("<color:#8d5caa>你现在在自定义世界"));
                    break;
            }
        } else {
            // 玩家离开了游戏世界
            plugin.getScoreboardManager().removeScoreboard(player);
        }

        plugin.getLogger().info("玩家 " + player.getName() + " 从 " +
            event.getFrom().getName() + " 切换到 " + player.getWorld().getName());
    }

    @EventHandler
    public void onWorldLoad(WorldLoadEvent event) {
        String worldName = event.getWorld().getName();

        // 检查是否是游戏世界
        String roomId = plugin.getWorldManager().getRoomIdByWorld(worldName);
        if (roomId != null) {
            plugin.getLogger().info("游戏世界已加载: " + worldName + " (房间: " + roomId + ")");

            // 可以在这里进行世界加载后的初始化
            setupGameWorld(event.getWorld());
        }
    }

    @EventHandler
    public void onWorldUnload(WorldUnloadEvent event) {
        String worldName = event.getWorld().getName();

        // 检查是否是游戏世界
        String roomId = plugin.getWorldManager().getRoomIdByWorld(worldName);
        if (roomId != null) {
            plugin.getLogger().info("游戏世界即将卸载: " + worldName + " (房间: " + roomId + ")");

            // 确保所有玩家都已离开
            if (!event.getWorld().getPlayers().isEmpty()) {
                plugin.getLogger().warning("游戏世界 " + worldName + " 中仍有玩家，取消卸载");
                event.setCancelled(true);

                // 强制传送所有玩家离开
                for (Player player : event.getWorld().getPlayers()) {
                    plugin.getWorldManager().teleportToMainWorld(player);
                    ComponentUtil.sendMessage(player, ComponentUtil.warning("游戏世界即将关闭，已将你传送到主世界"));
                }
            }
        }
    }

    /**
     * 设置游戏世界
     */
    private void setupGameWorld(org.bukkit.World world) {
        // 设置世界规则
        world.setGameRule(org.bukkit.GameRule.ANNOUNCE_ADVANCEMENTS, false);
        world.setGameRule(org.bukkit.GameRule.DO_IMMEDIATE_RESPAWN, false);
        world.setGameRule(org.bukkit.GameRule.SPECTATORS_GENERATE_CHUNKS, false);

        // 根据环境设置特殊规则
        switch (world.getEnvironment()) {
            case NORMAL:
                // 主世界设置
                world.setGameRule(org.bukkit.GameRule.DO_PATROL_SPAWNING, false);
                world.setGameRule(org.bukkit.GameRule.DO_TRADER_SPAWNING, false);
                break;
            case NETHER:
                // 下界设置
                world.setGameRule(org.bukkit.GameRule.DO_FIRE_TICK, true);
                break;
            case THE_END:
                // 末地设置
                world.setGameRule(org.bukkit.GameRule.DO_MOB_SPAWNING, true);
                break;
            case CUSTOM:
                // 自定义世界设置
                break;
        }

        plugin.getLogger().info("游戏世界 " + world.getName() + " 设置完成");
    }
}
