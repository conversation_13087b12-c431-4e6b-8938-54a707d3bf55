# 定位条游戏规则添加

## 🎯 新增功能

在房间设置中添加了 **定位条功能** 作为可配置的游戏规则选项，让房主可以控制是否启用定位条功能。

## 🔧 技术实现

### 1. RoomSettings.java 修改

**添加的字段：**
```java
private boolean locatorBar = true; // 定位条功能
```

**添加的方法：**
```java
public boolean isLocatorBar() { return locatorBar; }
public void setLocatorBar(boolean locatorBar) { this.locatorBar = locatorBar; }
```

### 2. RoomSettingsGui.java 修改

**GUI布局调整：**
- **位置 32：** 新增定位条功能开关
- **位置 33：** 末影珍珠冷却（原位置 32）
- **位置 34：** Ban Pick系统（原位置 33）
- **位置 35：** 职业系统（原位置 34）

**显示设置：**
```java
// 定位条功能
setItem(32, createToggleItem(Material.RECOVERY_COMPASS, "定位条功能", settings.isLocatorBar()));
```

**点击处理：**
```java
case 32: // 定位条功能
    settings.setLocatorBar(!settings.isLocatorBar());
    refresh();
    break;
```

## 📊 GUI布局更新

### 特殊功能区域（第4行）

| 位置 | 功能 | 图标 | 说明 |
|------|------|------|------|
| 31 | 指南针追踪 | COMPASS | 原有功能 |
| **32** | **定位条功能** | **RECOVERY_COMPASS** | **新增功能** |
| 33 | 末影珍珠冷却 | ENDER_PEARL | 位置调整 |
| 34 | Ban Pick系统 | BARRIER | 位置调整 |
| 35 | 职业系统 | ENCHANTED_BOOK | 位置调整 |

## 🎮 用户体验

### 房主操作
1. **开启定位条：** 点击定位条功能按钮，图标变为绿色
2. **关闭定位条：** 再次点击，图标变为红色
3. **实时预览：** 设置立即生效，无需保存

### 功能说明
- **默认状态：** 启用（true）
- **图标材质：** RECOVERY_COMPASS（恢复指南针）
- **切换方式：** 单击切换开关状态

## 🔄 兼容性

### 向后兼容
- **现有房间：** 自动启用定位条功能（默认值为 true）
- **配置文件：** 自动添加新字段，不影响现有配置
- **游戏逻辑：** 不影响其他游戏功能

### 数据持久化
- **保存机制：** 随房间设置一起保存
- **加载机制：** 房间加载时自动读取设置
- **默认值：** 新房间默认启用定位条功能

## 🎯 使用场景

### 适合启用定位条的情况
- **新手友好：** 帮助新玩家了解游戏进度
- **观赏模式：** 观众可以清楚看到游戏状态
- **竞技比赛：** 提供标准化的信息显示

### 适合禁用定位条的情况
- **纯净体验：** 追求原版Minecraft体验
- **高难度挑战：** 增加游戏难度和不确定性
- **自定义规则：** 特殊游戏模式需求

## 📝 开发注意事项

### 后续集成
当实现定位条功能时，需要在相关代码中检查此设置：

```java
// 示例：在定位条更新逻辑中检查设置
if (room.getSettings().isLocatorBar()) {
    // 更新定位条显示
    updateLocatorBar();
} else {
    // 隐藏定位条
    hideLocatorBar();
}
```

### 建议的实现位置
- **游戏管理器：** 在游戏开始时根据设置启用/禁用定位条
- **HUD管理器：** 控制定位条的显示和隐藏
- **事件监听器：** 根据游戏事件更新定位条状态

## 🎉 总结

成功添加了定位条功能作为房间设置中的可配置选项：

- ✅ **数据模型更新：** RoomSettings 类添加了 locatorBar 字段
- ✅ **GUI界面更新：** 房间设置界面添加了定位条开关
- ✅ **交互逻辑完善：** 点击处理和状态切换正常工作
- ✅ **布局重新排版：** 其他选项位置相应调整
- ✅ **向后兼容：** 不影响现有功能和数据

现在房主可以在房间设置中自由控制是否启用定位条功能，为不同类型的游戏提供更灵活的配置选项！🎮✨
