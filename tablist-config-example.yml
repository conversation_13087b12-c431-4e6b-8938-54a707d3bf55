# UltimateManhurt Tablist系统配置示例
# 这是一个完整的配置示例，展示了所有可用的配置选项

# Tablist系统设置
tablist:
  # 更新间隔（tick）- 控制Tablist刷新频率
  # 20 tick = 1秒，建议值：10-40
  update-interval: 20
  
  # 玩家显示名称设置
  player-display:
    # 是否启用自定义玩家显示名称
    enabled: true
    
    # 是否显示延迟指示器
    show-ping: true
    
    # 是否显示血量指示器
    show-health: true
    
    # 是否显示角色指示器（仅在游戏中）
    show-role: true
    
    # 延迟阈值设置（毫秒）
    ping-thresholds:
      good: 50      # 低于此值显示绿色
      medium: 150   # 低于此值显示黄色，高于此值显示红色
    
    # 血量阈值设置（百分比）
    health-thresholds:
      excellent: 75   # 高于此值显示绿色
      good: 50        # 高于此值显示黄色
      medium: 25      # 高于此值显示橙色，低于此值显示红色
  
  # 头部动画设置
  header:
    # 是否启用头部动画
    animation-enabled: true
    
    # 动画帧切换间隔（更新周期数）
    animation-interval: 3
  
  # 底部信息显示设置
  footer:
    # 大厅状态显示的信息
    lobby:
      show-online-players: true
      show-ping: true
      show-tps: true
      show-memory: true
      show-entities: true
    
    # 房间等待状态显示的信息
    room:
      show-room-info: true
      show-player-count: true
      show-role-distribution: true
      show-ping: true
    
    # 游戏中状态显示的信息
    game:
      show-game-time: true
      show-alive-players: true
      show-ping: true
      show-tps: true
      show-memory: true
  
  # 进度条设置
  progress-bars:
    # 进度条长度（字符数）
    length: 10
    
    # 是否使用渐变颜色
    gradient-colors: true
    
    # 自定义颜色设置
    colors:
      excellent: "#00FF00"  # 绿色
      good: "#FFFF00"       # 黄色
      warning: "#FFA500"    # 橙色
      critical: "#FF0000"   # 红色
  
  # 权限组颜色设置
  permission-colors:
    admin: "#FF0000"        # 管理员 - 红色
    vip: "#FFD700"          # VIP - 金色
    member: "#00FF00"       # 成员 - 绿色
    default: "#FFFFFF"      # 默认 - 白色
  
  # 角色图标设置
  role-icons:
    speedrunner: "⚔"        # 速通者图标
    hunter: "🏹"            # 猎人图标
    spectator: "👁"         # 观察者图标
  
  # 状态图标设置
  status-icons:
    health: "❤"             # 血量图标
    ping: "●"               # 延迟图标
    tps: "🔘"              # TPS图标
    memory: "💾"            # 内存图标
    entities: "🎯"          # 实体图标
    players: "👥"           # 玩家图标
    time: "⏱"              # 时间图标
    room: "🏠"              # 房间图标
  
  # 服务器信息设置
  server-info:
    # 服务器名称（显示在头部）
    name: "ULTIMATE MANHUNT"
    
    # 服务器标语
    tagline: "猎杀游戏进行中"
    
    # 大厅标语
    lobby-tagline: "游戏大厅"
    
    # 房间标语
    room-tagline: "等待游戏开始"
  
  # 性能监控设置
  performance:
    # TPS警告阈值
    tps-warning: 18.0
    tps-critical: 15.0
    
    # 内存使用警告阈值（百分比）
    memory-warning: 80
    memory-critical: 90
    
    # 实体数量警告阈值
    entity-warning: 1000
    entity-critical: 2000
  
  # 调试设置
  debug:
    # 是否启用调试日志
    enabled: false
    
    # 是否显示更新时间
    show-update-time: false
    
    # 是否显示性能统计
    show-performance-stats: false

# 计分板设置（保持兼容性）
scoreboard:
  # 更新间隔（tick）
  update-interval: 20
  # 服务器IP显示
  server-ip: "mc.example.com"

# 世界设置（保持兼容性）
world:
  # 世界名称前缀（用于生成游戏世界名称）
  name-prefix: "manhunt_"
